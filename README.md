<p align="center">
    <a href="https://github.com/yiisoft" target="_blank">
        <img src="https://avatars0.githubusercontent.com/u/993323" height="100px">
    </a>
    <h1 align="center">Yii 2 ESTOCK project</h1>
    <br>
</p>

GASTAN SERVER INSTALLATION postup
---------------------------------

### pgdump

- nahradene "TO postgres;" na "TO dba;"
- phppgadmin z apt, modifikovane configy
- weesee/yii2-pdflabel obsahuje moj subor, modifikovany
- crontab kopie
- parsovanie error v runtime/logs a odstranovanie neexistujucich variables

# locale-gen hu_HU && locale-gen hu_HU.UTF-8 && update-locale
apt install git
apt install apache2
apt install mc
apt install certbot
apt install postgresql
apt install net-tools
apt install php
apt install php-cli unzip
curl -sS https://getcomposer.org/installer -o /tmp/composer-setup.php
HASH=`curl -sS https://composer.github.io/installer.sig`
sudo php /tmp/composer-setup.php --install-dir=/usr/local/bin --filename=composer
apt install curl
composer install
php ./composer.phar install
php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');"
php ./composer.phar install
composer install
apt install php-xml
apt install php-dom
apt install php-db
apt install php-pgsql
apt install php-gd
apt install php-zip
apt install php-mbstring
apt-get install libreoffice --no-install-recommends
#apt  install mysql-server
#apt  install mysql-server
#apt-get install ia32-libs
#sudo apt install php-dev php-pear unixodbc unixodbc-dev
#sudo pecl install pdo_sqlanywhere
#sudo pecl install channel://pecl.php.net/pdo_sqlanywhere-0.1.4
sudo pecl install pdo
sudo apt install build-essential php-dev php-pear unixodbc unixodbc-dev
sudo pecl install pdo
sudo pecl install channel://pecl.php.net/pdo_sqlanywhere-0.1.4
apt install php-dev
pecl install pdo
apt install php-curl
apt install phppgadmin
  a2enmod ssl
  a2enmod sqlanywhere
  a2ensite ...vsetky potrebne
  a2enmod headers
  a2enmod rewrite
  a2ensite phppgadmin.conf // a to aj zaedituj v /etc/apache

~~~
php composer.phar create-project --prefer-dist --stability=dev yiisoft/yii2-app-basic basic
~~~


DB UPGRADE:
- see 2 new tables for merging from hybrid estock
-


```sql
CREATE  INDEX "tempmove_user_name_clip_id" ON "DBA"."tempmove"
    ( "user_name","clip_id" )
go

drop view m_detail_prod;
create view m_detail_prod as
select d.move_id, d.m_repli_id as repli_id, d.mat_id,	kod,	model,	d.price,	pcs,discount,	d.tax,	currency,	detail_info from dba.m_detail d join dba.product p on d.mat_id=p.mat_id;


//!//
create procedure DBA.pridajasuser( in adresa integer,in material decimal(13),in clipid integer,in poc decimal(11,3),in ddoctyp nchar(3),in cena decimal(11,2),in arepliid integer,in dd1 date,in dd2 date,in dd3 date,in dm_type_id smallint,in dprice_type nchar(2),in dstock_id1 nchar(3),in dstock_id2 nchar(3),in dtext1 nvarchar(30),in dtext2 nvarchar(255),in tempinfo nvarchar(255), in xuser nvarchar(255) ) 
begin atomic
  declare zlava decimal(5,3);
  declare kkod nchar(4);
  declare xxean13 nchar(13);
  declare xxmaterial integer;
  declare xxxmaterial integer;
  declare xxtax decimal(2);
  declare xspecprice nchar(3);
  declare xxsqltxt nchar(255);
  //special price, not from left table:
  set xspecprice = (select price from dba.office where active = 1);
  if xspecprice = any(select column_name from sys.syscolumn where table_id = any(select table_id from sys.systable where table_name in( 'product' ) )) then
    set xxsqltxt = 'set cena = (select ' || xspecprice || ' from "dba".product where mat_id=material )';
    execute immediate xxsqltxt
  end if;
  //set xxean13=String(material);
  set xxxmaterial = (select mat_id from dba.product where mat_id = material);
  set xxmaterial = (select mat_id from dba.product where ean13 = xxean13);
  if xxmaterial is not null then
    set material = xxmaterial
  elseif xxxmaterial is null then
    return
  end if;
  if(select 1 from dba.tempmove where mat_id = material and clip_id = clipid and user_name = xuser and tempmove_info = tempinfo) is not null then
    update dba.tempmove set pcs = pcs+poc where mat_id = material and clip_id = clipid and user_name = xuser and tempmove_info = tempinfo
  else
    set xxtax = (select tax from dba.product where mat_id = material);
    if xxtax = 1 then
      set xxtax = (select tax1 from dba.office where active = 1)
    elseif xxtax = 2 then
      set xxtax = (select tax2 from dba.office where active = 1)
    else
      set xxtax = 0
    end if;
    if dm_type_id in( 20,86,89,110,5,14,16,56,67,70,106,30,31,99,106,403,404,423,424,444 ) then set xxtax = 0
    end if;
    insert into dba.tempmove( mat_id,pcs,price,tax,discount,all1,all2,all3,all4,all5,
      ean13,kod,model,pdescr,unit,currency,clip_id,d1,d2,d3,m_type_id,price_type,
      stock_id1,stock_id2,text1,text2,user_name,tempmove_info,fifo_move_id ) values( material,poc,cena,xxtax,0,0,0,0,0,0,
      (select ean13 from dba.product where mat_id = material),
      (select kod from dba.product where mat_id = material),
      (select model from dba.product where mat_id = material),
      (select pdescr from dba.product where mat_id = material),'',
      (select currency from dba.office where active = 1),
      clipid,dd1,dd2,dd3,dm_type_id,dprice_type,dstock_id1,dstock_id2,dtext1,dtext2,
      xuser,tempinfo,(select ifnull(max(fifo_move_id),1,max(fifo_move_id)+1) from dba.tempmove where clip_id = clipid and user_name = xuser) ) ;
    if ddoctyp = 'E' then
      update dba.tempmove set fifo_move_id = (select ifnull(max(fifo_move_id),1,max(fifo_move_id)+1) from dba.tempmove where clip_id = clipid and user_name = xuser),tax = 0 where
 mat_id = material and clip_id = clipid and user_name = xuser and tempmove_info = tempinfo
    end if
  end if
end;

# Nov2020 -- some things for eshop/wstock stats
wstock=# alter table m_type alter stock_id1 type varchar(20);
ALTER TABLE
wstock=# alter table m_type alter stock_id2 type varchar(20);
ALTER TABLE
wstock=# insert into m_type (m_type_id,name,doc_id,flag,usergroup,moving,stock_id1,stock_id2) values (504,'Szamla //Eshop in shop for stats','Ho',1,'storekeepers H','S','WSHU',null);
INSERT 0 1
wstock=# insert into m_type (m_type_id,name,doc_id,flag,usergroup,moving,stock_id1,stock_id2) values (505,'Jóváírás //Eshop in shop for stats','Ho',1,'storekeepers H','R',null,'WSHU');
INSERT 0 1


```

Yii 2 Basic Project Template is a skeleton [Yii 2](http://www.yiiframework.com/) application best for
rapidly creating small projects.

The template contains the basic features including user login/logout and a contact page.
It includes all commonly used configurations that would allow you to focus on adding new
features to your application.

[![Latest Stable Version](https://img.shields.io/packagist/v/yiisoft/yii2-app-basic.svg)](https://packagist.org/packages/yiisoft/yii2-app-basic)
[![Total Downloads](https://img.shields.io/packagist/dt/yiisoft/yii2-app-basic.svg)](https://packagist.org/packages/yiisoft/yii2-app-basic)
[![Build Status](https://travis-ci.org/yiisoft/yii2-app-basic.svg?branch=master)](https://travis-ci.org/yiisoft/yii2-app-basic)

DIRECTORY STRUCTURE
-------------------

      assets/             contains assets definition
      commands/           contains console commands (controllers)
      config/             contains application configurations
      controllers/        contains Web controller classes
      mail/               contains view files for e-mails
      models/             contains model classes
      runtime/            contains files generated during runtime
      tests/              contains various tests for the basic application
      vendor/             contains dependent 3rd-party packages
      views/              contains view files for the Web application
      web/                contains the entry script and Web resources



REQUIREMENTS
------------

The minimum requirement by this project template that your Web server supports PHP 5.4.0.


INSTALLATION
------------

### Install via Composer

If you do not have [Composer](http://getcomposer.org/), you may install it by following the instructions
at [getcomposer.org](http://getcomposer.org/doc/00-intro.md#installation-nix).

You can then install this project template using the following command:

~~~
php composer.phar create-project --prefer-dist --stability=dev yiisoft/yii2-app-basic basic
~~~

Now you should be able to access the application through the following URL, assuming `basic` is the directory
directly under the Web root.

~~~
http://localhost/basic/web/
~~~

### Install from an Archive File

Extract the archive file downloaded from [yiiframework.com](http://www.yiiframework.com/download/) to
a directory named `basic` that is directly under the Web root.

Set cookie validation key in `config/web.php` file to some random secret string:

```php
'request' => [
    // !!! insert a secret key in the following (if it is empty) - this is required by cookie validation
    'cookieValidationKey' => '<secret random string goes here>',
],
```

You can then access the application through the following URL:

~~~
http://localhost/basic/web/
~~~


### Install with Docker

Update your vendor packages

    docker-compose run --rm php composer update --prefer-dist
    
Run the installation triggers (creating cookie validation code)

    docker-compose run --rm php composer install    
    
Start the container

    docker-compose up -d
    
You can then access the application through the following URL:

    http://127.0.0.1:8000

**NOTES:** 
- Minimum required Docker engine version `17.04` for development (see [Performance tuning for volume mounts](https://docs.docker.com/docker-for-mac/osxfs-caching/))
- The default configuration uses a host-volume in your home directory `.docker-composer` for composer caches


CONFIGURATION
-------------

### Database

Edit the file `config/db.php` with real data, for example:

```php
return [
    'class' => 'yii\db\Connection',
    'dsn' => 'mysql:host=localhost;dbname=yii2basic',
    'username' => 'root',
    'password' => '1234',
    'charset' => 'utf8',
];
```

**NOTES:**
- Yii won't create the database for you, this has to be done manually before you can access it.
- Check and edit the other files in the `config/` directory to customize your application as required.
- Refer to the README in the `tests` directory for information specific to basic application tests.


TESTING
-------

Tests are located in `tests` directory. They are developed with [Codeception PHP Testing Framework](http://codeception.com/).
By default there are 3 test suites:

- `unit`
- `functional`
- `acceptance`

Tests can be executed by running

```
vendor/bin/codecept run
```

The command above will execute unit and functional tests. Unit tests are testing the system components, while functional
tests are for testing user interaction. Acceptance tests are disabled by default as they require additional setup since
they perform testing in real browser. 


### Running  acceptance tests

To execute acceptance tests do the following:  

1. Rename `tests/acceptance.suite.yml.example` to `tests/acceptance.suite.yml` to enable suite configuration

2. Replace `codeception/base` package in `composer.json` with `codeception/codeception` to install full featured
   version of Codeception

3. Update dependencies with Composer 

    ```
    composer update  
    ```

4. Download [Selenium Server](http://www.seleniumhq.org/download/) and launch it:

    ```
    java -jar ~/selenium-server-standalone-x.xx.x.jar
    ```

    In case of using Selenium Server 3.0 with Firefox browser since v48 or Google Chrome since v53 you must download [GeckoDriver](https://github.com/mozilla/geckodriver/releases) or [ChromeDriver](https://sites.google.com/a/chromium.org/chromedriver/downloads) and launch Selenium with it:

    ```
    # for Firefox
    java -jar -Dwebdriver.gecko.driver=~/geckodriver ~/selenium-server-standalone-3.xx.x.jar
    
    # for Google Chrome
    java -jar -Dwebdriver.chrome.driver=~/chromedriver ~/selenium-server-standalone-3.xx.x.jar
    ``` 
    
    As an alternative way you can use already configured Docker container with older versions of Selenium and Firefox:
    
    ```
    docker run --net=host selenium/standalone-firefox:2.53.0
    ```

5. (Optional) Create `yii2_basic_tests` database and update it by applying migrations if you have them.

   ```
   tests/bin/yii migrate
   ```

   The database configuration can be found at `config/test_db.php`.


6. Start web server:

    ```
    tests/bin/yii serve
    ```

7. Now you can run all available tests

   ```
   # run all available tests
   vendor/bin/codecept run

   # run acceptance tests
   vendor/bin/codecept run acceptance

   # run only unit and functional tests
   vendor/bin/codecept run unit,functional
   ```

### Code coverage support

By default, code coverage is disabled in `codeception.yml` configuration file, you should uncomment needed rows to be able
to collect code coverage. You can run your tests and collect coverage with the following command:

```
#collect coverage for all tests
vendor/bin/codecept run -- --coverage-html --coverage-xml

#collect coverage only for unit tests
vendor/bin/codecept run unit -- --coverage-html --coverage-xml

#collect coverage for unit and functional tests
vendor/bin/codecept run functional,unit -- --coverage-html --coverage-xml
```

You can see code coverage output under the `tests/_output` directory.
