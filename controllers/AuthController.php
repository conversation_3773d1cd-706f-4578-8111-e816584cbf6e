<?php
namespace app\controllers;

use Yii;
use yii\web\Controller;
use yii\authclient\AuthAction;
use yii\authclient\ClientInterface;
use app\models\User;


namespace app\controllers;

use Yii;
use yii\web\Controller;
use yii\authclient\AuthAction;
use yii\authclient\ClientInterface;
use app\models\User;

class AuthController extends Controller
{
    public function actions()
    {
        return [
            'auth' => [
                'class' => AuthAction::class,
                'successCallback' => [$this, 'onAuthSuccess'],
            ],
        ];
    }

    public function onAuthSuccess(ClientInterface $client)
    {
        $attributes = $client->getUserAttributes();

        // Log attributes for debugging
        Yii::debug("User attributes: " . print_r($attributes, true), __METHOD__);

        if (empty($attributes)) {
            throw new \Exception('User attributes are empty.');
        }

        // Handle the user information here
        $user = User::find()->where(['authentik_id' => $attributes['sub']])->one(); // Assuming 'sub' is the unique identifier
        if (!$user) {
            // Create a new user
            $user = new User();
            $user->username = $attributes['name'];
            $user->email = $attributes['email'];
            $user->authentik_id = $attributes['sub']; // Assuming 'sub' is the unique identifier
            $user->save();
        }

        Yii::$app->user->login($user);

        // Redirect to a secure page
        return $this->redirect(['site/index']);
    }
}
