<?php

namespace app\controllers;

use Yii;
use app\models\Thserials;
use app\models\ThserialsSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\data\ArrayDataProvider;

/**
 * ThserialsController implements the CRUD actions for Thserials model.
 */
class ThserialsController extends Controller
{

    /**
     * @inheritdoc
     */
    public function beforeAction($action)
    {            
        if ($action->id == 'addthonemobile') {
            if ( !Empty(Yii::$app->request->get('scanner') ) ) {
                   $this->enableCsrfValidation = false;
                   Yii::debug('FALSE CSRF');

            }
        }
        return parent::beforeAction($action);
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
            'access' => [
                'class' => \yii\filters\AccessControl::className(),
                'rules' => [
                    [
                        'allow' => true,
                        'actions' => ['addthserial','addthoneserial','erasemoveid', 'showimported', 'showmoveid','delone','showpossiblethserials','getmaxid','index','view','delete','update','indexall'],
                        'matchCallback' => function ($rule, $action) {
                            return EstockTools::isUserShop() || EstockTools::isUserStock();
                        }
                    ],
                    [
                        'allow' => true,
                        'actions' => ['addthonemobile'],
                        'roles' => ['@']
                    ],
                    [
                        'roles' => ['admin'],
                        'allow' => true
                    ],
                    [
                        'allow' => false
                    ]
                ]
            ]
        ];
    }


    /**
     * Check whether we need refresh...
     */
    public function actionGetmaxid()
    {
        if( isset($_GET['move_id'])){
            $res = Yii::$app->db->createCommand("select max(id) from thserials where move_id=:mid")->bindValue(':mid',$_GET['move_id'])->queryScalar(); //rovno String, bez Array bez ActiveRecord
            //alternativa - iste viac resources:
            //$res = Thserials::find()->where(['move_id'=>$_GET['move_id'] ])->orderBy('id DESC')->asArray()->one();
            header('Content-Type: application/json');
            echo json_encode(["maxid"=>$res]);
            exit;
        }
    }



    /**
     * erase all thserials for move_id
     */
    public function actionErasemoveid($move_id)
    {
        $model = new Thserials();
        $model->move_id = $move_id;
        if( empty($move_id)){
            return $this->redirect(['index', 'move_id' => $move_id]);
        }

        try {
            $model->deleteAll(['move_id'=>$model->move_id]);
        } catch (\yii\db\Exception $e) {
                  Yii::$app->session->setFlash('error',$e->getMessage());
            return $this->redirect(['index', 'move_id' => $move_id]);
        } catch (\yii\base\Exception $e) {
                  Yii::$app->session->setFlash('error',$e);
            return $this->redirect(['index', 'move_id' => $move_id]);
        }
        $infotxt = "OK, deleted <br><br>";
        $infotxt .= \yii\helpers\Html::button('TH Serials', [ 'submit'=>['thserials/addthserial' ] ] );
        Yii::$app->session->setFlash('info',$infotxt);

            return $this->redirect(['index', 'move_id' => $move_id]);
    }


    /**
     * Adds New thserial from the app
     */
    public function actionAddthonemobile()
    {
        $thserial = new Thserials();

        $detid = "0";
        if( isset($_REQUEST['detId']) && $_REQUEST['detId'] != 'NoData' ){
            $detid = $_REQUEST['detId'];
        }
        if( isset($_REQUEST['move_id']) && $_REQUEST['move_id'] != '' ){
            $detid = $_REQUEST['move_id'];
        }
        $thserial->move_id = $detid;
        //Cez SCANNER v tabulke
        if(isset($_GET['scanner']) && $_GET['scanner'] > 0 && $thserial->move_id > 0 )
        {
            $thserial->attributes=$_GET;
            $thserial->move_id = $detid;
            //nemame mat_id ani model
            $res = Yii::$app->db->createCommand("select t.mat_id, model 
                from thserials t join product p on t.mat_id=p.mat_id 
                where thserial=:ths")->bindValue(':ths',$thserial->thserial)->queryOne();
            $thserial->mat_id = $res['mat_id'];
                $xmove = $thserial->move_id;
                header('Content-Type: application/json');
                try{
                        Yii::$app->db->createCommand(
                            "insert into thserials (mat_id,move_id,thserial) values ( :mat,:mov,:th )"
                            )->bindValue(':mat',$thserial->mat_id)
                             ->bindValue(':mov',$xmove)
                             ->bindValue(":th",$thserial->thserial)
                            ->query();
                } catch (\yii\db\Exception $e) {
                    echo json_encode(["message"=>"Error occured: ".$e->getMessage()]);
                    exit;

                } catch (\yii\base\Exception $e) {
                    echo json_encode(["message"=>"Error occured: ".$e->getMessage()]);
                    exit;
                }
                echo json_encode(["message"=>"Added ".$thserial->mat_id." ".$res['model']]);
        } else {
                    header('Content-Type: application/json');
                    echo json_encode(["message"=>"Error occured: missing move_id"]);
        }

    }


    /**
     * show some data...
     */
    public function actionShowimported()
    {
            if( $_GET['adr_repli_id'] == 2 ){//chrono...
                $obj = '29';
                $pohyby = '10,11,67,85';
                $dobropisy = '70,120,121';
                $sklad = 's2';
            } else {//blava, brno
                $obj = '17';
                $pohyby = '24,110,130,145';
                $sklad = 's0';
                $dobropisy = '-9999'; //ToDo - nezadaval som dobropisy na SK
            }
 

                $query = "
                    Select t.move_id, firma, d1, count(*) thserials, sum(d.pcs) pcsInDetail from thserials t
                    join movement m on m.move_id=t.move_id
                    join address a on m.adr_id=a.adr_id
                    join m_detail d on m.move_id=d.move_id
                    join product p on p.mat_id=d.mat_id
                    
                    group by t.move_id,firma,d1
                    order by d1,firma";

                $rawData = Yii::$app->db->createCommand($query)->queryAll();

                $dataProvider=new ArrayDataProvider([
                    'allModels'=>$rawData,
                    'pagination'=>[ 'pageSize'=>1000 ]
                ]);

                return $this->render('showimported',['data1'=>$dataProvider]);


    }



    /**
     * Lists all Thserials models.
     * @return mixed
     */
    public function actionIndexall()
    {
        //ToDo zaklad vsektych akcii v tomto to bude
        $model = new Thserials();
        $searchModel = new ThserialsSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $dataProvider->query->andWhere("thserial <>''");

        return $this->render('indexall', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }



    /**
     * Imports serials.
     */
    private function multiaddserials($id, $multiadd = '')
    {

        try {

          $mactiontxt = "Executed<hr>";
          $csla = explode("\n", $multiadd);
          for( $i = 0; $i < count($csla); $i++) {
            $pomoc = str_replace("\t","",$csla[$i]);
            $pomoc = trim(str_replace("\r","",$pomoc));
            $riadok = explode(",", $pomoc );
            if( count($riadok) == 2 ) {
                $mat_id = Yii::$app->db->createCommand("select mat_id from product where model=:model")->bindValue(':model',$riadok[0])->queryScalar();
                Yii::$app->db->createCommand(
                    "insert into thserials (mat_id,move_id,thserial) values ( :mat,:mov,:th )"
                            )->bindValue(':mat',$mat_id)
                             ->bindValue(':mov',$id)
                             ->bindValue(":th",trim($riadok[1]))
                            ->query();
              $mactiontxt .= $mat_id.' -> '.$riadok[0].' -> '.$riadok[1]."<br>\n";      
            }
          }

        } catch(\yii\db\Exception $e) {
          Yii::$app->session->setFlash('error', $e->getMessage() );
        }

        Yii::$app->session->setFlash('success',$mactiontxt);

      return true;
    }


    /**
     * Lists one move_id Thserials models.
     * @return mixed
     */
    public function actionIndex($move_id, $xmove_id=-111)
    {

        //ToDo zaklad vsektych akcii v tomto to bude
        $model = new Thserials();
        $model->move_id = $move_id;
        $minfo = "move_id not set...";

      if (Yii::$app->request->isAjax) {

          if( Yii::$app->request->get('mat_id') !== null &&   Yii::$app->request->get('thserial') !== null ){
            $model->mat_id = Yii::$app->request->get('mat_id');
            $model->thserial = Yii::$app->request->get('thserial');
          }

          if ( isset($model->thserial) && $model->save()) {
              Yii::$app->session->setFlash('info',"Serial ".$model->thserial."has been set...");
          }
      }

        try {
            $minfo =  Yii::$app->db->createCommand("select firma||', date: '||d1||', type: '||m_type_id||' '||', total:'||total as minfo from movement m
                 join address a on a.adr_id=m.adr_id 
                 where move_id=:mid")->bindValue(':mid',$model->move_id)->queryScalar();
            $missingcmd =  Yii::$app->db->createCommand("
                    Select d.mat_id||'-'||d.pcs missing, p.model from m_detail d
                    join product p on p.mat_id=d.mat_id where d.move_id=:mid
                     and d.mat_id||'-'||d.pcs not in (select mat_id||'-'||count(*) from thserials where move_id=:mid and mat_id=d.mat_id
                    group by move_id,mat_id) ")->bindValue(':mid',$model->move_id);

            if($xmove_id > 0  ){
              $possiblecmd = Yii::$app->db->createCommand("
                  select t.thserial,t.mat_id,p.model, :mid::integer as xmove, t.move_id from
                   thserials t join movement m on t.move_id=m.move_id 
				   join product p on p.mat_id=t.mat_id
                   where
                    m.move_id = :xmid
                    and t.thserial not in (select distinct thserial from thserials tt join movement mm
                           on tt.move_id=mm.move_id
                          where mm.m_type_id in (select m_type_id from m_type where moving in ('S'))
                              and tt.mat_id=t.mat_id)
                          and t.mat_id in (
                      Select distinct d.mat_id from m_detail d
                          join product p on p.mat_id=d.mat_id where d.move_id=:mid
                          and d.mat_id||'-'||d.pcs not in (select distinct mat_id||'-'||count(*) from thserials where move_id=:mid and mat_id=d.mat_id
                          group by move_id,mat_id)
                          )
                    group by thserial,t.mat_id,t.move_id,p.model
                  ")->bindValue(':mid',$model->move_id)->bindValue(':xmid',$xmove_id);

            } else {
              $possiblecmd = Yii::$app->db->createCommand("
              select t.thserial,t.mat_id,p.model, :mid::integer as xmove, t.move_id from
                   thserials t join movement m on t.move_id=m.move_id 
				   join product p on p.mat_id=t.mat_id
                   where
                    m.m_type_id in (select m_type_id from m_type where moving in ('R', 'M'))
                    and thserial not in (select distinct thserial from thserials tt join movement mm
                           on tt.move_id=mm.move_id
                          where mm.m_type_id in (select m_type_id from m_type where moving in ('S'))
                              and tt.mat_id=t.mat_id)
                          and t.mat_id in (
                      Select distinct d.mat_id from m_detail d
                          join product p on p.mat_id=d.mat_id where d.move_id=:mid
                          and d.mat_id||'-'||d.pcs not in (select distinct  mat_id||'-'||count(*) from thserials where move_id=:mid and mat_id=d.mat_id
                          group by move_id,mat_id)
                          )
                          group by thserial,t.mat_id,t.move_id,p.model
                  ")->bindValue(':mid',$model->move_id);

            }

            //Multiadd
            if ($model->load(Yii::$app->request->post()) && !empty($model->multiadd) ) {
                $this->multiaddserials($model->move_id, $model->multiadd);
            }
            else if ($model->load(Yii::$app->request->post()) && $model->save()) {
                $missing = new ArrayDataProvider(['allModels' => $missingcmd->queryAll(),'pagination' => ['pageSize' => 10000,]]);
                $possible = new ArrayDataProvider(['allModels' => $possiblecmd->queryAll(),'pagination' => ['pageSize' => 10000,]]);
                return $this->render('index', ['model' => $model, 'minfo' => $minfo, 'missing' => $missing, 'possible'=> $possible, 'saved' => 'YES']);
            }



        } catch (\yii\db\Exception $e) {
                  Yii::$app->session->setFlash('error',$e->getMessage());
        }

       $missing = new ArrayDataProvider(['allModels' => $missingcmd->queryAll(),'pagination' => ['pageSize' => 10000,]]);
       $possible = new ArrayDataProvider(['allModels' => $possiblecmd->queryAll(),'pagination' => ['pageSize' => 10000,]]);

       $searchModel = new ThserialsSearch();
       $dataProvider = $searchModel->search(Yii::$app->request->queryParams);


        return $this->render('index', [
            'model' => $model,
            'minfo' => $minfo,
            'missing' => $missing,
            'possible' => $possible,
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single Thserials model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new Thserials model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new Thserials();

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing Thserials model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing Thserials model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the Thserials model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return Thserials the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Thserials::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
