<?php

namespace app\controllers;

use Yii;
use yii\web\Controller;
use yii\helpers\Json;
use yii\data\ArrayDataProvider;
use yii\db\Query;
use app\models\StatisticsForm;
use app\models\Selects;

/**
 * ProductController implements the CRUD actions for Product model.
 */
class StatisticsController extends Controller
{

    public $DPHHU="1.27";
    public $DPHSK="1.20";
    public $DPHCZ="1.21";
    public $EUR_KURZ="1";
    public $EUR_CZK="27";

    public function behaviors()
    {
        return [
            'access' => [
                'class' => \yii\filters\AccessControl::className(),
                'rules' => [
                    [
                        'allow' => true,
                        'actions' => ['index', 'view', 'create', 'update', 'delete', 'pdf', 'viewdetail','viewmanagerstats','viewmanager2stats'],
                        'matchCallback' => function ($rule, $action) {
                            return EstockTools::isUserManagerStats() || EstockTools::isUserDba();
                        }
                    ],
                    [
                        'allow' => false
                    ]
                ]
            ]
        ];
    }

    /**
     * Lists all Product models.
     * @return mixed
     */
    public function actionIndex()
    {

        return $this->render('index');
    }

    /**
     * Displays a single Product model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Url action - expand from clipboard add new line product list
     */

    public function actionViewdetail() {

        $model = new StatisticsForm();
        if (!$model->load(Yii::$app->request->post()) ) {
            if( !isset($model->d1)){
                $model->d1 = date('Y').'-01-01';
            }
            if( !isset($model->d2)){
                $model->d2 = date('Y-m-d');
            }
            if( !isset($model->repli)){
                $model->repli =   Yii::$app->user->identity->repli;
            }
            return $this->render('view', ['form'=>$model]);
        }
        Yii::$app->session->setFlash('Statistics form results');

        foreach (
            [
                "pall" => "(max(d1)-date('".$model->d1."'))+1",
                "pmonths" => "month(max(d1))-month(date('".$model->d1."'))+(year(max(d1))-year(date('".$model->d1."')))*12+1"
            ]

            as $prov => $daysalias) {

            $mycommand = "
            SELECT
            days,
            adr_id,
            shop,
            cast(sum(xprice) as numeric(11,2)) as turnover,
            floor(
            case adr_id
when 3731 -- G-WATCH PREV. OPTIMA KOSICE
then 9993*days/30
when 3624 -- WATCH DE LUXE G-WATCH WOW-AUPARK BRATISLAVA
then 48000*days/30
when 4413 -- RACIO BB
then 12056*days/30
when 5039 -- eurovea wow
then 31638*days/30
when 5040 -- eurovea TH
then 11485*days/30
when 4585 -- Arena HU
then 4800000*days/30
when 4727 -- TH SHOP HU
then 3500000*days/30
when 6118 -- WEST WATCH HU
then 5500000*days/30
when 6487 --    ANDRASSY HU
then 27000000*days/30
when 6234 -- ESHOP
then
    case when 2 = ".$model->repli."
    then 1000000*days/30
    else 0*days/30
    end
else 0*days/30
end
) costs,
floor(sum(
case m_type_id
when 30 then
case when adr_id = 4156 -- WDL HU has price instead of p3
then xprice/".$this->DPHHU."-pcs*price
else xprice/".$this->DPHHU."-pcs*p3
end
when 31 then
case when adr_id = 4156 -- WDL HU has price instead of p3
then xprice-pcs*price
else xprice-pcs*p3
end
when 90 then xprice/".$this->DPHCZ."-pcs*p2
when 91 then xprice-pcs*p2
when 87 then 
    case m.\"X\" when '9' then 
        xprice-pcs*p0
    else
        xprice/".$this->DPHSK."-pcs*p0
    end
when 88 then xprice-pcs*p0

when 99 then
case when adr_id = 4156 -- WDL HU has price instead of p3
then -xprice/".$this->DPHHU."+pcs*price
else -xprice/".$this->DPHHU."+pcs*p3
end
when 106 then
case when adr_id = 4156 -- WDL HU has price instead of p3
then -xprice+pcs*price
else -xprice+pcs*p3
end
when 98 then -xprice/".$this->DPHCZ."+pcs*p2
when 105 then -xprice+pcs*p2
when 97 then -xprice/".$this->DPHSK."+pcs*p0
when 104 then -xprice+pcs*p0
when 138 then -xprice+pcs*p0
when 137 then xprice-pcs*p0
when 168 then -xprice+pcs*p0
when 167 then xprice-pcs*p0
when 148 then -xprice/".$this->DPHSK."+pcs*p0
when 147 then xprice/".$this->DPHSK."-pcs*p0
when 149 then xprice/".$this->DPHSK."-pcs*p0
when 140 then -xprice+pcs*p0
when 139 then xprice-pcs*p0
when 170 then -xprice/".$this->DPHSK."+pcs*p0
when 169 then xprice-pcs*p0

when 505 then -xprice+pcs*p3 -- ToDo also switch WDL HU price???
when 504 then xprice-pcs*p3

else 0
end
)) as margin, ' ', ' ', ' ',

currency

FROM
(
select 
adr_id,
m.m_type_id,
pcs,
p.price,
p.p0,
p.p1,
p.p2,
p.p3,
md.currency,
".$daysalias." as days,
(select firma||' '||city from address where adr_id=m.adr_id ) || case when strpos(t.name,'//') = 0 then '' else substring(t.name from strpos(t.name,'//') )  end as shop,
md.pcs*(md.price*(1-md.discount/100)) as xprice
FROM
movement m join m_detail md on m.move_id=md.move_id join product p on p.mat_id=md.mat_id
join m_type t on t.m_type_id=m.m_type_id
WHERE
d1 between '".$model->d1."' and '".$model->d2."'
and m.m_type_id in (30,31,99,106,90,91,98,105,87,88,97,104,137,138,139,140,167,168,169,170,147,148,149,504,505)

GROUP BY
m.adr_id, md.pcs, md.price, md.discount, m.m_type_id, md.currency,
p.price,p.p0,p.p1,p.p2,p.p3,t.name
) s1

group by s1.adr_id, s1.days, s1.shop, s1.currency
order by s1.shop, s1.days
";

$query = Yii::$app->db->createCommand($mycommand)->queryAll();

$provider[] = new ArrayDataProvider([
    'allModels' => $query,
    'pagination' => [
        'pageSize' => 10000,
    ]
]);

        } //foreach daysalias

            // $dataProvider = new ActiveDataProvider([
            //     'query' => $query,
            //     'pagination' => [
            //         'pageSize' => 200,
            //     ],
            // ]);

        return $this->render('view', ['pall'=>$provider[0], 'pmonths' => $provider[1], 'form'=>$model]);

    }


    //
    // Ak nieco navyse co nie je v selecte
    //
    //
    private function additional_business_logic( $arr, $dfrom, $dto, $typeofsel = '' ) {
        //global $arr; //Tu mame uz vyratane pole zo selectu
        $fromdate = mktime( 0, 0, 0, substr($dfrom,5,2), substr($dfrom,8,2), substr($dfrom,0,4) );
        $todate = mktime( 0, 0, 0, substr($dto,5,2), substr($dto,8,2), substr($dto,0,4) );

// var_dump($arr);
// exit;
        foreach ($arr as $key => $value) {

            if ( $typeofsel == 'months' ){//1% robime po mesiacoch
                $nasobok = $arr[$key][$typeofsel];
            } else if ( $typeofsel == 'days' ){//1% robime po dnoch priblizne teda 30 dnovy mesiac
                $nasobok = round($arr[$key][$typeofsel]/30,1);
            } else {
                $nasobok = 1;
            }
            switch ($value['adr_id']) { //adr_id, podla nej definujeme "note"
                case 4172: //BENIX s.r.o. BRATISLAVA
//                  $arr[$key]['Note'] = 'costs are minimum 60000 per month or turnover*0.2. Add also 50000 other costs. (here only fixed 110000 implemented)';
                $arr[$key]['Note'] = 'approx. 2006 costs';
                break;
                case 4413: // RACIO BB
//                  $arr[$key]['Note'] = 'Shop opened since Dec 2006, please use costs only from this date.';
//                  $arr[$key]['Note'] = 'approx. 2006 costs';
//                  $arr[$key]['Note'] = '6thMay08: costs set from 4315 eur  to 5463 eur';
                $arr[$key]['OnePerc'] = "1584.20"*$nasobok;
                $arr[$key]['Net profit'] = $arr[$key]['Brut profit']-$arr[$key]['OnePerc'];
                break;
                case 3128: //ZLATNÍCTVO LON BRATISLAVA
                $arr[$key]['Note'] = 'costs are turnover*0.2. Add 1660 eur as other costs. (here only fixe 3651 eur  implemented)';
                break;
                case 3731: //Optima kosice od 15.5.2006 ma o 50000 menej.
//                  if( $todate > mktime (0,0,0,5,15,2006) ) {
//                      //date ("M-d-Y", $fromdate )." ".date ("M-d-Y", $todate );
//                      $arr[$key]['Note'] = "since 15.5.2006 costs are 50000 lower per month. Please recount manualy.";
//                  }
//                  $arr[$key]['Note'] = 'approx. 2006 costs';
                $arr[$key]['OnePerc'] = "1510.39"*$nasobok;
                $arr[$key]['Net profit'] = $arr[$key]['Brut profit']-$arr[$key]['OnePerc'];
                break;
                case 5039: // WOW EUROVEA
                $arr[$key]['OnePerc'] = "3809.34"*$nasobok;
                $arr[$key]['Net profit'] = $arr[$key]['Brut profit']-$arr[$key]['OnePerc'];
                break;
                case 3624: // WDL AUPARK
                $arr[$key]['OnePerc'] = "3478.35"*$nasobok;
                $arr[$key]['Net profit'] = $arr[$key]['Brut profit']-$arr[$key]['OnePerc'];
                break;
                case 3511: // WOW POLUS
                $arr[$key]['OnePerc'] = "599.11"*$nasobok;
                $arr[$key]['Net profit'] = $arr[$key]['Brut profit']-$arr[$key]['OnePerc'];
                break;
                case 3512: // WDL POLUS
                $arr[$key]['OnePerc'] = "1149.92"*$nasobok;
                $arr[$key]['Net profit'] = $arr[$key]['Brut profit']-$arr[$key]['OnePerc'];
                break;
                case 4370: // WOW AUPARK
                $arr[$key]['OnePerc'] = "472.44"*$nasobok;
                $arr[$key]['Net profit'] = $arr[$key]['Brut profit']-$arr[$key]['OnePerc'];
                break;
                case 5040: // TH BUTIK EUROVEA
                $arr[$key]['OnePerc'] = "882.86"*$nasobok;
                $arr[$key]['Net profit'] = $arr[$key]['Brut profit']-$arr[$key]['OnePerc'];
                break;
            case 3450:  //G-WATCH-Ostrava zlava 25% v lete
            if( $todate > mktime (0,0,0,7,1,2006) and $fromdate < mktime (0,0,0,9,1,2006) ) {
                $arr[$key]['Note'] = "For Juni and July is in Ostrava discount 25% for rent - it´s 14250,- CZK. Please recount manualy.";
            }
            break;
            case 4312:  //G-WATCH-Flora Praha ma 150000 ale po 1.6.2006 zvysene o 50000!
            if( $fromdate < mktime (0,0,0,1,1,2011) ) {
                $arr[$key]['Note'] = "costs: 287000 (set 01.01.2011. Previous years recount manualy).";
            }
            $arr[$key]['OnePerc'] = "77180"*$nasobok;
            $arr[$key]['Net profit'] = $arr[$key]['Brut profit']-$arr[$key]['OnePerc'];
            break;
            case 4406:  //G-WATCH-LASO ostrava
            if( $fromdate < mktime (0,0,0,1,1,2011) ) {
                $arr[$key]['Note'] = "Costs set to 186000/month since 01.01.2011. Previous years please recount manualy.";
            }
            $arr[$key]['OnePerc'] = "35360"*$nasobok;
            $arr[$key]['Net profit'] = $arr[$key]['Brut profit']-$arr[$key]['OnePerc'];
            break;

            case 4310:  //vankovka brno
            if( $fromdate < mktime (0,0,0,1,1,2011) ) {
                $arr[$key]['Note'] = "Costs set to 262000/month since 01.01.2011. Previous years please recount manualy.";
            }
            $arr[$key]['OnePerc'] = "28120"*$nasobok;
            $arr[$key]['Net profit'] = $arr[$key]['Brut profit']-$arr[$key]['OnePerc'];

            break;
            case 3488:  //Nyíregyháza 
            $arr[$key]['OnePerc'] = "330000"*$nasobok;
            $arr[$key]['Net profit'] = $arr[$key]['Brut profit']-$arr[$key]['OnePerc'];
            break;
            case 4727:  //th hu
            $arr[$key]['OnePerc'] = "550000"*$nasobok;
            $arr[$key]['Net profit'] = $arr[$key]['Brut profit']-$arr[$key]['OnePerc'];
            break;
            case 4585:  //watch arena
            $arr[$key]['OnePerc'] = "1220000"*$nasobok;
            $arr[$key]['Net profit'] = $arr[$key]['Brut profit']-$arr[$key]['OnePerc'];
            break;
            case 6118:  //west-watch
            $arr[$key]['OnePerc'] = "900000"*$nasobok;
            $arr[$key]['Net profit'] = $arr[$key]['Brut profit']-$arr[$key]['OnePerc'];
            break;

            case 6234:  //eshop wdl.sk
            case 12065: //(adr_id=6234 or adr_id between 12065 and 12092)
            case 12066:
            case 12067:
            case 12068:
            case 12069:
            case 12070:
            case 12071:
            case 12072:
            case 12073:
            case 12074:
            case 12075:
            case 12076:
            case 12077:
            case 12078:
            case 12079:
            case 12080:
            case 12081:
            case 12082:
            case 12083:
            case 12084:
            case 12085:
            case 12086:
            case 12087:
            case 12088:
            case 12089:
            case 12090:
            case 12091:
            case 12092:
            $arr[$key]['Note'] = 'eshop... Costs ONLY HU Sept.2024';
            break;

            case 6487:  //andrassy
            $arr[$key]['OnePerc'] = "0";
            $arr[$key]['Net profit'] = $arr[$key]['Brut profit']-$arr[$key]['OnePerc'];
            break;
            default:
            $arr[$key]['Note'] = 'Costs not set yet.';
            $arr[$key]['OnePerc'] = "0";
            $arr[$key]['Net profit'] = $arr[$key]['Brut profit']-$arr[$key]['OnePerc'];
            break;
        }           

        
    }

    return $arr;

}


    /*
    * simple sales for statistics 2
    *
    */
    private function getMain2Select($dfrom, $dto, $repli, $other = 'kod,model,') {

        if( $repli == 2 ){
            Yii::$app->db->createCommand("set lc_monetary to \"hu_HU.UTF-8\"")->query();
        } else {
          Yii::$app->db->createCommand("set lc_monetary to \"en_IE.UTF-8\"")->query();
        }

        /* HLAVNY */
        $mycommand =    "
        select 
        shop, ".$other." turnover::money,sum_of_pcs, currency, margin, rate";
        $mycommand .= " from (
        SELECT currency,".$other."
        (select firma||' '||city from address where adr_id=m.adr_id ) || case when strpos(t.name,'//') = 0 then '' else substring(t.name from strpos(t.name,'//') )  end as shop,
        floor(sum(case when m.m_type_id in (99,106,98,105,97,104,138,140,148,505) then -md.pcs*(md.price*(1-md.discount/100))*(1+md.tax/100) else md.pcs*(md.price*(1-md.discount/100))*(1+md.tax/100) end)) turnover,
        round((select nbs_rate/nbs_multi from nbs_rates where nbs_cur=currency and nbs_date <= '".$dto."' order by nbs_date desc limit 1 ),3) as rate,
        floor(sum(
            case m.m_type_id
                when 30 then 
                    md.pcs*(md.price*(1-md.discount/100)/".$this->DPHHU."-p.p3)
                when 31 then 
                    md.pcs*(md.price*(1-md.discount/100)-p.p3)
                when 90 then md.pcs*(md.price*(1-md.discount/100)/".$this->DPHCZ."-p.p2)
                when 91 then md.pcs*(md.price*(1-md.discount/100)-p.p2)
                when 87 then 
                    case m.\"X\" when '9' then 
                        md.pcs*(md.price*(1-md.discount/100)-p.p0)
                    else
                        md.pcs*(md.price*(1-md.discount/100)/".$this->DPHSK."-p.p0)
                    end
                when 88 then md.pcs*(md.price*(1-md.discount/100)-p.p0)

                when 99 then 
                    -md.pcs*(md.price*(1-md.discount/100)/".$this->DPHHU."-p.p3)
                when 106 then 
                    -(md.pcs*(md.price*(1-md.discount/100)-p.p3))
                when 98 then -(md.pcs*(md.price*(1-md.discount/100)/".$this->DPHCZ."-p.p2))
                when 105 then -(md.pcs*(md.price*(1-md.discount/100)-p.p2))
                when 97 then -(md.pcs*(md.price*(1-md.discount/100)/".$this->DPHSK."-p.p0))
                when 104 then -(md.pcs*(md.price*(1-md.discount/100)-p.p0))
                when 138 then -(md.pcs*(md.price*(1-md.discount/100)/".$this->DPHSK."-p.p0))
                when 137 then md.pcs*(md.price*(1-md.discount/100)/".$this->DPHSK."-p.p0)
                when 148 then -(md.pcs*(md.price*(1-md.discount/100)/".$this->DPHSK."-p.p0))
                when 147 then md.pcs*(md.price*(1-md.discount/100)/".$this->DPHSK."-p.p0)
                when 149 then md.pcs*(md.price*(1-md.discount/100)/".$this->DPHSK."-p.p0)
                when 140 then -(md.pcs*(md.price*(1-md.discount/100)-p.p0))
                when 139 then md.pcs*(md.price*(1-md.discount/100)-p.p0)
                when 167 then md.pcs*(md.price*(1-md.discount/100)-p.p0)
                when 168 then -(md.pcs*(md.price*(1-md.discount/100)-p.p0))
                when 169 then md.pcs*(md.price*(1-md.discount/100)-p.p0)
                when 170 then -(md.pcs*(md.price*(1-md.discount/100)/".$this->DPHSK."-p.p0))


                when 505 then -(md.pcs*(md.price*(1-md.discount/100)-p.p3)) 
                when 504 then md.pcs*(md.price*(1-md.discount/100)-p.p3)
                
                else 0
            end
            )) margin,
            sum(md.pcs) sum_of_pcs
        FROM
         movement m join m_detail md on m.move_id=md.move_id join product p on p.mat_id=md.mat_id
         join m_type t on t.m_type_id=m.m_type_id
        WHERE
         year(d1)>2018 and 
         d1 between '".$dfrom."' and '".$dto."' 
         and m.m_type_id in (30,31,99,106,90,91,98,105,87,88,97,104,137,138,139,140,167,168,169,170,147,148,149,504,505) 
         and m.repli_id = ".$repli." 

        GROUP BY
         md.currency,".$other."m.adr_id, t.name
        ORDER BY
         shop,".$other."margin ) sss";
         // echo "<pre>";
         // echo $mycommand;
         // exit;

         return $mycommand;
    }



    private function getMainSelect($dfrom, $dto, $repli, $days, $dayslabel, $addfilter = '', $addcol = '', $addxcol = '', $addgroup = '') {

        if( $repli == 2 ){
            Yii::$app->db->createCommand("set lc_monetary to \"hu_HU.UTF-8\"")->query();
        } else {
          Yii::$app->db->createCommand("set lc_monetary to \"en_IE.UTF-8\"")->query();
        }

        $daysx = $days;
        //Months select ukazoval costs ako 1 den
        if( $dayslabel == "months" ){
            $days = 30*$days;
        }

        /* HLAVNY */
        $mycommand =    "
        select ".$dayslabel.", adr_id, 
        ".$addcol."
        shop, turnover::money";
        $mycommand .=  empty($addcol)?",sum_of_pcs, currency, costs, margin, margin-costs as \"Brut profit\", ' ' as \"Note\",' ' as \"OnePerc\", ' ' as \"Net profit\" ":"";
        $mycommand .= " from (
        SELECT currency,
        '".$daysx."' as ".$dayslabel.",
        adr_id,
        ".$addxcol."
        (select firma||' '||city from address where adr_id=m.adr_id ) || case when strpos(t.name,'//') = 0 then '' else substring(t.name from strpos(t.name,'//') )  end as shop,
        floor(sum(case when m.m_type_id in (99,106,98,105,97,104,138,140,148,505) then -md.pcs*(md.price*(1-md.discount/100))*(1+md.tax/100) else md.pcs*(md.price*(1-md.discount/100))*(1+md.tax/100) end)) turnover,
        floor(
            case adr_id
                when 3731 --    G-WATCH PREV. OPTIMA KOSICE         
                    then 9993*".$days."/30
                when 3624 --    WATCH DE LUXE G-WATCH WOW-AUPARK BRATISLAVA     
                    then 48000*".$days."/30
                when 4370 --    AUPARK2 BRATISLAVA      
                    then 8865*".$days."/30
                when 4413 --    RACIO BB        
                    then 12056*".$days."/30
                when 3511 --    G-WATCH WOW-POLUS BRATISLAVA            
                    then 8855*".$days."/30
                when 4171 --    VALENTINA Slovakia, s.r.o BRATISLAVA    
                    then 0*".$days."/30
                when 3512 --    WATCH DE LUXE - POLUS           
                    then 4434*".$days."/30
                when 5039 --    eurovea wow         
                    then 31638*".$days."/30                  
                when 5040 --    eurovea TH          
                    then 11485*".$days."/30
                when 3744 --    CAMPONA Budapest            
                    then 80000*".$days."/3
                when 3489 --    MISKOLC - PLÁZA MISKOLC
                    then 100000*".$days."/3
                when 3488 --    NYÍRPLÁZA NYIREGYHÁZA       
                    then 140000*".$days."/3
                when 4156 --    WDL HU
                    then 600000*".$days."/3
                when 4585 --     Arena  HU
                    then 480000*".$days."/3
                when 4727 --    TH SHOP HU
                    then 350000*".$days."/3
                when 6118 --    WEST WATCH HU
                    then 550000*".$days."/3
                when 6487 --    ANDRASSY HU
                    then 2700000*".$days."/3
                when 6234 -- ESHOP
                    then
                    case when 2 = ".$repli."
                        then 100000*".$days."/3
                        else 0*".$days."/30
                    end
                else 0*".$days."/30
                end
            ) costs,
        floor(sum(
            case m.m_type_id
                when 30 then 
                    md.pcs*(md.price*(1-md.discount/100)/".$this->DPHHU."-p.p3)
                when 31 then 
                    md.pcs*(md.price*(1-md.discount/100)-p.p3)
                when 90 then md.pcs*(md.price*(1-md.discount/100)/".$this->DPHCZ."-p.p2)
                when 91 then md.pcs*(md.price*(1-md.discount/100)-p.p2)
                when 87 then 
                    (case m.\"X\" when '9' then 
                        md.pcs*(md.price*(1-md.discount/100)-p.p0)
                    else
                        md.pcs*(md.price*(1-md.discount/100)/".$this->DPHSK."-p.p0)
                    end)
                when 88 then md.pcs*(md.price*(1-md.discount/100)-p.p0)

                when 99 then 
                    -md.pcs*(md.price*(1-md.discount/100)/".$this->DPHHU."-p.p3)
                when 106 then 
                    -(md.pcs*(md.price*(1-md.discount/100)-p.p3))
                when 98 then -(md.pcs*(md.price*(1-md.discount/100)/".$this->DPHCZ."-p.p2))
                when 105 then -(md.pcs*(md.price*(1-md.discount/100)-p.p2))
                when 97 then -(md.pcs*(md.price*(1-md.discount/100)/".$this->DPHSK."-p.p0))
                when 104 then -(md.pcs*(md.price*(1-md.discount/100)-p.p0))
                when 138 then -(md.pcs*(md.price*(1-md.discount/100)-p.p0))
                when 137 then md.pcs*(md.price*(1-md.discount/100)-p.p0)
                when 148 then -(md.pcs*(md.price*(1-md.discount/100)/".$this->DPHSK."-p.p0))
                when 147 then md.pcs*(md.price*(1-md.discount/100)/".$this->DPHSK."-p.p0)
                when 149 then md.pcs*(md.price*(1-md.discount/100)/".$this->DPHSK."-p.p0)
                when 140 then -(md.pcs*(md.price*(1-md.discount/100)-p.p0))
                when 139 then md.pcs*(md.price*(1-md.discount/100)-p.p0)
                when 167 then md.pcs*(md.price*(1-md.discount/100)-p.p0)
                when 168 then -(md.pcs*(md.price*(1-md.discount/100)-p.p0))
                when 169 then md.pcs*(md.price*(1-md.discount/100)-p.p0)
                when 170 then -(md.pcs*(md.price*(1-md.discount/100)/".$this->DPHSK."-p.p0))


                when 505 then -(md.pcs*(md.price*(1-md.discount/100)-p.p3)) -- ToDo .. also chceck WDL hu price
                when 504 then md.pcs*(md.price*(1-md.discount/100)-p.p3)
                
                else 0
            end
            )) margin,
            sum(md.pcs) sum_of_pcs
        FROM
         movement m join m_detail md on m.move_id=md.move_id join product p on p.mat_id=md.mat_id
         join m_type t on t.m_type_id=m.m_type_id
        WHERE
         year(d1)>2018 and 
         d1 between '".$dfrom."' and '".$dto."' 
         and m.m_type_id in (30,31,99,106,90,91,98,105,87,88,97,104,137,138,139,140,167,168,169,170,147,148,149,504,505) 
         and m.repli_id = ".$repli." 

         ".$addfilter."

        GROUP BY
         md.currency,
         (select firma||' '||city from address where adr_id=m.adr_id ) || case when strpos(t.name,'//') = 0 then '' else substring(t.name from strpos(t.name,'//') )  end,
         m.adr_id ".$addgroup."
        ORDER BY
         shop ) sss";
         // echo "<pre>";
         // echo $mycommand;
         // exit;

         return $mycommand;
    }



    /* PODLA POHYBOV */
    private function getMovSelect($dfrom, $dto, $repli){

        $mycommand =    "SELECT
         max(d1), adr_id,m.m_type_id,
         (select firma||' '||city from address where adr_id=m.adr_id ) || case when strpos(t.name,'//') = 0 then '' else substring(t.name from strpos(t.name,'//') )  end as shop,
         floor(sum(case when m.m_type_id in (99,106,98,105,97,104,138,140,148,505) then -md.pcs*(md.price*(1-md.discount/100)) else md.pcs*(md.price*(1-md.discount/100)) end))::money turnover,
         md.currency
        FROM
         movement m join m_detail md on m.move_id=md.move_id join product p on p.mat_id=md.mat_id
         join m_type t on t.m_type_id=m.m_type_id
        WHERE
         year(d1)>2018 and 
         d1 between '".$dfrom."' and '".$dto."' 
         and m.m_type_id in (30,31,99,106,90,91,98,105,87,88,97,104,137,138,139,140,167,168,169,170,147,148,149,504,505) and m.repli_id = ".$repli." 

        GROUP BY
         md.currency,
         m.adr_id, m.adr_repli_id, m.m_type_id, t.name
        ORDER BY 
         shop, m.m_type_id";

         return $mycommand;

    }


    /* PODLA KODOV */
    private function getKodSelect($dfrom, $dto, $repli, $days, $dayslabel){

        $mycommand =    "
        SELECT 
        ".$dayslabel.", kod, sum_pcs,
        turnover::money,currency,margin,
         round((select nbs_rate/nbs_multi from nbs_rates where nbs_cur=currency and nbs_date <= '".$dto."' order by nbs_date desc limit 1 ),3) as rate, 
         coalesce(
         floor(turnover / (select nbs_rate/nbs_multi from nbs_rates where nbs_cur=currency and nbs_date <= '".$dto."' order by nbs_date desc limit 1 ) ),turnover) as EUR_turnover 
         from (
        SELECT
        '".$days."' as ".$dayslabel.", kod, sum(pcs) as sum_pcs,
         floor(sum(case when m.m_type_id in (99,106,98,105,97,104,138,140,148,505) then -md.pcs*(md.price*(1-md.discount/100)) else md.pcs*(md.price*(1-md.discount/100)) end)) turnover,
         floor(sum(
            case m.m_type_id
                when 30 then 
                    md.pcs*(md.price*(1-md.discount/100)/".$this->DPHHU."-p.p3)
                when 31 then 
                    md.pcs*(md.price*(1-md.discount/100)-p.p3)
                when 90 then md.pcs*(md.price*(1-md.discount/100)/".$this->DPHCZ."-p.p2)
                when 91 then md.pcs*(md.price*(1-md.discount/100)-p.p2)
                when 87 then 
                    case m.\"X\" when '9' then 
                        md.pcs*(md.price*(1-md.discount/100)-p.p0)
                    else
                        md.pcs*(md.price*(1-md.discount/100)/".$this->DPHSK."-p.p0)
                    end
                when 88 then md.pcs*(md.price*(1-md.discount/100)-p.p0)

                when 99 then 
                    -md.pcs*(md.price*(1-md.discount/100)/".$this->DPHHU."-p.p3)
                when 106 then 
                    -(md.pcs*(md.price*(1-md.discount/100)-p.p3))
                when 98 then -(md.pcs*(md.price*(1-md.discount/100)/".$this->DPHCZ."-p.p2))
                when 105 then -(md.pcs*(md.price*(1-md.discount/100)-p.p2))
                when 97 then -(md.pcs*(md.price*(1-md.discount/100)/".$this->DPHSK."-p.p0))
                when 104 then -(md.pcs*(md.price*(1-md.discount/100)-p.p0))
                when 138 then -(md.pcs*(md.price*(1-md.discount/100)-p.p0))
                when 137 then md.pcs*(md.price*(1-md.discount/100)-p.p0)
                when 148 then -(md.pcs*(md.price*(1-md.discount/100)/".$this->DPHSK."-p.p0))
                when 147 then md.pcs*(md.price*(1-md.discount/100)/".$this->DPHSK."-p.p0)
                when 149 then md.pcs*(md.price*(1-md.discount/100)/".$this->DPHSK."-p.p0)
                when 140 then -(md.pcs*(md.price*(1-md.discount/100)-p.p0))
                when 139 then md.pcs*(md.price*(1-md.discount/100)-p.p0)

                when 167 then md.pcs*(md.price*(1-md.discount/100)-p.p0)
                when 168 then -(md.pcs*(md.price*(1-md.discount/100)-p.p0))
                when 169 then md.pcs*(md.price*(1-md.discount/100)-p.p0)
                when 170 then -(md.pcs*(md.price*(1-md.discount/100)/".$this->DPHSK."-p.p0))


                when 505 then -(md.pcs*(md.price*(1-md.discount/100)-p.p3)) -- ToDo .. also chceck WDL hu price
                when 504 then md.pcs*(md.price*(1-md.discount/100)-p.p3)
                
                else 0
            end
            )) margin,
         md.currency
        FROM
         movement m join m_detail md on m.move_id=md.move_id join product p on p.mat_id=md.mat_id
        WHERE
         year(d1)>2018 and 
         d1 between '".$dfrom."' and '".$dto."' 
         and m.m_type_id in (30,31,99,106,90,91,98,105,87,88,97,104,137,138,139,140,167,168,169,170,147,148,149,504,505) and m.repli_id = ".$repli." 

        GROUP BY
         kod,currency
        ORDER BY 
         kod) sss";

         return $mycommand;

    }

    /**
     * Url action - expand from clipboard add new line product list
     */

    public function actionViewmanagerstats() {

        $model = new StatisticsForm();
        if (!$model->load(Yii::$app->request->post()) ) {
            if( !isset($model->d1)){
                $model->d1 = Date('Y').'-01-01';
            }
            if( !isset($model->d2)){
                $model->d2 = Date('Y-m-d');
            }
            if( !isset($model->repli)){
                $model->repli =   Yii::$app->user->identity->repli;
            }
            if( !isset($_GET['datefilter']) ){
                return $this->render('viewmanagerstats', ['form'=>$model]);
            }
        }

        if( isset($_GET['datefilter'])){
            switch ($_GET['datefilter']) {
                case 'lastyear':
                    $year = Date('Y')-1;
                    $model->d1 = $year.'-01-01';
                    $model->d2 = $year.'-12-31';
                    break;
                case 'today':
                    $model->d1 = Date('Y-m-d');
                    $model->d2 = Date('Y-m-d');
                    break;
                case 'thisweek':
                    $this_week_start = strtotime('-1 week monday 00:00:00');
                    $model->d1 = date('Y-m-d', $this_week_start);
                    $model->d2 = date('Y-m-d');
                    break;
                case 'thismonth':
                    $this_month_start = mktime(0, 0, 0, date('m'), 01);
                    $model->d1 = date('Y-m-d', $this_month_start);
                    $model->d2 = date('Y-m-d');
                    break;
                case 'lastmonth':
                    $last_month_start = mktime(0, 0, 0, date('m')-1, 01);
                    $last_month_end   = mktime(23, 59, 59, date('m'), 0);
                    $model->d1 = date('Y-m-d', $last_month_start);
                    $model->d2 = date('Y-m-d', $last_month_end);
                    break;
                case 'thisyear':
                    $this_year_start = mktime(0, 0, 0, 1, 1, date('Y'));
                    $model->d1 = date('Y-m-d', $this_year_start);
                    $model->d2 = date('Y-m-d');
                    break;
                case 'lastyear':
                    $last_year_start = mktime(0, 0, 0, 1, 1, date('Y')-1);
                    $last_year_end   = mktime(23, 59, 59, 1, 0, date('Y'));
                    $model->d1 = date('Y-m-d', date('Y-m-d', $last_year_start));
                    $model->d2 = date('Y-m-d', date('Y-m-d', $last_year_end));
                    break;
                
                default:
                    # code...
                    break;
            }
        }

        Yii::$app->session->setFlash('Statistics form results');

        $pall = Yii::$app->db->createCommand("select (max(d1)-date(:d1))+1 days from movement WHERE
         year(d1)>2018 and 
         d1 between :d1 and :d2 
         and m_type_id in (30,31,99,106,90,91,98,105,87,88,97,104,137,138,139,140,167,168,169,170,147,148,149,504,505) 
         and movement.repli_id = :repli")->bindValue(":d1",$model->d1)->bindValue(":d2",$model->d2)->bindValue(":repli",$model->repli)->queryScalar();
        $pmonths = Yii::$app->db->createCommand("select month(max(d1))-month(date('".$model->d1."'))+(year(max(d1))-year(date('".$model->d1."')))*12+1 from movement WHERE
         year(d1)>2018 and 
         d1 between :d1 and :d2 
         and m_type_id in (30,31,99,106,90,91,98,105,87,88,97,104,137,138,139,140,167,168,169,170,147,148,149,504,505) 
         and movement.repli_id = :repli")->bindValue(":d1",$model->d1)->bindValue(":d2",$model->d2)->bindValue(":repli",$model->repli)->queryScalar();

        $_query = $this->getMainSelect($model->d1,$model->d2, $model->repli, $pall, "days");

        try {
            $arr = Yii::$app->db->createCommand($_query)->queryAll();
            $arr =  $this->additional_business_logic( $arr, $model->d1, $model->d2, "days" );
            $xpall = new ArrayDataProvider([
                'allModels' => $arr,
                'pagination' => [
                    'pageSize' => 10000,
                ]
            ]);

            $_query = $this->getMainSelect($model->d1,$model->d2, $model->repli, $pmonths, "months");
            $arr = Yii::$app->db->createCommand($_query)->queryAll();
            $arr =  $this->additional_business_logic($arr, $model->d1, $model->d2,"months" );

            $xpmonths = new ArrayDataProvider([
                'allModels' => $arr,
                'pagination' => [
                    'pageSize' => 10000,
                ]
            ]);

            $_query = $this->getMainSelect($model->d1,$model->d2, $model->repli, $pall, "days", 'and m.c_number is not null');
            $arr = Yii::$app->db->createCommand($_query)->queryAll();
            $arr =  $this->additional_business_logic( $arr, $model->d1, $model->d2, "days" );
            $xpeshop = new ArrayDataProvider(['allModels' => $arr,'pagination' => ['pageSize' => 10000, ]
            ]);

            $_query = $this->getMovSelect($model->d1,$model->d2, $model->repli);
            $arr = Yii::$app->db->createCommand($_query)->queryAll();
            $xpmov = new ArrayDataProvider(['allModels' => $arr,'pagination' => ['pageSize' => 10000, ]
            ]);

            $_query = $this->getMainSelect($model->d1,$model->d2, $model->repli, $pall, "days", '', 'kod,sumpcs,currency,margin,', 'kod,sum(pcs) as sumpcs,', ', p.kod');
            $arr = Yii::$app->db->createCommand($_query)->queryAll();
            $xbykod = new ArrayDataProvider(['allModels' => $arr,'pagination' => ['pageSize' => 10000, ]
            ]);

            $_query = $this->getKodSelect($model->d1,$model->d2, $model->repli, $pall, "days");
            $arr = Yii::$app->db->createCommand($_query)->queryAll();
            $xpkod = new ArrayDataProvider(['allModels' => $arr,'pagination' => ['pageSize' => 10000, ]
            ]);

            $_querymodif = str_replace('xxparamxx', $pall, Selects::$statsquery );
            $_query = Yii::$app->db->createCommand( $_querymodif );
            $_query->bindValue('p1', $model->d1);
            $_query->bindValue('p2', $model->d2);
            $_query->bindValue('p3', $model->repli);
            $xpselect = new ArrayDataProvider(['allModels' => $_query->queryAll(),'pagination' => ['pageSize' => 10000, ]
            ]);


        } catch (\yii\db\Exception $e) {
            Yii::$app->session->setFlash('error',$e->getMessage() );
        }

        return $this->render('viewmanagerstats', ['pselect'=>($xpselect ?? ''), 'pall'=>($xpall ?? ''), 'pmonths' => ($xpmonths ??  ''), 'peshop' => ($xpeshop ?? ''), 'pmov' => ($xpmov ?? ''), 'pbykod' => ($xbykod ?? ''), 'pkod' => ($xpkod ?? ''), 'form'=>$model]);
    }


   /**
     * Url action - expand from clipboard add new line product list
     */

    public function actionViewmanager2stats() {

        $model = new StatisticsForm();
        if (!$model->load(Yii::$app->request->post()) ) {
            if( !isset($model->d1)){
                $model->d1 = Date('Y').'-01-01';
            }
            if( !isset($model->d2)){
                $model->d2 = Date('Y-m-d');
            }
            if( !isset($model->repli)){
                $model->repli =   Yii::$app->user->identity->repli;
            }
            if( !isset($_GET['datefilter']) ){
                return $this->render('viewmanager2stats', ['form'=>$model]);
            }
        }

        if( isset($_GET['datefilter'])){
            switch ($_GET['datefilter']) {
                case 'lastyear':
                    $year = Date('Y')-1;
                    $model->d1 = $year.'-01-01';
                    $model->d2 = $year.'-12-31';
                    break;
                case 'today':
                    $model->d1 = Date('Y-m-d');
                    $model->d2 = Date('Y-m-d');
                    break;
                case 'thisweek':
                    $this_week_start = strtotime('-1 week monday 00:00:00');
                    $model->d1 = date('Y-m-d', $this_week_start);
                    $model->d2 = date('Y-m-d');
                    break;
                case 'thismonth':
                    $this_month_start = mktime(0, 0, 0, date('m'), 01);
                    $model->d1 = date('Y-m-d', $this_month_start);
                    $model->d2 = date('Y-m-d');
                    break;
                case 'lastmonth':
                    $last_month_start = mktime(0, 0, 0, date('m')-1, 01);
                    $last_month_end   = mktime(23, 59, 59, date('m'), 0);
                    $model->d1 = date('Y-m-d', $last_month_start);
                    $model->d2 = date('Y-m-d', $last_month_end);
                    break;
                case 'thisyear':
                    $this_year_start = mktime(0, 0, 0, 1, 1, date('Y'));
                    $model->d1 = date('Y-m-d', $this_year_start);
                    $model->d2 = date('Y-m-d');
                    break;
                case 'lastyear':
                    $last_year_start = mktime(0, 0, 0, 1, 1, date('Y')-1);
                    $last_year_end   = mktime(23, 59, 59, 1, 0, date('Y'));
                    $model->d1 = date('Y-m-d', date('Y-m-d', $last_year_start));
                    $model->d2 = date('Y-m-d', date('Y-m-d', $last_year_end));
                    break;
                
                default:
                    # code...
                    break;
            }
        }
        Yii::$app->session->setFlash('Statistics form results');
        try {
            $_query = $this->getMain2Select($model->d1,$model->d2, $model->repli, '');
            $arr = Yii::$app->db->createCommand($_query)->queryAll();
            $xpall = new ArrayDataProvider([
                'allModels' => $arr,
                'pagination' => [
                    'pageSize' => 10000,
                ]
            ]);

            $_query = $this->getMain2Select($model->d1,$model->d2, $model->repli);
            $arr = Yii::$app->db->createCommand($_query)->queryAll();
            $xpall2 = new ArrayDataProvider([
                'allModels' => $arr,
                'pagination' => [
                    'pageSize' => 10000,
                ]
            ]);


        } catch (\yii\db\Exception $e) {
            Yii::$app->session->setFlash('error',$e->getMessage() );
        }

        return $this->render('viewmanager2stats', ['pall'=>$xpall, 'pall2' => $xpall2, 'form'=>$model]);
    }


}
