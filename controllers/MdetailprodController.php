<?php

namespace app\controllers;

use Yii;
use app\models\Mdetailprod;
use app\models\MdetailprodSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\data\ArrayDataProvider;

/**
 * MdetailprodController implements the CRUD actions for Mdetailprod model.
 */
class MdetailprodController extends Controller
{
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['post'],
                ],
            ],
            'access' => [
                'class' => \yii\filters\AccessControl::class,
                'rules' => [
                    [
                        'allow' => true,
                        'actions' => ['index', 'view', 'create', 'update', 'delete', 'pdf', 'viewdetail','documentlist','documentdetaillist'],
                        'matchCallback' => function ($rule, $action) {
                            return EstockTools::isUserStock() || EstockTools::isUserWman() || EstockTools::isUserWstock();
                        }
                    ],
                    [
                        'allow' => true,
                        'actions' => ['viewb2bdetail'],
                        'matchCallback' => function ($rule, $action) {
                            return EstockTools::isUserB2b();
                        }
                    ],
                    [
                        'allow' => true,
                        'actions' => ['forkmatid','editprice','editpcs','editmatid'],
                        'matchCallback' => function ($rule, $action) {
                            return EstockTools::isUserUcto();
                        }
                    ],
                    [
                        'allow' => false
                    ]
                ]
            ]
        ];
    }

    /**
     * Lists all Mdetailprod models.
     * @return mixed
     */
    public function actionIndex($id)
    {
        $searchModel = new MdetailprodSearch($id);
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        if(isset($_POST['export_type'])){$dataProvider->pagination = false;}
        else {$dataProvider->pagination->pageSize=10;}


        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Detail of the movement
     */
    public function actionDocumentdetaillist($id)
    {
        $model = \app\models\Movement::findOne($id);
        return $this->render('_alluctodetail', [
            'model' => $model,
        ]);
    }


    /**
     * Url action - expand from shop movement list
     */
    public function actionViewdetail() {
        if (isset($_POST['expandRowKey'])) {
            $model = \app\models\Movement::findOne($_POST['expandRowKey']);
            return $this->renderPartial('_expand', ['model'=>$model]);
        } elseif (isset($_GET['move_id'])) {
            if (($model = \app\models\Movement::findOne(['move_id'=>$_GET['move_id']])) !== null) {
                return $this->renderPartial('_expand', ['model'=>$model]);
            } else {
                throw new NotFoundHttpException('The requested page does not exist.');
            }
        } else {
            return '<div class="alert alert-danger">No data found</div>';
        }
    }


    /**
     * Url action - expand from shop movement list
     */
    public function actionViewb2bdetail() {
        if (isset($_POST['expandRowKey'])) {
            $model = \app\models\Movement::findOne($_POST['expandRowKey']);
            return $this->renderPartial('//mdetailprod/_detailb2b', ['model'=>$model]);
        } else {
            return '<div class="alert alert-danger">No data found</div>';
        }
    }


    /**
     * Displays a single MDetailProd model.
     * 
     * @return mixed
     */
    public function actionView($move_id,$mat_id,$detail_info)
    {
        $model = $this->findModel($move_id,$mat_id,$detail_info);
        return $this->render('view', [
            'model' => $this->findModel($move_id,$mat_id,$detail_info),
        ]);
    }

    /**
     * Forks one line in detail to more matids
     * 
     * @return mixed
     */
    public function actionForkmatid()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $data = Yii::$app->request->post();
        $key = json_decode( $data['editableKey'] );
        try {
                    $cmd =  Yii::$app->db->createCommand("update m_detail set pcs=pcs-1 where move_id=:mid and mat_id=:matid and detail_info=:di")
                    ->bindValue(':mid', $key->move_id )
                    ->bindValue(':matid', $key->mat_id )
                    ->bindValue(':di', $key->detail_info );
                    $cmd->query();
                    $cmd =  Yii::$app->db->createCommand("insert into m_detail (move_id,mat_id,price,pcs,discount,tax,m_repli_id,currency,p_repli_id,fifo_currency,fifo_price, fifo_move_id,detail_info,fifo_repli_id, all1)
    (select move_id,:newmatid,price,1,discount,tax,m_repli_id,currency,p_repli_id,fifo_currency,fifo_price,
    fifo_move_id,detail_info||'mod '||floor(random() * 1000 + 1),fifo_repli_id, all1 from m_detail where move_id=:mid and mat_id=:matid and detail_info=:di )")
                    ->bindValue(':newmatid', $data['Mdetailprod'][$data['editableIndex']]['mat_id'] )
                    ->bindValue(':mid', $key->move_id )
                    ->bindValue(':matid', $key->mat_id )
                    ->bindValue(':di', $key->detail_info );
                    $cmd->query();

                    $cmd =  Yii::$app->db->createCommand("call totalcountasuser(:mid, :uname)")
                    ->bindValue(':mid', $key->move_id )
                    ->bindValue(':uname', Yii::$app->user->identity->username );
                    $cmd->query();



        } catch (\yii\db\Exception $e) {
                    Yii::$app->session->setFlash('error',$e->getMessage() );
                    return false;
        }    
        return  json_encode($data['Mdetailprod']);
    }

    /**
     * Edit price in detail of the movement
     * 
     * @return mixed
     */
    public function actionEditprice()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $data = Yii::$app->request->post();
        $key = json_decode( $data['editableKey'] );
        Yii::debug($key);
        Yii::debug($_POST);
        try {
                    $cmd =  Yii::$app->db->createCommand("update m_detail set price=:p where move_id=:mid and mat_id=:matid and detail_info=:di")
                    ->bindValue(':p', $data['Mdetailprod'][$data['editableIndex']]['price'] )
                    ->bindValue(':mid', $key->move_id )
                    ->bindValue(':matid', $key->mat_id )
                    ->bindValue(':di', $key->detail_info );
                    $cmd->query();

        } catch (\yii\db\Exception $e) {
                    Yii::$app->session->setFlash('error',$e->getMessage() );
                    return false;
        }    
        return  json_encode($data['Mdetailprod']);
    }

    /**
     * Edit price in detail of the movement
     * 
     * @return mixed
     */
    public function actionEditmatid()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $data = Yii::$app->request->post();
        $key = json_decode( $data['editableKey'] );
        try {
                    $cmd =  Yii::$app->db->createCommand("update m_detail set mat_id=:p where move_id=:mid and mat_id=:matid and detail_info=:di")
                    ->bindValue(':p', $data['Mdetailprod'][$data['editableIndex']]['mat_id'] )
                    ->bindValue(':mid', $key->move_id )
                    ->bindValue(':matid', $key->mat_id )
                    ->bindValue(':di', $key->detail_info );
                    $cmd->query();

        } catch (\yii\db\Exception $e) {
                    Yii::$app->session->setFlash('error',$e->getMessage() );
                    return false;
        }    
        return  json_encode($data['Mdetailprod']);
    }


    /**
     * Edit pcs in detail of the movement
     * 
     * @return mixed
     */
    public function actionEditpcs()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $data = Yii::$app->request->post();
        $key = json_decode( $data['editableKey'] );
        try {
            if( $data['Mdetailprod'][$data['editableIndex']]['pcs'] == 0 ){
                    $cmd =  Yii::$app->db->createCommand("delete from m_detail where move_id=:mid and mat_id=:matid and detail_info=:di")
                    ->bindValue(':mid', $key->move_id )
                    ->bindValue(':matid', $key->mat_id )
                    ->bindValue(':di', $key->detail_info );

                } else {

                    $cmd =  Yii::$app->db->createCommand("update m_detail set pcs=:p where move_id=:mid and mat_id=:matid and detail_info=:di")
                    ->bindValue(':p', $data['Mdetailprod'][$data['editableIndex']]['pcs'] )
                    ->bindValue(':mid', $key->move_id )
                    ->bindValue(':matid', $key->mat_id )
                    ->bindValue(':di', $key->detail_info );
                }
                    $cmd->query();


                    $cmd =  Yii::$app->db->createCommand("call totalcountasuser(:mid, :uname)")
                    ->bindValue(':mid', $key->move_id )
                    ->bindValue(':uname', Yii::$app->user->identity->username );
                    $cmd->query();


        } catch (\yii\db\Exception $e) {
                    Yii::$app->session->setFlash('error',$e->getMessage() );
                    return false;
        }    
        return  json_encode($data['Mdetailprod']);
    }


    /**
     * Creates a new MDetailProd model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new Mdetailprod();

        if ($model->loadAll(Yii::$app->request->post()) && $model->saveAll()) {
            return $this->redirect(['view', ]);
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing MDetailProd model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * 
     * @return mixed
     */
    public function actionUpdate($move_id,$mat_id,$detail_info)
    {
        $model = $this->findModel($move_id,$mat_id,$detail_info);

        if ($model->loadAll(Yii::$app->request->post()) && $model->saveAll()) {
            return $this->redirect(['view', ]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing MDetailProd model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * 
     * @return mixed
     */
    public function actionDelete($move_id,$mat_id,$detail_info)
    {
        $this->findModel($move_id,$mat_id,$detail_info)->deleteWithRelated();

        return $this->redirect(['index']);
    }
    
    /**
     * 
     * Export MDetailProd information into PDF format.
     * 
     * @return mixed
     */
    public function actionPdf($id) {
        $model = $this->findModel($id);

        $content = $this->renderAjax('_pdf', [
            'model' => $model,
        ]);

        $pdf = new \kartik\mpdf\Pdf([
            'mode' => \kartik\mpdf\Pdf::MODE_CORE,
            'format' => \kartik\mpdf\Pdf::FORMAT_A4,
            'orientation' => \kartik\mpdf\Pdf::ORIENT_PORTRAIT,
            'destination' => \kartik\mpdf\Pdf::DEST_BROWSER,
            'content' => $content,
            'cssFile' => '@vendor/kartik-v/yii2-mpdf/assets/kv-mpdf-bootstrap.min.css',
            'cssInline' => '.kv-heading-1{font-size:18px}',
            'options' => ['title' => \Yii::$app->name],
            'methods' => [
                'SetHeader' => [\Yii::$app->name],
                'SetFooter' => ['{PAGENO}'],
            ]
        ]);

        return $pdf->render();
    }


    
    /**
     * 
     * Export MDetailProd information into PDF format.
     * 
     * @return mixed
     */
    public function actionDocumentlist($id) {
        $model = $this->findModel($id);

        $query = Yii::$app->db->createCommand("select * from m_detail_prod where move_id=:move_id ")
            ->bindValue(':move_id', $id);
        $mdetail = new ArrayDataProvider(['allModels' => $query->queryAll(),'pagination' => ['pageSize' => 10000,]]);

        $sumpcs = Yii::$app->db->createCommand("select sum(pcs) from m_detail where move_id=:move_id and mat_id not in (select mat_id from product where kod='SERV')")
            ->bindValue(':move_id', $id)->queryScalar();

        $content = $this->renderAjax('pdfviews/documentlist', [
            'model' => $model,
            'dataProvider' => $mdetail,
            'sumpcs' => $sumpcs,
        ]);

        $pdf = new \kartik\mpdf\Pdf([
            'mode' => \kartik\mpdf\Pdf::MODE_UTF8,
            'format' => \kartik\mpdf\Pdf::FORMAT_A4,
            'orientation' => \kartik\mpdf\Pdf::ORIENT_PORTRAIT,
            'destination' => \kartik\mpdf\Pdf::DEST_BROWSER,
            'content' => $content,
            'cssFile' => '@vendor/kartik-v/yii2-mpdf/assets/kv-mpdf-bootstrap.min.css',
            'cssInline' => '.kv-heading-1{font-size:12px}',
            'options' => ['title' => \Yii::$app->name],
            'methods' => [
                'SetHeader' => [\Yii::$app->name.' '.date('d.M Y').', '.Yii::$app->user->identity->username],
                'SetFooter' => ['{PAGENO} '],
            ]
        ]);

        return $pdf->render();
    }

    
    /**
     * Finds the MDetailProd model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * 
     * @return MDetailProd the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Mdetailprod::findOne(['move_id'=>$id])) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
