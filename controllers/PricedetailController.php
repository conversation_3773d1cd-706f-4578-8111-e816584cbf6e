<?php

namespace app\controllers;

use app\models\PriceDetail;
use app\models\PriceDetailSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * PricedetailController implements the CRUD actions for PriceDetail model.
 */
class PricedetailController extends Controller
{
    /**
     * @inheritDoc
     */
    public function behaviors()
    {
        return array_merge(
            parent::behaviors(),
            [
                'verbs' => [
                    'class' => VerbFilter::className(),
                    'actions' => [
                        'delete' => ['POST'],
                    ],
                ],
            ]
        );
    }

    /**
     * Lists all PriceDetail models.
     *
     * @return string
     */
    public function actionIndex()
    {
        $searchModel = new PriceDetailSearch();
        $dataProvider = $searchModel->search($this->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single PriceDetail model.
     * @param string $price_id Price ID
     * @return string
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($price_id)
    {
        return $this->render('view', [
            'model' => $this->findModel($price_id),
        ]);
    }

    /**
     * Creates a new PriceDetail model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return string|\yii\web\Response
     */
    public function actionCreate()
    {
        $model = new PriceDetail();

        if ($this->request->isPost) {
            if ($model->load($this->request->post()) && $model->save()) {
                return $this->redirect(['view', 'price_id' => $model->price_id]);
            }
        } else {
            $model->loadDefaultValues();
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing PriceDetail model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param string $price_id Price ID
     * @return string|\yii\web\Response
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($price_id)
    {
        $model = $this->findModel($price_id);

        if ($this->request->isPost && $model->load($this->request->post()) && $model->save()) {
            return $this->redirect(['view', 'price_id' => $model->price_id]);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing PriceDetail model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param string $price_id Price ID
     * @return \yii\web\Response
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($price_id)
    {
        $this->findModel($price_id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the PriceDetail model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param string $price_id Price ID
     * @return PriceDetail the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($price_id)
    {
        if (($model = PriceDetail::findOne(['price_id' => $price_id])) !== null) {
            return $model;
        }

        throw new NotFoundHttpException(Yii::t('app', 'The requested page does not exist.'));
    }
}
