<?php

namespace app\controllers;

use Yii;
use DateTime;
use yii\web\Controller;
use yii\helpers\Json;

class <PERSON>dleshopController extends Controller
{

    // KONFIGURACIA - MAPOVANIE SKLADOV S eshop SYSTEMOM
    public $skshops = [
        "s0" => ['adr_id' => 318, 'repli_id' => 0, 'stock_id' => 's0'],
        "s1" => ['adr_id' => 3624, 'repli_id' => 0, 'stock_id' => 'WAUP'],
        "s2" => ['adr_id' => 5039, 'repli_id' => 0, 'stock_id' => 'WDEU'],
        "s3" => ['adr_id' => 5040, 'repli_id' => 0, 'stock_id' => 'THS'],
        "s4" => ['adr_id' => 4413, 'repli_id' => 0, 'stock_id' => 'WBB'],
        "s5" => ['adr_id' => 3731, 'repli_id' => 0, 'stock_id' => 'WOPT'],
        "s6" => ['adr_id' => 11737, 'repli_id' => 0, 'stock_id' => 'GARM'],
    ];

    public $hushops = [

        "s0" => ['adr_id' => 3806, 'repli_id' => 2, 'stock_id' => 's2'],
        "s1" => ['adr_id' => 2805, 'repli_id' => 2, 'stock_id' => ''], //DOES NOT EXISTS: WATCH DE LUXE KFT / LURDY-HÁZ | LEJTŐ U. 23.     | BUDAPEST
        "s2" => ['adr_id' => 4585, 'repli_id' => 2, 'stock_id' => 'SWA'],
        "s3" => ['adr_id' => 6118, 'repli_id' => 2, 'stock_id' => 'SWE'],
        "s4" => ['adr_id' => 4727, 'repli_id' => 2, 'stock_id' => 'STHH'],
        "s5" => ['adr_id' => 6487, 'repli_id' => 2, 'stock_id' => 'SRH1'],
    ];



    /**
     * @inheritdoc
     */
    public function beforeAction($action)
    {
        if ($action->id == 'shopify') {
            $this->enableCsrfValidation = false;
        }
        return parent::beforeAction($action);
    }


    /*
 * Feed for eshop wdl.sk
 * If all == false, only actual stocks shows, otherwise all models which in history had movements
 *
 */

    public function actionFeedsk($all = false)
    {
        Yii::$app->db->createCommand("call dyninfo_recount_all()")->query();


        $datetime = new DateTime();
        $kods = "'ATHS','AMON','EMON','BREI','CERS','JMON','LMON','OMEG','OMON','PMON','RADO','TISS','WLOX','WLON','WMON','MODA', 'PANE', 'BMN','WTHS','ZENI','WMID','IWC','GARS','GARM','IWCC','PANE','WOLF', 'TAGW','TAGC', 'MOSA', 'BELR', 'NORQ', 'BIAT', 'PRIM', 'CYRU', 'WRAD', 'SEIK', 'GRPW' ";

        $out = '{"created":"' . $datetime->format(DateTime::ATOM);
        $out .= '","products":
    [';
        $first = true;
        $select = "select x.mat_id code,'x' shops, p0e basepriceEur, p0f basepriceCzk from dyninfox x left outer join product p on p.mat_id=x.mat_id
    where
    x.kod in
    ( " . $kods . " )
    group by code,p0e,p0f,p6,x.kod
    order by code";

        if ($all == true) {
            $select = "select p.mat_id code,'x' shops, p0e basepriceEur, p0f basepriceCzk, p0a discb, p0b discg, p0c discp, k.cgroup from product p join kod k on p.kod=k.kod
        where
        p.kod in
        ( " . $kods . " )
        group by code,p0e,p0f,p6,p.kod,cgroup
        order by code";
        }


        // $select = "select x.mat_id code,'x' shops, p0e basepriceEur, p0f basepriceCzk from product x where
        // x.kod in
        // ( ('ATHS','AMON','BREI','CERS','FEST','JMON','LMON','OMEG','OMON','PMON','RADO','TISS','WLOX','WLON','WMON','MODA', 'PANE', 'BMN','WTHS','WTHC','ZENI','WMID','IWC','GARS','GARM','IWCC' )  or   x.kod in ('FCAS') )
        // and mat_id in (select mat_id from m_detail)
        // group by code,p0e,p0f,p6,x.kod
        // order by code";



        $rows = Yii::$app->db->createCommand($select)->queryAll();
        foreach ($rows as $row) {
            $row['basepriceeur'] = floatval($row['basepriceeur']);
            $row['basepriceczk'] = floatval($row['basepriceczk']);
            $rowshops = ['s0' => 0, 's1' => 0, 's2' => 0, 's3' => 0, 's4' => 0, 's5' => 0, 's6' => 0];
            foreach (
                Yii::$app->db->createCommand("select shop,sum(remains) sr from dyninfox where shop in ('s0','WAUP','WDEU','THS','WBB','WOPT','GARM') and mat_id=:matid group by shop order by shop")
                    ->bindValue(':matid', $row['code'])
                    ->queryAll() as $shop
            ) {
                //var_dump($shop);
                switch ($shop['shop']) {
                    case "s0":
                        $rowshops['s0'] = floatval($shop['sr']);
                        break;
                    case "WAUP": // "WATCH DE LUXE - AUPA 3624 0":
                        $rowshops['s1'] = floatval($shop['sr']);
                        break;
                    case "WDEU": // "Watch de Luxe, Eurov 5039 0":
                        $rowshops['s2'] = floatval($shop['sr']);
                        break;
                    case "THS": // "TAG Heuer,Eurovea 5040 0":
                        $rowshops['s3'] = floatval($shop['sr']);
                        break;
                    case "WBB": // "WOW  BB,RACIO,Export 4413 0":
                        $rowshops['s4'] = floatval($shop['sr']);
                        break;
                    case "WOPT": // "W-WATCH PREV. OPTIMA 3731 0":
                        $rowshops['s5'] = floatval($shop['sr']);
                        break;
                    case "GARM": // "GARMIN virtual external stock":
                        $rowshops['s6'] = floatval($shop['sr']);
                        break;
                }
            }
            $row['shops'] = $rowshops;

            if ($all === true || (
                $rowshops['s0'] != 0 || $rowshops['s1'] != 0 || $rowshops['s2'] != 0 || $rowshops['s3'] != 0 || $rowshops['s4'] != 0 || $rowshops['s5'] != 0 || $rowshops['s6'] != 0
            )) {

                if (!$first) {
                    $out .= ",\n";
                } else {
                    $first = false;
                }
                $out .= json_encode($row, JSON_FORCE_OBJECT);
            }
        }

        $out .= "]}";

        header('Content-Type: application/json');
        echo $out;
        exit;

        //return $this->asJson($out);

    }

    public function actionFeedskall()
    {
        return $this->actionFeedsk(true);
    }


    public function actionFeedskwow($all = false)
    {

        Yii::$app->db->createCommand("call dyninfo_recount_all()")->query();


        $datetime = new DateTime();

        $kods = "'CERS','FEST','RADO','TISS','WLOX','WLON','WMID','GARS','GARM', 'WOLF', 'GANT', 'MOSA', 'WRAD', 'SEIK' )  or ( remains>0 and x.kod in ('FCAS')";
        $out = '{"created":"' . $datetime->format(DateTime::ATOM);
        $out .= '","products":
    [';
        $first = true;
        $select = "select x.mat_id code,'x' shops, p0e basepriceEur, p0f basepriceCzk from dyninfox x left outer join product p on p.mat_id=x.mat_id
    where
    x.kod in
    ( " . $kods . " )
    group by code,p0e,p0f,p6,x.kod
    order by code";

        if ($all == true) {
            $select = "select x.mat_id code,'x' shops, p0e basepriceEur, p0f basepriceCzk from product x left outer join dyninfox zz on x.mat_id=zz.mat_id
        where
        x.kod in
        ( " . $kods . " )
        group by code,p0e,p0f,p6,x.kod
        order by code";
        }

        // $select = "select x.mat_id code,'x' shops, p0e basepriceEur, p0f basepriceCzk from product x where
        // x.kod in
        // ( ('ATHS','AMON','BREI','CERS','FEST','JMON','LMON','OMEG','OMON','PMON','RADO','TISS','WLOX','WLON','WMON','MODA', 'PANE', 'BMN','WTHS','WTHC','ZENI','WMID','IWC','GARS','GARM','IWCC' )  or   x.kod in ('FCAS') )
        // and mat_id in (select mat_id from m_detail)
        // group by code,p0e,p0f,p6,x.kod
        // order by code";



        $rows = Yii::$app->db->createCommand($select)->queryAll();
        foreach ($rows as $row) {
            $row['basepriceeur'] = floatval($row['basepriceeur']);
            $row['basepriceczk'] = floatval($row['basepriceczk']);
            $rowshops = ['s0' => 0, 's1' => 0, 's2' => 0, 's3' => 0, 's4' => 0, 's5' => 0, 's6' => 0];
            foreach (
                Yii::$app->db->createCommand("select shop,sum(remains) sr from dyninfox where shop in ('s0','WAUP','WDEU','THS','WBB','WOPT','GARM') and mat_id=:matid group by shop order by shop")
                    ->bindValue(':matid', $row['code'])
                    ->queryAll() as $shop
            ) {
                //var_dump($shop);
                switch ($shop['shop']) {
                    // case "s0":
                    // $rowshops['s0'] = floatval($shop['sr']);
                    // break;
                    // case "WAUP": // "WATCH DE LUXE - AUPA 3624 0":
                    // $rowshops['s1'] = floatval($shop['sr']);
                    // break;
                    // case "WDEU": // "Watch de Luxe, Eurov 5039 0":
                    // $rowshops['s2'] = floatval($shop['sr']);
                    // break;
                    // case "THS": // "TAG Heuer,Eurovea 5040 0":
                    // $rowshops['s3'] = floatval($shop['sr']);
                    // break;
                    // case "WBB": // "WOW  BB,RACIO,Export 4413 0":
                    // $rowshops['s4'] = floatval($shop['sr']);
                    // break;
                    case "WOPT": // "W-WATCH PREV. OPTIMA 3731 0":
                        $rowshops['s5'] = floatval($shop['sr']);
                        break;
                        // case "GARM": // "GARMIN virtual external stock":
                        // $rowshops['s6'] = floatval($shop['sr']);
                        // break;
                }
            }
            $row['shops'] = $rowshops;


            if ($all === true || (
                $rowshops['s0'] != 0 || $rowshops['s1'] != 0 || $rowshops['s2'] != 0 || $rowshops['s3'] != 0 || $rowshops['s4'] != 0 || $rowshops['s5'] != 0 || $rowshops['s6'] != 0
            )) {
                if (!$first) {
                    $out .= ",\n";
                } else {
                    $first = false;
                }
                $out .= json_encode($row, JSON_FORCE_OBJECT);
            }
        }

        $out .= "]}";

        header('Content-Type: application/json');
        echo $out;
        exit;

        //return $this->asJson($out);

    }

    public function actionFeedskwowall()
    {
        return $this->actionFeedskwow(true);
    }



    public function actionFeedhu($all = false)
    {

        Yii::$app->db->createCommand("call dyninfo_recount_all()")->query();


        $datetime = new DateTime();

        $kods = "    x.kod like 'GAR%' or
    x.kod like 'WTH%' or x.kod like 'WFC%' or x.kod like  'WLO%' or x.kod like  'WMO%' or x.kod like  'PMO%'  or x.kod like 'JMO%' or x.kod like  'LMO%' or x.kod like  'MID%'
    or x.kod = 'CART' or x.kod = 'PANE' or x.kod = 'IWCC' or x.kod = 'TUDO'
    or x.kod = 'BREI' or x.kod = 'ZENI' or x.kod = 'TISS' or x.kod = 'TISH' or x.kod = 'WMID' or x.kod = 'OMON' or x.kod = 'AMON' or x.kod = 'RADO'
    or x.kod = 'CHOJ' or x.kod = 'CHOW' or x.kod = 'WOLF' or x.kod = 'EMON'
    or x.kod = 'TAGW' or x.kod = 'TAGC' or x.kod = 'WRAD' or x.kod = 'MOSA'
    or x.kod = 'BTHC' or x.kod = 'NORQ' or x.kod = 'SMON' 
        ";
        $out = '{"created":"' . $datetime->format(DateTime::ATOM);
        $out .= '","products":
    [';
        $first = true;

        $select = "select x.mat_id code,'x' shops, p4 basepriceHu from dyninfox x left outer join product p on p.mat_id=x.mat_id
    where " . $kods . "
    group by code,p4,x.kod
    order by code";

        if ($all == true) {
            $select = "select x.mat_id code,'x' shops, p4 basepriceHu from product x
        where
        " . $kods . " 
        group by code,p0e,p0f,p6,kod
        order by code";
        }

        $rows = Yii::$app->db->createCommand($select)->queryAll();
        foreach ($rows as $row) {
            $row['basepricehu'] = floatval($row['basepricehu']);
            $rowshops = ['s0' => 0, 's1' => 0, 's2' => 0, 's3' => 0, 's4' => 0, 's5' => 0];
            foreach (
                Yii::$app->db->createCommand("select shop,sum(remains) sr from dyninfox where shop in ('s2','SWA','SWE','STHH','SRH1','s4') and mat_id=:matid group by shop order by shop")
                    ->bindValue(':matid', $row['code'])
                    ->queryAll() as $shop
            ) {
                //var_dump($shop);
                switch ($shop['shop']) {
                    case "s2":
                        $rowshops['s0'] = floatval($shop['sr']);
                        break;
                    case "s4":
                        $rowshops['s1'] = floatval($shop['sr']);
                        break;
                    case "SWA": // Watch Aréna Kft. 4585
                        $rowshops['s2'] = floatval($shop['sr']);
                        break;
                    case "SWE": // West-watch Kft. 6118 2
                        $rowshops['s3'] = floatval($shop['sr']);
                        break;
                    case "STHH": // TH SHOP KFT. 4727 2":
                        $rowshops['s4'] = floatval($shop['sr']);
                        break;
                    case "SRH1": // Watch de Luxe András 6487 2":
                        $rowshops['s5'] = floatval($shop['sr']);
                        break;
                }
            }
            $row['shops'] = $rowshops;

            if ($all === true || (
                $rowshops['s0'] != 0 || $rowshops['s1'] != 0 || $rowshops['s2'] != 0 || $rowshops['s3'] != 0 || $rowshops['s4'] != 0 || $rowshops['s5'] != 0
            )) {
                if (!$first) {
                    $out .= ",\n";
                } else {
                    $first = false;
                }
                $out .= json_encode($row, JSON_FORCE_OBJECT);
            }
        }

        $out .= "]}";

        header('Content-Type: application/json');
        echo $out;
        exit;

        //return $this->asJson($out);

    }

    public function actionFeedhuall()
    {
        return $this->actionFeedhu(true);
    }


    /**
     * Helper functions for SALE service
     */
    private function isValidJSON($str)
    {
        json_decode($str);
        return json_last_error() == JSON_ERROR_NONE;
    }

    private function exitOk($data)
    {
        header('Content-Type: application/json');
        $json = json_encode($data);
        echo $json;
        exit;
    }


    private function exitErr($msg = "unknown")
    {
        header("HTTP/1.1 500 Error");
        header('Content-Type: application/json');

        // $json = '{"jsonError": "'.rawurlencode($msg).'"}';
        $json = '{"jsonError": "' . $msg . '"}';
        echo $json;
        exit;
    }

    /**
     * get adr_id of the shopify order
     *
     */
    private function getAdrShopify($order_id)
    {
        $username = Yii::$app->params['shopifyAPIusername'];
        $password = Yii::$app->params['shopifyAPIpwd'];
        $url = "https://wow-watches-store.myshopify.com/admin/api/2021-10/orders/" . $order_id . "/fulfillment_orders.json";
        $context = stream_context_create(array(
            'http' => array(
                'header'  => "Authorization: Basic " . base64_encode("$username:$password")
            )
        ));
        $json = file_get_contents($url, false, $context);
        if (strlen($json) > 0 && $this->isValidJSON($json)) {
            $d = json_decode($json);
            if (!isset($d->fulfillment_orders[0]->assigned_location->location_id)) {
                // echo "<pre>";
                // print_r($d);
                return 's0';
            }
            switch ($d->fulfillment_orders[0]->assigned_location->location_id) {
                case '64096829627':
                    return 's1'; //aupark
                    break;
                case '64634912955':
                    return 's4'; //europa
                    break;
                case '64634847419':
                    return 's2'; //Eurovea
                    break;
                case '64634880187':
                    return 's6'; //GARMIN
                    break;
                case '63151571131':
                    return 's0'; //s0
                    break;
                case '64634945723':
                    return 's5'; //OPTIMA
                    break;
                default:
                    return 's0'; //s0
                    break;
            }
        } else {
            Yii::debug("Shopify error - no json received");
            return 's0';
        }
    }

    /**
     * test functionality only you can e.g. https://wstock.racio.com/wdleshop/shopifytest?id=4282040615099
     */
    public function actionShopifytest($id)
    {
        echo $this->getAdrShopify($id);
    }

    /**
     * test only temporary
     */
    public function actionTest()
    {
        $uname = 'eshopBot';
        $posledny = Yii::$app->db->createCommand("SELECT max(move_id) posledny FROM movement where emp_id='$uname'")->queryScalar();
        $poslednyxx = Yii::$app->db->createCommand("SELECT max(move_id) posledny FROM movement where emp_id='$uname'")->queryScalar();
        print("<pre>");
        print_r($posledny);
        if ($poslednyxx == $posledny) { // nic nevytvoril..
            echo "nic";
        }
        echo "SELECT max(move_id) posledny FROM movement where emp_id='$uname'";
    }

    /**
     * Automatic SALE system for Shopify
     * @return JSON
     */
    public function actionShopify()
    {
        $shops = $this->skshops;
        $ordermtypeid = 17;
        $defaultprice = 'p0';
        $toshop = 'sss';
        $uname = 'eshopBot';

        $json_params = file_get_contents("php://input");
        if (strlen($json_params) > 0 && $this->isValidJSON($json_params)) {
            $d = json_decode($json_params);
            Yii::debug($d);
        } else {
            Yii::debug("Shopify error - no json received");
            $this->exitErr("Error - nothing to do");
        }


        // UPRAVA NA TVORBU DOKUMENTOV
        $documents = [];

        foreach ($d->line_items as $key => $prod) {
            // Neviem adr_id... davam racio 318, s0
            // $documents[$prod->origin_location->id][$prod->sku] = $prod->quantity;
            //Stock podla order_id co je vlasne aj hlavne id jsonu:
            $stockname = $this->getAdrShopify($d->id);

            $documents[$stockname][$prod->sku] = $prod->quantity;
        }
        // AK NIC TAK KONIEC
        if (sizeof($documents) < 1) {
            $this->exitErr("nothing to do");
        }

        try {

            $query = Yii::$app->db->createCommand("INSERT INTO eshop (orderid,\"order\",created,email,iname,isurname,istreet,icity,izip,iphone,icountry,icompany,iico,idic,dcompany,dname,dsurname,dstreet,dcity,
            dzip,dphone,dcountry,
            gcompany,gname,gsurname,gstreet,gcity,gzip,gphone,gcountry,gift,note,
            shipment,branch,voucher,payment,totalitems,totalitemsnovat,totalshipment,totalpayment,totalclub,total,currency,products,alljson)  values (
            :orderid,:order,now(),:email,:iname,:isurname,:istreet,:icity,:izip,:iphone,:icountry,:icompany,:iico,:idic,:dcompany,:dname,:dsurname,:dstreet,
            :dcity,:dzip,:dphone,:dcountry,:gcompany,:gname,:gsurname,:gstreet,:gcity,:gzip,:gphone,:gcountry,:gift,:note,:shipment,:branch,
            :voucher,:payment,:totalitems,:totalitemsnovat,:totalshipment,:totalpayment,:totalclub,:total,:currency,:products,:alljson)")
                ->bindValue(':orderid', $d->order_number ?? 0)
                ->bindValue(':order', $d->number ?? 0)
                ->bindValue(':email', $d->customer->email ?? '')
                ->bindValue(':iname', $d->customer->first_name)
                ->bindValue(':isurname', $d->customer->last_name)
                ->bindValue(':istreet', $d->customer->default_address->address1 . ' ' . $d->customer->default_address->address2)
                ->bindValue(':icity', $d->customer->default_address->city)
                ->bindValue(':izip', $d->customer->default_address->zip)
                ->bindValue(':iphone', $d->customer->default_address->phone)
                ->bindValue(':icountry', $d->customer->default_address->country)
                ->bindValue(':icompany', '')

                ->bindValue(':iico', '')
                ->bindValue(':idic', '')
                ->bindValue(':dcompany', '')
                ->bindValue(':dname', '')
                ->bindValue(':dsurname', '')
                ->bindValue(':dstreet', '')

                ->bindValue(':dcity', '')
                ->bindValue(':dzip', '')
                ->bindValue(':dphone', '')
                ->bindValue(':dcountry', '')
                ->bindValue(':gcompany', '')
                ->bindValue(':gname', '')

                ->bindValue(':gsurname', '')
                ->bindValue(':gstreet', '')
                ->bindValue(':gcity', '')
                ->bindValue(':gzip', '')
                ->bindValue(':gphone', '')
                ->bindValue(':gcountry', '')

                ->bindValue(':gift', '0')
                ->bindValue(':note', $d->note)
                ->bindValue(':shipment', '')
                ->bindValue(':branch', '')
                ->bindValue(':voucher', '')
                ->bindValue(':payment', $d->payment_gateway_names[0])

                ->bindValue(':totalitems', count($d->line_items))
                ->bindValue(':totalitemsnovat', count($d->line_items))
                ->bindValue(':totalshipment', $d->total_shipping_price_set->shop_money->amount)
                ->bindValue(':totalpayment', $d->total_price)
                ->bindValue(':totalclub', 0)
                ->bindValue(':total', $d->total_price)
                ->bindValue(':currency', $d->total_price_set->shop_money->currency_code)
                ->bindValue(':products', json_encode($d->line_items))
                ->bindValue(':alljson', $json_params)
                ->query();

            //Bugfix - icompany sometimes empty...
            Yii::$app->db->createCommand("update eshop set icompany=' ' where orderid=:orderid and icompany is null")
                ->bindValue(':orderid', $d->order_number ? $d->order_number : 0)
                ->query();


            //Clean the clipboards;
            // use nr 50 ...
            // 60

            $stmt = Yii::$app->db->createCommand("DELETE FROM tempmove WHERE user_name='" . $uname . "' and clip_id between 50 and 60")->query();

            $clipnr = 50;
            $shopAdr = [];
            foreach ($documents as $shop => $prod) {
                foreach ($prod as $matid => $pcs) {
                    $shopAdr[$clipnr] = $shop;
                    $stmt = Yii::$app->db->createCommand("SELECT model FROM product where mat_id='" . $matid . "' limit 1")->queryScalar();
                    if (empty($stmt)) {
                        $this->exitErr($matid . " does not exists");
                    } else {
                        $query = "call pridajasuser(:adr_id, :mat_id, :clip_id, :pcs, :xx, :price, :d1, :d2, :d3, :m_type_id, :price2, :stock_id1,:stock_id2,:text1,:text2,:tempmove_info, :thserial, :user)";
                        Yii::$app->db->createCommand($query)
                            ->bindValue(':adr_id', $shops[$shopAdr[$clipnr]]["adr_id"])
                            ->bindValue(':mat_id', $matid)
                            ->bindValue(':clip_id', $clipnr)
                            ->bindValue(':pcs', $pcs)
                            ->bindValue(':price', 0)
                            ->bindValue(':xx', 'xx')
                            ->bindValue(':d1', date("Y-m-d"))
                            ->bindValue(':d2', date("Y-m-d"))
                            ->bindValue(':d3', date("Y-m-d"))
                            ->bindValue(':m_type_id', $ordermtypeid)
                            ->bindValue(':price2', $defaultprice)
                            ->bindValue(':stock_id1', null)
                            ->bindValue(':stock_id2', null)
                            ->bindValue(':text1', '')
                            ->bindValue(':text2', '')
                            ->bindValue(':tempmove_info', '')
                            ->bindValue(':thserial', null)
                            ->bindValue(':user', $uname)
                            ->query();
                    }
                }
                $clipnr += 1;
            }


            $posledny = Yii::$app->db->createCommand("SELECT max(move_id) posledny FROM movement where emp_id='$uname'")->queryScalar();


            for ($finalclip = 50; $finalclip < $clipnr; $finalclip++) {

                try {

                    Yii::$app->db->createCommand("call newpaper("
                        . "14,'x','"
                        . date("Y-m-d") . "','"
                        . date("Y-m-d") . "','"
                        . date("Y-m-d") . "',"
                        . "0,'"
                        . ($d->order ?? 'OrderNrUnknown') . "','"
                        . "created: " . ($d->created ?? "n/a") . "',"
                        . $shops[$shopAdr[$finalclip]]["adr_id"] . ",'"
                        . $uname . "',"
                        . "'" . $shops[$shopAdr[$finalclip]]["stock_id"] . "',"
                        . "'" . $toshop . "',"
                        . "'x'" . ","
                        . $shops[$shopAdr[$finalclip]]["repli_id"] . ","
                        . $finalclip . ")")->query();
                } catch (\yii\db\Exception $e) {

                    Yii::debug($e->getMessage());

                    $poslednyxx = Yii::$app->db->createCommand("SELECT max(move_id) posledny FROM movement where emp_id='$uname'")->queryScalar();
                    if ($poslednyxx == $posledny) { // nic nevytvoril..

                        Yii::$app->db->createCommand("call newpaper("
                            . $ordermtypeid . ",'x','"
                            . date("Y-m-d") . "','"
                            . date("Y-m-d") . "','"
                            . date("Y-m-d") . "',"
                            . "0,'"
                            . ($d->order ?? 'OrderNrUnknown') . "',:msg,"
                            . $shops[$shopAdr[$finalclip]]["adr_id"] . ",'"
                            . $uname . "',"
                            . "'" . $shops[$shopAdr[$finalclip]]["stock_id"] . "',"
                            . "'" . $toshop . "',"
                            . "'x'" . ","
                            . $shops[$shopAdr[$finalclip]]["repli_id"] . ","
                            . $finalclip . ")")
                            ->bindValue(':msg', "TRANFER FAILED!!! " . substr($e->getMessage(), 43, 190))
                            ->query();
                    }
                }
            }
        } catch (\yii\db\Exception $e) {
            $this->exitErr($e->getMessage());
        }

        $this->exitOk(["result" => "OK"]);
    }


    /**
     * Automatic SALE system
     * @param $id integer // 0 = SK 2 = HU
     * @return JSON
     */
    public function actionSale($id = 99999)
    {
        switch ($id) {
            case 0:
                $shops = $this->skshops;
                $ordermtypeid = 17;
                $defaultprice = 'p0';
                $toshop = 'sss';
                $uname = 'eshopBot';
                break;
            case 2:
                $shops = $this->hushops;
                $ordermtypeid = 29;
                $defaultprice = 'p2';
                $toshop = 'WSHU';
                $uname = 'eshopHu';
                break;

            default:
                $this->exitErr("Bad parameter ID of eshop");
                break;
        }

        $json_params = file_get_contents("php://input");

        if (strlen($json_params) > 0 && $this->isValidJSON($json_params)) {
            $d = json_decode($json_params);
        } else {
            $this->exitErr("nothing to do");
        }

        // UPRAVA NA TVORBU DOKUMENTOV
        $documents = [];

        foreach ($d->products as $key => $prod) {
            foreach ($prod->shops as $skey => $detail) {
                $documents[$skey][$prod->code] = $detail;
            }
        }



        // AK NIC TAK KONIEC
        if (sizeof($documents) < 1) {
            $this->exitErr("nothing to do");
        }

        try {

            if (is_null($d->orderData)) {
                $query = Yii::$app->db->createCommand("INSERT INTO eshop (orderid,\"order\",created,email,iname,isurname,istreet,icity,izip,iphone,icountry,icompany,iico,idic,dcompany,dname,dsurname,dstreet,dcity,
                dzip,dphone,dcountry,
                gcompany,gname,gsurname,gstreet,gcity,gzip,gphone,gcountry,gift,note,
                shipment,branch,voucher,payment,totalitems,totalitemsnovat,totalshipment,totalpayment,totalclub,total,currency,products,alljson)  values (
                :orderid,:order,now(),:email,:iname,:isurname,:istreet,:icity,:izip,:iphone,:icountry,:icompany,:iico,:idic,:dcompany,:dname,:dsurname,:dstreet,
                :dcity,:dzip,:dphone,:dcountry,:gcompany,:gname,:gsurname,:gstreet,:gcity,:gzip,:gphone,:gcountry,:gift,:note,:shipment,:branch,
                :voucher,:payment,:totalitems,:totalitemsnovat,:totalshipment,:totalpayment,:totalclub,:total,:currency,:products,:alljson)")
                    ->bindValue(':orderid', $d->orderId ?? 0)
                    ->bindValue(':order', $d->order ?? 0)
                    ->bindValue(':email', $d->inquiryData->email ?? null)
                    ->bindValue(':iname', $d->inquiryData->invoiceInfo->name ?? null)
                    ->bindValue(':isurname', $d->inquiryData->invoiceInfo->surname ?? null)
                    ->bindValue(':istreet', $d->inquiryData->invoiceInfo->street ?? null)
                    ->bindValue(':icity', $d->inquiryData->invoiceInfo->city ?? null)
                    ->bindValue(':izip', $d->inquiryData->invoiceInfo->zip ?? null)
                    ->bindValue(':iphone', $d->inquiryData->invoiceInfo->phone ?? null)
                    ->bindValue(':icountry', $d->inquiryData->invoiceInfo->country ?? null)
                    ->bindValue(':icompany', $d->inquiryData->companyInfo->company ?? null)

                    ->bindValue(':iico', $d->inquiryData->companyInfo->ico ?? null)
                    ->bindValue(':idic', $d->inquiryData->companyInfo->dic ?? null)
                    ->bindValue(':dcompany', $d->inquiryData->deliveryInfo->company ?? null)
                    ->bindValue(':dname', $d->inquiryData->deliveryInfo->name ?? null)
                    ->bindValue(':dsurname', $d->inquiryData->deliveryInfo->surname ?? null)
                    ->bindValue(':dstreet', $d->inquiryData->deliveryInfo->street ?? null)

                    ->bindValue(':dcity', $d->inquiryData->deliveryInfo->city ?? null)
                    ->bindValue(':dzip', $d->inquiryData->deliveryInfo->zip ?? null)
                    ->bindValue(':dphone', $d->inquiryData->deliveryInfo->phone ?? null)
                    ->bindValue(':dcountry', $d->inquiryData->deliveryInfo->country ?? null)
                    ->bindValue(':gcompany', $d->inquiryData->giftInfo->company ?? null)
                    ->bindValue(':gname', $d->inquiryData->giftInfo->name ?? null)

                    ->bindValue(':gsurname', $d->inquiryData->giftInfo->surname ?? null)
                    ->bindValue(':gstreet', $d->inquiryData->giftInfo->street ?? null)
                    ->bindValue(':gcity', $d->inquiryData->giftInfo->city ?? null)
                    ->bindValue(':gzip', $d->inquiryData->giftInfo->zip ?? null)
                    ->bindValue(':gphone', $d->inquiryData->giftInfo->phone ?? null)
                    ->bindValue(':gcountry', $d->inquiryData->giftInfo->country ?? null)

                    ->bindValue(':gift', empty($d->inquiryData->gift) ? '0' : $d->inquiryData->gift)
                    ->bindValue(':note', $d->inquiryData->note ?? null)
                    ->bindValue(':shipment', $d->inquiryData->shipment ?? null)
                    ->bindValue(':branch', $d->inquiryData->branch ?? null)
                    ->bindValue(':voucher', $d->inquiryData->voucher ?? null)
                    ->bindValue(':payment', $d->inquiryData->payment ?? null)

                    ->bindValue(':totalitems', $d->inquiryData->price->items ?? null)
                    ->bindValue(':totalitemsnovat', $d->inquiryData->price->items ?? null)
                    ->bindValue(':totalshipment', $d->inquiryData->price->items ?? null)
                    ->bindValue(':totalpayment', $d->inquiryData->price->items ?? null)
                    ->bindValue(':totalclub', $d->inquiryData->price->items ?? null)
                    ->bindValue(':total', $d->inquiryData->price->items ?? null)
                    ->bindValue(':currency', $d->inquiryData->price->currency ?? null)
                    ->bindValue(':products', json_encode($d->products))
                    ->bindValue(':alljson', $json_params ?? null)
                    ->query();
            } else {

                $query = Yii::$app->db->createCommand("INSERT INTO eshop (orderid,\"order\",created,email,iname,isurname,istreet,icity,izip,iphone,icountry,icompany,iico,idic,dcompany,dname,dsurname,dstreet,dcity,
                dzip,dphone,dcountry,
                gcompany,gname,gsurname,gstreet,gcity,gzip,gphone,gcountry,gift,note,
                shipment,branch,voucher,payment,totalitems,totalitemsnovat,totalshipment,totalpayment,totalclub,total,currency,products,alljson)  values (
                :orderid,:order,now(),:email,:iname,:isurname,:istreet,:icity,:izip,:iphone,:icountry,:icompany,:iico,:idic,:dcompany,:dname,:dsurname,:dstreet,
                :dcity,:dzip,:dphone,:dcountry,:gcompany,:gname,:gsurname,:gstreet,:gcity,:gzip,:gphone,:gcountry,:gift,:note,:shipment,:branch,
                :voucher,:payment,:totalitems,:totalitemsnovat,:totalshipment,:totalpayment,:totalclub,:total,:currency,:products,:alljson)")
                    ->bindValue(':orderid', $d->orderId ?? 0)
                    ->bindValue(':order', $d->order ?? 0)
                    ->bindValue(':email', $d->orderData->email ?? null)
                    ->bindValue(':iname', $d->orderData->invoiceInfo->name ?? null)
                    ->bindValue(':isurname', $d->orderData->invoiceInfo->surname ?? null)
                    ->bindValue(':istreet', $d->orderData->invoiceInfo->street ?? null)
                    ->bindValue(':icity', $d->orderData->invoiceInfo->city ?? null)
                    ->bindValue(':izip', $d->orderData->invoiceInfo->zip ?? null)
                    ->bindValue(':iphone', $d->orderData->invoiceInfo->phone ?? null)
                    ->bindValue(':icountry', $d->orderData->invoiceInfo->country ?? null)
                    ->bindValue(':icompany', $d->orderData->companyInfo->company ?? null)

                    ->bindValue(':iico', $d->orderData->companyInfo->ico ?? null)
                    ->bindValue(':idic', $d->orderData->companyInfo->dic ?? null)
                    ->bindValue(':dcompany', $d->orderData->deliveryInfo->company ?? null)
                    ->bindValue(':dname', $d->orderData->deliveryInfo->name ?? null)
                    ->bindValue(':dsurname', $d->orderData->deliveryInfo->surname ?? null)
                    ->bindValue(':dstreet', $d->orderData->deliveryInfo->street ?? null)

                    ->bindValue(':dcity', $d->orderData->deliveryInfo->city ?? null)
                    ->bindValue(':dzip', $d->orderData->deliveryInfo->zip ?? null)
                    ->bindValue(':dphone', $d->orderData->deliveryInfo->phone ?? null)
                    ->bindValue(':dcountry', $d->orderData->deliveryInfo->country ?? null)
                    ->bindValue(':gcompany', $d->orderData->giftInfo->company ?? null)
                    ->bindValue(':gname', $d->orderData->giftInfo->name ?? null)

                    ->bindValue(':gsurname', $d->orderData->giftInfo->surname ?? null)
                    ->bindValue(':gstreet', $d->orderData->giftInfo->street ?? null)
                    ->bindValue(':gcity', $d->orderData->giftInfo->city ?? null)
                    ->bindValue(':gzip', $d->orderData->giftInfo->zip ?? null)
                    ->bindValue(':gphone', $d->orderData->giftInfo->phone ?? null)
                    ->bindValue(':gcountry', $d->orderData->giftInfo->country ?? null)

                    ->bindValue(':gift', empty($d->orderData->gift) ? '0' : $d->orderData->gift)
                    ->bindValue(':note', $d->orderData->note ?? null)
                    ->bindValue(':shipment', $d->orderData->shipment ?? null)
                    ->bindValue(':branch', $d->orderData->branch ?? null)
                    ->bindValue(':voucher', $d->orderData->voucher ?? null)
                    ->bindValue(':payment', $d->orderData->payment ?? null)

                    ->bindValue(':totalitems', $d->orderData->price->items ?? null)
                    ->bindValue(':totalitemsnovat', $d->orderData->price->items ?? null)
                    ->bindValue(':totalshipment', $d->orderData->price->items ?? null)
                    ->bindValue(':totalpayment', $d->orderData->price->items ?? null)
                    ->bindValue(':totalclub', $d->orderData->price->items ?? null)
                    ->bindValue(':total', $d->orderData->price->items ?? null)
                    ->bindValue(':currency', $d->orderData->price->currency ?? null)
                    ->bindValue(':products', json_encode($d->products))
                    ->bindValue(':alljson', $json_params ?? null)
                    ->query();
            }

            //Bugfix - icompany sometimes empty...
            Yii::$app->db->createCommand("update eshop set icompany=' ' where orderid=:orderid and icompany is null")
                ->bindValue(':orderid', $d->orderId ?? 0)
                ->query();


            //Clean the clipboards;
            // use nr 50 ...
            // 60

            $stmt = Yii::$app->db->createCommand("DELETE FROM tempmove WHERE user_name='" . $uname . "' and clip_id between 50 and 60")->query();

            $clipnr = 50;
            $shopAdr = [];
            foreach ($documents as $shop => $prod) {
                foreach ($prod as $matid => $pcs) {
                    $shopAdr[$clipnr] = $shop;
                    $stmt = Yii::$app->db->createCommand("SELECT model FROM product where mat_id='" . $matid . "' limit 1")->queryScalar();
                    if (empty($stmt)) {
                        $this->exitErr($matid . " does not exists");
                    } else {
                        $query = "call pridajasuser(:adr_id, :mat_id, :clip_id, :pcs, :xx, :price, :d1, :d2, :d3, :m_type_id, :price2, :stock_id1,:stock_id2,:text1,:text2,:tempmove_info, :thserial, :user)";
                        Yii::$app->db->createCommand($query)
                            ->bindValue(':adr_id', $shops[$shop]["adr_id"])
                            ->bindValue(':mat_id', $matid)
                            ->bindValue(':clip_id', $clipnr)
                            ->bindValue(':pcs', $pcs)
                            ->bindValue(':price', 0)
                            ->bindValue(':xx', 'xx')
                            ->bindValue(':d1', date("Y-m-d"))
                            ->bindValue(':d2', date("Y-m-d"))
                            ->bindValue(':d3', date("Y-m-d"))
                            ->bindValue(':m_type_id', $ordermtypeid)
                            ->bindValue(':price2', $defaultprice)
                            ->bindValue(':stock_id1', null)
                            ->bindValue(':stock_id2', null)
                            ->bindValue(':text1', '')
                            ->bindValue(':text2', '')
                            ->bindValue(':tempmove_info', '')
                            ->bindValue(':thserial', null)
                            ->bindValue(':user', $uname)
                            ->query();
                    }
                }
                $clipnr += 1;
            }


            $posledny = Yii::$app->db->createCommand("SELECT max(move_id) posledny FROM movement")->queryScalar();


            for ($finalclip = 50; $finalclip < $clipnr; $finalclip++) {

                try {

                    Yii::$app->db->createCommand("call newpaper("
                        . "14,'x','"
                        . date("Y-m-d") . "','"
                        . date("Y-m-d") . "','"
                        . date("Y-m-d") . "',"
                        . "0,'"
                        . ($d->order ?? '') . "','"
                        . "created: " . ($d->created ?? 'n/a') . "',"
                        . $shops[$shopAdr[$finalclip]]["adr_id"] . ",'"
                        . $uname . "',"
                        . "'" . $shops[$shopAdr[$finalclip]]["stock_id"] . "',"
                        . "'" . $toshop . "',"
                        . "'x'" . ","
                        . $shops[$shopAdr[$finalclip]]["repli_id"] . ","
                        . $finalclip . ")")->query();
                } catch (\yii\db\Exception $e) {

                    Yii::debug($e->getMessage());

                    $poslednyxx = Yii::$app->db->createCommand("SELECT max(move_id) posledny FROM movement")->queryScalar();
                    if ($poslednyxx == $posledny) { // nic nevytvoril..

                        Yii::$app->db->createCommand("call newpaper("
                            . $ordermtypeid . ",'x','"
                            . date("Y-m-d") . "','"
                            . date("Y-m-d") . "','"
                            . date("Y-m-d") . "',"
                            . "0,'"
                            . ($d->order ?? '') . "',:msg,"
                            . $shops[$shopAdr[$finalclip]]["adr_id"] . ",'"
                            . $uname . "',"
                            . "'" . $shops[$shopAdr[$finalclip]]["stock_id"] . "',"
                            . "'" . $toshop . "',"
                            . "'x'" . ","
                            . $shops[$shopAdr[$finalclip]]["repli_id"] . ","
                            . $finalclip . ")")
                            ->bindValue(':msg', "TRANFER FAILED!!! " . substr($e->getMessage(), 43, 190))
                            ->query();
                    }
                }
            }
        } catch (\yii\db\Exception $e) {
            $this->exitErr($e->getMessage());
        }

        $this->exitOk(["result" => "OK"]);
    } //actionsalesk



    /**
     * Stock S2 and s4 report for Janos and Dani for Android APP
     * @return JSON
     */
    public function actionS2p4()
    {
        $q = "select mat_id,kod, sum(remains) s2, (select p4 from product where mat_id=x.mat_id) p4 from dyninfox x where  shop='s2' group by mat_id,kod order by kod,mat_id";
        return $this->asJson(Yii::$app->db->createCommand($q)->queryAll());
    }

    public function actionS4p4()
    {
        $q = "select mat_id,kod, sum(remains) s4, (select p4 from product where mat_id=x.mat_id) p4 from dyninfox x where  shop='s4' group by mat_id,kod order by kod,mat_id";
        return $this->asJson(Yii::$app->db->createCommand($q)->queryAll());
    }

    /**
     * XML feed for s0 stock and kod WMID
     * 
     * <SHOP>
     *  <SHOPITEM>
     *  <PRODUCT_NAME>CIRCUIT náhrdelník ocel</PRODUCT_NAME>
     *  <CODE>35000266</CODE>
     *  <RETAIL_PRICE>2090,000003</RETAIL_PRICE>
     *  <DESCRIPTION>pánskýocel</DESCRIPTION>
     *  <TRADMARK>CK Movado šperky</TRADMARK>
     *  <STOCK>0</STOCK>
     *  </SHOPITEM>
     *  ...
     *  where PRODUCT_name ... model
     * where CODE ... mat_id
     * where RETAIL_PRICE ... p0e
     * where DESCRIPTION ... pdescr
     * where TRADMARK ... kod->label1    
     * annd stock is s0
     * 
     */
    public function actionFeedxml1()
    {
        $kods = "x.kod = 'WMID'";
        $out = '<?xml version="1.0" encoding="UTF-8"?>';
        $out .= '<SHOP>';
        $rows = Yii::$app->db->createCommand("select x.mat_id code, x.model, p0e retail_price, pdescr description, x.kod, (select label1 from kod where kod=x.kod) tradmark from dyninfox x left outer join product p on p.mat_id=x.mat_id 
            where shop='s0' and  " . $kods . " and p0e>9 group by x.model,code,p0e,pdescr,x.kod order by code")->queryAll();
        foreach ($rows as $row) {
            $row['retail_price'] = floatval($row['retail_price']);
            $row['stock'] = 1;
            $out .= '<SHOPITEM>';
            $out .= '<PRODUCT_NAME>' . htmlspecialchars($row['model'], ENT_XML1, 'UTF-8') . '</PRODUCT_NAME>';
            $out .= '<CODE>' . $row['code'] . '</CODE>';
            $out .= '<RETAIL_PRICE>' . $row['retail_price'] . '</RETAIL_PRICE>';
            $out .= '<DESCRIPTION>' . htmlspecialchars($row['description'], ENT_XML1, 'UTF-8') . '</DESCRIPTION>';
            $out .= '<TRADMARK>' . htmlspecialchars($row['tradmark'], ENT_XML1, 'UTF-8') . '</TRADMARK>';
            $out .= '<STOCK>' . $row['stock'] . '</STOCK>';
            $out .= '</SHOPITEM>';
        }
        $out .= '</SHOP>';
        header('Content-Type: application/xml');
        echo $out;
        exit;
    }

    public function actionFeedxml2()
    {
        $kods = "x.kod ilike '%MON'";
        $out = '<?xml version="1.0" encoding="UTF-8"?>';
        $out .= '<SHOP>';
        $rows = Yii::$app->db->createCommand("select x.mat_id code, x.model, p0e retail_price, pdescr description, x.kod, (select label1 from kod where kod=x.kod) tradmark from dyninfox x left outer join product p on p.mat_id=x.mat_id 
            where shop='s0' and  " . $kods . " and p0e>9 group by x.model,code,p0e,pdescr,x.kod order by code")->queryAll();
        foreach ($rows as $row) {
            $row['retail_price'] = floatval($row['retail_price']);
            $row['stock'] = 1;
            $out .= '<SHOPITEM>';
            $out .= '<PRODUCT_NAME>' . htmlspecialchars($row['model'], ENT_XML1, 'UTF-8') . '</PRODUCT_NAME>';
            $out .= '<CODE>' . $row['code'] . '</CODE>';
            $out .= '<RETAIL_PRICE>' . $row['retail_price'] . '</RETAIL_PRICE>';
            $out .= '<DESCRIPTION>' . htmlspecialchars($row['description'], ENT_XML1, 'UTF-8') . '</DESCRIPTION>';
            $out .= '<TRADMARK>' . htmlspecialchars($row['tradmark'], ENT_XML1, 'UTF-8') . '</TRADMARK>';
            $out .= '<STOCK>' . $row['stock'] . '</STOCK>';
            $out .= '</SHOPITEM>';
        }
        $out .= '</SHOP>';
        header('Content-Type: application/xml');
        echo $out;
        exit;
    }
}
