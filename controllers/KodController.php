<?php

namespace app\controllers;

use Yii;
use app\models\Kod;
use app\models\KodSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\helpers\Json;

/**
 * KodController implements the CRUD actions for Kod model.
 */
class KodController extends Controller
{
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['post'],
                ],
            ],
            'access' => [
                'class' => \yii\filters\AccessControl::class,
                'rules' => [
                    [                    
                        'allow' => true,
                        'actions' => ['index', 'view', 'create', 'update', 'delete', 'add-product'],
                        'matchCallback' => function ($rule, $action) {
                            return EstockTools::isUserStock()|| EstockTools::isUserWman();
                        }
                    ],
                    [
                        'allow' => false
                    ]
                ]
            ]
        ];
    }

    /**
     * Lists all Kod models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new KodSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $dataProvider->pagination->pageSize=50;
        $dataProvider->sort = ['defaultOrder' => ['kod'=>SORT_ASC]];

        if( Yii::$app->request->post('hasEditable')){
            $cId = Yii::$app->request->post('editableKey');
            $c = Kod::findOne($cId);

            $out = Json::encode(['output' => '', 'message' => '']);
            $post = [];
            $posted = current($_POST['Kod']);
            $post['Kod'] = $posted;
            if($c->load($post)){
                $c->save();
            }
            return $this->asJson($out);

        }

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single Kod model.
     * @param string $id
     * @return mixed
     */
    public function actionView($id)
    {
        $model = $this->findModel($id);
        $providerProduct = new \yii\data\ArrayDataProvider([
            'allModels' => $model->products,
        ]);
        return $this->render('view', [
            'model' => $this->findModel($id),
            'providerProduct' => $providerProduct,
        ]);
    }

    /**
     * Creates a new Kod model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new Kod();

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect('index');
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing Kod model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param string $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->kod]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing Kod model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param string $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        try {
            $this->findModel($id)->delete();
        } catch (\yii\db\Exception $e) {
            Yii::$app->session->setFlash('error',$e->getMessage() );
        }     
        return $this->redirect(['index']);
    }

    
    /**
     * Finds the Kod model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param string $id
     * @return Kod the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Kod::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
    
    /**
    * Action to load a tabular form grid
    * for Product
    * <AUTHOR> Candrajaya <<EMAIL>>
    * <AUTHOR> Ndaru <<EMAIL>>
    *
    * @return mixed
    */
    public function actionAddProduct()
    {
        if (Yii::$app->request->isAjax) {
            $row = Yii::$app->request->post('Product');
            if((Yii::$app->request->post('isNewRecord') && Yii::$app->request->post('_action') == 'load' && empty($row)) || Yii::$app->request->post('_action') == 'add')
                $row[] = [];
            return $this->renderAjax('_formProduct', ['row' => $row]);
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
