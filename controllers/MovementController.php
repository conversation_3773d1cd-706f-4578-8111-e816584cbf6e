<?php

namespace app\controllers;

use Yii;
use app\models\Movement;
use app\models\MovementSearch;
use app\models\User;
use app\models\Thserials;
use app\models\OnstockForm;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use app\controllers\EstockTools;
use yii\filters\AccessControl;
use yii\data\ArrayDataProvider;
use yii2tech\csvgrid\CsvGrid;

/**
 * MovementController implements the CRUD actions for Movement model.
 */
class MovementController extends Controller
{
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['post'],
                ],
            ],
            'access' => [
                'class' => \yii\filters\AccessControl::class,
                'rules' => [
                    [
                        'allow' => true,
                        'actions' => ['index', 'view', 'create', 'update', 'updateparent', 'delete', 'pdf', 'onstockremote', 'onstock', 'salesreport', 'showmultiadd', 'recount', 'exportforchrono', 'movementlist', 'movementajaxdetail'],
                        'matchCallback' => function ($rule, $action) {
                            return EstockTools::isUserStock() || EstockTools::isUserW();
                        }
                    ],
                    [
                        'allow' => true,
                        'actions' => [
                            'mymoves',
                            'salesreport',
                            'onstock',
                            'update',
                            'updateparent',
                            'docnumbers',
                            'showmultiadd',
                            'recount'
                        ],
                        'matchCallback' => function ($rule, $action) {
                            return EstockTools::isUserShop();
                        }
                    ],
                    [
                        'allow' => true,
                        'actions' => ['b2b', 'b2bmoves'],
                        'matchCallback' => function ($rule, $action) {
                            return EstockTools::isUserB2b();
                        }
                    ],
                    [
                        'allow' => true,
                        'actions' => ['mymanagermoves'],
                        'matchCallback' => function ($rule, $action) {
                            return EstockTools::isUserStock() || EstockTools::isUserWman();
                        }
                    ],
                    [
                        'allow' => true,
                        'actions' => ['onstockforintrex'],
                        'ips' => ['91.141.*.*', '**************', '**************', '78.*.*.*', '**************', '127.0.0.1', '**************', '77.*.*.*', '80.110.*.*', '84.115.*.*'],
                    ],
                    [
                        'allow' => true,
                        'actions' => ['onstockforintrex'],
                        'matchCallback' => function ($rule, $action) {
                            return (Yii::$app->user->identity->username ?? null) === 'intrex';
                        }
                    ],
                    [
                        'allow' => false
                    ]
                ]
            ]
        ];
    }


    /**
     * @inheritdoc
     */
    public function beforeAction($action)
    {
        if ($action->id == 'onstockforintrex') {
            $this->enableCsrfValidation = false;
        }
        return parent::beforeAction($action);
    }


    /**
     * Lists all Movement models.
     * @return mixed
     */
    public function actionIndex()
    {

        // Novy thserials cez form
        $model = new THserials();

        try {

            if ($model->load(Yii::$app->request->post()) && $model->save()) {
                Yii::$app->session->setFlash('success', "Serial " . $model->thserial . " created");
            }
        } catch (\yii\db\Exception $e) {
            Yii::$app->session->setFlash('error', $e->getMessage());
        }

        // Novy thserials cez editcolumn
        // if( Yii::$app->request->post('hasEditable')){
        //     $cId = Yii::$app->request->post('editableKey');
        //     Yii::debug($_POST);
        //     // $c = THserials::findOne($cId);
        //     // Yii::debug($c);

        //     $out = Json::encode(['output' => '', 'message' => '']);
        //     // $post = [];
        //     // $posted = current($_POST['THserials']);
        //     // $post['THserials'] = $posted;
        //     // if($c->load($post)){
        //     //     $c->save();
        //     // }
        //     return $this->asJson($out);

        // }

        $searchModel = new MovementSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        if (EstockTools::getUserRepliId() == 2) {
            $dataProvider->query->andWhere(['repli_id' => EstockTools::getUserRepliId()]);
        } else {
            $dataProvider->query->andWhere(' repli_id = 0 OR repli_id = 1 ');
        }

        if (isset($_POST['export_type'])) {
            $dataProvider->pagination = false;
        } else {
            $dataProvider->pagination->pageSize = 30;
        }


        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }


    /**
     * Lists our Movements models. Used for SHOP program
     * @return mixed
     */
    public function actionMymoves()
    {
        $filtertitle = "All dates";
        $searchModel = new MovementSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $adresa = -1;
        if (($a = EstockTools::getUserAllFlags()) !== null) {
            $aa = explode("-", $a);
            $dataProvider->query->andWhere(['or', ['adr_id' => $aa[1]], ['emp_id' => Yii::$app->user->identity->username]]);
            //Pridaj final stock pohyby
            $adresa = $aa[1];
            if (!isset($_GET['MovementSearch']['m_type_id']) || $_GET['MovementSearch']['m_type_id'] == '14') {
                $isfirst = true;
                $query = "";
                foreach (EstockTools::getMyStocks($adresa) as $stock) {
                    if ($isfirst == false) {
                        $query .= " union ";
                    }
                    $query = "select move_id from mlog where flags='OnTheWay,$stock'";
                    $query .= " union select m.move_id from movement m join mlog l 
                        on m.move_id=l.move_id 
                        where stock_id2='$stock' and l.move_id not in
                        (select move_id from mlog where move_id=l.move_id and flags='Received')
                        and l.flags='OnTheWay'";
                    $isfirst = false;
                }
                $move_ids = Yii::$app->db->createCommand($query)->queryAll();
                if (!empty($move_ids)) {
                    if (isset($_GET['datefilter']) && $_GET['datefilter'] == 'toconfirm') {
                        // echo $query;
                        // echo 'move_id in ( '.implode(",",array_column($move_ids,'move_id')).' )';
                        $dataProvider->query->where('move_id in ( ' . implode(",", array_column($move_ids, 'move_id')) . ' )');
                    } else {
                        $dataProvider->query->orWhere('move_id in ( ' . implode(",", array_column($move_ids, 'move_id')) . ' )');
                    }
                }
            }
            if (isset($_GET['datefilter'])) {
                switch ($_GET['datefilter']) {
                    case 'thismonth':
                        $dataProvider->query->andWhere('year(d1)=year(now()) and month(d1)=month(now())');
                        $filtertitle = "This month";
                        break;
                    case 'lastmonth':
                        $dataProvider->query->andWhere('year(d1)=year(now()) and month(d1)=month(now())-1');
                        $filtertitle = "Last month";
                        break;
                    case 'thisyear':
                        $dataProvider->query->andWhere('year(d1)=year(now())');
                        $filtertitle = "This year, " . date('Y');
                        break;
                    case 'lastyear':
                        $dataProvider->query->andWhere('year(d1)=year(now())-1');
                        $filtertitle = "Last year, " . date("Y", strtotime("-1 year"));
                        break;

                    default:
                        # code...
                        break;
                }
            }
            $dataProvider->sort = ['defaultOrder' => ['d1' => SORT_DESC, 'm_type_id' => SORT_ASC]];
        }

        // echo $dataProvider->query->createCommand()->sql;

        if (isset($_POST['export_type'])) {
            $dataProvider->pagination = false;
        } else {
            $dataProvider->pagination->pageSize = 10;
        }

        return $this->render('mymoves', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'filtertitle' => $filtertitle,
            'adresa' => $adresa,
        ]);
    }


    /**
     * Lists our Movements models. Used for SHOP program
     * @return mixed
     */
    public function actionB2bmoves()
    {
        $filtertitle = "All dates";
        $searchModel = new MovementSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        if (($a = EstockTools::getUserAllFlags()) !== null) {
            $aa = explode("-", $a);
            $dataProvider->query->andWhere(['adr_id' => EstockTools::getB2bParams()['profile']['adr_id']]);
            if (isset($_GET['datefilter'])) {
                switch ($_GET['datefilter']) {
                    case 'thismonth':
                        $dataProvider->query->andWhere('year(d1)=year(now()) and month(d1)=month(now())');
                        $filtertitle = "This month";
                        break;
                    case 'lastmonth':
                        $dataProvider->query->andWhere('year(d1)=year(now()) and month(d1)=month(now())-1');
                        $filtertitle = "Last month";
                        break;
                    case 'thisyear':
                        $dataProvider->query->andWhere('year(d1)=year(now())');
                        $filtertitle = "This year, " . date('Y');
                        break;
                    case 'lastyear':
                        $dataProvider->query->andWhere('year(d1)=year(now())-1');
                        $filtertitle = "Last year, " . date("Y", strtotime("-1 year"));
                        break;

                    default:
                        # code...
                        break;
                }
            }
            $dataProvider->sort = ['defaultOrder' => ['d1' => SORT_DESC, 'm_type_id' => SORT_ASC]];
        }

        if (isset($_POST['export_type'])) {
            $dataProvider->pagination = false;
        } else {
            $dataProvider->pagination->pageSize = 10;
        }


        return $this->render('b2bmoves', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'filtertitle' => $filtertitle,
        ]);
    }


    /**
     * Displays a next document numbers for shop
     * 
     * @return mixed
     */
    public function actionDocnumbers()
    {


        $query = Yii::$app->db->createCommand("select d.doc_id, d.name, d.number as NasledujeCislo from  document d join m_type  t on t.doc_id=d.doc_id and m_type_id in (150,151,154,155,
            156, 157, 152, 153) order by d.doc_id");
        $r1 = new ArrayDataProvider(['allModels' => $query->queryAll(), 'pagination' => ['pageSize' => 10000,]]);

        return $this->render('docnumbers', ['r1' => $r1]);
    }



    /**
     * Displays a report for shop
     * 
     * @return mixed
     */
    public function actionSalesreport()
    {


        $query = Yii::$app->db->createCommand("select adr_id,m_type_id,(select name from m_type where m_type_id=m.m_type_id) movement, sum(total)::money total, max(d1) from movement m where d1 = date(now()) and m.adr_id=:adr
            and m_type_id in ( 87,88,97,101, 90,91,98,100, 30,31,99,106, 130,132,131,133,145,146 )
            group by m.adr_id, m.m_type_id")->bindValue(':adr', EstockTools::getUserAdrId());
        $r1 = new ArrayDataProvider(['allModels' => $query->queryAll(), 'pagination' => ['pageSize' => 10000,]]);

        $query = Yii::$app->db->createCommand("sELECT kod, model, sum(pcs), md.price::money as selprice, sum(md.price*pcs-md.price * pcs * md.discount/100 )::money as seltotal from m_detail md, movement m, product p where md.move_id = m.move_id and md.m_repli_id =m.repli_id and md.mat_id = p.mat_id and d1 = date(now()) and 
            m.adr_id=:adr
            and m_type_id in ( 87,88, 90,91, 30,31, 130,132,145 )
            group by kod, model, md.price, m.m_type_id, md.tax, p.price order by kod asc, model asc")->bindValue(':adr', EstockTools::getUserAdrId());
        $r2 = new ArrayDataProvider(['allModels' => $query->queryAll(), 'pagination' => ['pageSize' => 10000,]]);

        $query = Yii::$app->db->createCommand("select adr_id,m_type_id,(select name from m_type where m_type_id=m.m_type_id) movement, sum(total)::money total, max(d1) from movement m where year(d1) = year(now()) and month(d1) = month(now()) and m.adr_id=:adr
            and m_type_id in ( 87,88,97,101, 90,91,98,100, 30,31,99,106, 130,132,131,133,145,146 )
            group by m.adr_id, m.m_type_id")->bindValue(':adr', EstockTools::getUserAdrId());
        $r3 = new ArrayDataProvider(['allModels' => $query->queryAll(), 'pagination' => ['pageSize' => 10000,]]);
        $query = Yii::$app->db->createCommand("sELECT kod, model, sum(pcs), md.price::money as selprice, sum(md.price*pcs-md.price * pcs * md.discount/100 )::money as seltotal from m_detail md, movement m, product p where md.move_id = m.move_id and md.m_repli_id =m.repli_id and md.mat_id = p.mat_id and year(d1) = year(now()) and month(d1) = month(now()) and 
            m.adr_id=:adr
            and m_type_id in ( 87,88, 90,91, 30,31, 130,132,145 )
            group by kod, model, md.price, m.m_type_id, md.tax, p.price order by kod asc, model asc")->bindValue(':adr', EstockTools::getUserAdrId());
        $r4 = new ArrayDataProvider(['allModels' => $query->queryAll(), 'pagination' => ['pageSize' => 10000,]]);
        $query = Yii::$app->db->createCommand("select adr_id,m_type_id,(select name from m_type where m_type_id=m.m_type_id) movement, sum(total)::money total, max(d1) from movement m where year(d1) = year(date(now())- integer '365') and month(d1) = month(date(now())- integer '365') and m.adr_id=:adr 
            and m_type_id in ( 87,88,97,101, 90,91,98,100, 30,31,99,106, 130,132,131,133,145,146 )
            group by m.adr_id, m.m_type_id")->bindValue(':adr', EstockTools::getUserAdrId());
        $r5 = new ArrayDataProvider(['allModels' => $query->queryAll(), 'pagination' => ['pageSize' => 10000,]]);
        $query = Yii::$app->db->createCommand("sELECT kod, model, sum(pcs), md.price::money as selprice, sum(md.price*pcs-md.price * pcs * md.discount/100 )::money as seltotal from m_detail md, movement m, product p where md.move_id = m.move_id and md.m_repli_id =m.repli_id and md.mat_id = p.mat_id and year(d1) = year(date(now())- integer '365') and month(d1) = month(date(now())- integer '365') and 
            m.adr_id=:adr 
            and m_type_id in ( 87,88, 90,91, 30,31, 130,132,145 )
            group by kod, model, md.price, m.m_type_id, md.tax, p.price order by kod asc, model asc")->bindValue(':adr', EstockTools::getUserAdrId());
        $r6 = new ArrayDataProvider(['allModels' => $query->queryAll(), 'pagination' => ['pageSize' => 10000,]]);

        return $this->render('salesreport', ['r1' => $r1, 'r2' => $r2, 'r3' => $r3, 'r4' => $r4, 'r5' => $r5, 'r6' => $r6]);
    }


    /**
     * Displays a single MDetailProd model.
     * 
     * @return mixed
     */
    public function actionShowmultiadd($id)
    {
        try {
            $query = "SELECT mat_id, pcs,price,replace(detail_info,',','~') from m_detail where move_id=:id";
            $data =  Yii::$app->db->createCommand($query)
                ->bindValue(':id', $id)->queryAll();
            $out = "Multiadd text from move_id={$id}<hr><pre>";
            foreach ($data as $key => $v) {
                $out .= $v['mat_id'] . ',' . $v['pcs'] . ',' . $v['price'] . ',' . $v['replace'] . "\n";
            }
            Yii::$app->session->setFlash('info', $out . "</pre>");
        } catch (\yii\db\Exception $e) {
            Yii::$app->session->setFlash('error', $e->getMessage());
        }

        return $this->redirect(['index']);
    }


    /**
     * Lists our Movements models. Used for SHOP program
     * @return mixed
     */
    public function actionMymanagermoves()
    {
        $filtertitle = "All dates";
        $searchModel = new MovementSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $dataProvider->query->andWhere(['emp_id' => Yii::$app->user->identity->username]);
        if (isset($_GET['datefilter'])) {
            switch ($_GET['datefilter']) {
                case 'thismonth':
                    $dataProvider->query->andWhere('year(d1)=year(now()) and month(d1)=month(now())');
                    $filtertitle = "This month";
                    break;
                case 'lastmonth':
                    $dataProvider->query->andWhere('year(d1)=year(now()) and month(d1)=month(now())-1');
                    $filtertitle = "Last month";
                    break;
                case 'thisyear':
                    $dataProvider->query->andWhere('year(d1)=year(now())');
                    $filtertitle = "This year, " . date('Y');
                    break;
                case 'lastyear':
                    $dataProvider->query->andWhere('year(d1)=year(now())-1');
                    $filtertitle = "Last year, " . date("Y", strtotime("-1 year"));
                    break;

                default:
                    # code...
                    break;
            }
        }
        $dataProvider->sort = ['defaultOrder' => ['move_id' => SORT_DESC]];

        if (isset($_POST['export_type'])) {
            $dataProvider->pagination = false;
        } else {
            $dataProvider->pagination->pageSize = 10;
        }

        return $this->render('mymoves', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'filtertitle' => $filtertitle,
            'adresa' => -1,
        ]);
    }


    /**
     * Url ajax action - movement-detail
     */
    public function actionMovementajaxdetail()
    {
        if (isset($_POST['expandRowKey'])) {
            $model = \app\models\Movement::findOne($_POST['expandRowKey']);
            return $this->renderPartial('_expand', ['model' => $model]);
        } else {
            return '<div class="alert alert-danger">No data found</div>';
        }
    }


    /**
     * Shows remote onstock
     */
    public function actionOnstockremote()
    {
        return $this->render('onstockremote');
    }

    /**
     * Recount
     * @return mixed
     */
    public function actionRecount()
    {
        Yii::$app->db->createCommand("call dyninfo_recount_all()")->query();

        return $this->redirect(['onstock']);
    }

    /**
     * Shows remote onstock for intrex
     */
    public function actionOnstockforintrex()
    {
        return $this->actionOnstock(0);
    }


    /**
     * Shows onstock
     */
    public function actionOnstock($alsoprices =  1)
    {
        $model = new OnstockForm();
        if (!$model->load(Yii::$app->request->post())) {
            return $this->render('onstock', ['form' => $model]);
        }

        $notRecounted = Yii::$app->db->createCommand("select (select max(move_id) from movement where stock_id1 is not null or stock_id2 is not null)-remains pocet from dyninfox where mat_id = 0")->queryScalar();
        if ($notRecounted) {
            Yii::$app->session->setFlash('warning', "<b>WARNING:</b> " . $notRecounted . " new movement(s) after the last recounting!");
        } else {
            Yii::$app->session->setFlash('info', "Current status: uses actual recounted data");
        }




        $_query = $price1 = $price2 = $price3 = $d1 = "";


        if ($alsoprices == 1) {
            if (!empty($model->price1)) {
                $price1 = " , (select " . $model->price1 . " from product where mat_id=dx.mat_id) as \"" . $model->price1 . "\"";
            }
            if (!empty($model->price2)) {
                $price2 = " , (select " . $model->price2 . " from product where mat_id=dx.mat_id) as \"" . $model->price2 . "\"";
            }
            if (!empty($model->price3)) {
                $price3 = " , (select " . $model->price3 . " from product where mat_id=dx.mat_id) as \"" . $model->price3 . "\"";
            }
            if (!empty($model->d1)) {
                if ($model->d1 == 'all') {
                    $d1 = " , (select min(d1) from movement m join m_detail d on m.move_id=d.move_id where d.mat_id=dx.mat_id and m.stock_id2 is not null) as \"first_on_stock\"";
                } else {
                    $d1 = " , (select min(d1) from movement m join m_detail d on m.move_id=d.move_id where d.mat_id=dx.mat_id and m.stock_id2='" . $model->d1 . "') as \"first_on_" . $model->d1 . "\"";
                }
            }
        }


        if ($model->repli != "-1" && $model->repli != "0" && $model->repli != "2") {
            $model->repli = "-1";
        }
        if (empty($model->mat_id)) {
            $model->mat_id = null;
        }
        if (empty($model->kod)) {
            $model->kod = null;
        }
        if (empty($model->model)) {
            $model->model = null;
        }
        if (empty($model->stock_id)) {
            $model->stock_id = null;
        } else {

            // Special - only show stock

            $hasAdr = Yii::$app->db->createCommand("select adr_id, sdescr from stock_detail where stock_id=:stock_id")->bindValue(':stock_id', $model->stock_id)->queryOne();
            if (!empty($model->kod) || !empty($model->model) || !empty($model->mat_id)) {
                $_query = "select mat_id,kod,model,
            (select model2 from product_with_model2 where mat_id = dx.mat_id) as model2,            
            sum(remains) as \"" . $hasAdr['sdescr'] . "\"  " . $price1 . " " . $price2 . " " . $price3 . " from dyninfox dx where shop=:adr 
            and ( model ilike :model or model2 ilike :model2 or kod ilike :kod or mat_id=:matid )  group by mat_id,kod,model having sum(remains)<>0  order by kod asc,model asc";
                $query = Yii::$app->db->createCommand($_query)->bindValue(':adr', $model->stock_id)
                    ->bindValue(':model', $model->model)
                    ->bindValue(':kod', $model->kod)
                    ->bindValue(':matid', $model->mat_id)
                    ->bindValue(':model2', str_replace('.', '', $model->model))
                    ->queryAll();
            } else {
                $_query = "select mat_id,kod,model,
            (select model2 from product_with_model2 where mat_id = dx.mat_id) as model2,
            sum(remains) as \"" . $hasAdr['sdescr'] . "\"  " . $price1 . " " . $price2 . " " . $price3 . ", getserials(mat_id,:adr) serials, getlastreceiveddate(mat_id,:adr) LastReceived, getlastreceivedfromstock(mat_id,:adr) LastFromStock  from dyninfox dx where shop=:adr
            group by mat_id,kod,model having sum(remains)<>0  order by kod asc,model asc";
                $query = Yii::$app->db->createCommand($_query)->bindValue(':adr', $model->stock_id)
                    ->queryAll();
            }

            $provider = new ArrayDataProvider([
                'allModels' => $query,
                'key' => 'mat_id',
                'pagination' => [
                    'pageSize' => 10000,
                ]
            ]);
            return $this->render('onstock', ['pall' => $provider, 'form' => $model]);
        }

        try {
            Yii::$app->db->createCommand("call dyninfo(:model,:kod,:mat_id,:repli)")
                ->bindValue(':model', $model->model)
                ->bindValue(':kod', $model->kod)
                ->bindValue(':mat_id', $model->mat_id)
                ->bindValue(':repli', $model->repli)
                ->query();

            $cols = Yii::$app->db->createCommand("SELECT column_name, coalesce(( select '\"'||d.column_name||'\" as \"'||firma || ' ' || adr_id||'\" ' from address where adr_id = coalesce((select adr_id from stock_detail where stock_id ilike d.column_name),0)),'\"'||d.column_name||'\"') as allname
        FROM information_schema.columns d
        WHERE table_schema = 'public'
        AND table_name   = 'dyninfo'")->queryAll();
            $mycols = "(select model2 from product_with_model2 where mat_id = dx.mat_id) as model2,";
            $comma = "";
            $isOtw = false;
            foreach ($cols as $key => $value) {
                if ($value['column_name'] == 'OTW') {
                    $isOtw = true;
                } else {
                    $mycols .= $comma . $value['allname'];
                    $comma = ",";
                }
            }
            //OTW na konci chcu...
            if ($isOtw == true) {
                $mycols .= $comma . '"OTW"';
            }
            $_query = "select " . $mycols;
            $_query .=  $price1 . " " . $price2 . " " . $price3 . " " . $d1 . " from dyninfo dx where mat_id <> 0 order by kod asc,model asc";
            // var_dump($_query);
            // exit;


            $query = Yii::$app->db->createCommand($_query)->queryAll();

            $provider = new ArrayDataProvider([
                'allModels' => $query,
                'key' => 'mat_id',
                'pagination' => [
                    'pageSize' => 10000,
                ]
            ]);
        } catch (\yii\db\Exception $e) {
            Yii::$app->session->setFlash('error', $e->getMessage());
        }

        return $this->render('onstock', ['pall' => ($provider ?? null), 'form' => $model]);
    }



    /**
     * Displays a single Movement model.
     * @param integer $move_id
     * @return mixed
     */
    public function actionView($move_id)
    {
        $model = $this->findModel($move_id);
        return $this->render('view', [
            'model' => $this->findModel($move_id),
        ]);
    }

    /**
     * Creates a new Movement model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new Movement();

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'move_id' => $model->move_id]);
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing Movement model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $move_id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'move_id' => $model->move_id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing Movement model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $move_id
     * @return mixed
     */
    public function actionUpdateparent($id)
    {

        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            Yii::$app->session->setFlash('success', "Parent move_id " . $model->parent_move_id . " set");
            return $this->render('view', ['model' => $model]);
        }
        return $this->render('_adv_payment', [
            'model2' => $model
        ]);
    }


    /**
     * Deletes an existing Movement model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $move_id
     * @return mixed
     */
    public function actionDelete($id)
    {

        try {
            $query = "call stornopaper(:move_id, :typeofstorno)";
            $cmd =  Yii::$app->db->createCommand($query)
                ->bindValue(':move_id', $id)
                ->bindValue(':typeofstorno', '1');

            $cmd->query();
        } catch (\yii\db\Exception $e) {
            Yii::$app->session->setFlash('error', $e->getMessage());
        }

        return $this->redirect(['index']);
    }

    /**
     * 
     * Export Movement information into PDF format.
     * @param integer $move_id
     * @return mixed
     */
    public function actionPdf($move_id)
    {
        $model = $this->findModel($move_id);

        $content = $this->renderAjax('_pdf', [
            'model' => $model,
        ]);

        $pdf = new \kartik\mpdf\Pdf([
            // 'mode' => \kartik\mpdf\Pdf::MODE_CORE,
            'format' => \kartik\mpdf\Pdf::FORMAT_A4,
            'orientation' => \kartik\mpdf\Pdf::ORIENT_PORTRAIT,
            // 'destination' => \kartik\mpdf\Pdf::DEST_BROWSER,
            'content' => $content,
            'cssFile' => '@vendor/kartik-v/yii2-mpdf/assets/kv-mpdf-bootstrap.min.css',
            'cssInline' => '.kv-heading-1{font-size:18px}',
            // 'options' => ['title' => \Yii::$app->name],
            // 'methods' => [
            //     'SetHeader' => [\Yii::$app->name],
            //     'SetFooter' => ['{PAGENO}'],
            // ]
        ]);

        return $pdf->render();
    }


    public function actionExportforchrono($id)
    {

        $_query = 'select a.firma as "NEV",a.zip as "IRSZ",a.city as "VAROS",a.street as "UTCA",
        m.d1 as "SZALADATUM", m.d2 as "TELJDATUM", m.d3 as "FIZDATUM",d.currency "DEVNEM",
        k.name2 as "vtsz",p.model||\' \'||d.detail_info "MEGNEVEZES",
        case when d.currency<>\'HUF\' then floor(100*d.price*(1-d.discount/100))/100 else floor(d.price*(1-d.discount/100)) end "EGYSEGAR", d.pcs "MENNYISEG"  from m_detail d join product p on p.mat_id=d.mat_id join kod k on k.kod=p.kod join movement m on m.move_id=d.move_id join address a on a.adr_id=m.adr_id
        where m.move_id=:move_id';

        try {
            $cmd =  Yii::$app->db->createCommand($_query)
                ->bindValue(':move_id', $id);
            $r1 = new ArrayDataProvider(['allModels' => $cmd->queryAll(), 'pagination' => ['pageSize' => 10000,]]);
        } catch (\yii\db\Exception $e) {
            Yii::$app->session->setFlash('error', $e->getMessage());
        }

        $exporter = new CsvGrid([
            'dataProvider' => $r1,
            'csvFileConfig' => [
                'cellDelimiter' => ";",
                //                'rowDelimiter' => "\n",
                'enclosure' => "\"",
            ],
        ]);
        $result = $exporter->export()->send("moveid" . $id . ".csv");
    }



    /** 
     * Your controller action to fetch the list
     */
    public function actionMovementlist($q = null)
    {


        $query = "SELECT m.adr_id,move_id, d1, m.stock_id1, m.stock_id2, total, firma FROM movement m 
         join address a on a.adr_id=m.adr_id where repli_id=:repli and 
            firma ILIKE :param or move_id=:nr::integer 
            order by move_id desc limit 50";

        $result = Movement::findBySql($query, [':repli' => EstockTools::getUserRepliId(), ':param' => '%' . $q . '%', ':nr' => is_numeric($q) ? $q : 0])->all();

        $out = [];
        foreach ($result as $d) {

            $out[] = [
                'mtitle' => $d['move_id'] . ' ' . $d['adr']->firma . ' d1: ' . $d['d1'] . ' From: ' . $d['stock_id1'] . ' To: ' . $d['stock_id2'] . ' total:' . $d['total'],
                'move_id' => $d['move_id']
            ];
        }
        return $this->asJson($out);
    }


    /**
     * Finds the Movement model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $move_id
     * @return Movement the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($move_id)
    {
        if (($model = Movement::findOne(['move_id' => $move_id])) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
