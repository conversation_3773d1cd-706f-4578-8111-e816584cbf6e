<?php

namespace app\controllers;

use Yii;
use app\models\AdrContact;
use app\models\AdrcontactSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * AdrcontactController implements the CRUD actions for AdrContact model.
 */
class AdrcontactController extends Controller
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all AdrContact models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new AdrcontactSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single AdrContact model.
     * @param integer $adr_id
     * @param string $number
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($adr_id, $number)
    {
        return $this->render('view', [
            'model' => $this->findModel($adr_id, $number),
        ]);
    }

    /**
     * Creates a new AdrContact model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new AdrContact();

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'adr_id' => $model->adr_id, 'number' => $model->number]);
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing AdrContact model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $adr_id
     * @param string $number
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($adr_id, $number)
    {
        $model = $this->findModel($adr_id, $number);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'adr_id' => $model->adr_id, 'number' => $model->number]);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing AdrContact model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $adr_id
     * @param string $number
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($adr_id, $number)
    {
        $this->findModel($adr_id, $number)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the AdrContact model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $adr_id
     * @param string $number
     * @return AdrContact the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($adr_id, $number)
    {
        if (($model = AdrContact::findOne(['adr_id' => $adr_id, 'number' => $number])) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
