<?php
namespace app\controllers;
use Yii;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\base\DynamicModel;
use yii\helpers\Url;
use yii\data\ArrayDataProvider;
use weesee\pdflabel\PdfLabel;
use Mpdf\QrCode\QrCode;
use Mpdf\QrCode\Output;
use yii\web\Response;
use rikudou\SkQrPayment\QrPayment;
use rikudou\SkQrPayment\Payment\QrPaymentOptions;
use Rikudou\Iban\Iban\IBAN;
// use clsOpenTBS;
// use clsTinyButStrong;
use DateTime;

class PrintController extends Controller
{
    private $TBS; //doc container
    private $dbdata; //pole vysledkov selectu
    private $dbsumdata; //pole sum vysledkov selectu
    private $dbprepaid; //pomocne data z ineho selectu
    
    public function behaviors()
    {
      return [
        'verbs' => [
          'class' => VerbFilter::className(),
          'actions' => [
            'delete' => ['post'],
          ],
        ],
        'access' => [
          'class' => \yii\filters\AccessControl::className(),
          'rules' => [
            [
              'allow' => true,
              'actions' => ['getodt', 'getanydoc', 'getpdflabels','qrcode','paybysquare','qrpaysk'],
              'roles' => ['@']
            ],
            [
              'allow' => false
            ]
          ]
        ]
      ];
    }

	/*
	IMPORT WITH PHPEXCEL
	*/ 	
    // public function actionImport()
    // {
    //     $field = [
    //         'fileImport' => 'File Import',
    //     ];

    //     $modelImport = DynamicModel::validateData($field, [
    //         [['fileImport'], 'required'],
    //         [['fileImport'], 'file', 'extensions'=>'xls,xlsx','maxSize'=>1024*1024],
    //     ]);
    //     if (Yii::$app->request->post()) {
    //         $modelImport->fileImport = \yii\web\UploadedFile::getInstance($modelImport, 'fileImport');
    //         if ($modelImport->fileImport && $modelImport->validate()) {                                
    //             $inputFileType = \PHPExcel_IOFactory::identify($modelImport->fileImport->tempName );
    //             $objReader = \PHPExcel_IOFactory::createReader($inputFileType);
    //             $objPHPExcel = $objReader->load($modelImport->fileImport->tempName);
    //             $sheetData = $objPHPExcel->getActiveSheet()->to[null,true,true,true);
    //             $baseRow = 2;
    //             while(!empty($sheetData[$baseRow]['A'])){
    //                 $model = new Mahasiswa();
    //                 $model->nama = (string)$sheetData[$baseRow]['B'];
    //                 $model->nim = (string)$sheetData[$baseRow]['C'];
    //                 $model->save(); 
    //                 //die(print_r($model->errors));
    //                 $baseRow++;
    //             }
    //             Yii::$app->getSession()->setFlash('success', 'Success');
    //         }
    //         else{
    //             Yii::$app->getSession()->setFlash('error', 'Error');
    //         }
    //     }

    //     return $this->redirect(['index']);
    // }

    /*
	EXPORT WITH PHPEXCEL
	*/ 
	// public function actionExportExcel()
 //    {
 //        $searchModel = new MahasiswaSearch();
 //        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

 //        $objReader = \PHPExcel_IOFactory::createReader('Excel2007');
 //        $template = Yii::getAlias('@hscstudio/export').'/templates/phpexcel/export.xlsx';
 //        $objPHPExcel = $objReader->load($template);
 //        $objPHPExcel->getActiveSheet()->getPageSetup()->setOrientation(\PHPExcel_Worksheet_PageSetup::ORIENTATION_LANDSCAPE);
 //        $objPHPExcel->getActiveSheet()->getPageSetup()->setPaperSize(\PHPExcel_Worksheet_PageSetup::PAPERSIZE_FOLIO);
 //        $baseRow=2; // line 2
 //        foreach($dataProvider->getModels() as $mahasiswa){
 //            $objPHPExcel->getActiveSheet()->setCellValue('A'.$baseRow, $baseRow-1);
 //            $objPHPExcel->getActiveSheet()->setCellValue('B'.$baseRow, $mahasiswa->nama);
 //            $objPHPExcel->getActiveSheet()->setCellValue('C'.$baseRow, $mahasiswa->nim);
 //            $baseRow++;
 //        }
 //        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
 //        header('Content-Disposition: attachment;filename="export.xlsx"');
 //        header('Cache-Control: max-age=0');
 //        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, "Excel2007");
 //        $objWriter->save('php://output');
 //        exit;
 //    }   

	/*
	EXPORT WITH OPENTBS
	*/
  //   public function actionExportExcel2()
  //   {
  //       $searchModel = new MahasiswaSearch();
  //       $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

  //       // Initalize the TBS instance
  //       $OpenTBS = new \hscstudio\export\OpenTBS; // new instance of TBS
  //       // Change with Your template kaka
		// $template = Yii::getAlias('@hscstudio/export').'/templates/opentbs/ms-excel.xlsx';
  //       $OpenTBS->LoadTemplate($template); // Also merge some [onload] automatic fields (depends of the type of document).
  //       //$OpenTBS->VarRef['modelName']= "Mahasiswa";				
  //       $data = [];
  //       $no=1;
  //       foreach($dataProvider->getModels() as $mahasiswa){
  //           $data[] = [
  //               'no'=>$no++,
  //               'nama'=>$mahasiswa->nama,
  //               'nim'=>$mahasiswa->nim,
  //           ];
  //       }

  //       $data2[0] = [
  //               'no'=>'X',
  //               'nama'=>'Y',
  //               'nim'=>'Z',
  //           ];
  //       $data2[1] = [
  //               'no'=>'X',
  //               'nama'=>'Y',
  //               'nim'=>'Z',
  //           ];
  //       $OpenTBS->MergeBlock('data', $data);
  //       $OpenTBS->MergeBlock('data2', $data2);
  //       // Output the result as a file on the server. You can change output file
  //       $OpenTBS->Show(OPENTBS_DOWNLOAD, 'export.xlsx'); // Also merges all [onshow] automatic fields.			
  //       exit;
  //   } 

	// /*
	// EXPORT WITH OPENTBS
	// */
 //    public function actionExportWord()
 //    {
 //        $searchModel = new MahasiswaSearch();
 //        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

 //        // Initalize the TBS instance
 //        $OpenTBS = new \hscstudio\export\OpenTBS; // new instance of TBS
 //        // Change with Your template kaka
	// 	$template = Yii::getAlias('@hscstudio/export').'/templates/opentbs/ms-word.docx';
 //        $OpenTBS->LoadTemplate($template); // Also merge some [onload] automatic fields (depends of the type of document).
 //        //$OpenTBS->VarRef['modelName']= "Mahasiswa";				
 //        $data = [];
 //        $no=1;
 //        foreach($dataProvider->getModels() as $mahasiswa){
 //            $data[] = [
 //                'no'=>$no++,
 //                'nama'=>$mahasiswa->nama,
 //                'nim'=>$mahasiswa->nim,
 //            ];
 //        }
 //        $OpenTBS->MergeBlock('data', $data);
 //        // Output the result as a file on the server. You can change output file
 //        $OpenTBS->Show(OPENTBS_DOWNLOAD, 'export.docx'); // Also merges all [onshow] automatic fields.			
 //        exit;
 //    } 

	// /*
	// EXPORT WITH MPDF
	// */
 //    public function actionExportPdf()
 //    {
 //        $searchModel = new MahasiswaSearch();
 //        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
 //        $html = $this->renderPartial('_pdf',['dataProvider'=>$dataProvider]);
 //        $mpdf=new \mPDF('c','A4','','' , 0 , 0 , 0 , 0 , 0 , 0);  
 //        $mpdf->SetDisplayMode('fullpage');
 //        $mpdf->list_indent_first_level = 0;  // 1 or 0 - whether to indent the first level of a list
 //        $mpdf->WriteHTML($html);
 //        $mpdf->Output();
 //        exit;
 //    }


  private function createDoc($move_id, $template, $heading, $firma = [] ) {

    $q = "

    select 'A' as rank,
    d.pcs,
    cast(d.price*(1-d.discount/100) as numeric(11,2)) as pdisc,
    cast(d.price*(1-(d.discount-d.fifo_price)/100) as numeric(11,2)) as pnettodisc,
    cast(d.pcs*d.price*(1-d.discount/100) as numeric(11,2)) as pcsxprice,
    cast(d.pcs*d.price*(1-(d.discount-d.fifo_price)/100) as numeric(11,2)) as nettopcsxprice,
    cast(d.price*(1-d.discount/100)*(100+d.tax)/100 as numeric(11,2)) as pwtax,
    cast(d.price*(1-d.discount/100)*d.tax/100 as numeric(11,2)) as ptax,
    cast(d.pcs*d.price*(1-d.discount/100)*(100+d.tax)/100 as numeric(11,2)) as ppwtax,
    cast(d.pcs*d.price*(1-d.discount/100)*d.tax/100 as numeric(11,2)) as pptax,
    
    cast(d.price*(1-(d.discount-d.fifo_price)/100)*(100+d.tax)/100 as numeric(11,2)) as dpwtax,
    cast(d.price*(1-(d.discount-d.fifo_price)/100)*d.tax/100 as numeric(11,2)) as dptax,
    cast(d.pcs*d.price*(1-(d.discount-d.fifo_price)/100)*(100+d.tax)/100 as numeric(11,2)) as dppwtax,
    cast(d.pcs*d.price*(1-(d.discount-d.fifo_price)/100)*d.tax/100 as numeric(11,2)) as dpptax,

    cast(total0+total1+total2 as numeric(11,2)) as total012,
    cast(tax1+tax2 as numeric(11,2)) as tax12,
    cast(total0+total1+total2+tax1+tax2 as numeric(11,2)) as totalwithtax012,
    cast(total0+total1+total2+tax1+tax2+rounding as numeric(11,2)) as totalpay,
    cast(d.pcs*p.p4 as numeric(11)) as pcsxp4,
    cast(d.pcs*d.price as numeric(11)) as pcsxdprice,
    (select sum( cast(d.pcs*d.price as numeric(11) ) ) from m_detail d where move_id=:move_id) as pcsxdpricesum,
    cast(d.price as numeric(11) ) as dprice,
    d.price,
    cast(d.discount as numeric(11,2)) as discount,
    p.kod,
    p.model,
    d.tax,
    a.firma,
    a.owner_name,
    a.street,
    a.city,
    a.zip,
    a.ico,
    replace(a.drc1,'SK','SK')||' '||a.drc2 as dic,
    replace(a.drc1,'SK','SK') drc1,
    a.drc2,
    to_char(m.d1,'DD.MM.YYYY') as dat_1,
    to_char(m.d2,'DD.MM.YYYY') as dat_2,
    to_char(m.d3,'DD.MM.YYYY') as dat_3,
    m.number,
    p.mat_id,
    '/var/www/estock3/pic/'||left(cast(p.mat_id as varchar(222)),2)||'/'||p.mat_id||'.jpg' pictpath,
    p.p1,
    cast(p.p4 as numeric(11)) as p4,
    p.p6,
    m.total0,
    m.total1,
    m.total2,
    m.rounding,
    m.tax1,
    m.tax2,
    m.text1,
    m.text2,
    m.m_type_id,
    (select tax2 from office where user_name=(select emp_id from movement where move_id=:move_id)) as officetax2,
    cc.firma_id as cccfirma,
    0 as cccrepli,
    cc.firma as ccfirma,
    cc.street as ccstreet,
    cc.city as cccity,
    cc.zip as cczip,
    cc.ico as ccico,
    replace(cc.drc1,'SK','SK')||' '||cc.drc2 as ccdic,
    replace(cc.drc1,'SK','SK') as ccdrc1,
    cc.drc2 as ccdrc2,
    cc.owner_name as ccowner_name,

    m.adr_id,
    0 as adr_repli_id,
    m.stock_id1,
    m.stock_id2,
    (select printable_name || ' [' || iso3 || ']' from country where iso = a.iso) as country,
    coalesce(d.currency,'') as xcurrency,
    p.pdescr,
    p.ean13,
    d.detail_info,
    (select name0 from kod where kod = p.kod) as name0,
    (select name1 from kod where kod = p.kod) as name1,
    (select name2 from kod where kod = p.kod) as name2,
    (select name3 from kod where kod = p.kod) as name3,
    (select sum(dd.pcs) from m_detail dd join product pp on pp.mat_id=dd.mat_id where move_id=m.move_id and pp.unit <> 'n') as sumpcs,
    (select cast(sum(pcs*price-pcs*price*(1-discount/100)) as numeric(11,2)) from m_detail where move_id=m.move_id) as sumdiscount,
    cast(d.discount-d.fifo_price as numeric(11,2)) as nettodiscount,
    (select cast(sum(pcs*price-pcs*price*(1-fifo_price/100))as numeric(11,2)) from m_detail where move_id=m.move_id) as sumnettodiscount,

    m.c_number,
    e.*,
    (select string_agg(thserial, ',') from thserials where move_id=m.move_id and mat_id=d.mat_id) ths,
    (select taxable from m_type where m_type_id=m.m_type_id) taxable,
    m.parent_move_id,
    i.iname as iiname,i.isurname as iisurname,i.istreet as iistreet,i.icity as iicity,i.izip as iizip,i.iphone as iiphone,i.icountry as iicountry,
    i.icompany as iicompany,i.iico as iiico,i.idic as iidic,i.iicdph as iiicdph,
    i.dname as idname,i.dsurname as idsurname,i.dstreet as idstreet,i.dcity as idcity,i.dzip as idzip,i.dphone as idphone,i.dcountry as idcountry,
    i.dcompany as idcompany, i.mnote as imnote, i.shipment as ishipment, i.payment as ipayment

    from m_detail as d join product as p on d.mat_id=p.mat_id
    left outer join movement as m on d.move_id=m.move_id
    left outer join address as a on (a.adr_id = m.adr_id) 
    left outer join eshop as e on m.c_number=e.id
    left outer join address as cc on (a.firma_id = cc.adr_id )
    left outer join minfo as i on d.move_id=i.move_id
    where d.move_id = :move_id
    order by p.model asc";

    $q2="
    select 
    cast(sum(pcs*price*(1-discount/100)) as numeric(11,2)) as pcsxprice,
    tax,
    cast(sum(pcs*price*(1-discount/100)*tax/100) as numeric(11,2)) as xtax,
    cast(sum(pcs*price*(1-discount/100)*(100+tax)/100) as numeric(11,2)) as withtax
    from m_detail d where move_id= :move_id group by tax";

    $this->dbdata = Yii::$app->db->createCommand($q)->bindValue(':move_id',$move_id)->queryAll();
    $this->dbsumdata = Yii::$app->db->createCommand($q2)->bindValue(':move_id',$move_id)->queryAll();

    // Initalize the TBS instance
    $this->TBS = new \hscstudio\export\OpenTBS; // new instance of TBS
    // include_once('../vendor/tinybutstrong/tinybutstrong/tbs_class.php');
    // include_once('../vendor/tinybutstrong/opentbs/tbs_plugin_opentbs.php');

    // $this->TBS = new clsTinyButStrong; // new instance of TBS
    // $this->TBS->Plugin(TBS_INSTALL, OPENTBS_PLUGIN);

        // Change with Your template kaka
  //    $OpenTBS->Plugin(TBS_INSTALL, OPENTBS_PLUGIN);
        $this->TBS->VarRef = $this->dbdata[0];


        // Prepaid variables
        if( $this->dbdata[0]['parent_move_id'] !== null ) {
         $this->dbprepaid = Yii::$app->db->createCommand("
          select cast(total0+total1+total2 as numeric(11,2)) as prepaidtotal, d3 as prepaiddate,
          d.tax as prepaidtax,
          cast(tax1+tax2 as numeric(11,2)) as prepaidtotaltax
          from movement m join m_detail d on m.move_id=d.move_id where m.move_id=:parent_move_id limit 1

          ")->bindValue(':parent_move_id',$this->dbdata[0]['parent_move_id'])->queryAll();

         $this->TBS->VarRef['xprepaiddate'] = " Prijatá záloha dňa: ".$this->dbprepaid[0]['prepaiddate'];
         $this->TBS->VarRef['xprepaidtotal'] = $this->dbprepaid[0]['prepaidtotal'];
         $this->TBS->VarRef['xprepaidtax'] = $this->dbprepaid[0]['prepaidtax'];
         $this->TBS->VarRef['xprepaidtotaltax'] = $this->dbprepaid[0]['prepaidtotaltax'];

         $this->TBS->VarRef['ptotal012'] = number_format((float)($this->TBS->VarRef['total012'] - $this->TBS->VarRef['xprepaidtotal']), 2, '.', '');
         $this->TBS->VarRef['ptax2'] = number_format((float)($this->TBS->VarRef['tax2'] - $this->TBS->VarRef['xprepaidtotaltax']), 2, '.', '');
         $this->TBS->VarRef['ptotalpay'] = number_format((float)($this->TBS->VarRef['totalpay'] - $this->TBS->VarRef['xprepaidtotal'] - $this->TBS->VarRef['xprepaidtotaltax']), 2, '.', '');
         $this->TBS->VarRef['ptotalwithtax012'] = number_format((float)($this->TBS->VarRef['totalwithtax012'] - $this->TBS->VarRef['xprepaidtotal'] - $this->TBS->VarRef['xprepaidtotaltax']), 2, '.', '');

       }

// echo "<pre>";
// var_dump($this->dbdata);
// exit;
       $texts = isset($_GET['tpl']) && ($_GET['tpl'] == 'faEN' || $_GET['tpl'] == 'faENqr') ?
          ['ddoklad'=>" (tax document)",'pocetks'=>'Pcs total: ','fromstock'=>'From stock: ','tostock'=>'To stock: ',
          'ico'=>'TAX no.: ','icdph'=> 'VAT no.: ', 'doico' => isset($firma['icoEN'])?$firma['icoEN']:(isset($firma['ico'])?$firma['ico']:''), 'dodic' => isset($firma['dicEN'])?$firma['dicEN']:(isset($firma['dic'])?$firma['dic']:''), 'doreg' => isset($firma['ortextEN'])?$firma['ortextEN']:(isset($firma['ortext'])?$firma['ortext']:'') ] :
          ['ddoklad'=>" (daňový doklad)",'pocetks'=>'Počet ks spolu: ','fromstock'=>'Zo skladu: ','tostock'=>'Na sklad: ',
          'ico'=>'IČO: ','icdph'=> 'IČ DPH: ', 'doico' => isset($firma['ico'])?$firma['ico']:'', 'dodic' => isset($firma['dic'])?$firma['dic']:'', 'doreg' => isset($firma['ortext'])?$firma['ortext']:'' ];

        //
        // Hack: in Yii2 can not get column currency... still null only...
        // short name because of soft page breaks...
       switch ($this->dbdata[0]['xcurrency']) {
        case 'EUR':
        $this->TBS->VarRef['c'] = '€';
        break;
        case 'HUF':
        $this->TBS->VarRef['c'] = 'Ft';
        break;
        case 'CZK':
        $this->TBS->VarRef['c'] = 'Kč';
        break;
        case 'USD':
        $this->TBS->VarRef['c'] = '$';
        break;
        default:
        $this->TBS->VarRef['c'] = $this->dbdata[0]['xcurrency'];
        break;
      }

      $this->TBS->VarRef['xtaxable'] = $this->dbdata[0]['taxable'] == 'Y' && Yii::$app->user->identity->repli == 0 ? $texts['ddoklad']:'';


      $this->TBS->VarRef['xsumpcs'] = isset($this->dbdata[0]['sumpcs'])  && Yii::$app->user->identity->repli == 0 ? $texts['pocetks'] . $this->dbdata[0]['sumpcs']:'';

      $this->TBS->VarRef['xsumpcs2'] = isset($this->dbdata[0]['sumpcs']) ? $this->dbdata[0]['sumpcs'] : '';

        // minfo staff
      $this->TBS->VarRef['xmnote'] = isset($this->dbdata[0]['imnote']) ? $this->dbdata[0]['imnote'] : '';
      $this->TBS->VarRef['iiname'] = isset($this->dbdata[0]['iiname']) ? $this->dbdata[0]['iiname'] : '';
      $this->TBS->VarRef['iisurname'] = isset($this->dbdata[0]['iisurname']) ? $this->dbdata[0]['iisurname'] : '';
      $this->TBS->VarRef['iistreet'] = isset($this->dbdata[0]['iistreet']) ? $this->dbdata[0]['iistreet'] : '';
      $this->TBS->VarRef['iicity'] = isset($this->dbdata[0]['iicity']) ? $this->dbdata[0]['iicity'] : '';
      $this->TBS->VarRef['iizip'] = isset($this->dbdata[0]['iizip']) ? $this->dbdata[0]['iizip'] : '';
      $this->TBS->VarRef['iiphone'] = isset($this->dbdata[0]['iiphone']) ? $this->dbdata[0]['iiphone'] : '';
      $this->TBS->VarRef['iicountry'] = isset($this->dbdata[0]['iicountry']) ? $this->dbdata[0]['iicountry'] : '';
      $this->TBS->VarRef['iicompany'] = isset($this->dbdata[0]['iicompany']) ? $this->dbdata[0]['iicompany'] : '';
      $this->TBS->VarRef['iiico'] = isset($this->dbdata[0]['iiico']) ? $this->dbdata[0]['iiico'] : '';
      $this->TBS->VarRef['iidic'] = isset($this->dbdata[0]['iidic']) ? $this->dbdata[0]['iidic'] : '';
      $this->TBS->VarRef['iiicdph'] = isset($this->dbdata[0]['iiicdph']) ? $this->dbdata[0]['iiicdph'] : '';
      $this->TBS->VarRef['idname'] = isset($this->dbdata[0]['idname']) ? $this->dbdata[0]['idname'] : '';
      $this->TBS->VarRef['idsurname'] = isset($this->dbdata[0]['idsurname']) ? $this->dbdata[0]['idsurname'] : '';
      $this->TBS->VarRef['idstreet'] = isset($this->dbdata[0]['idstreet']) ? $this->dbdata[0]['idstreet'] : '';
      $this->TBS->VarRef['idcity'] = isset($this->dbdata[0]['idcity']) ? $this->dbdata[0]['idcity'] : '';
      $this->TBS->VarRef['idzip'] = isset($this->dbdata[0]['idzip']) ? $this->dbdata[0]['idzip'] : '';
      $this->TBS->VarRef['idphone'] = isset($this->dbdata[0]['idphone']) ? $this->dbdata[0]['idphone'] : '';
      $this->TBS->VarRef['idcountry'] = isset($this->dbdata[0]['idcountry']) ? $this->dbdata[0]['idcountry'] : '';
      $this->TBS->VarRef['idcompany'] = isset($this->dbdata[0]['idcompany']) ? $this->dbdata[0]['idcompany'] : '';

      $this->TBS->VarRef['xtotal012'] = isset($this->dbdata[0]['total012'])?$this->dbdata[0]['total012']:'';
      $this->TBS->VarRef['xtax12'] = isset($this->dbdata[0]['tax12'])?$this->dbdata[0]['tax12']:'';
      $this->TBS->VarRef['xtotalwithtax012'] = isset($this->dbdata[0]['totalwithtax012'])?$this->dbdata[0]['totalwithtax012']:'';


      $this->TBS->VarRef['xfromstock'] = isset($this->dbdata[0]['stock_id1']) ? $texts['fromstock'] . $this->dbdata[0]['stock_id1'] : '';
      $this->TBS->VarRef['xtostock'] = isset($this->dbdata[0]['stock_id2']) ? $texts['tostock'] . $this->dbdata[0]['stock_id2'] : '';

      $this->TBS->VarRef['xfirma'] = isset($firma['firma'])?$firma['firma']:'';
      $this->TBS->VarRef['xstreet'] = isset($firma['street'])?$firma['street']:'';
      $this->TBS->VarRef['xcity'] = isset($firma['city'])?$firma['city']:'';
      $this->TBS->VarRef['xzip'] = isset($firma['zip'])?$firma['zip']:'';
      $this->TBS->VarRef['xico'] = $texts['doico'];
      $this->TBS->VarRef['xdic'] = $texts['dodic'];
      $this->TBS->VarRef['xicdph'] = isset($firma['icdph'])?$firma['icdph']:'';

      $this->TBS->VarRef['xiico'] = isset($this->dbdata[0]['iico']) ? $texts['ico'] . $this->dbdata[0]['iico'] : '';
      $this->TBS->VarRef['xidic'] = isset($this->dbdata[0]['idic']) ? $texts['icdph'] . $this->dbdata[0]['idic'] : '';


      $this->TBS->VarRef['xiban'] = isset($firma['iban'])?$firma['iban']:'';
      $this->TBS->VarRef['xswift'] = isset($firma['swift'])?$firma['swift']:'';
      $this->TBS->VarRef['xbanka'] = isset($firma['banka'])?$firma['banka']:'';
      $this->TBS->VarRef['xortext'] = $texts['doreg'];
        //Old name also
      $this->TBS->VarRef['currency'] = $this->dbdata[0]['xcurrency'];
      $title = empty($heading)?' ':preg_replace('/\/\/.*/', '', $heading);
      if ( strpos($title, 'Fakt') !== false ) {
        if ( isset($_GET['tpl']) && ( $_GET['tpl'] == 'qr' || $_GET['tpl'] == 'faEN' ) ) {        
          $title = str_replace('Faktura', 'Invoice', $title);
          $title = str_replace('Faktúra', 'Invoice', $title);
        }
      }
      $this->TBS->VarRef['title'] = $title;

      //Nove: pridavame move_id
      $this->TBS->VarRef['xmoveid'] = $move_id;

      if ( isset($_GET['tpl']) && ( $_GET['tpl'] == 'faENqr' || $_GET['tpl'] == 'faqr' ) ) {
        //Vytvor qr kod subor
        $this->actionQrpaysk( $this->TBS->VarRef['xtotalwithtax012'],
            "PAY by square id ".$move_id." ".$this->TBS->VarRef['iicompany'],
            "0008",
            "SK",
            $this->TBS->VarRef['currency'],
            $this->TBS->VarRef['dat_3'],
            $move_id,
            "", null,
            $this->TBS->VarRef['number'],
            str_replace(' ','',preg_replace('/.*IBAN:/','', explode("\n",$this->TBS->VarRef['xiban'])[0]) ) );

        //prirad ho
        $this->TBS->VarRef['qrpict'] = Yii::getAlias('@webroot').'/templates/out/qr'.$move_id.'.png';
      }

        $this->TBS->LoadTemplate($template, OPENTBS_ALREADY_UTF8); // Also merge some [onload] automatic fields (depends of the type of document).

       // if ( isset($_GET['tpl']) && $_GET['tpl'] == 'faENx'  ){
       //    $prms = ['unique' => true,
       //      'as' => '../web/images/female.png',
       //      'from' => '../web/images/images/female.png' ];
       //    $this->TBS->Plugin(OPENTBS_CHANGE_PICTURE, '#imagex#', '../web/images/female.png', $prms);
       // } 

      // ---------------------- 
      // Debug mode of the template engine 
      // ---------------------- 
      // if (isset($_GET['debug']) && ($_GET['debug']=='current')) $this->TBS->Plugin(OPENTBS_DEBUG_XML_CURRENT, true); // Display the intented XML of the current sub-file, and exit. 
      // if (isset($_GET['debug']) && ($_GET['debug']=='info'))    $this->TBS->Plugin(OPENTBS_DEBUG_INFO, true); // Display information about the document, and exit. 
      // if (isset($_GET['debug']) && ($_GET['debug']=='show'))    $this->TBS->Plugin(OPENTBS_DEBUG_XML_SHOW); // Tells TBS to display information when the document is merged. No exit. 


        $this->TBS->MergeBlock('a,b', $this->dbdata);
        $this->TBS->MergeBlock('c', $this->dbsumdata);
        // // Output the result as a file on the server. You can change output file
        // $OpenTBS->Show(OPENTBS_DOWNLOAD, 'export.odt'); // Also merges all [onshow] automatic fields.			

        return;
      }


    /**
     * Zobraz ako Odt
     * 
     */
    public function actionGetodt(){
      $template = \Yii::getAlias('@webroot').'/templates/'.$_GET['tpl'].'.zip';
      $this->createDoc((int)$_GET['move_id'],$template, $_GET['heading'], $_GET['xfirma']);
      $output_file_name = str_replace('.zip', '_'.date('Y-m-d').'_'.$_GET['move_id'].'_'.Yii::$app->user->identity->username.'.odt', $template);
        // Output the result as a downloadable file (only streaming, no data saved in the server)
        $this->TBS->Show(OPENTBS_DOWNLOAD, $output_file_name); // Also merges all [onshow] automatic fields.

        exit;
        
      }



    /**
     * Sejvni a Zobraz ako DOC HTML PDF ...
     * 
     */
    public function actionGetanydoc(){
      $doctype = $_GET['doctype'];
      $template = \Yii::getAlias('@webroot').'/templates/'.$_GET['tpl'].'.zip';
      $this->createDoc((int)$_GET['move_id'],$template,$_GET['heading'], $_GET['xfirma']);
        //spec suma pre chrono_p4
      if( $_GET['tpl'] === 'chrono_p4' ){
        $p4sum = 0;
        foreach($this->dbdata as $key=>$value){
          $p4sum += $value['pcsxp4'];
        }
        $this->TBS->VarRef['p4sum'] = $p4sum;
      }
        //spec suma pre purchasing_list
      elseif( $_GET['tpl'] === 'purchasing_list' ){
        $p4sum = 0;
        foreach($this->dbdata as $key=>$value){
          $p4sum += $value['pcsxdprice'];
        }
        $this->TBS->VarRef['pcsxdpricesum'] = $p4sum;
      }

      if(  $doctype === 'xls' or  $doctype === 'XLS' or $doctype === 'ODS' or $doctype === 'ods' ){
        $origtype = '.ods';
      } else {
        $origtype = '.odt';
      }

      $output_file_name = str_replace('.zip', '_'.date('Y-m-d').'_'.$_GET['move_id'].'_'.Yii::$app->user->identity->username.$origtype, $template);
      $output_file_name = str_replace('/templates/', '/templates/out/', $output_file_name);
      // header('Content-Type: application/vnd.oasis.opendocument.text');
      // $this->TBS->Show(OPENTBS_DOWNLOAD + OPENTBS_NOHEADER);
      // exit;
      $this->TBS->Show(OPENTBS_FILE, $output_file_name);

      $output_to = str_replace($origtype, '.'.$doctype, $output_file_name);
       // $last_line = exec("/usr/bin/jodconverter -v \"".$output_file_name."\" \"".$output_to."\"", $retval);
      Yii::debug("HOME=/tmp /usr/bin/libreoffice --headless --convert-to ".$doctype." \"".$output_file_name."\" --outdir \"".\Yii::getAlias('@webroot').'/templates/out'."\"  2>&1");
      $last_line = exec("HOME=/tmp /usr/bin/libreoffice --headless --convert-to ".$doctype." \"".$output_file_name."\" --outdir \"".\Yii::getAlias('@webroot').'/templates/out'."\"  2>&1",$output,$retval);

      if( $doctype === 'html' ){
        echo file_get_contents($output_to);
        exit;
      } else {
              // check filename for allowed chars (do not allow ../ to avoid security issue: downloading arbitrary files)
        if (!is_file("$output_to")) {
          throw new \yii\web\NotFoundHttpException('The file does not exists.');
        }
        Yii::debug($output_to);
        return $this->redirect(Url::to('@web/templates/out/'.$_GET['tpl'].'_'.date('Y-m-d').'_'.$_GET['move_id'].'_'.Yii::$app->user->identity->username.'.'.$doctype));

      }
      exit;
      return false;

    }

    public function actionGetpdflabels($id, $offset = 0, $labeltype = 'L7163', $first = 9999999, $from = 1, $money = "no", $price="d.price" ){

     if( Yii::$app->user->identity->repli == 2 ){
       Yii::$app->db->createCommand("set lc_monetary to \"hu_HU.UTF-8\"")->query();
       $conv = $money == "yes"?$price."::money":"TO_CHAR(".$price.", '999 999 999')";
     } else {
      Yii::$app->db->createCommand("set lc_monetary to \"en_IE.UTF-8\"")->query();
      $conv = $money == "yes"?$price."::money":"case when ".$price." > 1000 then TO_CHAR(".$price.", '999 999 999,-') else REPLACE(TO_CHAR(".$price.",'999 999 999.99'),'.',',') end ";
    }

    $query = Yii::$app->db->createCommand("select d.mat_id mm,pcs,".$conv." pp,model,label1 from m_detail d 
      join product p on d.mat_id=p.mat_id
      join kod k on k.kod=p.kod where move_id=:moveid")->bindValue(':moveid',$id);
    $q = $query->queryAll();
    $qq = [];
    $j = 0;
    $k = 0;
    foreach ($q as $key => $value) {
      for($i = 0; $i<$value['pcs']; $i++){
        $k++;
        if( $from > $k ) continue;
        $j++;
        if ( $j > $first ) break;

        $qq[] = [ 'mat_id' => $value['mm'], 'price' => $value['pp'], 'model' => $value['model'], 'kod' => $value['label1'] ];
      }
    }
    $r1 = new ArrayDataProvider(['allModels' => $qq,'pagination' => ['pageSize' => 10000,]]);

    $pdfLabel = new PdfLabel([
      'labelName' => $labeltype,
      'dataProvider' => $r1,
      'asHtml' => true,
      'font' => 'helvetica',
      'size' => 5,
      'offsetEmptyLabels' => $offset,
          //'border' => true,
      'renderLabel' => function($model, $key, $index) {
            //return addLabel("\"<h3>\""."\n"."234,02"."\n"."\"</h3>\"";
        return "<span style=\"text-align: center;\">".$model["kod"]."</span><br>".
        "<span style=\"text-align: center;\">".$model["mat_id"]."</span><br>".
        "<span style=\"text-align: center;\">".$model["model"]."</span>".
        "<h2 style=\"text-align: center;\">".$model["price"]."</h2>";
      },
    ]);
    if ($labeltype == "L4731"){
      $pdfLabel->renderLabel = function($model, $key, $index) {
        return "<span style=\"text-align: center; font-size: 90%;\"><strong>".$model["mat_id"]."</strong> ".
        $model["model"]."</span><br>".
        "<span style=\"text-align: center; font-size: 160%;\">".$model["price"]."</span>";
      };
    } else {
      $pdfLabel->renderLabel = function($model, $key, $index) {
        return "<span style=\"text-align: center;\">".$model["kod"]."</span><br>".
        "<span style=\"text-align: center;\">".$model["mat_id"]."</span><br>".
        "<span style=\"text-align: center;\">".$model["model"]."</span>".
        "<h2 style=\"text-align: center;\">".$model["price"]."</h2>";
      };
    }
    return $pdfLabel->render();

  }

   // public function actionGetlabels(){

   //      $template = \Yii::getAlias('@webroot').'/templates/L4732.zip';
   //      $this->dbdata = [
   //        0=>[
   //        'linea' => 'line.....0',
   //        'lineb' => 'line.....10',
   //        'linec' => 'line.....20',
   //        'lined' => 'line.....30',
   //        'linee' => 'line.....40',
   //        'linef' => 'line.....50',
   //        'lineg' => 'line.....60',
   //        'lineh' => 'line.....70',
   //        'linei' => 'line.....80',
   //        'linej' => 'line.....90',
   //        'linek' => 'line.....100',
   //        'linel' => 'line.....110',
   //        'linem' => 'line.....120',
   //        'linen' => 'line.....130',
   //        'lineo' => 'line.....140',
   //        'linep' => 'line.....150',
   //        'liner' => 'line.....160',
   //        'lines' => 'line.....170',
   //        'linet' => 'line.....180',
   //        'lineu' => 'line.....190',
   //      ],
   //         1=>       [
   //        'linea' => 'line.....a0',
   //        'lineb' => 'line.....10',
   //        'linec' => 'line.....20',
   //        'lined' => 'line.....30',
   //        'linee' => 'line..xx40',
   //        'linef' => 'line.....50',
   //        'lineg' => 'line.....60',
   //        'lineh' => 'line.....70',
   //        'linei' => 'line.....80',
   //        'linej' => 'line.....90',
   //        'linek' => 'line.....100',
   //        'linel' => 'line.....110',
   //        'linem' => 'line.....120',
   //        'linen' => 'line.....130',
   //        'lineo' => 'line.....140',
   //        'linep' => 'line.....150',
   //        'liner' => 'line.....160',
   //        'lines' => 'line.....170',
   //        'linet' => 'line.....180',
   //        'lineu' => 'line.....190',
   //      ],
   //             2=>   [
   //        'linea' => 'line.....b0',
   //        'lineb' => 'line.....10',
   //        'linec' => 'line.....20',
   //        'lined' => 'line.....30',
   //        'linee' => 'line..yy.40',
   //        'linef' => 'line.....50',
   //        'lineg' => 'line.....60',
   //        'lineh' => 'line.....70',
   //        'linei' => 'line.....80',
   //        'linej' => 'line.....90',
   //        'linek' => 'line.....100',
   //        'linel' => 'line.....110',
   //        'linem' => 'line.....120',
   //        'linen' => 'line.....130',
   //        'lineo' => 'line.....140',
   //        'linep' => 'line.....150',
   //        'liner' => 'line.....160',
   //        'lines' => 'line.....170',
   //        'linet' => 'line.....180',
   //        'lineu' => 'line.....190',
   //      ],
   //            3=>    [
   //        'linea' => 'line.....c0',
   //        'lineb' => 'line.....10',
   //        'linec' => 'line.....20',
   //        'lined' => 'line.....30',
   //        'linee' => 'line..zz.40',
   //        'linef' => 'line.....50',
   //        'lineg' => 'line.....60',
   //        'lineh' => 'line.....70',
   //        'linei' => 'line.....80',
   //        'linej' => 'line.....90',
   //        'linek' => 'line.....100',
   //        'linel' => 'line.....110',
   //        'linem' => 'line.....120',
   //        'linen' => 'line.....130',
   //        'lineo' => 'line.....140',
   //        'linep' => 'line.....150',
   //        'liner' => 'line.....160',
   //        'lines' => 'line.....170',
   //        'linet' => 'line.....180',
   //        'lineu' => 'line.....190',
   //      ],
   //           4=>     [
   //        'linea' => 'line.....d0',
   //        'lineb' => 'line.....10',
   //        'linec' => 'line.....20',
   //        'lined' => 'line.....30',
   //        'linee' => 'line..ww.40',
   //        'linef' => 'line.....50',
   //        'lineg' => 'line.....60',
   //        'lineh' => 'line.....70',
   //        'linei' => 'line.....80',
   //        'linej' => 'line.....90',
   //        'linek' => 'line.....100',
   //        'linel' => 'line.....110',
   //        'linem' => 'line.....120',
   //        'linen' => 'line.....130',
   //        'lineo' => 'line.....140',
   //        'linep' => 'line.....150',
   //        'liner' => 'line.....160',
   //        'lines' => 'line.....170',
   //        'linet' => 'line.....180',
   //        'lineu' => 'line.....190',
   //      ],
   //    ];
   //      $this->TBS = new \hscstudio\export\OpenTBS; // new instance of TBS
   //      $this->TBS->LoadTemplate($template, OPENTBS_ALREADY_UTF8); // Also merge some [onload] automatic fields (depends of the type of document).

   //      $this->TBS->MergeBlock('a,b', $this->dbdata);

   //      $this->TBS->Show(OPENTBS_FILE, '/tmp/xxx.odt');
   //          return $this->redirect(Url::to('file:///tmp/xxx.odt'));


   //  }

  public function actionQrpaysk($amount = 1, $comment = "", $csymbol =  "0308", $country = "SK",
      $currency = "EUR", $duedate = "", $internalid = "", $payee = "", $ssymbol = null, $vsymbol = null, $iban = "************************"){
    $date = new DateTime($duedate);
    $payment = new QrPayment();
    $payment
        ->setAmount($amount)
        ->setComment($comment)
        ->setConstantSymbol($csymbol)
        ->setCountry($country)
        ->setCurrency($currency)
        ->setDueDate($date)
        ->setInternalId($internalid)
        ->setPayeeName($payee)
        ->setSpecificSymbol($ssymbol)
        ->setVariableSymbol($vsymbol)
        ->setXzBinary( is_file('/usr/bin/xz' ) ? '/usr/bin/xz' : '/usr/local/bin/xz'  )
        ->setIbans([
            new IBAN($iban),
            // new IBAN('************************'),
            // new IBAN('************************'),
        ]);

      if (!empty($payment->getQrString())) {
        $qrCode = new QrCode($payment->getQrString());

        // $response = Yii::$app->getResponse();
        // $response->headers->set('Content-Type', 'image/png');
        // $response->format = Response::FORMAT_RAW;
        // $response->send();
        $png = new \Mpdf\QrCode\Output\Png();
        // echo $png->output($qrCode);

        // Create image instances
        $dest = imagecreatefrompng(Yii::getAlias('@webroot').'/images/paybysquare.png');
        $src = imagecreatefromstring($png->output($qrCode,364));
        $im = imagecreatetruecolor(55, 30);
        imagecolortransparent($src, imagecolorallocate($im, 255, 255, 255));
        // Copy and merge
        imagecopymerge($dest, $src, 10, 10, 0, 0, 364, 364, 100);

        imagepng($dest,Yii::getAlias('@webroot').'/templates/out/qr'.$internalid.'.png',9);

//        echo $dest;

        // imagegif($dest);

        imagedestroy($dest);

    }
  }

  /**
   * Function returns QR code as PNG image using QR Code Generator library
   * param string $text
   */
  public function actionQrcode($text = ''){
    $qrCode = new QrCode($text);
    //display PNG
    $png = new Output\Png();
    $im = imagecreatefromstring($png->output($qrCode,100));
    if ($im !== false) {
      header('Content-Type: image/png');
      imagepng($im);
      imagedestroy($im);
  }

  }

  public function actionPaybysquare($code = ''){
    $d = implode("\t", array(
      0 => '123456789', ///INVOICE ID
      1 => '1', // PAYMENTS - COUNT
      2 => implode("\t", array(
        true,
    123.45,           // SUMA
    'EUR',            // JEDNOTKA
    '2021121',         // DATUM
    123456789,          // VARIABILNY SYMBOL
    '0308',           // KONSTANTNY SYMBOL
    '1111',           // SPECIFICKY SYMBOL
    '',
    'poznamka',         // POZNAMKA
    '1',
    '************************', // IBAN
    'TATRSKBX',         // SWIFT
    '0',
    '0'
  ))
    ));
    $d = strrev(hash("crc32b", $d, TRUE)) . $d;
    $x = proc_open("/usr/bin/xz '--format=raw' '--lzma1=lc=3,lp=0,pb=2,dict=128KiB' '-c' '-'", [0 => ["pipe", "r"], 1 => ["pipe", "w"]], $p);
    fwrite($p[0], $d);
    fclose($p[0]);
    $o = stream_get_contents($p[1]);
    fclose($p[1]);
    proc_close($x);
    $d = bin2hex("\x00\x00" . pack("v", strlen($d)) . $o);
    $b = "";
    for ($i = 0;$i < strlen($d);$i++) {
      $b .= str_pad(base_convert($d[$i], 16, 2), 4, "0", STR_PAD_LEFT);
    }
    $l = strlen($b);
    $r = $l % 5;
    if ($r > 0) {
      $p = 5 - $r;
      $b .= str_repeat("0", $p);
      $l += $p;
    }
    $l = $l / 5;
    $d = str_repeat("_", $l);
    for ($i = 0;$i < $l;$i += 1) {
      $d[$i] = "0123456789ABCDEFGHIJKLMNOPQRSTUV"[bindec(substr($b, $i * 5, 5))];
    }
    if (!empty($d)) {
      $qrCode = new QrCode($d);


      $response = Yii::$app->getResponse();
      $response->headers->set('Content-Type', 'image/png');
      $response->format = Response::FORMAT_RAW;
      $response->send();
      return $qrCode->displayPng();
    }

  }

}