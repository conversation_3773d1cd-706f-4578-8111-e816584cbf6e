<?php

namespace app\controllers;

use Yii;
use app\models\WowSales;
use app\models\Product;
use app\models\SearchWowSales;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\helpers\Json;

/**
 * SalesController implements the CRUD actions for WowSales model.
 */
class SalesController extends Controller
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all WowSales models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new SearchWowSales();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }


    /**
     * Lists Personal WowSales models.
     * @return mixed
     */
    public function actionMysales()
    {
        $searchModel = new SearchWowSales();
        $dataProvider = $searchModel->search(['cust_id' => Yii::$app->request->get('cust_id')]);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single WowSales model.
     * @param integer $orders_id
     * @param string $card_nr
     * @param integer $mat_id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($orders_id, $card_nr, $mat_id)
    {
        return $this->render('view', [
            'model' => $this->findModel($orders_id, $card_nr, $mat_id),
        ]);
    }

    /**
     * Creates a new WowSales model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new WowSales();

        if ($model->load(Yii::$app->request->post()) && $model->validate() ) {

            //ToDo: WDL HU
            switch ($model->sale_info) {
                case 'sale87':
                    $m_type_id=87;
                    $clip = true;
                    break;
                case 'sale88':
                    $clip = true;
                    $m_type_id=88;
                    break;
                case 'order17':
                    $clip = true;
                    $m_type_id=17;
                    break;
                
                default:
                    $clip = false;
                    break;
            }

            if( $clip ){
//ToDo: stock_id1 stock_id2
                try {
                      $query = "insert into tempmove( mat_id,pcs,price,tax,discount,all1,all2,all3,all4,all5,
                                  ean13,kod,model,pdescr,unit,currency,clip_id,d1,d2,d3,m_type_id,price_type,
                                  stock_id1,stock_id2,text1,text2,user_name,tempmove_info,fifo_move_id ) values(
                                   :material,:poc,:cena,0,0,0,0,0,0,0,
                                  (select ean13 from product where mat_id = :material2),
                                  (select kod from product where mat_id = :material3),
                                  (select model from product where mat_id = :material4),
                                  (select pdescr from product where mat_id = :material5),'',
                                  (select currency from office where username = :user_name ),
                                  :clipid,now(),now(),now(),:m_type_id,'p1',null,null,:dtext1,:dtext2,
                                  :user,:tempinfo, 1 )" ;
                                
                                    Yii::$app->db->createCommand($query)
                                        ->bindValue(':material', $model->mat_id)
                                        ->bindValue(':material2', $model->mat_id)
                                        ->bindValue(':material3', $model->mat_id)
                                        ->bindValue(':material4', $model->mat_id)
                                        ->bindValue(':material5', $model->mat_id)
                                        ->bindValue(':poc', $model->pcs)
                                        ->bindValue(':cena', $model->sale_price)
                                        ->bindValue(':user_name', Yii::$app->user->identity->username)
                                        ->bindValue(':clipid', 1)
                                        ->bindValue(':dtext1', '')
                                        ->bindValue(':dtext2', 'WDL Admin')
                                        ->bindValue(':user', $_SERVER['PHP_AUTH_USER'])
                                        ->bindValue(':tempinfo', "WDL CARD: ".$model->card_nr)
                                        ->bindValue(':m_type_id', $m_type_id)
                                        ->execute();
                } catch (\yii\db\Exception $e) {
                    Yii::$app->session->setFlash('error',$e->getMessage());
                    return $this->render('create', [
                        'model' => $model,
                    ]);

                }catch (Exception $e) {

                    Yii::$app->session->setFlash('error',$e);
                    return $this->render('create', [
                        'model' => $model
                    ]);
                }

            }

            if( $model->save() ){
                return $this->redirect(['customers/view', 'id' => $model->cust_id]);
            }
        }


        //Yii::$app->session->setFlash('error',$model->errors);

        return $this->render('create', [
            'model' => $model
        ]);
    }

    /**
     * Updates an existing WowSales model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $orders_id
     * @param string $card_nr
     * @param integer $mat_id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($orders_id, $card_nr, $mat_id)
    {
        $model = $this->findModel($orders_id, $card_nr, $mat_id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'orders_id' => $model->orders_id, 'card_nr' => $model->card_nr, 'mat_id' => $model->mat_id]);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing WowSales model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $orders_id
     * @param string $card_nr
     * @param integer $mat_id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($orders_id, $card_nr, $mat_id)
    {
        $this->findModel($orders_id, $card_nr, $mat_id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the WowSales model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $orders_id
     * @param string $card_nr
     * @param integer $mat_id
     * @return WowSales the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($orders_id, $card_nr, $mat_id)
    {
        if (($model = WowSales::findOne(['orders_id' => $orders_id, 'card_nr' => $card_nr, 'mat_id' => $mat_id])) !== null) {
            return $model;
        }

        throw new NotFoundHttpException(Yii::t('app', 'The requested page does not exist.'));
    }


    public function actionGetmodel($mat_id, $price="p1")
    {
        if( is_numeric($mat_id)){
            if (($model = Product::findOne(['mat_id' => $mat_id])) !== null) {
                return $this->asJson(['model'=>$model->model,'kod'=>$model->kod, $price=>$model[$price]]);
            } else {
                return $this->asJson(['model'=>'Not found','kod'=>'',$price=>'']);
            }

        } else {
                return $this->asJson(['model'=>'Error - mat_id not numeric','kod'=>'',$price=>'']);
        }

    }
}
