<?php

namespace app\controllers;

use Yii;
use app\models\Customers;
use app\models\Countries;
use app\models\WowSales;
use app\models\CustomersSearch;
use yii\web\Controller;
use yii\data\ActiveDataProvider;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\helpers\Json;

/**
 * CustomersController implements the CRUD actions for Customers model.
 */
class CustomersController extends Controller
{
    /**
     * {@inheritdoc}
     */

    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['post'],
                ],
            ],
            'access' => [
                'class' => \yii\filters\AccessControl::class,
                'rules' => [
                    [
                        'allow' => true,
                        'actions' => ['index', 'getpwd', 'view', 'viewsearch', 'create', 'update', 'sendpwd', 'delete'],
                        'matchCallback' => function ($rule, $action) {
                            return EstockTools::isUserShop() || EstockTools::isUserStock();
                        }
                    ],
                    [
                        'allow' => false
                    ]
                ]
            ]
        ];
    }

    /**
     * Lists all Customers models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new CustomersSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        if(isset($_POST['export_type'])){$dataProvider->pagination = false;}
        else {$dataProvider->pagination->pageSize=20;}

        if( Yii::$app->request->post('hasEditable')){
            $cId = Yii::$app->request->post('editableKey');
            $c = Customers::findOne($cId);

            $out = Json::encode(['output' => '', 'message' => '']);
            $post = [];
            $posted = current($_POST['Customers']);
            $post['Customers'] = $posted;
            if($c->load($post)){
                $c->save();
            }
            echo $out;
            return;
        }

        $dataProvider->sort = ['defaultOrder' => ['id'=>SORT_DESC]];

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }



    /**
     * get new pwd
     * @return string
     */
    public function actionGetpwd()
    {
        return $this->asJson(Json::encode(['pwd' => EstockTools::generateStrongPassword(12) ]));
    }

    /**
     * Displays a single Customers model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        $dataProvider = new ActiveDataProvider([
            'query' => WowSales::find()
                    ->where( ['cust_id' => $id] )
                    ->orderBy( ['sale_date'=>SORT_DESC] )
                    ]);
        $dataProvider->pagination->pageSize=20;

        return $this->render('view', [
            'model' => $this->findModel($id), 'salesdata' => $dataProvider,
        ]);
    }


    /**
     * Displays a main search results.
     * @param string $q
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionViewsearch($q)
    {
        $what = $q;// Yii::$app->request->get('q');
        $searchModel = new CustomersSearch();
        $dataProvider = new ActiveDataProvider([
            'query' => Customers::find()
                    ->where( "nick ilike '${what}%'")
                    ->orWhere( "card_nr ilike '${what}%'")
                    ->orWhere( "firstname ilike '%${what}%'")
                    ->orWhere( "lastname ilike '%${what}%'")
                    ]);
        $dataProvider->pagination->pageSize=20;

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }


    /**
     * Creates a new Customers model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new Customers();

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        }

        $cmodel = Countries::find()->all();

        return $this->render('create', [
            'model' => $model, 'cmodel' => $cmodel
        ]);
    }

    /**
     * Updates an existing Customers model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $cmodel = Countries::find()->all();

        if ($model->load(Yii::$app->request->post()) ){
            if( $model->save() ){
                 Yii::$app->session->setFlash('success', "Updated successfully."); 
                return $this->redirect(['view', 'id' => $model->id]);
            } else {
                Yii::$app->session->setFlash('error', "Not updated.");
                return $this->render('update', [
                    'model' => $model, 'cmodel' => $cmodel
                ]);

            }
        }

        return $this->render('update', [
            'model' => $model,'cmodel' => $cmodel
        ]);


    }


    /**
     * Updates an existing Customers model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionSendpwd($id)
    {
        $model = $this->findModel($id);

        if (
        Yii::$app->mailer->compose('layouts/sendpwd',
            [
                'content' => 'Prihlasovacie meno: '.$model->nick."<br>Heslo: ".$model->password."<br>",
            ]) // a view rendering result becomes the message body here
            ->setFrom('<EMAIL>')
            ->setTo($model->email_address)
            ->setSubject('WDL KLUB password')
            ->send()
        )
         {
                  Yii::$app->session->setFlash('success', "Email sent successfully."); 
              } else {
                  Yii::$app->session->setFlash('error', "Email not sent.");
              }

        $dataProvider = new ActiveDataProvider([
            'query' => WowSales::find()
                    ->where( ['cust_id' => $id] )
                    ->orderBy( ['sale_date'=>SORT_DESC] )
                    ]);
        $dataProvider->pagination->pageSize=20;

        return $this->render('view', [
            'model' => $this->findModel($id), 'salesdata' => $dataProvider,
        ]);
        
    }


    /**
     * Deletes an existing Customers model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
      if(  $this->findModel($id)->delete()
        ){          Yii::$app->session->setFlash('success', "Deleted successfully."); 
              } else {
                  Yii::$app->session->setFlash('error', "Not deleted.");
              }
        return $this->redirect(['index']);
    }

    /**
     * Finds the Customers model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return Customers the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Customers::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
