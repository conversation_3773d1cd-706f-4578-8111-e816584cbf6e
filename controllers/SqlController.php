<?php

namespace app\controllers;

use Yii;
use yii\web\Controller;
use yii\helpers\Json;
use yii\data\ArrayDataProvider;
use yii\db\Query;
use app\models\SqlForm;
use app\models\Selects;
use app\models\UpdateUctoMovementForm;
use app\models\UpdateUctoPricesMovementForm;

/**
 * ProductController implements the CRUD actions for Product model.
 */
class SqlController extends Controller
{
    public function behaviors()
    {
        return [
            'access' => [
                'class' => \yii\filters\AccessControl::class,
                'rules' => [
                    [
                        'allow' => true,
                        'actions' => ['index', 'stat1','users','document','b2breport','zalreport','createdocreport','mtype','movementswithcurr','zlavyof','intrastat','sqlcall', 'sqlcalluser'],
                        'matchCallback' => function ($rule, $action) {
                            return EstockTools::isUserStock() || EstockTools::isUserWman();
                        }
                    ],
                    [
                        'allow' => true,
                        'actions' => ['customselect'],
                        'matchCallback' => function ($rule, $action) {
                            return EstockTools::isUserDba() || EstockTools::isUserVip() || EstockTools::isUserWman();
                        }
                    ],
                    [
                        'allow' => true,
                        'actions' => ['sqlconfigcall'],
                        'matchCallback' => function ($rule, $action) {
                            return EstockTools::isUserWman();
                        }
                    ],
                    [
                        'allow' => true,
                        'actions' => ['uctoselect'],
                        'matchCallback' => function ($rule, $action) {
                            return EstockTools::isUserUcto();
                        }
                    ],
                    [
                        'allow' => false
                    ]
                ]
            ]
        ];
    }

    public function actionUsers()
    {
            $query = Yii::$app->db->createCommand("select id,username,email,profile,repli,all_flags from wuser where password_hash <> '10e0126cbe85e713815f7825bf0201ec'");
            $provider = new ArrayDataProvider(['allModels' => $query->queryAll(),'pagination' => ['pageSize' => 333333,]]);

        return $this->render('stat1', ['pall'=>$provider, 'title' => 'USERS', 'sql' => $query->getRawSql()]);
    }

    public function actionMtype()
    {
        $mt = "";
        foreach (EstockTools::getUserMtypes() as $key => $value) {
            if( !empty($mt)){
                $mt .= ",";
            }
            $mt .= $value['m_type_id'];
        }
           $query = Yii::$app->db->createCommand("select * from m_type where m_type_id in (".$mt.")");
            $provider = new ArrayDataProvider(['allModels' => $query->queryAll(),'pagination' => ['pageSize' => 333333,]]);

        return $this->render('stat1', ['form' => isset($model)?$model:null, 'pall'=>$provider, 'title' => 'Movement types defined for this user',
         'sql' => $query->getRawSql()]);

    }

   //
    // UCTO selects
    //


    public function actionUctoselect()
    {
        $uctoupdatepricesform = new UpdateUctoPricesMovementForm();

        $uctoupdateform = new UpdateUctoMovementForm();

        $params = Yii::$app->request->get();
        $filter = "";
        try {
            $uctoupdateform->load(Yii::$app->request->post());
            $uctoupdatepricesform->load(Yii::$app->request->post());

            if( $uctoupdateform->validate() && isset($uctoupdateform->multiadd) && !empty($uctoupdateform->multiadd)){
                $csla = explode("\n", $uctoupdateform->multiadd);
                $command = "";
                $ucommands = [];
                for( $i = 0; $i < count($csla); $i++) {
                    $ucommand = "";
                    $riadok = explode(",", str_replace("\t","",$csla[$i]));
                  if( count($riadok) > 2 && $riadok[0] > 100000 && $riadok[1] > 0 && $riadok[2] > 100000) {
                    //check whether d1 in move_id = $riadok[1] is current year minus 1
                    $d1 = Yii::$app->db->createCommand("select year(d1) from movement where move_id=".$riadok[1])->queryScalar();
                    if( $d1 != Yii::$app->params['uctoyear'] ) {
                        Yii::$app->session->setFlash('error', "Nespravny parameter - jeden z move_id je pre iny rok ako ZADANY v systeme" );
                        return $this->render('ucto',['uctoupdateform' => $uctoupdateform, 'uctoupdatepricesform' => $uctoupdatepricesform ]);      
                    }
                    if( $command !== "") {
                        $command .= " union all ";
                    }
                    $command .=  "select 'bude ".$riadok[0]."' new_mat_id,
                     d.move_id,d.mat_id,p.kod,p.model,d.price, d.detail_info from m_detail d join product p on d.mat_id=p.mat_id 
                     where d.move_id=".$riadok[1]." and d.mat_id=".$riadok[2];
                    $ucommand = "update m_detail set mat_id='".$riadok[0]."' where move_id=".$riadok[1]." and mat_id=".$riadok[2];

                    if ( isset($riadok[3]) && !empty($riadok[3]) && $riadok[3] >= 0) {
                        $command .= " and d.price=".$riadok[3];
                        $ucommand .= " and price=".$riadok[3];
                    }
                    if ( isset($riadok[4]) && !empty($riadok[4])) {
                        $command .= " and d.detail_info='".trim($riadok[4])."'";
                        $ucommand .= " and detail_info='".trim($riadok[4])."'";
                    }
                  }
                  if( $ucommand !== "") {
                    $ucommands[] = $ucommand;
                  }
                }
                if( $command !== "" ) {
                    $query = Yii::$app->db->createCommand($command);
                    $pupdate = new ArrayDataProvider(['allModels' => $query->queryAll()]);
                    // echo $command;   
                    // print_r($ucommands);
                } else {
                    Yii::$app->session->setFlash('error', "Nespravny parameter" );
                    return $this->render('ucto');      
                }

              Yii::$app->session->setFlash('warning', 'Skontroluj, toto sa vykona: <br>' .implode( ";\n<br>", $ucommands ));
              return $this->render('ucto', [
                'pupdate'=>$pupdate,
                'uctoupdateform' => $uctoupdateform,
                'uctoupdatepricesform' => $uctoupdatepricesform,
                'pupdatecmd' => $ucommands,
                        ]);
    
            }

            if( $uctoupdatepricesform->validate() && isset($uctoupdatepricesform->multiaddprices) && !empty($uctoupdatepricesform->multiaddprices)){
                $csla = explode("\n", $uctoupdatepricesform->multiaddprices);
                $command = "";
                $ucommands = [];
                for( $i = 0; $i < count($csla); $i++) {
                    $ucommand = "";
                    $riadok = explode(",", str_replace("\t","",$csla[$i]));
                  if( count($riadok) > 2 && $riadok[0] >= 0 && $riadok[1] > 0 && $riadok[2] > 100000) {
                    //check whether d1 in move_id = $riadok[1] is current year minus 1
                    $d1 = Yii::$app->db->createCommand("select year(d1) from movement where move_id=".$riadok[1])->queryScalar();
                    if( $d1 != Yii::$app->params['uctoyear'] ) {
                        Yii::$app->session->setFlash('error', "Nespravny parameter - jeden z move_id je pre iny rok ako ZADANY v systeme" );
                        return $this->render('ucto',['uctoupdateform' => $uctoupdateform, 'uctoupdatepricesform' => $uctoupdatepricesform ]);      
                    }
                    if( $command !== "") {
                        $command .= " union all ";
                    }
                    $command .=  "select 'bude ".$riadok[0]."' new_price,
                     d.move_id,d.mat_id,p.kod,p.model,d.price old_price, d.detail_info from m_detail d join product p on d.mat_id=p.mat_id 
                     where d.move_id=".$riadok[1]." and d.mat_id=".$riadok[2];
                    $ucommand = "update m_detail set price='".$riadok[0]."' where move_id=".$riadok[1]." and mat_id=".$riadok[2];

                    if ( isset($riadok[3]) && !empty($riadok[3]) && $riadok[3] >= 0) {
                        $command .= " and d.price=".$riadok[3];
                        $ucommand .= " and price=".$riadok[3];
                    }
                    if ( isset($riadok[4]) && !empty($riadok[4])) {
                        $command .= " and d.detail_info='".trim($riadok[4])."'";
                        $ucommand .= " and detail_info='".trim($riadok[4])."'";
                    }
                  }
                  if( $ucommand !== "") {
                    $ucommands[] = $ucommand;
                  }
                }
                if( $command !== "" ) {
                    $query = Yii::$app->db->createCommand($command);
                    $pupdate = new ArrayDataProvider(['allModels' => $query->queryAll()]);
                    // echo $command;   
                    // print_r($ucommands);
                } else {
                    Yii::$app->session->setFlash('error', "Nespravny parameter" );
                    return $this->render('ucto');      
                }

              Yii::$app->session->setFlash('warning', 'Skontroluj, toto sa vykona: <br>' .implode( ";\n<br>", $ucommands ));
              return $this->render('ucto', [
                'pupdate'=>$pupdate,
                'uctoupdateform' => $uctoupdateform,
                'uctoupdatepricesform' => $uctoupdatepricesform,
                'pupdatecmd' => $ucommands,
                        ]);
    
            }

    
            if( $params) {
                if( isset($params['updateprikazy'])) {        
                    foreach( $params['updateprikazy'] as $key => $value) {
                        $cmd = Yii::$app->db->createCommand( $value );
                        $cmd->query();
                    }
                    Yii::$app->session->setFlash('success', 'VYKONANE: <br>' . implode( ";\n<br>", $params['updateprikazy'] ));
                    return $this->render('ucto');
                }

                if( isset($params['recount'])) {            
                    $cmd = Yii::$app->db->createCommand( 'call '. $params['proc'] . '()' );
                    $cmd->query();
                }
                if( isset($params['recount'])) {            
                    $cmd = Yii::$app->db->createCommand( 'call '. $params['proc'] . '()' );
                    $cmd->query();
                }
                if( isset($params['missingonly'])) {
                    $filter = " where xx='!' or f.mat_id in (select mat_id from ".$params['tab']." where xx='!') ";
                }
                $cmd = Yii::$app->db->createCommand( 'select matid as riadok, xx, f.mat_id,p.kod,p.model,f.n_price,f.s_price,f.d1,m.d2, fnumber,f.pcs,remains,beforeonstock,f.move_id,doc_nr, m.m_type_id ,p.price,p0,p1,
                    (select currency||\' \'||detail_info||\' \'||tax from m_detail where move_id=f.move_id and mat_id=f.mat_id limit 1) detail_info,
                    a.firma,total,text1,text2, emp_id
                    from '.$params['tab'].' f join movement m on m.move_id=f.move_id join product p on p.mat_id=f.mat_id join address a on a.adr_id=m.adr_id
                     '.$filter.' order by matid');

         

               $provider = new ArrayDataProvider(['allModels' => $cmd->queryAll()]);

                if(isset($_POST['export_type'])){$provider->pagination = false;}
                else {$provider->pagination->pageSize=333333;}

                return $this->render('ucto', ['pall'=>$provider,  'title' => $params['proc'].' Selects: '.$params['tab'] ,
                 'sql' => $cmd->getRawSql()]);

            }

        } catch (\yii\db\Exception $e) {
                    Yii::$app->session->setFlash('error',$e->getMessage() );
                    return $this->render('ucto', []);
        }


        return $this->render('ucto');


    }

    //
    // CONFIG selects
    //

    public function actionSqlconfigcall($name = 'matejek',$q = 'all')
    {

        $model = new SqlForm();
        if (!$model->load(Yii::$app->request->post()) || !$model->validate() ) {
            if( !Empty(Yii::$app->params['query'][$name][$q]['l1'])){
                $model->label1 =  Yii::$app->params['query'][$name][$q]['l1'];
            }
            if( !Empty(Yii::$app->params['query'][$name][$q]['l2'])){
                $model->label2 =  Yii::$app->params['query'][$name][$q]['l2'];
            }
            if( !Empty(Yii::$app->params['query'][$name][$q]['l3'])){
                $model->label3 =  Yii::$app->params['query'][$name][$q]['l3'];
            }
            if( !Empty(Yii::$app->params['query'][$name][$q]['xxlabxx'])){
                $model->xxlabxx =  Yii::$app->params['query'][$name][$q]['xxlabxx'];
            }
            if( !Empty(Yii::$app->params['query'][$name][$q]['p1'])){
                $model->param1 =  Yii::$app->params['query'][$name][$q]['p1'];
            }
            if( !Empty(Yii::$app->params['query'][$name][$q]['p2'])){
                $model->param2 =  Yii::$app->params['query'][$name][$q]['p2'];
            }
            if( !Empty(Yii::$app->params['query'][$name][$q]['p3'])){
                $model->param3 =  Yii::$app->params['query'][$name][$q]['p3'];
            }
            if( !Empty(Yii::$app->params['query'][$name][$q]['xxparamxx'])){
                $model->xxparamxx =  Yii::$app->params['query'][$name][$q]['xxparamxx'];
            }
            return $this->render('stat1', ['form'=>$model]);
        }

        $_query = str_replace('xxparamxx', is_null($model->xxparamxx)?"":$model->xxparamxx , Yii::$app->params['query'][$name][$q]['q'] );

        $query = Yii::$app->db->createCommand( $_query );

        if( !Empty(Yii::$app->params['query'][$name][$q]['p1'])){
            $query->bindValue('p1', $model->param1);
        }
        if( !Empty(Yii::$app->params['query'][$name][$q]['p2'])){
            $query->bindValue('p2', $model->param2);
        }
        if( !Empty(Yii::$app->params['query'][$name][$q]['p3'])){
            $query->bindValue('p3', $model->param3);
        }

       $provider = new ArrayDataProvider(['allModels' => $query->queryAll()]);

        if(isset($_POST['export_type'])){$provider->pagination = false;}
        else {$provider->pagination->pageSize=333333;}


        return $this->render('stat1', ['form' => $model, 'pall'=>$provider, 'title' => $name.' Selects: '.$q ,
         'sql' => $query->getRawSql()]);

    }



    //
    // USER selects
    //


    public function actionSqlcall($name = 'matejek',$q = 'all')
    {

        $model = new SqlForm();
        if (!$model->load(Yii::$app->request->post()) || !$model->validate() ) {
            if( !Empty(Selects::$query[$name][$q]['l1'])){
                $model->label1 =  Selects::$query[$name][$q]['l1'];
            }
            if( !Empty(Selects::$query[$name][$q]['l2'])){
                $model->label2 =  Selects::$query[$name][$q]['l2'];
            }
            if( !Empty(Selects::$query[$name][$q]['l3'])){
                $model->label3 =  Selects::$query[$name][$q]['l3'];
            }
            if( !Empty(Selects::$query[$name][$q]['xxlabxx'])){
                $model->xxlabxx =  Selects::$query[$name][$q]['xxlabxx'];
            }
            if( !Empty(Selects::$query[$name][$q]['p1'])){
                $model->param1 =  Selects::$query[$name][$q]['p1'];
            }
            if( !Empty(Selects::$query[$name][$q]['p2'])){
                $model->param2 =  Selects::$query[$name][$q]['p2'];
            }
            if( !Empty(Selects::$query[$name][$q]['p3'])){
                $model->param3 =  Selects::$query[$name][$q]['p3'];
            }
            if( !Empty(Selects::$query[$name][$q]['xxparamxx'])){
                $model->xxparamxx =  Selects::$query[$name][$q]['xxparamxx'];
            }
            return $this->render('stat1', ['form'=>$model]);
        }
        // Yii::$app->session->setFlash('info',$name.' Selects: '.$q);

        $_query = str_replace('xxparamxx', is_null($model->xxparamxx)?"":$model->xxparamxx , Selects::$query[$name][$q]['q'] );

        $query = Yii::$app->db->createCommand( $_query );

        if( !Empty(Selects::$query[$name][$q]['p1'])){
            $query->bindValue('p1', $model->param1);
        }
        if( !Empty(Selects::$query[$name][$q]['p2'])){
            $query->bindValue('p2', $model->param2);
        }
        if( !Empty(Selects::$query[$name][$q]['p3'])){
            $query->bindValue('p3', $model->param3);
        }

       $provider = new ArrayDataProvider(['allModels' => $query->queryAll()]);

        if(isset($_POST['export_type'])){$provider->pagination = false;}
        else {$provider->pagination->pageSize=333333;}


        return $this->render('stat1', ['form' => $model, 'pall'=>$provider, 'title' => $name.' Selects: '.$q ,
         'sql' => $query->getRawSql()]);

    }


    public function actionSqlcalluser($q = 'all')
    {

        $model = new SqlForm();
        $name = Yii::$app->user->identity->username;
        if (!$model->load(Yii::$app->request->post()) || !$model->validate() ) {
            if( !Empty(Selects::$selectsbyname[$name][$q]['l1'])){
                $model->label1 =  Selects::$selectsbyname[$name][$q]['l1'];
            }
            if( !Empty(Selects::$selectsbyname[$name][$q]['l2'])){
                $model->label2 =  Selects::$selectsbyname[$name][$q]['l2'];
            }
            if( !Empty(Selects::$selectsbyname[$name][$q]['l3'])){
                $model->label3 =  Selects::$selectsbyname[$name][$q]['l3'];
            }
            if( !Empty(Selects::$selectsbyname[$name][$q]['xxlabxx'])){
                $model->xxlabxx =  Selects::$selectsbyname[$name][$q]['xxlabxx'];
            }
            if( !Empty(Selects::$selectsbyname[$name][$q]['p1'])){
                $model->param1 =  Selects::$selectsbyname[$name][$q]['p1'];
            }
            if( !Empty(Selects::$selectsbyname[$name][$q]['p2'])){
                $model->param2 =  Selects::$selectsbyname[$name][$q]['p2'];
            }
            if( !Empty(Selects::$selectsbyname[$name][$q]['p3'])){
                $model->param3 =  Selects::$selectsbyname[$name][$q]['p3'];
            }
            if( !Empty(Selects::$selectsbyname[$name][$q]['xxparamxx'])){
                $model->xxparamxx =  Selects::$selectsbyname[$name][$q]['xxparamxx'];
            }
            return $this->render('stat1', ['form'=>$model]);
        }
        // Yii::$app->session->setFlash('info',$name.' Selects: '.$q);

        $_query = str_replace('xxparamxx', is_null($model->xxparamxx)?"":$model->xxparamxx , Selects::$selectsbyname[$name][$q]['q'] );

        $query = Yii::$app->db->createCommand( $_query );

        if( !Empty(Selects::$selectsbyname[$name][$q]['p1'])){
            $query->bindValue('p1', $model->param1);
        }
        if( !Empty(Selects::$selectsbyname[$name][$q]['p2'])){
            $query->bindValue('p2', $model->param2);
        }
        if( !Empty(Selects::$selectsbyname[$name][$q]['p3'])){
            $query->bindValue('p3', $model->param3);
        }

       $provider = new ArrayDataProvider(['allModels' => $query->queryAll()]);

        if(isset($_POST['export_type'])){$provider->pagination = false;}
        else {$provider->pagination->pageSize=333333;}


        return $this->render('stat1', ['form' => $model, 'pall'=>$provider, 'title' => $name.' Selects: '.$q ,
         'sql' => $query->getRawSql()]);

    }

    public function actionDocument()
    {
            $query = Yii::$app->db->createCommand("select * from document");
            $provider = new ArrayDataProvider(['allModels' => $query->queryAll(),'pagination' => ['pageSize' => 333333,]]);

        return $this->render('stat1', ['pall'=>$provider, 'title' => 'Document type and numbers',
         'sql' => $query->getRawSql()]);
    }


    public function actionB2breport()
    {
        $q = "

        select
                adr_id,firma,mat_id,kod,model,avg(average_price)::numeric(11,2) avgprice,sum(ordered) sumordered,sum(delivered) sumdelivered,
                sum(last_delivered) sumlastdelivered,sum(ordered-delivered) clean_backorder,sum(ordered-last_delivered) fresh_backorder,sum(greatest(ordered-delivered,ordered-last_delivered)) summaxorder,sum(s0) sums0, sum(s0j) sums0j, sum(sc) sumsc,
                from_date as min_from_date

                from
                (
                select adr_id,
                (select firma from address where adr_id=m.adr_id) firma,
                min(d1) from_date,m.m_type_id,
                case when m_type_id=17 then max(d1) end max_from_date,
                d.mat_id,kod,model,floor(avg(d.price)*100)/100 average_price,sum(d.pcs) ordered,
                (select case when sum(dd.pcs) is null then 0 else sum(dd.pcs) end from m_detail dd join
                movement mm on dd.move_id=mm.move_id
                where mm.d1 >= :from_date and mm.adr_id=m.adr_id 
                and dd.mat_id=d.mat_id and mm.m_type_id in (14,24,110,130,145)) delivered,
                (select case when sum(dd.pcs) is null then 0 else sum(dd.pcs) end from m_detail dd
                join movement mm on dd.move_id=mm.move_id
                where mm.d1 >= '2023-01-01' and mm.adr_id=m.adr_id
                and dd.mat_id=d.mat_id and mm.m_type_id in (14,24,110,130,145)) last_delivered,


                (select sum(remains) from dyninfox where shop='s0' and mat_id=d.mat_id) s0,
                (select sum(remains) from dyninfox where shop='s0j' and mat_id=d.mat_id) s0j,
                (select sum(remains) from dyninfox where shop='sc' and mat_id=d.mat_id) sc

                from movement m join m_detail d on m.move_id =d.move_id
                join product p on p.mat_id=d.mat_id
                where (m.d1 >= '2023-01-01' and m_type_id in (17) and text1 ilike 'B2B%') and adr_id<>11
                group by m.m_type_id,m.adr_id,d.mat_id,kod,model

                ) as ss
                group by
                adr_id,firma,mat_id,kod,model,from_date
                having sum(ordered-delivered)<>0 and (sum(ordered-last_delivered)>0 or sum(greatest(ordered-delivered,ordered-last_delivered))>0) order by firma,from_date
";

           $query = Yii::$app->db->createCommand($q)->bindValue(':from_date','2023-01-01');
            $provider = new ArrayDataProvider(['allModels' => $query->queryAll(),'pagination' => ['pageSize' => 333333,]]);

        return $this->render('stat1', ['pall'=>$provider, 'title' => 'B2B report',
         'sql' => $query->getRawSql()]);
    }


    public function actionZalreport()
    {
    	$q = "

        select
                adr_id,firma,mat_id,kod,model,avg(average_price)::numeric(11,2) avgprice,sum(ordered) sumordered,sum(delivered) sumdelivered,
                sum(last_delivered) sumlastdelivered,sum(ordered-delivered) clean_backorder,sum(ordered-last_delivered) fresh_backorder,sum(greatest(ordered-delivered,ordered-last_delivered)) summaxorder,sum(s0) sums0, sum(s0j) sums0j, sum(sc) sumsc,
                from_date as min_from_date,
                lastmoveid as last_move_id

                from
                (
                select adr_id,
                (select firma from address where adr_id=m.adr_id) firma, max(m.move_id) lastmoveid,
                min(d1) from_date,m.m_type_id,
                case when m_type_id=17 then max(d1) end max_from_date,
                d.mat_id,kod,model,floor(avg(d.price)*100)/100 average_price,sum(d.pcs) ordered,
                (select case when sum(dd.pcs) is null then 0 else sum(dd.pcs) end from m_detail dd join
                movement mm on dd.move_id=mm.move_id
                where mm.d1 >= :from_date and mm.adr_id=m.adr_id 
                and dd.mat_id=d.mat_id and mm.m_type_id in (24,110,102,150,137,139,167,169)) delivered,
                (select case when sum(dd.pcs) is null then 0 else sum(dd.pcs) end from m_detail dd
                join movement mm on dd.move_id=mm.move_id
                where mm.d1 >= :from_date and mm.adr_id=m.adr_id
                and dd.mat_id=d.mat_id and mm.m_type_id in (24,110,102,150,137,139,167,169)) last_delivered,


                (select sum(remains) from dyninfox where shop='s0' and mat_id=d.mat_id) s0,
                (select sum(remains) from dyninfox where shop='s0j' and mat_id=d.mat_id) s0j,
                (select sum(remains) from dyninfox where shop='sc' and mat_id=d.mat_id) sc

                from movement m join m_detail d on m.move_id =d.move_id
                join product p on p.mat_id=d.mat_id
                where (m_type_id in (17) and text1 ilike :text1 ) 
                group by m.m_type_id,m.adr_id,d.mat_id,kod,model

                ) as ss
                group by
                adr_id,firma,mat_id,kod,model,from_date, lastmoveid
                having sum(ordered-delivered)<>0 and (sum(ordered-last_delivered)>0 or sum(greatest(ordered-delivered,ordered-last_delivered))>0) order by firma,from_date
";

           $query = Yii::$app->db->createCommand($q)->bindValue(':from_date','2023-01-01')
                ->bindValue(':text1',isset($_REQUEST['text1'])?$_REQUEST['text1'] : "ZAL%" );
            $provider = new ArrayDataProvider(['allModels' => $query->queryAll(),'pagination' => ['pageSize' => 333333,]]);

        return $this->render('stat1', ['pall'=>$provider, 'title' => 'ZALOHOVE PLATBY report',
         'sql' => $query->getRawSql()]);
    }

    public function actionCustomselect()
    {

        $model = new SqlForm();
        if (!$model->load(Yii::$app->request->post()) || !$model->validate() ) {
            $model->sql =  "select * from ";
            return $this->render('stat1', ['form'=>$model]);
        }
        Yii::$app->session->setFlash('info','Sql form results');

        try {
           $query = Yii::$app->db->createCommand($model->sql);
            $provider = new ArrayDataProvider(['allModels' => $query->queryAll(),'pagination' => ['pageSize' => 333333,]]);
        } catch (\yii\db\Exception $e) {
                    Yii::$app->session->setFlash('error',$e->getMessage() );
                    return $this->render('stat1', [
                        'form' => $model,
                    ]);


        } catch(\yii\base\Exception $e) {
            Yii::$app->session->setFlash('error', $e->getMessage(). "\n Select executed: ". $query->getRawSql());
        }

        return $this->render('stat1', ['form' => $model, 'pall'=>$provider, 'title' => 'Document report',
         'sql' => $query->getRawSql()]);
    }




    public function actionCreatedocreport()
    {

        $model = new SqlForm();
        if (!$model->load(Yii::$app->request->post()) || !$model->validate() ) {
            $model->label1 =  "Enter move_id";
            return $this->render('stat1', ['form'=>$model]);
        }
        Yii::$app->session->setFlash('info','Sql form results');

        $q = "

    select 'A' as rank,
d.pcs,
cast(d.price*(1-d.discount/100) as numeric(11,2)) as pdisc,
cast(d.price*(1-(d.discount-d.fifo_price)/100) as numeric(11,2)) as pnettodisc,
cast(d.pcs*d.price*(1-d.discount/100) as numeric(11,2)) as pcsxprice,
cast(d.pcs*d.price*(1-(d.discount-d.fifo_price)/100) as numeric(11,2)) as nettopcsxprice,
cast(d.price*(1-d.discount/100)*(100+d.tax)/100 as numeric(11,2)) as pwtax,
cast(total0+total1+total2 as numeric(11,2)) as total012,
cast(tax1+tax2 as numeric(11,2)) as tax12,
cast(total0+total1+total2+tax1+tax2 as numeric(11,2)) as totalwithtax012,
cast(total0+total1+total2+tax1+tax2+rounding as numeric(11,2)) as totalpay,
cast(d.pcs*p.p4 as numeric(11)) as pcsxp4,
cast(d.pcs*d.price as numeric(11)) as pcsxdprice,
(select sum( cast(d.pcs*d.price as numeric(11) ) ) from m_detail d where move_id=:move_id) as pcsxdpricesum,
cast(d.price as numeric(11) ) as dprice,
d.price,
cast(d.discount as numeric(11,2)) as discount,
p.kod,
p.model,
d.tax,
a.firma,
a.owner_name,
a.street,
a.city,
a.zip,
a.ico,
replace(a.drc1,'SK','SK')||' '||a.drc2 as dic,
replace(a.drc1,'SK','SK') drc1,
a.drc2,
to_char(m.d1,'DD.MM.YYYY') as dat_1,
to_char(m.d2,'DD.MM.YYYY') as dat_2,
to_char(m.d3,'DD.MM.YYYY') as dat_3,
m.number,
p.mat_id,
'/var/www/estock3/pic/'||left(cast(p.mat_id as varchar(222)),2)||'/'||p.mat_id||'.jpg' pictpath,
p.p1,
cast(p.p4 as numeric(11)) as p4,
p.p6,
m.total0,
m.total1,
m.total2,
m.rounding,
m.tax1,
m.tax2,
m.text1,
m.text2,
m.m_type_id,
(select tax2 from office where user_name=(select emp_id from movement where move_id=:move_id)) as officetax2,
cc.firma_id as cccfirma,
0 as cccrepli,
cc.firma as ccfirma,
cc.street as ccstreet,
cc.city as cccity,
cc.zip as cczip,
cc.ico as ccico,
replace(cc.drc1,'SK','SK')||' '||cc.drc2 as ccdic,
replace(cc.drc1,'SK','SK') as ccdrc1,
cc.drc2 as ccdrc2,
cc.owner_name as ccowner_name,

m.adr_id,
m.stock_id1,
m.stock_id2,
(select printable_name || ' [' || iso3 || ']' from country where iso = a.iso) as country,
d.currency,
p.pdescr,
p.ean13,
d.detail_info,
(select name0 from kod where kod = p.kod) as name0,
(select name1 from kod where kod = p.kod) as name1,
(select name2 from kod where kod = p.kod) as name2,
(select name3 from kod where kod = p.kod) as name3,
(select sum(dd.pcs) from m_detail dd join product pp on pp.mat_id=dd.mat_id where move_id=m.move_id and pp.unit <> 'n') as sumpcs,
(select cast(sum(pcs*price-pcs*price*(1-discount/100)) as numeric(11,2)) from m_detail where move_id=m.move_id) as sumdiscount,
cast(d.discount-d.fifo_price as numeric(11,2)) as nettodiscount,
(select cast(sum(pcs*price-pcs*price*(1-fifo_price/100))as numeric(11,2)) from m_detail where move_id=m.move_id) as sumnettodiscount,

m.c_number,
e.iname,e.isurname,e.istreet,e.icity,e.izip,e.iphone,e.icountry,
e.icompany,e.iico,e.idic,
e.dname,e.dsurname,e.dstreet,e.dcity,e.dzip,e.dphone,e.dcountry,
e.dcompany,
i.iname as iiname,i.isurname as iisurname,i.istreet as iistreet,i.icity as iicity,i.izip as iizip,i.iphone as iiphone,i.icountry as iicountry,
i.icompany as iicompany,i.iico as iiico,i.idic as iidic,i.iicdph as iiicdph,
i.dname as idname,i.dsurname as idsurname,i.dstreet as idstreet,i.dcity as idcity,i.dzip as idzip,i.dphone as idphone,i.dcountry as idcountry,
i.dcompany as idcompany, i.mnote as imnote, i.shipment as ishipment, i.payment as ipayment

from m_detail as d join product as p on d.mat_id=p.mat_id
left outer join movement as m on d.move_id=m.move_id
left outer join address as a on (a.adr_id = m.adr_id) 
left outer join eshop as e on m.c_number=e.id
left outer join address as cc on (a.firma_id = cc.adr_id )
left outer join minfo as i on d.move_id=i.move_id
where d.move_id = :move_id
order by p.model asc";


           $query = Yii::$app->db->createCommand($q)->bindValue(':move_id',$model->param1);
            $provider = new ArrayDataProvider(['allModels' => $query->queryAll(),'pagination' => ['pageSize' => 333333,]]);

        return $this->render('stat1', ['form' => $model, 'pall'=>$provider, 'title' => 'Document report',
         'sql' => $query->getRawSql()]);
    }





    /**
     * Lists all Product models.
     * @return mixed
     */
    public function actionIndex()
    {

        return $this->render('index');
    }

    /**
     * Url action - expand from clipboard add new line product list
     */

    public function actionStat1() {

        $model = new SqlForm();
        if (!$model->load(Yii::$app->request->post()) ) {
            return $this->render('stat1', ['form'=>$model]);
        }
        Yii::$app->session->setFlash('info','Sql form results');



            $query = Yii::$app->db->createCommand("select * from wuser")->queryAll();

            $provider = new ArrayDataProvider([
                'allModels' => $query,
                'pagination' => [
                    'pageSize' => 333333,
                ]
            ]);


        return $this->render('stat1', ['pall'=>$provider, 'form'=>$model]);

    }


}
