<?php

namespace app\controllers;

use Yii;
use yii\filters\AccessControl;
use yii\web\Controller;
use yii\web\Response;
use yii\filters\VerbFilter;
use app\models\LoginForm;
use app\models\ContactForm;
use app\models\Customers;
use app\models\Movement;
use yii\helpers\ArrayHelper;
use app\controllers\EstockTools;
use yii\data\ArrayDataProvider;


class SiteController extends Controller
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::className(),
                'only' => ['logout'],
                'rules' => [
                    [
                        'actions' => ['logout','unsearch'],
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                ],
            ],
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'logout' => ['get','post'],
                ],
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function actions()
    {
        return [
            'error' => [
                'class' => 'yii\web\ErrorAction',
            ],
            'captcha' => [
                'class' => 'yii\captcha\CaptchaAction',
                'fixedVerifyCode' => YII_ENV_TEST ? 'testme' : null,
            ],
    ];
}

    /**
     * @inheritdoc
     */
    public function beforeAction($action)
    {            
        if ($action->id == 'login') {
            if ( Yii::$app->request->post('mobilelogin') == 'true') {
                   $this->enableCsrfValidation = false;
                   Yii::debug('FALSE CSRF');

            }
        }
        return parent::beforeAction($action);
    }


    /**
     * Displays homepage.
     *
     * @return string
     */
    public function actionIndex()
    {
        // Yii::debug($_SERVER['PHP_AUTH_USER']);
        if (Yii::$app->user->isGuest) {
            $model = new LoginForm();

            $model->load(Yii::$app->request->post());
            if( !$model->login()) {
                return $this->render('login',['model' => $model]);
            }
        }

        //
        //Search for customer in shop accound
        //
        if( Yii::$app->request->get('q') && EstockTools::isUserShop() ){
            $what = Yii::$app->request->get('q');
            $this->redirect(['customers/viewsearch', 'q'=>$what]);
            return;
        }

        //
        // General search for the rest but not b2b
        //
        if( Yii::$app->request->get('q') && (EstockTools::isUserStock() || EstockTools::isUserWman() || EstockTools::isUserWstock() ) ){
            $what = Yii::$app->request->get('q');
            if ( is_numeric($what)) {
                $query = "SELECT move_id, d1, emp_id, \"number\", total, m_type_id, stock_id1, stock_id2, text1, text2, firma from movement m join address a on m.adr_id=a.adr_id  where
                   move_id=:qq or text1 ilike :qqq  or text2 ilike :qqq or \"number\"=:qq or total=:qq or total2=:qq
                order by d1 desc limit 10";

                $query2 = "SELECT d.move_id, d.mat_id, d.price, pcs, detail_info, kod, model  from m_detail d join product p on d.mat_id=p.mat_id where p.mat_id in (SELECT mat_id from product p where
                   p.mat_id=:qq or p.pdescr ilike :qqq) or d.move_id=:qq or d.detail_info ilike :qqq
                    order by d.move_id desc limit 10";


            } else {
                $query = "SELECT move_id, d1, emp_id, \"number\", total, m_type_id, stock_id1, stock_id2, text1, text2, firma from movement m join address a on m.adr_id=a.adr_id  where
                   text1 ilike :qqq  or text2 ilike :qqq or stock_id1 = :qq or stock_id2 = :qq or firma like :qqq
                order by d1 desc limit 20";
                $query2 = "
                    SELECT d.move_id, d.mat_id, d.price, pcs, detail_info, kod, model, thserial from product p  join  m_detail d on p.mat_id=d.mat_id left outer join thserials t on d.move_id=t.move_id and d.mat_id=t.mat_id where
                    p.mat_id in (SELECT mat_id from product p where         
                    p.model=:qq or p.model ilike :qqq or p.pdescr ilike :qqq) 
                    union all
                    SELECT d.move_id, d.mat_id, d.price, pcs, detail_info, kod, model, thserial  from product p  join  m_detail d on p.mat_id=d.mat_id  left outer join thserials t on d.move_id=t.move_id and d.mat_id=t.mat_id where
                            d.detail_info ilike :qqq or t.thserial ilike :qqq
                    order by move_id desc limit 50";
            }
            $model1 = Yii::$app->db->createCommand($query)->bindValue(':qq',$what)->bindValue(':qqq',$what.'%')->queryAll();
            $provider1 = new ArrayDataProvider(['allModels' => $model1,'pagination' => ['pageSize' => 50,]]);
            $model2 = Yii::$app->db->createCommand($query2)->bindValue(':qq',$what)->bindValue(':qqq',$what.'%')->queryAll();
            $provider2 = new ArrayDataProvider(['allModels' => $model2,'pagination' => ['pageSize' => 50,]]);

            return $this->render('viewsearch', ['q'=>$what, 'm1'=>$provider1, 'm2'=>$provider2]);
        }



        //Show chart
        $model = Yii::$app->db->createCommand("SELECT floor(sum(total)) as \"data\", d1 as \"name\" from movement where
        d1>=:d1 group by d1
        order by d1 limit 1111")->bindValue(':d1','2019-01-01')->queryAll();
        // $model = Movement::find()
        //     ->select(["sum(total) as data",'m_type_id','d1 name'])
        //     ->where([ 'and', ['>','d1', '2019-01-01'], ['=', 'm_type_id', 87] ])
        //     ->groupBy(['m_type_id','d1'])
        //     ->orderBy('d1')
        //     ->limit(20)
        //     ->all();
        $model2 = Yii::$app->db->createCommand("SELECT floor(sum(total)) as total, m_type_id  from movement where
        d1=:d1 group by m_type_id
        ")->bindValue(':d1',date('Y-m-d'))->queryAll();

        $model3 = Yii::$app->db->createCommand("SELECT floor(sum(total)) as total, m_type_id  from movement where
        month(d1)=:d1 and year(d1) = :d2 group by m_type_id
        ")->bindValue(':d1',9)->bindValue(':d2',date('Y'))->queryAll();

        $model4 = Yii::$app->db->createCommand("SELECT floor(sum(total)) as total, m_type_id  from movement where
        month(d1)=:d1 and year(d1) = :d2 group by m_type_id
        ")->bindValue(':d1',10)->bindValue(':d2',date('Y'))->queryAll();

        return $this->render('index',[ 'model'=>$model, 'model2'=>$model2, 'model3'=>$model3, 'model4'=>$model4  ]);


    }

    /**
     * Login action.
     *
     * @return Response|string
     */
    public function actionLogin()
    {
        if (!Yii::$app->user->isGuest) {
            return $this->goHome();
        }

        $model = new LoginForm();

        if ($model->load(Yii::$app->request->post()) && $model->login()) {
            return $this->goBack();
        }

        $model->password = '';
        return $this->render('login', [
            'model' => $model,
        ]);
    }


    /**
     * Logout action.
     *
     * @return Response
     */
    public function actionLogout()
    {
        if(!Yii::$app->user->isGuest) {
            Yii::$app->user->logout(true);
            return $this->render('about');            
        } else {
            return $this->render('index');            
        }

    }

    /**
     * Displays contact page.
     *
     * @return Response|string
     */
    public function actionContact()
    {
        $model = new ContactForm();
        if ($model->load(Yii::$app->request->post()) && $model->contact(Yii::$app->params['adminEmail'])) {
            Yii::$app->session->setFlash('contactFormSubmitted');

            return $this->refresh();
        }
        return $this->render('contact', [
            'model' => $model,
        ]);
    }

    /**
     * Displays about page.
     *
     * @return string
     */
    public function actionAbout()
    {

        return $this->render('about');
    }

    /**
     * Displays UNSEARCH page
     *
     * @return string
     */
    public function actionUnsearch()
    {

        return $this->render('unsearch');
    }

}
