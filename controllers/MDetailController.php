<?php

namespace app\controllers;

use Yii;
use app\models\MDetail;
use app\models\MDetailSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * MDetailController implements the CRUD actions for MDetail model.
 */
class MDetailController extends Controller
{
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['post'],
                ],
            ],
            'access' => [
                'class' => \yii\filters\AccessControl::className(),
                'rules' => [
                    [
                        'allow' => true,
                        'actions' => ['index', 'view', 'create', 'update', 'delete', 'pdf'],
                        'roles' => ['@']
                    ],
                    [
                        'allow' => false
                    ]
                ]
            ]
        ];
    }

    /**
     * Lists all MDetail models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new MDetailSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single MDetail model.
     * @param integer $move_id
     * @param integer $mat_id
     * @param integer $m_repli_id
     * @param integer $fifo_move_id
     * @param string $detail_info
     * @param integer $fifo_repli_id
     * @return mixed
     */
    public function actionView($move_id, $mat_id, $m_repli_id, $fifo_move_id, $detail_info, $fifo_repli_id)
    {
        $model = $this->findModel($move_id, $mat_id, $m_repli_id, $fifo_move_id, $detail_info, $fifo_repli_id);
        return $this->render('view', [
            'model' => $this->findModel($move_id, $mat_id, $m_repli_id, $fifo_move_id, $detail_info, $fifo_repli_id),
        ]);
    }

    /**
     * Creates a new MDetail model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new MDetail();

        if ($model->loadAll(Yii::$app->request->post()) && $model->saveAll()) {
            return $this->redirect(['view', 'move_id' => $model->move_id, 'mat_id' => $model->mat_id, 'm_repli_id' => $model->m_repli_id, 'fifo_move_id' => $model->fifo_move_id, 'detail_info' => $model->detail_info, 'fifo_repli_id' => $model->fifo_repli_id]);
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing MDetail model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $move_id
     * @param integer $mat_id
     * @param integer $m_repli_id
     * @param integer $fifo_move_id
     * @param string $detail_info
     * @param integer $fifo_repli_id
     * @return mixed
     */
    public function actionUpdate($move_id, $mat_id, $m_repli_id, $fifo_move_id, $detail_info, $fifo_repli_id)
    {
        $model = $this->findModel($move_id, $mat_id, $m_repli_id, $fifo_move_id, $detail_info, $fifo_repli_id);

        if ($model->loadAll(Yii::$app->request->post()) && $model->saveAll()) {
            return $this->redirect(['view', 'move_id' => $model->move_id, 'mat_id' => $model->mat_id, 'm_repli_id' => $model->m_repli_id, 'fifo_move_id' => $model->fifo_move_id, 'detail_info' => $model->detail_info, 'fifo_repli_id' => $model->fifo_repli_id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing MDetail model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $move_id
     * @param integer $mat_id
     * @param integer $m_repli_id
     * @param integer $fifo_move_id
     * @param string $detail_info
     * @param integer $fifo_repli_id
     * @return mixed
     */
    public function actionDelete($move_id, $mat_id, $m_repli_id, $fifo_move_id, $detail_info, $fifo_repli_id)
    {
        $this->findModel($move_id, $mat_id, $m_repli_id, $fifo_move_id, $detail_info, $fifo_repli_id)->deleteWithRelated();

        return $this->redirect(['index']);
    }
    
    /**
     * 
     * Export MDetail information into PDF format.
     * @param integer $move_id
     * @param integer $mat_id
     * @param integer $m_repli_id
     * @param integer $fifo_move_id
     * @param string $detail_info
     * @param integer $fifo_repli_id
     * @return mixed
     */
    public function actionPdf($move_id, $mat_id, $m_repli_id, $fifo_move_id, $detail_info, $fifo_repli_id) {
        $model = $this->findModel($move_id, $mat_id, $m_repli_id, $fifo_move_id, $detail_info, $fifo_repli_id);

        $content = $this->renderAjax('_pdf', [
            'model' => $model,
        ]);

        $pdf = new \kartik\mpdf\Pdf([
            'mode' => \kartik\mpdf\Pdf::MODE_CORE,
            'format' => \kartik\mpdf\Pdf::FORMAT_A4,
            'orientation' => \kartik\mpdf\Pdf::ORIENT_PORTRAIT,
            'destination' => \kartik\mpdf\Pdf::DEST_BROWSER,
            'content' => $content,
            'cssFile' => '@vendor/kartik-v/yii2-mpdf/assets/kv-mpdf-bootstrap.min.css',
            'cssInline' => '.kv-heading-1{font-size:18px}',
            'options' => ['title' => \Yii::$app->name],
            'methods' => [
                'SetHeader' => [\Yii::$app->name],
                'SetFooter' => ['{PAGENO}'],
            ]
        ]);

        return $pdf->render();
    }

    
    /**
     * Finds the MDetail model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $move_id
     * @param integer $mat_id
     * @param integer $m_repli_id
     * @param integer $fifo_move_id
     * @param string $detail_info
     * @param integer $fifo_repli_id
     * @return MDetail the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($move_id, $mat_id, $m_repli_id, $fifo_move_id, $detail_info, $fifo_repli_id)
    {
        if (($model = MDetail::findOne(['move_id' => $move_id, 'mat_id' => $mat_id, 'm_repli_id' => $m_repli_id, 'fifo_move_id' => $fifo_move_id, 'detail_info' => $detail_info, 'fifo_repli_id' => $fifo_repli_id])) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
