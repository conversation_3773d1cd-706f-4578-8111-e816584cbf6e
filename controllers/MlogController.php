<?php

namespace app\controllers;

use app\models\Mlog;
use app\models\MlogSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use Yii;


/**
 * MlogController implements the CRUD actions for Mlog model.
 */
class MlogController extends Controller
{
    /**
     * @inheritDoc
     */
    public function behaviors()
    {
        return array_merge(
            parent::behaviors(),
            [
                'verbs' => [
                    'class' => VerbFilter::class,
                    'actions' => [
                        'delete' => ['POST'],
                    ],
                ],
                'access' => [
                    'class' => \yii\filters\AccessControl::class,
                    'rules' => [
                        [
                            'allow' => true,
                            'actions' => ['index', 'view', 'viewmove', 'create', 'createontheway', 'createreceived', 'update', 'delete'],
                            'roles' => ['@']
                        ],
                        [
                            'allow' => false                            ]
                    ]
                ]
            ]
        );
    }

    /**
     * Lists all Mlog models.
     *
     * @return string
     */
    public function actionIndex()
    {
        $searchModel = new MlogSearch();
        $dataProvider = $searchModel->search($this->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single Mlog model.
     * @param int $id ID
     * @return string
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Displays a single Mlog model.
     * @param int $id ID
     * @return string
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionViewmove($id)
    {
        $searchModel = new MlogSearch();
        $dataProvider = $searchModel->search($this->request->queryParams);

        return $this->render('viewmove', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'move_id' => $id,
        ]);

    }


    /**
     * Creates a new Mlog model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return string|\yii\web\Response
     */
    public function actionCreate()
    {
        $model = new Mlog();

        if ($this->request->isPost) {
            if ($model->load($this->request->post()) && $model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            }
        } else {
            $model->loadDefaultValues();
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }


    /**
     * Creates a OnTheWay message.
     * @return string|\yii\web\Response
     */
    public function actionCreateontheway($move_id)
    {
        $model = new Mlog();
        $model->move_id = $move_id;
        $model->flags = Mlog::FLAG_ON_THE_WAY;
        $model->user_name = \Yii::$app->user->identity->username;
        $model->note = 'On the way';
        $model->save();
        return $this->redirect(['view', 'id' => $model->id]);
        
    }

    /**
     * Creates a Received message.
     * @return string|\yii\web\Response
     */
    public function actionCreatereceived($move_id)
    {
        // $mystocks = EstockTools::getMystocks($move_id);
        // $model = new Mlog();
        // $model->move_id = $move_id;
        // $model->flags = Mlog::FLAG_RECEIVED;
        // $model->user_name = \Yii::$app->user->identity->username;
        // $model->note = 'Received';
        $transaction = Yii::$app->db->beginTransaction();
        try {
            // $model->save();
            $cmd  = Yii::$app->db->createCommand("call receive_on_the_way(:moveid, :user_name)")
                    ->bindValue(':moveid', $move_id)
                    ->bindValue(':user_name', \Yii::$app->user->identity->username);
             $cmd->execute();
            $transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollBack();
            Yii::$app->session->setFlash('error', $e->getMessage()); 
        } catch (\Throwable $e) {
            $transaction->rollBack();
            throw $e;
        }
        $newid =  Yii::$app->db->createCommand("SELECT max(id) from mlog where move_id=:moveid")
        ->bindValue(':moveid',$move_id)->queryScalar();
        return $this->redirect(['view', 'id' => $newid]);
        
    }

    /**
     * Updates an existing Mlog model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param int $id ID
     * @return string|\yii\web\Response
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($this->request->isPost && $model->load($this->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing Mlog model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param int $id ID
     * @return \yii\web\Response
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the Mlog model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param int $id ID
     * @return Mlog the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Mlog::findOne(['id' => $id])) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
