<?php

namespace app\controllers;

use app\models\MType;
use app\models\MTypeSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * MtypeController implements the CRUD actions for MType model.
 */
class MtypeController extends Controller
{
    /**
     * @inheritDoc
     */
    public function behaviors()
    {
        return array_merge(
            parent::behaviors(),
            [
                'verbs' => [
                    'class' => VerbFilter::class,
                    'actions' => [
                        'delete' => ['POST'],
                    ],
                ],
            ]
        );
    }

    /**
     * Lists all MType models.
     *
     * @return string
     */
    public function actionIndex()
    {
        $searchModel = new MTypeSearch();
        $dataProvider = $searchModel->search($this->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single MType model.
     * @param int $m_type_id M Type ID
     * @return string
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($m_type_id)
    {
        return $this->render('view', [
            'model' => $this->findModel($m_type_id),
        ]);
    }

    /**
     * Creates a new MType model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return string|\yii\web\Response
     */
    public function actionCreate()
    {
        $model = new MType();

        if ($this->request->isPost) {
            if ($model->load($this->request->post()) && $model->save()) {
                return $this->redirect(['index']);
            }
        } else {
            $model->loadDefaultValues();
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing MType model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param int $m_type_id M Type ID
     * @return string|\yii\web\Response
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($m_type_id)
    {
        $model = $this->findModel($m_type_id);

        if ($this->request->isPost && $model->load($this->request->post()) && $model->save()) {
            return $this->redirect(['view', 'm_type_id' => $model->m_type_id]);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing MType model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param int $m_type_id M Type ID
     * @return \yii\web\Response
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($m_type_id)
    {
        $this->findModel($m_type_id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the MType model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param int $m_type_id M Type ID
     * @return MType the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($m_type_id)
    {
        if (($model = MType::findOne(['m_type_id' => $m_type_id])) !== null) {
            return $model;
        }

        throw new NotFoundHttpException(Yii::t('app', 'The requested page does not exist.'));
    }
}
