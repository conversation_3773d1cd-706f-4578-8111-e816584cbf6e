<?php

namespace app\controllers;

use Yii;
use app\models\Tempmove;
use app\models\MUserConfig;
use app\models\TempmoveSearch;
use app\models\Product;
use app\models\ProductSearch;
use app\models\MultiaddForm;
use app\models\MultiaddProductForm;
use app\models\SqlclipForm;
use app\models\SqlclipdiscForm;
use app\models\SqlclipcopyForm;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use app\controllers\EstockTools;


/**
 * TempmoveController implements the CRUD actions for Tempmove model.
 */
class TempmoveController extends Controller
{
  public function behaviors()
  {
    return [
      'verbs' => [
        'class' => VerbFilter::class,
        'actions' => [
          'delete' => ['post'],
        ],
      ],
      'access' => [
        'class' => \yii\filters\AccessControl::class,
        'rules' => [
          [
            'allow' => true,
            'actions' => ['shop', 'update', 'delete', 'createfromshop', 'saveclipboard'],
            'matchCallback' => function ($rule, $action) {
              return EstockTools::isUserShop();
            }
          ],
          [
            'allow' => true,
            'actions' => ['index', 'view', 'createfromconfig', 'update', 'delete', 'pdf', 'savedocument', 'saveclipboard2', 'debugdata', 'multiadd', 'multiaddproduct'],
            'matchCallback' => function ($rule, $action) {
              return EstockTools::isUserStock() || EstockTools::isUserW();
            }
          ],
          [
            'allow' => true,
            'actions' => ['b2b', 'createfromb2b', 'saveclipboard', 'update', 'delete'],
            'matchCallback' => function ($rule, $action) {
              return EstockTools::isUserB2b();
            }
          ],
          [
            'allow' => false
          ]
        ]
      ]
    ];
  }

  /**
   * Lists all Tempmove models.
   * disabled, by permission above
   * @return mixed
   */
  public function actionIndex()
  {
    $searchModel = new TempmoveSearch();
    $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

    return $this->render('index', [
      'searchModel' => $searchModel,
      'dataProvider' => $dataProvider,
    ]);
  }

  /**
   * Lists SHOP Tempmove models.
   * @return mixed
   */
  public function actionShop($tabIndex = 0)
  {

    $mtypes = EstockTools::getShopParams() ? EstockTools::getShopParams()['shopmtypes'] : [];
    $dataProvider = [];
    $dataSumProvider = [];

    foreach ($mtypes as $key => $value) {
      $dataProvider[$key] = [];
      $dataSumProvider[$key] = [];
    }

    $searchModel = new TempmoveSearch();

    foreach ($mtypes as $key => $value) {
      if (($model = Tempmove::find()
        ->select(['clip_id', 'm_type_id', 'stock_id1', 'stock_id2'])
        ->where(['user_name' => Yii::$app->user->identity->username, 'm_type_id' => $value[0], 'stock_id1' => $value[2], 'stock_id2' => $value[3]])
        ->groupBy(['user_name', 'm_type_id', 'clip_id', 'stock_id1', 'stock_id2'])
        ->all()) !== null) {
        //ToDo Je to foreach ale teoreticky len 1 record dostavam
        foreach ($model as $ckey => $clipboard) {
          # get sum of clipboard
          // $sumofclip = Yii::$app->db->createCommand("SELECT sum(pcs) xpcs, (sum(price*pcs*(100-discount))/100)::money xtotal from tempmove where user_name=:user and clip_id=:clip_id")->bindValue(':user',Yii::$app->user->identity->username)->bindValue(':clip_id',$clipboard->clip_id)->queryOne();
          # add subarray dataprovider
          $cl = $searchModel->search(Yii::$app->request->queryParams);
          $cl->sort = ['defaultOrder' => ['kod' => SORT_ASC, 'model' => SORT_ASC]];
          $cl->query->where(['user_name' => Yii::$app->user->identity->username, 'm_type_id' => $value[0], 'clip_id' => $clipboard->clip_id, 'stock_id1' => $clipboard->stock_id1, 'stock_id2' => $clipboard->stock_id2]);
          $dataProvider[$key] = $cl;
          $dataSumProvider[$key] = $this->sumofclip($clipboard->clip_id);
        }
      }
    }

    return $this->render('shopView', [
      'searchModel' => $searchModel,
      'dataProvider' => $dataProvider,
      'dataSumProvider' => $dataSumProvider,
      'mtypes' => $mtypes,
      'tabIndex' => $tabIndex
    ]);
  }


  /**
   * Lists SHOP Tempmove models.
   * @return mixed
   */
  public function actionB2b($tabIndex = 0)
  {

    $mtypes = EstockTools::getB2bParams() ? EstockTools::getB2bParams()['shopmtypes'] : [];
    $dataProvider = [];
    $dataSumProvider = [];

    foreach ($mtypes as $key => $value) {
      $dataProvider[$key] = [];
      $dataSumProvider[$key] = [];
    }

    $searchModel = new TempmoveSearch();

    foreach ($mtypes as $key => $value) {
      if (($model = Tempmove::find()
        ->select(['clip_id', 'm_type_id', 'stock_id1', 'stock_id2'])
        ->where(['user_name' => Yii::$app->user->identity->username, 'm_type_id' => $value[0], 'stock_id1' => $value[2], 'stock_id2' => $value[3]])
        ->groupBy(['user_name', 'm_type_id', 'clip_id', 'stock_id1', 'stock_id2'])
        ->all()) !== null) {
        //ToDo Je to foreach ale teoreticky len 1 record dostavam
        foreach ($model as $ckey => $clipboard) {
          # get sum of clipboard
          // $sumofclip = Yii::$app->db->createCommand("SELECT sum(pcs) xpcs, (sum(price*pcs*(100-discount))/100)::money xtotal from tempmove where user_name=:user and clip_id=:clip_id")->bindValue(':user',Yii::$app->user->identity->username)->bindValue(':clip_id',$clipboard->clip_id)->queryOne();
          # add subarray dataprovider
          $cl = $searchModel->search(Yii::$app->request->queryParams);
          $cl->sort = ['defaultOrder' => ['kod' => SORT_ASC, 'model' => SORT_ASC]];
          $cl->query->where(['user_name' => Yii::$app->user->identity->username, 'm_type_id' => $value[0], 'clip_id' => $clipboard->clip_id, 'stock_id1' => $clipboard->stock_id1, 'stock_id2' => $clipboard->stock_id2]);
          $dataProvider[$key] = $cl;
          $dataSumProvider[$key] = $this->sumofclip($clipboard->clip_id);
        }
      }
    }

    return $this->render('b2bView', [
      'searchModel' => $searchModel,
      'dataProvider' => $dataProvider,
      'dataSumProvider' => $dataSumProvider,
      'mtypes' => $mtypes,
      'tabIndex' => $tabIndex
    ]);
  }




  /**
   * Lists Manager clipboard by clip_id
   * @return mixed
   */
  // public function actionView( $id )
  // {

  //     if ( $modelconfig = MUserConfig::find()->where(['clip_id'=>$id, 'userid'=>Yii::$app->user->id])->one() ) {
  //         $searchModel = new TempmoveSearch();
  //         if (($model = Tempmove::find()
  //                     ->where(['user_name' => Yii::$app->user->identity->username, 'clip_id' => $id])
  //                     ->all() ) !== null) {
  //                     # get sum of clipboard
  //                     $sumofclip = Yii::$app->db->createCommand("SELECT sum(pcs) xpcs, floor(sum(price*pcs*(100-discount)))/100 xtotal from tempmove where user_name=:user and clip_id=:clip_id")->bindValue(':user',Yii::$app->user->identity->username)->bindValue(':clip_id',$id)->queryOne();
  //                     # add subarray dataprovider
  //                     $cl = $searchModel->search(Yii::$app->request->queryParams);
  //                     $cl->sort = ['defaultOrder' => ['kod'=>SORT_ASC, 'model'=>SORT_ASC]];
  //                     $cl->query->where(['user_name' => Yii::$app->user->identity->username,'clip_id' => $id ]);
  //         }

  //         return $this->render('clipView', [
  //             'searchModel' => $searchModel,
  //             'dataProvider' => $cl,
  //             'dataSumProvider' => $sumofclip,
  //             'config_id' => $modelconfig->id,
  //             'clip_id' => $id
  //         ]);

  //   } else {
  //       return $this->render('index');
  //   }
  // }



  /**
   * Creates a new Tempmove model.
   * If creation is successful, the browser will be redirected to the 'view' page.
   * @return mixed
   */
  public function actionCreatefromb2b()
  {
    $model = new Tempmove();

    if ($model->load(Yii::$app->request->post())) { //pridaj namiesto && model.save()

      if (empty($model->tax)) {
        $model->tax = 0;
      }


      if (empty($model->m_type_id)) {
        $model->m_type_id = EstockTools::getB2bParams()['shopmtypes'][0][0];
      }

      if ($model->validate()) {

        if ($model->clip_id == "-1") {
          $model->clip_id = Yii::$app->db->createCommand("SELECT coalesce(max(clip_id)+1,1) from tempmove where user_name=:user")->bindValue(':user', Yii::$app->user->identity->username)->queryScalar();
        }
        try {
          $query = "call pridajasuser(:adr_id, :mat_id, :clip_id, :pcs, :xx, :price, :d1, :d2, :d3, :m_type_id, :price2, :stock_id1,:stock_id2,:text1,:text2,:tempmove_info, :thserial, :user)";
          $cmd =  Yii::$app->db->createCommand($query)
            ->bindValue(':adr_id', EstockTools::getB2bParams()['profile']['adr_id'])
            ->bindValue(':mat_id', $model->mat_id)
            ->bindValue(':clip_id', $model->clip_id)
            ->bindValue(':pcs', $model->pcs)
            ->bindValue(':price', $model->price)
            ->bindValue(':xx', 'xx')
            ->bindValue(':d1', $model->d1)
            ->bindValue(':d2', $model->d2)
            ->bindValue(':d3', $model->d3)
            ->bindValue(':m_type_id', $model->m_type_id)
            ->bindValue(':price2', '')
            ->bindValue(':stock_id1', $model->stock_id1)
            ->bindValue(':stock_id2', $model->stock_id2)
            ->bindValue(':text1', trim($model->text1))
            ->bindValue(':text2', trim($model->text2))
            ->bindValue(':tempmove_info', trim($model->tempmove_info))
            ->bindValue(':thserial', empty($model->thserial) ? null : trim($model->thserial))
            ->bindValue(':user', Yii::$app->user->identity->username);
          $cmd->query();
        } catch (\yii\db\Exception $e) {
          Yii::$app->session->setFlash('error', $e->getMessage());
          return $this->render('createFromB2b', [
            'model' => $model,
            'shopPrice' =>  $model->price_type ?? EstockTools::getB2bParams()['shopmtypes'][0][4],
          ]);
        } catch (\yii\base\Exception $e) {
          Yii::$app->session->setFlash('error', $e);
          return $this->render('createFromB2b', [
            'model' => $model,
            'shopPrice' => $model->price_type ?? EstockTools::getB2bParams()['shopmtypes'][0][4],
          ]);
        }
      } else {
        // add also product table for searching
        $searchModel = new ProductSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('createFromB2b', [
          'model' => $model,
          'searchModel' => $searchModel,
          'dataProvider' => $dataProvider,
          'shopPrice' => $model->price_type ?? EstockTools::getB2bParams()['shopmtypes'][0][4],
        ]);
      }

      return $this->redirect(['b2b', 'tabIndex' => $model->m_type_id . $model->stock_id1 . $model->stock_id2]);
    } else {

      $model->clip_id = Yii::$app->request->get()['clip_id'] || "-1";
      if (!empty(Yii::$app->request->get()['stock_id1'])) {
        $model->stock_id1 = Yii::$app->request->get()['stock_id1'];
      }
      if (!empty(Yii::$app->request->get()['stock_id2'])) {
        $model->stock_id2 = Yii::$app->request->get()['stock_id2'];
      }
      $model->m_type_id = Yii::$app->request->get()['mtype'];
      // add also product table for searching
      $searchModel = new ProductSearch();
      $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
      $kodfilters = explode(',', EstockTools::getB2bParams()['profile']['productWhere']);
      Yii::debug($kodfilters[0] ?? 'n/a');
      Yii::debug($kodfilters[1] ?? 'n/a');
      Yii::debug(count($kodfilters));
      switch (count($kodfilters)) {
        case 0:
          break;
        case 1:
          $dataProvider->query->andFilterWhere(['like', 'kod', $kodfilters[0]]);
          break;
        case 2:
          $dataProvider->query->andFilterWhere(['or', ['like', 'kod', $kodfilters[0]], ['like', 'kod', $kodfilters[1]]]);
          break;
        case 3:
          $dataProvider->query->andFilterWhere(['or', ['like', 'kod', $kodfilters[0]], ['like', 'kod', $kodfilters[1]], ['like', 'kod', $kodfilters[2]]]);
          break;
        case 4:
          $dataProvider->query->andFilterWhere(['or', ['like', 'kod', $kodfilters[0]], ['like', 'kod', $kodfilters[1]], ['like', 'kod', $kodfilters[2]], ['like', 'kod', $kodfilters[3]]]);
          break;
        case 5:
        default:
          $dataProvider->query->andFilterWhere(['or', ['like', 'kod', $kodfilters[0]], ['like', 'kod', $kodfilters[1]], ['like', 'kod', $kodfilters[2]], ['like', 'kod', $kodfilters[3]], ['like', 'kod', $kodfilters[4]]]);
          break;
      }

      return $this->render('createFromB2b', [
        'model' => $model,
        'searchModel' => $searchModel,
        'dataProvider' => $dataProvider,
        'shopPrice' => EstockTools::getB2bParams()['shopmtypes'][0][4],
      ]);
    }
  }





  /**
   * Creates a new Tempmove model.
   * If creation is successful, the browser will be redirected to the 'view' page.
   * @return mixed
   */
  public function actionCreatefromshop()
  {
    $model = new Tempmove();

    if ($model->load(Yii::$app->request->post())) { //pridaj namiesto && model.save()

      if ($model->validate(array('pcs'))) {

        if ($model->clip_id == "-1") {
          $model->clip_id = Yii::$app->db->createCommand("SELECT coalesce(max(clip_id)+1,1) from tempmove where user_name=:user")->bindValue(':user', Yii::$app->user->identity->username)->queryScalar();
        }
        try {

          $query = "call pridajasuser(:adr_id, :mat_id, :clip_id, :pcs, :xx, :price, :d1, :d2, :d3, :m_type_id, :price2, :stock_id1,:stock_id2,:text1,:text2,:tempmove_info, :thserial, :user)";

          // ZMENA 11.8.2022: 87,97,147,149 nechame s DPH v clipboarde
          //
          if (in_array($model->m_type_id, [137, 138, 148, 167, 168])) {
            $model->price = $model->price / EstockTools::ourCompanies['racio']['dph'];
          }
          // if( $model->m_type_id == 504 or $model->m_type_id == 505 ) {
          //     $model->price = $model->price / EstockTools::ourCompanies['chrono']['dph'];
          // }
          $cmd =  Yii::$app->db->createCommand($query)
            ->bindValue(':adr_id', EstockTools::getUserAdrId())
            ->bindValue(':mat_id', $model->mat_id)
            ->bindValue(':clip_id', $model->clip_id)
            ->bindValue(':pcs', $model->pcs)
            ->bindValue(':price', $model->price)
            ->bindValue(':xx', 'xx')
            ->bindValue(':d1', $model->d1)
            ->bindValue(':d2', $model->d2)
            ->bindValue(':d3', $model->d3)
            ->bindValue(':m_type_id', $model->m_type_id)
            ->bindValue(':price2', '')
            ->bindValue(':stock_id1', $model->stock_id1)
            ->bindValue(':stock_id2', $model->stock_id2)
            ->bindValue(':text1', trim(is_null($model->text1) ? '' : $model->text1))
            ->bindValue(':text2', trim(is_null($model->text2) ? '' : $model->text2))
            ->bindValue(':tempmove_info', trim(is_null($model->tempmove_info) ? '' : $model->tempmove_info))
            ->bindValue(':thserial', empty($model->thserial) ? null : trim($model->thserial))
            ->bindValue(':user', Yii::$app->user->identity->username);
          $cmd->query();
        } catch (\yii\db\Exception $e) {
          Yii::$app->session->setFlash('error', $e->getMessage());
          return $this->render('createFromShop', [
            'model' => $model,
            'shopPrice' => $model->price_type ?? EstockTools::getCurrentClipboardPrice($model->m_type_id) ?? EstockTools::getShopParams()['shopmtypes'][0][4],
          ]);
        } catch (\yii\base\Exception $e) {
          Yii::$app->session->setFlash('error', $e);
          return $this->render('createFromShop', [
            'model' => $model,
            'shopPrice' => $model->price_type ?? EstockTools::getCurrentClipboardPrice($model->m_type_id) ?? EstockTools::getShopParams()['shopmtypes'][0][4],
          ]);
        }
      } else {
        // add also product table for searching
        $searchModel = new ProductSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('createFromShop', [
          'model' => $model,
          'searchModel' => $searchModel,
          'dataProvider' => $dataProvider,
          'shopPrice' => $model->price_type ?? EstockTools::getCurrentClipboardPrice($model->m_type_id) ?? EstockTools::getShopParams()['shopmtypes'][0][4],
        ]);
      }

      return $this->redirect(['shop', 'tabIndex' => $model->m_type_id . $model->stock_id1 . $model->stock_id2]);
    } else {

      $model->clip_id = Yii::$app->request->get()['clip_id'] || "-1";
      if (!empty(Yii::$app->request->get()['stock_id1'])) {
        $model->stock_id1 = Yii::$app->request->get()['stock_id1'];
      }
      if (!empty(Yii::$app->request->get()['stock_id2'])) {
        $model->stock_id2 = Yii::$app->request->get()['stock_id2'];
      }
      $model->m_type_id = Yii::$app->request->get()['mtype'];
      // add also product table for searching
      $searchModel = new ProductSearch();
      $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

      if ($model->m_type_id == "147") {
        Yii::$app->session->setFlash('info', "Tvoríte dodací list na predaj cez terminál, objednávka z WDL.sk eshopu. Zmena JE POTREBNE zadávať cenu s DPH, ako v ostatných pohyboch.");
      }
      if ($model->m_type_id == "149") {
        Yii::$app->session->setFlash('info', "Tvoríte dodací list na predaj cez terminál, objednávka z WOW.sk eshopu. Zmena  JE POTREBNE zadávať cenu s DPH, ako v ostatných pohyboch.");
      }
      // if ( $model->m_type_id == "504" ){
      //         Yii::$app->session->setFlash('info', "You are making Szamla. Use prices with VAT." );
      // }
      // if ( $model->m_type_id == "505" ){
      //         Yii::$app->session->setFlash('info', "You are making Jóváírás. Use prices with VAT." );
      // }
      // echo "<pre>";
      // print_r( $model );//->clip_id;// EstockTools::getCurrentClipboardPrice($model->m_type_id);/// ?? EstockTools::getShopParams()['shopmtypes'][0][4];
      return $this->render('createFromShop', [
        'model' => $model,
        'searchModel' => $searchModel,
        'dataProvider' => $dataProvider,
        'shopPrice' => $model->price_type ?? EstockTools::getCurrentClipboardPrice($model->m_type_id) ?? EstockTools::getShopParams()['shopmtypes'][0][4],
      ]);
    }
  }



  /**
   * Creates a new Tempmove model.
   * If creation is successful, the browser will be redirected to the 'view' page.
   * @return mixed
   */
  public function actionCreatefromconfig($id)
  {
    $model = new Tempmove();
    $modelconfig = MUserConfig::find()->where(['id' => $id])->one();
    if ($modelconfig->userid != Yii::$app->user->identity->id) {
      Yii::$app->session->setFlash('error', 'This is not your clipboard..' . $id);
    }

    $searchModel = new TempmoveSearch();
    $productsearchModel = new ProductSearch();
    $dataProvider = $productsearchModel->search(Yii::$app->request->queryParams);

    $cl = $searchModel->search(Yii::$app->request->queryParams);
    $cl->sort = ['defaultOrder' => ['kod' => SORT_ASC, 'model' => SORT_ASC]];
    $cl->query->andWhere(['user_name' => Yii::$app->user->identity->username, 'clip_id' => $modelconfig->clip_id]);
    $cl->pagination->pageSize = 10000;

    if ($model->load(Yii::$app->request->post())) {


      if ($model->validate(['pcs'])) {

        try {
          $query = "call pridajasuser(:adr_id, :mat_id, :clip_id, :pcs, :xx, :price, :d1, :d2, :d3, :m_type_id, :price2, :stock_id1,:stock_id2,:text1,:text2,:tempmove_info, :thserial, :user)";
          $cmd =  Yii::$app->db->createCommand($query)
            ->bindValue(':adr_id', $modelconfig->adr_id)
            ->bindValue(':mat_id', $model->mat_id)
            ->bindValue(':clip_id', $model->clip_id)
            ->bindValue(':pcs', $model->pcs)
            ->bindValue(':price', $model->price)
            ->bindValue(':xx', 'xx')
            ->bindValue(':d1', isset($model->d1) && preg_match('/^\d{4}-\d{2}-\d{2}$/', $model->d1) ? $model->d1 : '9999-12-31')
            ->bindValue(':d2', $modelconfig->d2)
            ->bindValue(':d3', $modelconfig->d3)
            ->bindValue(':m_type_id', $modelconfig->m_type_id)
            ->bindValue(':price2', $modelconfig->price)
            ->bindValue(':stock_id1', $modelconfig->stock_id1)
            ->bindValue(':stock_id2', $modelconfig->stock_id2)
            ->bindValue(':text1', trim($modelconfig->text1))
            ->bindValue(':text2', trim($modelconfig->text2))
            ->bindValue(':tempmove_info', trim($model->tempmove_info))
            ->bindValue(':thserial', empty($model->thserial) ? null : trim($model->thserial))
            ->bindValue(':user', Yii::$app->user->identity->username);


          $cmd->query();
        } catch (\yii\db\Exception $e) {
          Yii::$app->session->setFlash('error', $e->getMessage());
          return $this->render('clipView', [
            'model' => $model,
            'modelconfig' => $modelconfig,
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'clipdataProvider' => $cl,
            'productsearchModel' => $productsearchModel,
            'dataSumProvider' => $this->sumofclip($modelconfig->clip_id),
            'config_id' => $modelconfig->id,
            'clip_id' => $modelconfig->clip_id,
            'modelMultiadd' => new MultiaddForm(),
            'newProduct' => new Product(),
            'modelMultiaddProduct' => new MultiaddProductForm(),
            'sqlclipdiscForm' => new SqlclipdiscForm(),
            'sqlclipForm' => new SqlclipForm(),
            'sqlclipcopyForm' => new SqlclipcopyForm(),
            'sqlclipformulaForm' => new \yii\base\DynamicModel(['formulasql']),

          ]);
        }
      } else {

        return $this->render('clipView', [
          'model' => $model,
          'modelconfig' => $modelconfig,
          'searchModel' => $searchModel,
          'productsearchModel' => $productsearchModel,
          'newProduct' => new Product(),
          'dataProvider' => $dataProvider,
          'clipdataProvider' => $cl,
          'dataSumProvider' => $this->sumofclip($modelconfig->clip_id),
          'config_id' => $modelconfig->id,
          'clip_id' => $modelconfig->clip_id,
          'modelMultiadd' => new MultiaddForm(),
          'modelMultiaddProduct' => new MultiaddProductForm(),
          'sqlclipdiscForm' => new SqlclipdiscForm(),
          'sqlclipForm' => new SqlclipForm(),
          'sqlclipcopyForm' => new SqlclipcopyForm(),
          'sqlclipformulaForm' => new \yii\base\DynamicModel(['formulasql']),
        ]);
      }

      return $this->render('clipView', [
        'model' => $model,
        'modelconfig' => $modelconfig,
        'searchModel' => $searchModel,
        'productsearchModel' => $productsearchModel,
        'newProduct' => new Product(),
        'dataProvider' => $dataProvider,
        'clipdataProvider' => $cl,
        'dataSumProvider' => $this->sumofclip($modelconfig->clip_id),
        'config_id' => $modelconfig->id,
        'clip_id' => $modelconfig->clip_id,
        'modelMultiadd' => new MultiaddForm(),
        'modelMultiaddProduct' => new MultiaddProductForm(),
        'sqlclipdiscForm' => new SqlclipdiscForm(),
        'sqlclipForm' => new SqlclipForm(),
        'sqlclipcopyForm' => new SqlclipcopyForm(),
        'sqlclipformulaForm' => new \yii\base\DynamicModel(['formulasql']),

      ]);
    } else {


      //Check for multiadd 
      $this->multiadd($id, $modelconfig);

      //Check for sql 
      $this->sqlMod($id, $modelconfig);

      //Check for sql 
      if (EstockTools::isUserW()) {
        $this->sqlFormula($id, $modelconfig);
      }

      //Check for discount change form 
      $this->sqlDisc($id, $modelconfig);

      //Check for copy from movement
      $this->sqlCopymov($id, $modelconfig);

      //Check for product import 
      $this->multiaddproduct($id, $modelconfig);

      //Create new model
      $this->createproduct($id);

      return $this->render('clipView', [
        'model' => $model,
        'modelconfig' => $modelconfig,
        'searchModel' => $searchModel,
        'productsearchModel' => $productsearchModel,
        'newProduct' => new Product(),
        'dataProvider' => $dataProvider,
        'clipdataProvider' => $cl,
        'dataSumProvider' => $this->sumofclip($modelconfig->clip_id),
        'config_id' => $modelconfig->id,
        'clip_id' => $modelconfig->clip_id,
        'modelMultiadd' => new MultiaddForm(),
        'modelMultiaddProduct' => new MultiaddProductForm(),
        'sqlclipdiscForm' => new SqlclipdiscForm(),
        'sqlclipForm' => new SqlclipForm(),
        'sqlclipcopyForm' => new SqlclipcopyForm(),
        'sqlclipformulaForm' => new \yii\base\DynamicModel(['formulasql']),
      ]);
    }
  }

  /**
   * Creates a new Product model.
   * If creation is successful, returns true
   * @return mixed
   */
  public function createproduct($id)
  {
    $model = new Product();
    if ($model->load(Yii::$app->request->post())) {
      if (empty($model->mat_id)) {
        $model->mat_id = Yii::$app->db->createCommand("select max(mat_id)+1 from product")->queryScalar();
      }
      if (empty($model->ean13)) {
        $model->ean13 = $model->mat_id;
      }
      try {
        $model->save(false);
      } catch (\yii\db\Exception $e) {
        Yii::$app->session->setFlash('error', $e->getMessage());
        return false;
      } catch (\yii\base\Exception $e) {
        Yii::$app->session->setFlash('error', $e);
        return false;
      }
      Yii::$app->session->setFlash('success', "Model " . $model->model . " created.");
      return true;
    } else {
      return false;
    }
  }


  private function sumofclip($clip_id)
  {

    return Yii::$app->db->createCommand("SELECT sum(pcs) xpcs,
         (floor(sum(price*pcs*(100-discount)))/100)::numeric(11,2) xtotal,
         (floor(sum(((100+tax)/100)*price*pcs*(100-discount)))/100)::numeric(11,2) xtotaltax
         from tempmove where user_name=:user and clip_id=:clip_id")->bindValue(':user', Yii::$app->user->identity->username)->bindValue(':clip_id', $clip_id)->queryOne();
  }

  private function multiadd($id, $modelconfig)
  {


    $model = new MultiaddForm();
    if ($model->load(Yii::$app->request->post()) && $model->validate()) {

      try {

        $mactiontxt = "";
        $merrortxt = "";
        $csla = explode("\n", $model->multiadd);
        for ($i = 0; $i < count($csla); $i++) {
          $riadok = explode(",", str_replace("\t", "", $csla[$i]));
          //XXX.DAT - TOTO v pgestock nedavam, davam len:
          //mat_id alebo ean13 alebo model
          if (!isset($riadok[2])) {
            $riadok[2] = "";
          }
          if (!isset($riadok[3])) {
            $riadok[3] = "";
          }
          if (empty($modelconfig->price)) {
            $modelconfig->price = "p0";
          }
          if (settype($riadok[1], "double")) {
            $pcs = $riadok[1] < 1 ? 1 : $riadok[1];
          } else {
            $pcs = 1;
          }
          // switch first column
          switch ($model->multiaddSwitch) {
            case "1":
              $_query = "select " . trim($modelconfig->price) . " prrx, 'X' , mat_id from product where model='" . trim($riadok[0]) . "'";
              break;
            case "2":
              $_query = "select " . trim($modelconfig->price) . " prrx, 'X' , mat_id from product where mat_id='" . trim($riadok[0]) . "'";
              break;
            default:
              if ($riadok[0] > 999999) {
                $_query = "select " . trim($modelconfig->price) . " prrx, 'X' , mat_id from product where ean13='" . trim($riadok[0]) . "'";
              } elseif ($riadok[0] > 99999) {
                $_query = "select " . trim($modelconfig->price) . " prrx, 'X' , mat_id from product where mat_id='" . trim($riadok[0]) . "'";
              } else {
                //Try model also...
                $_query = "select " . trim($modelconfig->price) . " prrx, 'X' , mat_id from product where model='" . trim($riadok[0]) . "'";
              }
              break;
          }

          $mymatid = Yii::$app->db->createCommand($_query)->queryOne();

          if (empty($mymatid['mat_id'])) {
            //Try model also...
            $_query = "select " . trim($modelconfig->price) . " prrx, 'X' , mat_id from product where model='" . trim($riadok[0]) . "'";
            $mymatid = Yii::$app->db->createCommand($_query)->queryOne();
          }

          if (empty($mymatid['mat_id'])) {
            $merrortxt .= $riadok[0] . " <br>";
          } else {
            if (!empty($riadok[2])) {
              $price = $riadok[2];
            } else {
              $price = $mymatid['prrx'];
            }
            //B2B osetrenie pre tax=0: strcim do pridaj Ecko
            if (EstockTools::isUserB2b()) {
              $bBezDph = 'E';
            } else {
              $bBezDph = 'xx';
            }

            try {
              $query = "call pridajasuser(:adr_id, :mat_id, :clip_id, :pcs, :xx, :price, :d1, :d2, :d3, :m_type_id, :price2, :stock_id1,:stock_id2,:text1,:text2,:tempmove_info, :thserial, :user)";
              $cmd =  Yii::$app->db->createCommand($query)
                ->bindValue(':adr_id', $modelconfig->adr_id)
                ->bindValue(':mat_id', $mymatid['mat_id'])
                ->bindValue(':clip_id', $modelconfig->clip_id)
                ->bindValue(':pcs', $pcs)
                ->bindValue(':price', $price)
                ->bindValue(':xx', $bBezDph)
                ->bindValue(':d1', $modelconfig->d1)
                ->bindValue(':d2', $modelconfig->d2)
                ->bindValue(':d3', $modelconfig->d3)
                ->bindValue(':m_type_id', $modelconfig->m_type_id)
                ->bindValue(':price2',  $modelconfig->price)
                ->bindValue(':stock_id1', $modelconfig->stock_id1)
                ->bindValue(':stock_id2', $modelconfig->stock_id2)
                ->bindValue(':text1', trim($modelconfig->text1))
                ->bindValue(':text2', trim($modelconfig->text2))
                ->bindValue(':tempmove_info', trim(str_replace('~', ',', $riadok[3])))
                ->bindValue(':thserial', null)
                ->bindValue(':user', Yii::$app->user->identity->username);

              $cmd->query();

              $mactiontxt .= "Added " . ($riadok[0] ?? '') . ' ' . ($riadok[1] ?? '') . ' ' . ($riadok[2] ?? '') . ' ' . ($riadok[3] ?? '') . "&nbsp;&nbsp;&nbsp;&nbsp;\n";
            } catch (\yii\db\Exception $e) {
              Yii::$app->session->setFlash('error', $e->getMessage());
              return false;
            }
          }
        }
      } catch (\yii\base\Exception $e) {
        Yii::$app->session->setFlash('error', $e->getMessage());
      }

      if (!empty($merrortxt)) {
        Yii::$app->session->setFlash('error', "Not found:<br>" . $merrortxt);
      }
      if (!empty($mactiontxt)) {
        Yii::$app->session->setFlash('success', $mactiontxt);
      }
    }

    return true;
  }


  /**
   * Imports product.
   */
  private function multiaddproduct($id, $modelconfig)
  {

    //TO DO
    // Add index
    // ALTER TABLE public.product
    // ADD CONSTRAINT product_model_pdescr UNIQUE (kod, model, pdescr);
    //
    $model = new MultiaddProductForm();
    if ($model->load(Yii::$app->request->post()) && $model->validate()) {

      try {

        $mactiontxt = "Executed<hr>";
        $csla = explode("\n", $model->multiadd);
        $columns = explode(",", str_replace("\t", "", $csla[0]));
        for ($i = 1; $i < count($csla); $i++) {
          $riadok = explode(",", str_replace("\t", "", $csla[$i]));
          if (count($riadok) > 1) {
            $vals = "";
            foreach ($riadok as $key => $value) {
              $vals .= ",'" . trim($value) . "'";
            }
            if (strpos(strtolower($csla[0]), 'ean13') === false) {
              $_query = "insert into product (mat_id,ean13," . $csla[0] . ") values (
                  (select max(mat_id)+1 from product),(select max(mat_id)+1 from product)" . $vals . ")";
            } else {
              $_query = "insert into product (mat_id," . $csla[0] . ") values (
                  (select max(mat_id)+1 from product)" . $vals . ")";
            }
            Yii::$app->db->createCommand($_query)->query();
            $mactiontxt .= $_query . "<br>\n";
          }
        }
      } catch (\yii\db\Exception $e) {
        Yii::$app->session->setFlash('error', $e->getMessage());
      }

      Yii::$app->session->setFlash('success', $mactiontxt);
    }

    return true;
  }

  /*
    * Modify clipboard by SQL formula
    *
    */
  private function sqlFormula($id, $modelconfig)
  {
    $model = new \yii\base\DynamicModel(['formulasql']);
    $model->addRule(['formulasql'], 'string', ['min' => 2, 'max' => 255]);
    if ($model->load(Yii::$app->request->post())) {
      if ($model->validate()) {
        $_query = "update tempmove clip set " .
          str_replace('%', '', stripslashes($model->formulasql)) .
          " where clip.user_name = :username and clip_id = :clipid";
        try {
          $cmd =  Yii::$app->db->createCommand($_query)
            ->bindValue(':username', Yii::$app->user->identity->username)
            ->bindValue(':clipid', $modelconfig->clip_id);

          $cmd->query();
        } catch (\yii\db\Exception $e) {
          Yii::$app->session->setFlash('error', $e->getMessage());
          return false;
        }
        Yii::$app->session->setFlash('success', 'Clipboard set to: ' . $model->formulasql);
        return true;
      } else {
        Yii::$app->session->setFlash('error', "formula form verification failed!");
      }
    }

    return false;
  }


  /*
    * Modify clipboard by SQL command
    *
    */
  private function sqlMod($id, $modelconfig)
  {

    $form = new SqlclipForm();
    if ($form->load(Yii::$app->request->post()) && $form->validate()) {
      // var_dump($form);
      // var_dump(Yii::$app->request->post());

      if ($form->isUpdate == "update") {

        $_query = "update tempmove clip set " .
          str_replace('%', '', stripslashes($form->sqlCommand)) .
          " where clip.user_name = :username and clip_id = :clipid";
        if (!empty($form->sqlWhere)) {
          $_query .= " and (" . stripslashes($form->sqlWhere) . " ) ";
        }
      } elseif ($form->isUpdate == "delete") {
        $_query = "delete from tempmove where user_name = :username and clip_id = :clipid and (" . stripslashes($form->sqlWhere) . ")";
      }
      if ($form->isErase == true) {
        $_query = "delete from tempmove where user_name = :username and clip_id = :clipid ";
      }
      if (isset($_query)) {
        try {
          $cmd =  Yii::$app->db->createCommand($_query)
            ->bindValue(':username', Yii::$app->user->identity->username)
            ->bindValue(':clipid', $modelconfig->clip_id);

          $cmd->query();
        } catch (\yii\db\Exception $e) {
          Yii::$app->session->setFlash('error', $e->getMessage());
          return false;
        }
      }
      return true;
    }
    return false;
  }

  private function sqlDisc($id, $modelconfig)
  {


    $form = new SqlclipdiscForm();
    if ($form->load(Yii::$app->request->post()) && $form->validate()) {

      $add = 1;
      $percent = '0';
      $amount = '0';
      $sqlfilter = '';
      if ($form->dradio === "rewrite") {
        $add = 0;
      }
      if (!empty($form->dperc)) {
        $amount = $form->dperc  + 0;
        $percent = 1;
        $sqlfilter = str_replace('%', '', stripslashes($form->dfilter));
      } else {
        $amount = $form->damount + 0;
        $percent = 0;
      }
      $_query = "call discountasuser( $add, $percent, $amount, :sqlfilter, :clipid, :username)";
      try {

        $cmd =  Yii::$app->db->createCommand($_query)
          ->bindValue(':sqlfilter', $sqlfilter)
          ->bindValue(':username', Yii::$app->user->identity->username)
          ->bindValue(':clipid', $modelconfig->clip_id);

        $cmd->query();
      } catch (\yii\db\Exception $e) {
        Yii::$app->session->setFlash('error', $e->getMessage());
        return false;
      }
      return true;
    }
    return false;
  }


  private function sqlCopymov($id, $modelconfig)
  {


    $form = new SqlclipcopyForm();
    if ($form->load(Yii::$app->request->post()) && $form->validate()) {

      if ($form->dradio === "rewrite") {
        try {

          $cmd =  Yii::$app->db->createCommand("delete from tempmove where clip_id=:clipid and user_name=:username")
            ->bindValue(':username', Yii::$app->user->identity->username)
            ->bindValue(':clipid', $modelconfig->clip_id);

          $cmd->query();
        } catch (\yii\db\Exception $e) {
          Yii::$app->session->setFlash('error', $e->getMessage());
          return false;
        }
      }

      $_query = "insert into tempmove( mat_id,pcs,price,tax,discount,all1,all2,all3,all4,all5,
                                  ean13,kod,model,pdescr,unit,currency,clip_id,d1,d2,d3,
                                  user_name,tempmove_info,fifo_move_id,m_type_id,stock_id1,stock_id2,adr_id, thserial ) 
                                  select d.mat_id,d.pcs,d.price,d.tax,d.discount,0,0,0,0,0,
                                  ean13,kod,model,pdescr,p.unit,d.currency,:clipid,:d1,:d2,:d3,
                                  :username,detail_info,1,:m_type_id,:stock_id1,:stock_id2,:adr_id,
                                  (select thserial from thserials where move_id=d.move_id and mat_id=d.mat_id order by thserial limit 1) thserial
                                  from m_detail d join product p on d.mat_id=p.mat_id
                                  where d.move_id=:move_id
                          on conflict (mat_id, user_name, clip_id, tempmove_info)
                          do update set pcs = tempmove.pcs + EXCLUDED.pcs";
      try {

        $cmd =  Yii::$app->db->createCommand($_query)
          ->bindValue(':move_id', $form->dmoveid)
          ->bindValue(':username', Yii::$app->user->identity->username)
          ->bindValue(':m_type_id', $modelconfig->m_type_id)
          ->bindValue(':d1', $modelconfig->d1)
          ->bindValue(':d2', $modelconfig->d2)
          ->bindValue(':d3', $modelconfig->d3)
          ->bindValue(':stock_id1', $modelconfig->stock_id1)
          ->bindValue(':stock_id2', $modelconfig->stock_id2)
          ->bindValue(':adr_id', $modelconfig->adr_id)
          ->bindValue(':clipid', $modelconfig->clip_id);

        $cmd->query();
      } catch (\yii\db\Exception $e) {
        Yii::$app->session->setFlash('error', $e->getMessage());
        return false;
      }
      return true;
    }
    return false;
  }

  /**
   * Save MOP, Objednavka, etc.
   * @return mixed
   */
  // public function actionSavedocument()
  // {

  //     $formmodel = new \app\models\SaveClipForm();
  //     Yii::debug($formmodel);
  //     if ($formmodel->load(Yii::$app->request->post()) ) {

  //         if (($model = Tempmove::find()
  //                 ->select(['clip_id','m_type_id','d1'])
  //                 ->where(['user_name' => Yii::$app->user->identity->username, 'm_type_id' => $formmodel->mtype, 'clip_id' => $formmodel->clip_id])
  //                 ->one() ) !== null) {

  //             try {
  //                 $query = "CALL newpaper(:m_type_id, 'x', :d1, :d2, :d3, 0, :text1,:text2, :adr_id, :user, :stock_id1,:stock_id2,'x', :adr_repli_id, :clip_id)";            
  //                 $cmd =   Yii::$app->db->createCommand($query)
  //                                     ->bindValue(':m_type_id', $model->m_type_id)
  //                                     ->bindValue(':d1', is_null($model->d1) ? date('Y-m-d') : $model->d1 )
  //                                     ->bindValue(':d2', is_null($model->d2) ? date('Y-m-d') : $model->d2 )
  //                                     ->bindValue(':d3', is_null($model->d3) ? date('Y-m-d') : $model->d3 )
  //                                     ->bindValue(':text1', is_null($model->text1) ? 'Saved from NEW system':$model->text1 )
  //                                     ->bindValue(':text2', $model->text2)
  //                                     ->bindValue(':adr_id', EstockTools::getUserAdrId())
  //                                     ->bindValue(':user', Yii::$app->user->identity->username)
  //                                     ->bindValue(':stock_id1', $model->stock_id1)
  //                                     ->bindValue(':stock_id2', $model->stock_id2)
  //                                     ->bindValue(':adr_repli_id', EstockTools::getUserAdrRepliId())
  //                                     ->bindValue(':clip_id', $model->clip_id);

  //                 $cmd2 =   Yii::$app->db->createCommand("delete from tempmove where  user_name = :user and clip_id = :clip_id")
  //                                     ->bindValue(':user', Yii::$app->user->identity->username)
  //                                     ->bindValue(':clip_id', $model->clip_id);
  //                 $cmd2->execute();
  //             } catch (\yii\db\Exception $e) {
  //                 Yii::$app->session->setFlash('error',$e->getMessage() );
  //                 Yii::debug($e);
  //                 return $this->redirect(['shop']);

  //             }catch (Exception $e) {

  //                 Yii::$app->session->setFlash('error',$e);
  //                 return $this->redirect(['shop']);
  //             }
  //                 Yii::$app->session->setFlash('success','OK, SAVED');
  //                 return $this->redirect(['shop']);
  //             } else {
  //                 Yii::$app->session->setFlash('error','ACTION DISABLED, class instance unknown');
  //                 return $this->redirect(['shop']);
  //             }
  //         } else {

  //                 Yii::$app->session->setFlash('error','WRONG POST ACTION type');
  //                 return $this->redirect(['shop']);
  //         }

  // }


  public function actionDebugdata()
  {
    # code...
    echo EstockTools::getUserRepliId();
    echo "...";
    echo EstockTools::getUserAdrRepliId();

    echo "...";
    print_r(EstockTools::getShopParams(), false);
  }


  /**
   * Save clipboard for B2B and SHOP
   */
  public function actionSaveclipboard()
  {
    $formmodel = new \app\models\SaveClipForm();

    if ($formmodel->load(Yii::$app->request->post())) {

      if (EstockTools::isUserB2b()) {
        $redirectview = "b2b";
        $xtext1 = 'B2B';
        $xtext2 = is_null($formmodel->text1) ? '' : $formmodel->text1;
        $xtext2 .= is_null($formmodel->text2) ? '' : $formmodel->text2;
      } else {
        $redirectview = "shop";
        $xtext1 = is_null($formmodel->text1) ? '' : $formmodel->text1;
        $xtext2 = is_null($formmodel->text2) ? '' : $formmodel->text2;
      }
      if ($formmodel->validate()) {

        if (($model = Tempmove::find()
          ->select(['clip_id', 'm_type_id', 'd1', 'stock_id1', 'stock_id2', 'd2', 'd3'])
          ->where(['user_name' => Yii::$app->user->identity->username, 'clip_id' => $formmodel->clip_id])
          ->one()) !== null) {

          if (is_null($formmodel->adr_id)) {
            // If ESHOP then 6234, else get user adr_id
            $xadrid = in_array($model->m_type_id, [137, 138, 147, 148, 149, 504, 505, 167, 168])  ? 6234 : EstockTools::getUserAdrId();
          } else {
            // set from form for shop mtype=20
            $xadrid = $formmodel->adr_id;
          }


          if ($model->m_type_id == 17 && $redirectview == "shop") {
            $xtext1 = 'B2B ' . $xtext1;
          }


          //   if( $model->m_type_id == 14 ) {
          //     $model->stock_id1 = 'sj';
          //       if( EstockTools::getUserAdrRepliId() == 2 ){
          //           $model->stock_id2 = 's2';
          //       } else {
          //           $model->stock_id2 = 's0';
          //       }
          // }
          //   else if( $model->m_type_id == 87 ) {
          //       $model->stock_id1 = 'sj';
          //   }
          //   else if( $model->m_type_id == 30 ) {
          //       $model->stock_id1 = 'sj';
          //   }
          //   else if( $model->m_type_id == 97 ) {
          //     $model->stock_id2 = 'sj';
          // }
          //   else if( $model->m_type_id == 99 ) {
          //       $model->stock_id2 = 'sj';
          //   }


          $query = "call newpaper(:m_type_id, 'x', :d1, :d2, :d3, 0, :text1,:text2, :adr_id, :user, :stock_id1,:stock_id2,:final_stock, :adr_repli_id, :clip_id)";
          $cmd =   Yii::$app->db->createCommand($query)
            ->bindValue(':m_type_id', $model->m_type_id)
            ->bindValue(':d1', is_null($formmodel->d1) ? date('Y-m-d') : $formmodel->d1)
            ->bindValue(':d2', is_null($model->d2) ? date('Y-m-d') : $model->d2)
            ->bindValue(':d3', is_null($model->d3) ? date('Y-m-d') : $model->d3)
            ->bindValue(':text1', $xtext1)
            ->bindValue(':text2', $xtext2)
            ->bindValue(':adr_id', $xadrid)
            ->bindValue(':user', Yii::$app->user->identity->username)
            ->bindValue(':stock_id1', $model->stock_id1)
            ->bindValue(':stock_id2', $model->stock_id2)
            ->bindValue(':final_stock', isset($formmodel->final_stock)  ? $formmodel->final_stock : 'x')
            ->bindValue(':adr_repli_id', $formmodel->x)
            ->bindValue(':clip_id', $model->clip_id);
          //ADRREPLI je len historicky parameter, pouzil som ho na zapis X ako priznaku DPH pre 87

          if ($formmodel->also_invoice == 1) {
            // create also invoice - m_type_Id= 150
            $query2 = "call newpaper(:m_type_id, 'x', :d1, :d2, :d3, 0, :text1,:text2, :adr_id, :user, :stock_id1,:stock_id2,:final_stock, :adr_repli_id, :clip_id)";
            $cmd2 =  Yii::$app->db->createCommand($query2)
              ->bindValue(':m_type_id', EstockTools::getAdditionalMtypeforShop($model->m_type_id))
              ->bindValue(':d1', is_null($formmodel->d1) ? date('Y-m-d') : $formmodel->d1)
              ->bindValue(':d2', is_null($model->d2) ? date('Y-m-d') : $model->d2)
              ->bindValue(':d3', date('Y-m-d', strtotime('+10 days')))
              ->bindValue(':text1', $xtext1)
              ->bindValue(':text2', $xtext2)
              ->bindValue(':adr_id', $xadrid)
              ->bindValue(':user', Yii::$app->user->identity->username)
              ->bindValue(':stock_id1', $model->stock_id1)
              ->bindValue(':stock_id2', $model->stock_id2)
              ->bindValue(':final_stock', isset($formmodel->final_stock)  ? $formmodel->final_stock : 'x')
              ->bindValue(':adr_repli_id', $formmodel->x)
              ->bindValue(':clip_id', $model->clip_id);
          } else {
            $query2 = "";
          }

          if ($formmodel->also_creditnote == 1) {
            //create also dobropis 181, resp. 183
            $query3 = "call newpaper(:m_type_id, 'x', :d1, :d2, :d3, 0, :text1,:text2, :adr_id, :user, :stock_id1,:stock_id2,:final_stock, :adr_repli_id, :clip_id)";
            $cmd3 =  Yii::$app->db->createCommand($query3)
              ->bindValue(':m_type_id', EstockTools::getAdditionalMtypeforShop($model->m_type_id))
              ->bindValue(':d1', is_null($formmodel->d1) ? date('Y-m-d') : $formmodel->d1)
              ->bindValue(':d2', is_null($model->d2) ? date('Y-m-d') : $model->d2)
              ->bindValue(':d3', date('Y-m-d', strtotime('+10 days')))
              ->bindValue(':text1', $xtext1)
              ->bindValue(':text2', $xtext2)
              ->bindValue(':adr_id', $xadrid)
              ->bindValue(':user', Yii::$app->user->identity->username)
              ->bindValue(':stock_id1', $model->stock_id1)
              ->bindValue(':stock_id2', $model->stock_id2)
              ->bindValue(':final_stock', isset($formmodel->final_stock)  ? $formmodel->final_stock : 'x')
              ->bindValue(':adr_repli_id', $formmodel->x)
              ->bindValue(':clip_id', $model->clip_id);
          } else {
            $query3 = "";
          }
          try {

            $cmd->query();

            $total012 = Yii::$app->db->createCommand("select 
                cast(total0+total1+total2+tax1+tax2+rounding as numeric(11,2)) as totalpay from movement where move_id = (
                select max(move_id) from movement where emp_id=:userid and m_type_id=:mtypeid)")
              ->bindValue(':userid', Yii::$app->user->identity->username)
              ->bindValue(':mtypeid', $model->m_type_id)
              ->queryScalar();

            if ($formmodel->x != 9) { //ceny s dph, musim vydelit vo fakture, dobropise
              $dph = Yii::$app->db->createCommand("select tax2 from office where user_name=:userid")
                ->bindValue(':userid', Yii::$app->user->identity->username)
                ->queryScalar();
              Yii::$app->db->createCommand("update tempmove set price = price / (1.0 + :dph/100.0), tax=:dph where user_name=:userid and clip_id=:cid")
                ->bindValue(':dph', $dph)
                ->bindValue(':userid', Yii::$app->user->identity->username)
                ->bindValue(':cid', $model->clip_id)
                ->query();
            }

            if (!empty($query2)) {
              $cmd2->query();
              $this->recountRounding($model->m_type_id, $total012);
            }
            if (!empty($query3)) {
              $cmd3->query();
            }

            //Notific if B2B order
            if (EstockTools::isUserB2b()) {

              $query = "SELECT mat_id, kod, model ,pcs, price,replace(tempmove_info,',','~') tinfo from tempmove where user_name=:id
                     and clip_id=:cid";
              $data =  Yii::$app->db->createCommand($query)
                ->bindValue(':id', Yii::$app->user->identity->username)
                ->bindValue(':cid', $formmodel->clip_id)
                ->queryAll();
              $out = "<p>Clipboard data</p><hr><pre>";
              foreach ($data as $key => $v) {
                $out .= $v['mat_id'] . ' <b>' . $v['kod'] . '</b> ' . $v['model'] . ' ' . $v['pcs'] . ' pcs <b>' . $v['price'] . '</b> ' . $v['tinfo'] . "\n";
              }
              $out .= "</pre><hr>";


              Yii::$app->mailer->compose(
                'layouts/b2borderinfo',
                [
                  'content' => $formmodel->text1 . "<br>" . $formmodel->text2 . "<br>" . $formmodel->d1 . $out
                ]
              )
                ->setFrom('<EMAIL>')
                ->setTo(['<EMAIL>', '<EMAIL>', '<EMAIL>'])
                ->setSubject('B2B.RACIO.COM ORDER from ' . Yii::$app->user->identity->username)
                ->send();
            }


            $cmd2 =   Yii::$app->db->createCommand("delete from tempmove where  user_name = :user and clip_id = :clip_id")
              ->bindValue(':user', Yii::$app->user->identity->username)
              ->bindValue(':clip_id', $model->clip_id);

            $cmd2->execute();
          } catch (\yii\db\Exception $e) {
            Yii::$app->session->setFlash('error', $e->getMessage());
            Yii::debug($e);
            return $this->redirect([$redirectview, 'tabIndex' => $model->m_type_id . $model->stock_id1 . $model->stock_id2]);
          } catch (\yii\base\Exception $e) {

            Yii::$app->session->setFlash('error', $e);
            return $this->redirect([$redirectview, 'tabIndex' => $model->m_type_id . $model->stock_id1 . $model->stock_id2]);
          }
          Yii::$app->session->setFlash('success', 'OK, SAVED');
          return $this->redirect([$redirectview, 'tabIndex' => $model->m_type_id . $model->stock_id1 . $model->stock_id2]);
        } else {
          Yii::$app->session->setFlash('error', 'ACTION DISABLED, class instance unknown');
          return $this->redirect($redirectview);
        }
      }
    }

    if (is_null($formmodel->d1)) {
      $formmodel->d1 = date('Y-m-d');
    }
    if (is_null($formmodel->mtype)) {
      $formmodel->x = '0';
      $formmodel->mtype = Yii::$app->request->get('mtype', 1);
    }

    return $this->render('SaveClipboard', [
      'model' => $formmodel,
    ]);
  }

  /**
   * Helpin function to recount rounding
   * @param int $m_type_id
   * @param float $total012
   * @return void
   */
  private function recountRounding($m_type_id, $total012)
  {
    //[$newmove_id,$newtotal012]
    $new = Yii::$app->db->createCommand("select move_id,
        cast(total0+total1+total2+tax1+tax2+rounding as numeric(11,2)) as totalpay from movement where move_id = (
        select max(move_id) from movement where emp_id=:userid and m_type_id=:mtypeid)")
      ->bindValue(':userid', Yii::$app->user->identity->username)
      ->bindValue(':mtypeid', EstockTools::getAdditionalMtypeforShop($m_type_id))
      ->queryOne();
    if ($total012 != $new['totalpay']) {
      //ToDo - recount funkcia to moze zrusit. Bude nutne do recount zapojit aj m_type_id parenta a preratat rovnako.
      //ToDo - parent id nie je nastaveny, takze toto je len docasne riesenie
      Yii::$app->db->createCommand("update movement set rounding = rounding + cast(:diff as numeric(11,2)) where move_id=:moveid")
        ->bindValue(':diff', $total012 - $new['totalpay'])
        ->bindValue(':moveid', $new['move_id'])
        ->query();
    }
  }


  /**
   * Save clipboard for office use
   */
  public function actionSaveclipboard2()
  {
    $formmodel = new \app\models\SaveClipForm();
    $newmove_id = null;

    if ($formmodel->load(Yii::$app->request->post())) {

      if ($formmodel->validate()) {

        if (($model = Tempmove::find()
          ->where(['user_name' => Yii::$app->user->identity->username, 'clip_id' => $formmodel->clip_id])
          ->one()) !== null) {

          $query = "call newpaper(:m_type_id, 'x', :d1, :d2, :d3, 0, :text1,:text2, :adr_id, :user, :stock_id1,:stock_id2,:final_stock, :adr_repli_id, :clip_id)";
          $cmd =   Yii::$app->db->createCommand($query)
            ->bindValue(':m_type_id', $model->m_type_id)
            ->bindValue(':d1', is_null($formmodel->d1) ? date('Y-m-d') : $formmodel->d1)
            ->bindValue(':d2', is_null($model->d2) ? date('Y-m-d') : $model->d2)
            ->bindValue(':d3', is_null($model->d3) ? date('Y-m-d') : $model->d3)
            ->bindValue(':text1', is_null($formmodel->text1) ? '' : $formmodel->text1)
            ->bindValue(':text2', is_null($formmodel->text2) ? '' : $formmodel->text2)
            ->bindValue(':adr_id', $model->adr_id)
            ->bindValue(':user', Yii::$app->user->identity->username)
            ->bindValue(':stock_id1', $model->stock_id1)
            ->bindValue(':stock_id2', $model->stock_id2)
            ->bindValue(':final_stock', isset($formmodel->final_stock)  ? $formmodel->final_stock : 'x')
            ->bindValue(':adr_repli_id', $formmodel->x)
            ->bindValue(':clip_id', $model->clip_id);
          //ADRREPLI je len historicky parameter, pouzil som ho na zapis X ako priznaku DPH pre 87
          try {

            $cmd->query();

            $cmd2 =   Yii::$app->db->createCommand("delete from tempmove where  user_name = :user and clip_id = :clip_id")
              ->bindValue(':user', Yii::$app->user->identity->username)
              ->bindValue(':clip_id', $model->clip_id);

            $cmd2->execute();

            $newmove_id = Yii::$app->db->createCommand("select max(move_id) from movement where emp_id=:userid and m_type_id=:mtypeid")
              ->bindValue(':userid', Yii::$app->user->identity->username)
              ->bindValue(':mtypeid', $model->m_type_id)
              ->queryScalar();
          } catch (\yii\db\Exception $e) {
            Yii::$app->session->setFlash('error', $e->getMessage());
            Yii::debug($e);
            return $this->redirect(['movement/mymanagermoves']);
          } catch (\yii\base\Exception $e) {

            Yii::$app->session->setFlash('error', $e);
            return $this->redirect(['movement/mymanagermoves']);
          }
          Yii::$app->session->setFlash('success', 'OK, SAVED');
          // if exists new minfo, go to update this record
          if ($newmove_id !== null) {
            $minfoid = Yii::$app->db->createCommand("select id from minfo where move_id=:moveid")
              ->bindValue(':moveid', $newmove_id)->queryScalar();
            if ($minfoid != null) {
              return $this->redirect(['minfo/update', 'id' => $minfoid]);
            }
          }
          return $this->redirect(['movement/mymanagermoves']);
        } else {
          Yii::$app->session->setFlash('error', 'ACTION DISABLED, class instance unknown');
          return $this->redirect(['movement/mymanagermoves']);
        }
      }
    }

    if (($model = MUserConfig::find()
      ->where(['userid' => Yii::$app->user->id, 'clip_id' => Yii::$app->request->get('clip_id')])
      ->one()) !== null) {
      $formmodel->x = '0';
      $formmodel->mtype = $model->m_type_id;
      $formmodel->d1 = $model->d1;
      $formmodel->text1 = $model->text1;
      $formmodel->text2 = $model->text2;
    }

    return $this->render('SaveClipboard', [
      'model' => $formmodel,
    ]);
  }



  /**
   * Updates an existing Tempmove model.
   * If update is successful, the browser will be redirected to the 'view' page.
   * @param integer $mat_id
   * @param string $user_name
   * @param integer $clip_id
   * @param string $tempmove_info
   * @return mixed
   */
  public function actionUpdate($mat_id, $user_name, $clip_id, $tempmove_info)
  {
    $model = $this->findModel($mat_id, $user_name, $clip_id, $tempmove_info);

    if ($model->load(Yii::$app->request->post()) && $model->save()) {
      if (EstockTools::isUserShop()) {
        return $this->redirect(['shop']);
      } elseif (EstockTools::isUserB2b()) {
        return $this->redirect(['b2b']);
      } else {
        $muc = MUserConfig::find()->where(['clip_id' => $clip_id, 'userid' => Yii::$app->user->id])->one();
        return $this->redirect(['createfromconfig', 'id' => $muc->id]);
      }
    } else {
      if (EstockTools::isUserShop()) {
        $pricex = $model->price_type ?? EstockTools::getCurrentClipboardPrice($model->m_type_id) ?? EstockTools::getShopParams()['shopmtypes'][0][4];
      } elseif (EstockTools::isUserB2b()) {
        $pricex = $model->price_type ?? EstockTools::getB2bParams()['shopmtypes'][0][4];
      } else {
        $pricex = Yii::$app->db->createCommand("select price from m_user_config where clip_id=:clip_id and userid = :userid")
          ->bindValue(':clip_id', $clip_id)
          ->bindValue(":userid", Yii::$app->user->id)
          ->queryScalar();
      }
      return $this->render('update', [
        'model' => $model,
        'shopPrice' => $pricex,
      ]);
    }
  }

  /**
   * Deletes an existing Tempmove model.
   * If deletion is successful, the browser will be redirected to the 'index' page.
   * @param integer $mat_id
   * @param string $user_name
   * @param integer $clip_id
   * @param string $tempmove_info
   * @return mixed
   */
  public function actionDelete($mat_id, $user_name, $clip_id, $tempmove_info)
  {
    $this->findModel($mat_id, $user_name, $clip_id, $tempmove_info)->delete();

    if (EstockTools::isUserShop()) {
      return $this->redirect(['shop']);
    } elseif (EstockTools::isUserB2b()) {
      return $this->redirect(['b2b']);
    } else {
      $muc = MUserConfig::find()->where(['clip_id' => $clip_id, 'userid' => Yii::$app->user->id])->one();
      return $this->redirect(['createfromconfig', 'id' => $muc->id]);
    }
  }


  /**
   * 
   * Export Tempmove information into PDF format.
   * @param integer $mat_id
   * @param string $user_name
   * @param integer $clip_id
   * @param string $tempmove_info
   * @return mixed
   */
  public function actionPdf($mat_id, $user_name, $clip_id, $tempmove_info)
  {
    $model = $this->findModel($mat_id, $user_name, $clip_id, $tempmove_info);

    $content = $this->renderAjax('_pdf', [
      'model' => $model,
    ]);

    $pdf = new \kartik\mpdf\Pdf([
      'mode' => \kartik\mpdf\Pdf::MODE_CORE,
      'format' => \kartik\mpdf\Pdf::FORMAT_A4,
      'orientation' => \kartik\mpdf\Pdf::ORIENT_PORTRAIT,
      'destination' => \kartik\mpdf\Pdf::DEST_BROWSER,
      'content' => $content,
      'cssFile' => '@vendor/kartik-v/yii2-mpdf/assets/kv-mpdf-bootstrap.min.css',
      'cssInline' => '.kv-heading-1{font-size:18px}',
      'options' => ['title' => \Yii::$app->name],
      'methods' => [
        'SetHeader' => [\Yii::$app->name],
        'SetFooter' => ['{PAGENO}'],
      ]
    ]);

    return $pdf->render();
  }


  /**
   * Finds the Tempmove model based on its primary key value.
   * If the model is not found, a 404 HTTP exception will be thrown.
   * @param integer $mat_id
   * @param string $user_name
   * @param integer $clip_id
   * @param string $tempmove_info
   * @return Tempmove the loaded model
   * @throws NotFoundHttpException if the model cannot be found
   */
  protected function findModel($mat_id, $user_name, $clip_id, $tempmove_info)
  {
    if (($model = Tempmove::findOne(['mat_id' => $mat_id, 'user_name' => $user_name, 'clip_id' => $clip_id, 'tempmove_info' => $tempmove_info])) !== null) {
      return $model;
    } else {
      throw new NotFoundHttpException('The requested page does not exist.');
    }
  }
}
