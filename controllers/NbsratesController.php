<?php

namespace app\controllers;

use Yii;
use app\models\Nbsrates;
use app\models\NbsratesSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * NbsratesController implements the CRUD actions for Nbsrates model.
 */
class NbsratesController extends Controller
{
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['post'],
                ],
            ],
            'access' => [
                'class' => \yii\filters\AccessControl::className(),
                'rules' => [
                    [
                        'allow' => true,
                        'actions' => ['index', 'view', 'create', 'update', 'delete', 'pdf'],
                        'roles' => ['@']
                    ],
                    [
                        'allow' => false
                    ]
                ]
            ]
        ];
    }

    /**
     * Lists all Nbsrates models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new NbsratesSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single Nbsrates model.
     * @param string $nbs_date
     * @param string $nbs_cur
     * @return mixed
     */
    public function actionView($nbs_date, $nbs_cur)
    {
        $model = $this->findModel($nbs_date, $nbs_cur);
        return $this->render('view', [
            'model' => $this->findModel($nbs_date, $nbs_cur),
        ]);
    }

    /**
     * Creates a new Nbsrates model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new Nbsrates();

        #
        # POZOR TEST ONLY SKUSAL SOM FUNKCNOST TRY CATCH
        #

        if ($model->load(Yii::$app->request->post()) ) {
                    $query = "insert into nbs_rates values (now(),'xxx',0,'x')" ;
                
                    $cmd = Yii::$app->db->createCommand($query);


               try {
                    $cmd->execute();
                } catch (\yii\db\Exception $e) {
                    Yii::$app->session->setFlash('error',$e->getMessage() );
                    Yii::debug($e);
                    return $this->render('create', [
                        'model' => $model,
                    ]);
                }

            return $this->redirect(['view', 'nbs_date' => $model->nbs_date, 'nbs_cur' => $model->nbs_cur]);
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing Nbsrates model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param string $nbs_date
     * @param string $nbs_cur
     * @return mixed
     */
    public function actionUpdate($nbs_date, $nbs_cur)
    {
        $model = $this->findModel($nbs_date, $nbs_cur);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'nbs_date' => $model->nbs_date, 'nbs_cur' => $model->nbs_cur]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing Nbsrates model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param string $nbs_date
     * @param string $nbs_cur
     * @return mixed
     */
    public function actionDelete($nbs_date, $nbs_cur)
    {
        $this->findModel($nbs_date, $nbs_cur)->deleteWithRelated();

        return $this->redirect(['index']);
    }
    
    /**
     * 
     * Export Nbsrates information into PDF format.
     * @param string $nbs_date
     * @param string $nbs_cur
     * @return mixed
     */
    public function actionPdf($nbs_date, $nbs_cur) {
        $model = $this->findModel($nbs_date, $nbs_cur);

        $content = $this->renderAjax('_pdf', [
            'model' => $model,
        ]);

        $pdf = new \kartik\mpdf\Pdf([
            'mode' => \kartik\mpdf\Pdf::MODE_CORE,
            'format' => \kartik\mpdf\Pdf::FORMAT_A4,
            'orientation' => \kartik\mpdf\Pdf::ORIENT_PORTRAIT,
            'destination' => \kartik\mpdf\Pdf::DEST_BROWSER,
            'content' => $content,
            'cssFile' => '@vendor/kartik-v/yii2-mpdf/assets/kv-mpdf-bootstrap.min.css',
            'cssInline' => '.kv-heading-1{font-size:18px}',
            'options' => ['title' => \Yii::$app->name],
            'methods' => [
                'SetHeader' => [\Yii::$app->name],
                'SetFooter' => ['{PAGENO}'],
            ]
        ]);

        return $pdf->render();
    }

    
    /**
     * Finds the Nbsrates model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param string $nbs_date
     * @param string $nbs_cur
     * @return Nbsrates the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($nbs_date, $nbs_cur)
    {
        if (($model = Nbsrates::findOne(['nbs_date' => $nbs_date, 'nbs_cur' => $nbs_cur])) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
