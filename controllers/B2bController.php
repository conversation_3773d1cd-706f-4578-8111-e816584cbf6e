<?php

namespace app\controllers;

use Yii;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\helpers\Json;
use app\controllers\EstockTools;
use yii\data\ArrayDataProvider;

/**
 * B2bController implements some b2b reports
 */
class B2bController extends Controller
{
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['post'],
                ],
            ],
            'access' => [
                'class' => \yii\filters\AccessControl::className(),
                'rules' => [
                    [                    
                        'allow' => true,
                        'actions' => ['report'],
                        'matchCallback' => function ($rule, $action) {
                            return EstockTools::isUserB2b();
                        }
                    ],
                    [
                        'allow' => false
                    ]
                ]
            ]
        ];
    }

    /**
     * Lists all B2b reports.
     * @return mixed
     */
    public function actionReport()
    {
        $q1 = "select first_date,last_date,kod,model,average_price::numeric(11,2),ordered,delivered,returned, ordered+returned-delivered backorder from (
        select 
        min(d1) first_date,
        max(d1) last_date,
        d.mat_id,kod,model,floor(avg(d.price)*100)/100 average_price,sum(d.pcs) ordered,
        (select case when sum(dd.pcs) is null then 0 else sum(dd.pcs) end from m_detail dd join movement mm on dd.move_id=mm.move_id where mm.adr_id=m.adr_id 
        and dd.mat_id=d.mat_id and mm.m_type_id in (select m_type_id from m_type where stock_id1 is not null)) delivered, 
        (select case when sum(dd.pcs) is null then 0 else sum(dd.pcs) end from m_detail dd join movement mm on dd.move_id=mm.move_id where mm.adr_id=m.adr_id 
        and dd.mat_id=d.mat_id and mm.m_type_id in (select m_type_id from m_type where stock_id2 is not null)) returned
        
        from 
            movement m join m_detail d on m.move_id=d.move_id join product p on p.mat_id=d.mat_id 
        where 
            adr_id=:adr_id
        group by  m.adr_id,d.mat_id,kod,model
 
            ) as mm
            where ordered+returned-delivered >0
        order by model, first_date";

        $q2="select from_date,kod,model,average_price,ordered,delivered, ordered-delivered backorder from (
        select 
        min(d1) from_date,
        kod,model,(floor(avg(d.price)*100)/100)::numeric(11,2) average_price,sum(d.pcs) ordered,
        (select case when sum(dd.pcs) is null then 0 else sum(dd.pcs) end  from 
        m_detail dd join movement mm on dd.move_id=mm.move_id
        join product pp on pp.mat_id=dd.mat_id
         where mm.adr_id=m.adr_id 
        and  pp.model=p.model and mm.m_type_id in (select m_type_id from m_type where stock_id1 is not null) ) delivered

        from 
            movement m join m_detail d on m.move_id=d.move_id join product p on p.mat_id=d.mat_id 
        where 
            adr_id=:adr_id 
        group by  m.adr_id,kod,model
        ) sss
        where ordered-delivered > 0
        order by from_date";


    $q = "

        select
                adr_id,firma,mat_id,kod,model,avg(average_price)::numeric(11,2) avgprice,sum(ordered) sumordered,sum(delivered) sumdelivered,
                sum(last_delivered) sumlastdelivered,sum(ordered-delivered) clean_backorder,sum(ordered-last_delivered) fresh_backorder,sum(greatest(ordered-delivered,ordered-last_delivered)) summaxorder,
                from_date as min_from_date

                from
                (
                select adr_id,
                (select firma from address where adr_id=m.adr_id) firma,
                min(d1) from_date,m.m_type_id,
                case when m_type_id=17 then max(d1) end max_from_date,
                d.mat_id,kod,model,floor(avg(d.price)*100)/100 average_price,sum(d.pcs) ordered,
                (select case when sum(dd.pcs) is null then 0 else sum(dd.pcs) end from m_detail dd join
                movement mm on dd.move_id=mm.move_id
                where mm.d1 >= :from_date and mm.adr_id=m.adr_id 
                and dd.mat_id=d.mat_id and mm.m_type_id in (14,24,110,130,145)) delivered,
                (select case when sum(dd.pcs) is null then 0 else sum(dd.pcs) end from m_detail dd
                join movement mm on dd.move_id=mm.move_id
                where mm.d1 >= '2023-01-01' and mm.adr_id=m.adr_id
                and dd.mat_id=d.mat_id and mm.m_type_id in (14,24,110,130,145)) last_delivered


                from movement m join m_detail d on m.move_id =d.move_id
                join product p on p.mat_id=d.mat_id
                where (m.d1 >= '2023-01-01' and m_type_id in (17) and text1 ilike 'B2B%') and adr_id=:adr_id
                group by m.m_type_id,m.adr_id,d.mat_id,kod,model

                ) as ss
                group by
                adr_id,firma,mat_id,kod,model,from_date
                having sum(ordered-delivered)<>0 and (sum(ordered-last_delivered)>0 or sum(greatest(ordered-delivered,ordered-last_delivered))>0) order by firma,from_date
";

           $query = Yii::$app->db->createCommand($q)->bindValue(':from_date','2023-10-01')->bindValue(':adr_id',EstockTools::getUserAdrId())->queryAll();


            // $query = Yii::$app->db->createCommand($q1)->bindValue(':adr_id',EstockTools::getUserAdrId())->queryAll();

            $provider = new ArrayDataProvider([
                'allModels' => $query,
                'pagination' => [
                    'pageSize' => 10000,
                ]
            ]);

            // $query2 = Yii::$app->db->createCommand($q2)->bindValue(':adr_id',EstockTools::getUserAdrId())->queryAll();

            // $provider2 = new ArrayDataProvider([
            //     'allModels' => $query2,
            //     'pagination' => [
            //         'pageSize' => 10000,
            //     ]
            // ]);


                //TOOD: add second provider
                
        return $this->render('report', ['p1'=>$provider,'p2'=>$provider,'title'=>'Sum of ordered and delivered', 'title2'=>'Details of orders']);

    }

}
