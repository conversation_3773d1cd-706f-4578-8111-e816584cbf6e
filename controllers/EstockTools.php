<?php


namespace app\controllers;

use Yii;
use app\models\User;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\bootstrap\ButtonDropdown;

class EstockTools
{

    // Array of companies used for ODT reports
    //
    const ourCompanies = [
        'racio' => [
            'firma' => "RACIO, Export-Import-Consulting, s.r.o.",
            'street' => "Krajinská 1",
            'city' => "Bratislava",
            'zip' => "821 06",
            'ico' => "IČO: ******** DIČ: **********",
            'dic' => "IČ pre DPH: SK**********",
            'icoEN' => "Reg. ID: ******** TAX no.: **********",
            'dicEN' => "VAT no.: SK**********",
            'iban' => "IBAN: SK29 1100 0000 0026 2973 0122",
            'swift' => "BIC: TATRSKBX",
            'banka' => "Banka: Tatrabanka, a.s.",
            'ortext' => "Obchodný register Okresného súdu Bratislava I, oddiel: Sro, vložka 9403/B",
            'ortextEN' => "Company registered in the Companies Register at Municipal court in Bratislava I, section: Sro, 9403/B",
            'tel' => "Tel.: +421 2 ********, Fax: +421 2 ********",
            'email' => "E-mail: <EMAIL>    http://racio.com",
            'dph' => "1.20",
        ],
        'raciowithcz' => [
            'firma' => "RACIO, Export-Import-Consulting, s.r.o.",
            'street' => "Krajinská 1",
            'city' => "Bratislava",
            'zip' => "821 06",
            'ico' => "IČO: ******** DIČ: **********",
            'dic' => "IČ pre DPH: SK**********",
            'icoEN' => "Reg. ID: ******** TAX no.: **********",
            'dicEN' => "VAT no.: SK**********",
            'iban' => "EUR IBAN: SK29 1100 0000 0026 2973 0122",
            'swift' => "CZK IBAN: CZ54 5500 0000 0072 9227 3001\nCZK BBAN: **********/5500\nCHF IBAN: SK14 1100 0000 0025 2074 0028",
            'ortext' => "Obchodný register Okresného súdu Bratislava I, oddiel: Sro, vložka 9403/B",
            'ortextEN' => "Company registered in the Companies Register at Municipal court in Bratislava I, section: Sro, 9403/B",
            'tel' => "Tel.: +421 2 ********, Fax: +421 2 ********",
            'email' => "E-mail: <EMAIL>    http://racio.com",
            'dph' => "1.20",
        ],
        'gw' => [
            'firma' => "G-WATCH, spol. s r.o.",
            'street' => "Cukrová 14",
            'city' => "Bratislava",
            'zip' => "811 08",
            'ico' => "IČO: ******** DIČ: **********",
            'dic2' => "**********",
            'dic' => "IČ DPH : SK**********",
            'icoEN' => "Reg. ID: ******** TAX no.: **********",
            'dicEN' => "VAT no.: SK**********",
            'iban' => "IBAN: SK23 1100 0000 0026 2276 2041",
            'swift' => "Swift: TATR SK BX",
            'banka' => "Banka: Tatrabanka, a.s.",
            'ortext' => "Obchodný register Okresného súdu Bratislava I., oddiel Sro, vložka č. 17344/B",
            'ortextEN' => "Company registered in the Companies Register at Municipal court in Bratislava I, section: Sro, 17344/B",
            'tel' => "",
            'email' => "",
            'dph' => "1.20",
        ],
        'swd' => [
            'firma' => "Swiss Watch Distribution, spol. s r.o.",
            'street' => "Krajinská 1",
            'city' => "Bratislava",
            'zip' => "821 06",
            'ico' => "IČO:  ******** DIČ: **********",
            'dic' => "IČ DPH : SK**********",
            'icoEN' => "Reg. ID: ******** TAX no.: **********",
            'dicEN' => "VAT no.: SK**********",
            'iban' => "EUR IBAN: SK75 1100 0000 0029 2484 4034\nCHF IBAN: SK48 1100 0000 0025 2078 8013",
            'swift' => "Swift: TATR SK BX",
            'banka' => "Banka: Tatrabanka, a.s.",
            'ortext' => "Obchodný register Okresného súdu Bratislava I., oddiel Sro, vložka č. 67400/B",
            'ortextEN' => "Company registered in the Companies Register at Municipal court in Bratislava I, section: Sro, 67400/B",
            'tel' => "",
            'email' => "",
            'dph' => "1.20",
        ],
        'sw' => [
            'firma' => "Swiss Watches, s. r. o.",
            'street' => "Pribinova 8",
            'city' => "Bratislava",
            'zip' => "811 09",
            'ico' => "IČO:  ******** DIČ: **********",
            'dic' => "IČ DPH : SK**********",
            'icoEN' => "Reg. ID: ******** TAX no.: **********",
            'dicEN' => "VAT no.: SK**********",
            'iban' => "IBAN: SK59 1100 0000 0026 2821 4345",
            'swift' => "Swift: TATR SK BX",
            'banka' => "Banka: Tatrabanka, a.s.",
            'ortext' => "Obchodný register Okresného súdu Bratislava I., oddiel Sro, vložka č. 60575/B",
            'ortextEN' => "Company registered in the Companies Register at Municipal court in Bratislava I, section: Sro, 60575/B",
            'tel' => "",
            'email' => "",
            'dph' => "1.20",
        ],
        'ww' => [
            'firma' => "W-WATCH, spol. s r.o.",
            'street' => "Cukrová 14",
            'city' => "Bratislava",
            'zip' => "811 08",
            'ico' => "IČO:  ******** DIČ: **********",
            'dic' => "IČ DPH : SK**********",
            'icoEN' => "Reg. ID: ******** TAX no.: **********",
            'dicEN' => "VAT no.: SK**********",
            'iban' => "IBAN: SK35 1100 0000 0029 2583 3863",
            'swift' => "Swift: TATR SK BX",
            'banka' => "Banka: Tatrabanka, a.s.",
            'ortext' => "Obchodný register Okresného súdu Bratislava I., oddiel Sro, vložka č. 63850/B",
            'ortextEN' => "Company registered in the Companies Register at Municipal court in Bratislava I, section: Sro, 63850/B",
            'tel' => "",
            'email' => "",
            'dph' => "1.20",
        ],
        'th' => [
            'firma' => "TH shop, spol. s r.o.",
            'street' => "Pribinova 8",
            'city' => "Bratislava",
            'zip' => "811 09",
            'ico' => "IČO:  ******** DIČ: **********",
            'dic' => "IČ DPH : SK**********",
            'icoEN' => "Reg. ID: ******** TAX no.: **********",
            'dicEN' => "VAT no.: SK**********",
            'iban' => "IBAN: SK69 1100 0000 0026 2820 6599",
            'swift' => "Swift: TATR SK BX",
            'banka' => "Banka: Tatrabanka, a.s.",
            'ortext' => "Obchodný register Okresného súdu Bratislava I., oddiel Sro, vložka č. 59125/B",
            'ortextEN' => "Company registered in the Companies Register at Municipal court in Bratislava I, section: Sro, 59125/B",
            'tel' => "",
            'email' => "",
            'dph' => "1.20",
        ],
        'chrono' => [
            'firma' => "CHRONO Exp.-Imp. Kft.",
            'street' => "1117 Budapest, Hunyadi J. út 1.",
            'city' => "Tel: 371-0833 , Fax: 371-0832",
            'zip' => "",
            'ico' => "",
            'dic' => "",
            'iban' => "",
            'swift' => "",
            'ortext' => "",
            'tel' => "",
            'email' => "",
            'dph' => "1.27",
        ]

    ];


    public static function isMtypeWithAdvancedPayment( $m_type_id ){
            return $m_type_id == 24 || $m_type_id == 110 || $m_type_id == 102 || $m_type_id == 150
                || $m_type_id == 167 || $m_type_id == 169 || $m_type_id == 154
                || $m_type_id == 137 || $m_type_id == 139; // Typy pohybov, ktore mozu mat zalohovu platbu
    }

    /* Shop moze vyberat len z urcitych adries pri tvorbe prijemky */
    public static function getShopSuppliersArray(){
        $data =  Yii::$app->db->CreateCommand("select adr_id,  adr_id||' - '||firma||', '||city as xfirma from address where adr_id in 
                        (771, 3961, 5331, 5765, 11765, 12309, 5309, 5117, 3636, 5516, 11964, 11737,
                         773, 11808, 254, 6045, 5894, 6486, 3699, 4956, 12361, 6344, 12317, 12311, 12094, 12304)
                        ")->queryAll();
        return Yii\helpers\ArrayHelper::map($data, 'adr_id', 'xfirma');
    }

        /* Shop moze presuvat len na tieto prevadzky */
        public static function getShopWhereCanTransferArray($adr_id=0, $user_repli=0){
            if( $user_repli ==  0 ) {
                $query =  "select stock_id, Sdescr,max(whoiam) mystock from (select stock_id, Sdescr, 1 whoiam from stock_detail where adr_id = :adr_id
                union
                    SELECT c1,c2,c3  FROM (VALUES 
                        ('s0', 'SK office Bratislava',0),
                        ('WDEU', 'EUROVEA',0),
                        ('WAUP', 'WDL AUPARK',0)
                         ) 
                            AS t(c1,c2,c3)  order by 2) tt group by stock_id,Sdescr order by 1
                ";    
            } else if( $user_repli == 2 ){
                $query =  "select stock_id, Sdescr,max(whoiam) mystock from (select stock_id, Sdescr, 1 whoiam from stock_detail where adr_id = :adr_id
                union
                    SELECT c1,c2,c3  FROM (VALUES 
                        ('s2', 's2 H office raktár',0),
                        ('s4','s4 HU office RACIO Hungary', 0),
                        ('SWE', 'West-watch',0),
                        ('SWA', 'Watch Arena',0),
                        ('SRH1', 'WDL Andrassy',0),
                        ('STHH', 'TH Shop Kft',0) ) 
                            AS t(c1,c2,c3)  order by 2) tt group by stock_id,Sdescr order by 1
                ";    

            } else { // toto asi nikdy nebude
                $query =  "select stock_id, Sdescr,max(whoiam) mystock from (select stock_id, Sdescr, 1 whoiam from stock_detail where adr_id = :adr_id
                ";    
            }
            // echo $query;
            $data =  Yii::$app->db->CreateCommand($query)->bindValue('adr_id',$adr_id)->queryAll();
            return $data;
        }

    // Generates a strong password of N length containing at least one lower case letter,
    // one uppercase letter, one digit, and one special character. The remaining characters
    // in the password are chosen at random from those four sets.
    //
    // The available characters in each set are user friendly - there are no ambiguous
    // characters such as i, l, 1, o, 0, etc. This, coupled with the $add_dashes option,
    // makes it much easier for users to manually type or speak their passwords.
    //
    // Note: the $add_dashes option will increase the length of the password by
    // floor(sqrt(N)) characters.

    public static function generateStrongPassword($length = 9, $add_dashes = false, $available_sets = 'luds')
    {
        $sets = array();
        if(strpos($available_sets, 'l') !== false)
            $sets[] = 'abcdefghjkmnpqrstuvwxyz';
        if(strpos($available_sets, 'u') !== false)
            $sets[] = 'ABCDEFGHJKMNPQRSTUVWXYZ';
        if(strpos($available_sets, 'd') !== false)
            $sets[] = '23456789';
        if(strpos($available_sets, 's') !== false)
            $sets[] = '!@#$%&*?';
        $all = '';
        $password = '';
        foreach($sets as $set)
        {
            $password .= $set[array_rand(str_split($set))];
            $all .= $set;
        }
        $all = str_split($all);
        for($i = 0; $i < $length - count($sets); $i++)
            $password .= $all[array_rand($all)];
        $password = str_shuffle($password);
        if(!$add_dashes)
            return $password;
        $dash_len = floor(sqrt($length));
        $dash_str = '';
        while(strlen($password) > $dash_len)
        {
            $dash_str .= substr($password, 0, $dash_len) . '-';
            $password = substr($password, $dash_len);
        }
        $dash_str .= $password;
        return $dash_str;
    }

    public static function getUserRepliId(){
        if( !Yii::$app->user->isGuest ) {
            return Yii::$app->user->identity->repli;
        } else {
            return false;
        }
    }


    public static function getUserAllFlags(){
        if( !Yii::$app->user->isGuest ) {

            $u = User::find()
            ->select(['all_flags'])
            ->where(['username' => Yii::$app->user->identity->username])
            ->one();
            return $u->all_flags;
        } else {
            return false;
        }
    }

    public static function getAdditionalMtypeforShop( $m_type_id ){ //In Shop program we need additional Invoice/Credit note for some mtypes
        // Faktura GWatch is 182, SwissWatch is 180 
        // Dobropis GWatch is 183, SwissWatch is 181

        if ( Yii::$app->user->identity->username == 'aupark' and $m_type_id == 87 )
            return 182;
        if ( Yii::$app->user->identity->username == 'aupark' and $m_type_id == 97 )
            return 183;
        if ( $m_type_id == 87 )
            return 180;
        if ( $m_type_id == 97 )
            return 181;

        return 0; //Inac nema zmysel

    }

    /**
     * Vrati pole s id skladov, ktore moze pouzivatel vidiet
     * TODO: Doplnit pouzivatelov
     * @param int $adr_id
     */

    public static function getMystocks($adr_id=0){
        if( !Yii::$app->user->isGuest ) {
            if( $adr_id != 0 ){
                $mystock = Yii::$app->db->CreateCommand("select stock_id from stock_detail where adr_id=:adr")
                ->bindValue(':adr',intval($adr_id))->queryScalar();
                if( $mystock !== null ){
                    return [$mystock];
                } else {
                    return [];
                }
            }
            switch( Yii::$app->user->identity->username ){
                case 'dba':
                    return ['s0', 's2', 's4'];
                    break;
                default:
                    return [];
            }

        } else {
            return [];
        }
    }


    public static function isUserShop() {
        if( Yii::$app->user->isGuest ) {
            return false;
        }
        if( ($a = EstockTools::getUserAllFlags()) !== null ) {
            $aa = explode("-",$a);
            return strtolower($aa[0]) === "shop";
        } else {
            return false;
        }
    }

    public static function isUserStock() {
        if( Yii::$app->user->isGuest ) {
            return false;
        }
        if( ($a = EstockTools::getUserAllFlags()) !== null ) {
            $aa = explode("-",$a);
            return strtolower($aa[0]) === "stock";
        } else {
            return false;
        }
    }

    public static function isUserWman() { // Warehouse manager
        if( Yii::$app->user->isGuest ) {
            return false;
        }
        if( ($a = EstockTools::getUserAllFlags()) !== null ) {
            $aa = explode("-",$a);
            return strtolower($aa[0]) === "wmanager";
        } else {
            return false;
        }
    }

    public static function isUserWstock() { // Warehouse stock
        if( Yii::$app->user->isGuest ) {
            return false;
        }
        if( ($a = EstockTools::getUserAllFlags()) !== null ) {
            $aa = explode("-",$a);
            return strtolower($aa[0]) === "wstock";
        } else {
            return false;
        }
    }

    public static function isUserW() {
        return EstockTools::isUserWman() || EstockTools::isUserWstock();
    }

    public static function isUserB2b() {
        if( Yii::$app->user->isGuest ) {
            return false;
        }
        if( ($a = EstockTools::getUserAllFlags()) !== null ) {
            $aa = explode("-",$a);
            return strtolower($aa[0]) === "b2b";
        } else {
            return false;
        }
    }

    public static function isUserVip() {
        if( Yii::$app->user->isGuest ) {
            return false;
        }
        $u = strtolower(Yii::$app->user->identity->username);
        return $u === "gaborhu" || $u === "janos" || $u === "zuzana" || $u === "evask"  || $u === "gaborsk";
    }

    public static function isUserUcto() {
        if( Yii::$app->user->isGuest ) {
            return false;
        }
        $u = strtolower(Yii::$app->user->identity->username);
        return $u === "dba" || $u === "peter" || $u === "zuzana" || $u === "novakova" || $u === "veronika" ;
    }

    public static function isUserManagerStats() {
        if( Yii::$app->user->isGuest ) {
            return false;
        }
        $u = strtolower(Yii::$app->user->identity->username);
        return $u === "gaborhu" 
        || $u === "gaborsk" || $u === "zuzana" || $u === "brigitta" 
        || $u === "orosz" || $u === "petrsk" || $u === "janos"
        || $u === "davidhu" || $u === "davidsk" || $u === "rgw"
        || $u === "zafirhu" || $u === "zafirsk";
    }

    public static function isUserManagerProd() {
        if( Yii::$app->user->isGuest ) {
            return false;
        }
        $u = strtolower(Yii::$app->user->identity->username);
        return $u === "zoli" || $u === "peter"
        || $u === "gaborsk" || $u === "zuzana" || $u === "proland"
        || $u === "petrsk" || $u === "rgw";
    }

    public static function isUserAdminForUsers() {
        // check for active login
        if( Yii::$app->user->isGuest ) {
            return false;
        }
        $u = strtolower(Yii::$app->user->identity->username);
        return $u === "dba"
        || $u === "petrsk";
    }

    public static function getShopParams() {
        if( ($a = EstockTools::getUserAllFlags()) !== null ) {
            $aa = explode("-",$a);
            $mystock = Yii::$app->db->CreateCommand("select stock_id from stock_detail where adr_id=:adr")
                ->bindValue(':adr',intval($aa[1]))->queryScalar();
            
            if( is_null($mystock) ) {
                $mystock = 'SERV';
            }
            
            if ( strtolower($aa[0]) === "shop" ){
                //pridal som do shopmtypes productPrices parameter nech mam spec. na kazdy pohyb
                switch (  $aa[2] ){
                    case 0:
                    return [
                        'shopmtypes' => [ [87,'MOP',$mystock,null,'p1'],
                            [17,'Objednávka B2B',null,null,'p1'],
                            [97,'Dobropis',null,$mystock,'p1'],
                            [14,'→ to Service',$mystock,'SERV','p1'],
                            [14,'from service →','SERV',$mystock,'p1'] ,
                            [147,'*Eshop WDL.sk MOP','sss',null,'p1'],
                            // [149,'*Eshop WOW.sk MOP','sss',null,'p1'], //wow.sk zavrete od aprila 2024
                            [148,'*Eshop WDL.sk Dobropis',null,$mystock,'p1'],
                            [20,'Nový tovar → Príjemka to Service',null,'SERV','p0'] ], //nakupna cena
                        'productEditPrices' => ['p0','p1', 'p0e'], //na vytvaranie noveho produktu
                        'shopAdrList' => EstockTools::getShopSuppliersArray(),
                        'shopWhereCanTransfer' => EstockTools::getShopWhereCanTransferArray(intval($aa[1]),0),
                    ];
                    break;
                    // case 1:
                    // return [
                    //     'shopmtypes' => [90=>'MOP', 28=>'Objednávka',98=>'Dobropis',14=>'TRANSFER'],
                    // ];
                    // break;
                    case 2:
                        if( $mystock == 'SRH1' ){
                            $toStock = 's4';
                        } else {
                            $toStock = 's2';
                        }
                        return [
                            'shopmtypes' => [ [30,'SALE (30)',$mystock,null,'p4'],
                                [29,'ORDER (29)',null,null,'p4'],
                                [99,'CreditNote (99)',null,$mystock,'p4'],
                                [14,'TRANSFER to Racio '.$toStock,$mystock,$toStock,'p4'],
                                [14,'→ to service SERH',$mystock,'SERH','p4'],
                                [14,'from SERH →','SERH',$mystock,'p4'],
                                [14,'→ OTW or Reservation',$mystock,'OTW','p4'],
                                [504,'*Eshop SALE (504)','WSHU',null,'p4'],
                                [505,'*Eshop Jóváírás (505)',null,$mystock,'p4'] ],
                            'productEditPrices' => ['p4'],
                            'shopWhereCanTransfer' => EstockTools::getShopWhereCanTransferArray(intval($aa[1]),2),
                        ];
                    break;
                    default:
                    return false;
                }
            }
        }
        if( EstockTools::isUserDba() ){
            return [
                'productEditPrices' => ['p0','p1', 'p0e', 'p2'],
            ];
        }
        return false;
    }

    // NEDOKONCENE, NEPOUZITE s tymto
    static public function getCurrentClipboardPrice( $mtype = -1 )
    {
        // // zober m_type_id z clipboardu podla typu pohybu
        // $mType = Yii::$app->db->createCommand("select m_type_id from tempmove where m_type_id=:clip and user_name=:user")
        //     ->bindValue(':mtype',intval($mtype))
        //     ->bindValue(':user', Yii::$app->user->identity->username )
        //     ->queryScalar();
        // echo $mType;
        if( empty($mType) || $mType === -1 ) {
            $mType = Yii::$app->request->get('mtype');
        }
        // echo '.'.$mType;
        // if is shop
        if  (EstockTools::isUserShop()){
            foreach ( EstockTools::getShopParams() as $key => $value) {
                if( $key === 'shopmtypes' ){
                    foreach ($value as $key2 => $value2) {
                        if( intval($value2[0]) === intval($mType) ){
                            return $value2[4];
                        }
                    }
                }
            }
            // Nestacilo? Zober prvu hodnotu
            return EstockTools::getShopParams()['shopmtypes'][0][4];
        }

        //Toto asi este nepouzivame
        if (EstockTools::isUserB2b()){
            foreach ( EstockTools::getB2bParams() as $key => $value) {
                if( $key === 'shopmtypes' ){
                    foreach ($value as $key2 => $value2) {
                        if( $value2[0] === intval($mType) ){
                            return $value2[4];
                        }
                    }
                }
            }    
            // Nestacilo? Zober prvu hodnotu
            return EstockTools::getB2bParams()['shopmtypes'][0][4];
        }
        // Ostatni, asi tiez nepouzijeme, - zober prvu public cenu z configu
        return  isset( Yii::$app->params['productEditPrices'] ) ? Yii::$app->params['productEditPrices'][0] : null;
    }
    
    // array of clips for B2B
    public static function getB2bParams() {
        if( ($a = EstockTools::getUserAllFlags()) !== null ) {
            $aa = explode("-",$a);
            if ( strtolower($aa[0]) === "b2b" ){
                $u = User::find()->select(['profile'])->where(['username' => Yii::$app->user->identity->username])->one();
                parse_str($u->profile, $profile);

                switch (Yii::$app->user->identity->repli) {
                    case '0':
                        return ['shopmtypes' => [ [17,'Objednávka B2B',null,null,'p1'], ],
                        'profile' => $profile ];
                        break;
                    case '1':
                        return ['shopmtypes' => [ [17,'Objednávka B2B',null,null,'p6'], ],
                        'profile' => $profile ];
                        break;
                    case '2':
                        return ['shopmtypes' => [ [29,'ORDER (29)',null,null,'p4'], ],
                        'profile' => $profile ];
                        break;                    
                    default:
                        return false;
                        break;
                }

            }
        } else {
            return false;
        }
    }

    // Left main menu with array of clipboards
    public static function getClipboardsMenu() {
        if( EstockTools::isUserStock() || EstockTools::isUserWman() || EstockTools::isUserWstock() ){
            $clips = Yii::$app->db->createCommand("
                select 'New/ manage clipboard' as label, 'plus' as icon, '/muserconfig/index' as url union all
                select m_type_id||' '||firma||' '||coalesce(stock_id1,'')||' '||coalesce(stock_id2,'') as label, 'clipboard' as icon, '/tempmove/createfromconfig/'||id as url from m_user_config 
                c join address a on c.adr_id=a.adr_id
                where userid = (select id from wuser where username=:uname) ")
            ->bindValue(':uname',Yii::$app->user->identity->username)->queryAll();
             // echo "<pre>";
             // var_dump(array_unshift( EstockTools::getClipboardsMenu() , 
             //                ['label' => 'NEW clipboard', 'icon' => 'plus', 'url' => ['muserconfig/index']] 
             //             )
             // );
             // exit;
            return $clips;
        }
    }

    public static function getFirma($move_id) {
        $m = \app\models\Movement::find()->where(['move_id'=>$move_id])->one();
        switch ($m->m_type_id) {
            case 109:
            case 110: // Tieto pohyby chcu aj s CZK IBAN
            case 169:
            case 170:
                return EstockTools::ourCompanies['raciowithcz'];
                break;
            case 130:
            case 131:
            case 132:
            case 133:
            case 134:
            case 135:
            case 136:
            case 145:
            case 146:
                return EstockTools::ourCompanies['swd'];
                break;
            case 139:
                $addedBban = EstockTools::ourCompanies['sw'];
                $addedBban['iban'] .= "\nCZK BBAN: 1115499002/5500";
                return $addedBban;
                break;
            case 26:
            case 137:
            case 138:
            case 140:
            case 150:
            case 151:
            case 147: //Eshop wdl.sk na predajni
            case 148:
            case 180:
            case 181:
                return EstockTools::ourCompanies['sw'];
                break;
            case 89:
            // case 113:
            case 117:
            case 154:
            case 155:
            case 158:
            case 182:
            case 183:
                return EstockTools::ourCompanies['gw'];
                break;            
            case 152:
            case 153:
                return EstockTools::ourCompanies['ww'];
                break;            
            case 156:
            case 157:
                return EstockTools::ourCompanies['th'];
                break;            
            default:
                if( $m->repli_id == "2"){
                    return EstockTools::ourCompanies['chrono'];
                } else {
                    return EstockTools::ourCompanies['racio']; // Racio ma aj eshop WOW 167-170 a dodak 149
                }
                break;
        }
    }

    public static function getUserMtypes() {

        if( EstockTools::isUserDba()){
            return Yii::$app->db->createCommand("select m_type_id from m_type")->queryAll();
        }

        switch (Yii::$app->user->identity->repli) {
            case '0':
            return Yii::$app->db->createCommand("select m_type_id from m_type where m_type_id=14 or usergroup ilike '%SK' or usergroup ilike '%Bratislava%'")->queryAll();
            break;
            case '1':
            return Yii::$app->db->createCommand("select m_type_id from m_type where m_type_id=14 or usergroup ilike '%CZ'")->queryAll();
            break;
            case '2':
            return Yii::$app->db->createCommand("select m_type_id from m_type where m_type_id=14 or usergroup ilike '%H'")->queryAll();
            break;
            case '3':
            return Yii::$app->db->createCommand("select m_type_id from m_type where m_type_id=14 or usergroup ilike '%AT' or usergroup ilike '%Bratislava%'")->queryAll();
            break;
            
            default:
            return [];
            break;
        }

    }

    public static function isUserDba() {
        return Yii::$app->user->isGuest ? false : strtolower( Yii::$app->user->identity->username ) === 'dba';
    }

    public static function getUserAdrId() {
        if( EstockTools::isUserShop() ) {
            if( ($a = EstockTools::getUserAllFlags()) !== null ) {
                $aa = explode("-",$a);
                return $aa[1];
            } else {
                return false;
            }
        } elseif( EstockTools::isUserB2b() ) {
            return EstockTools::getB2bParams()['profile']['adr_id'];
        } else {
            return false;
        }
    }

    public static function getUserAdrRepliId() {
        if( EstockTools::isUserShop() ) {
            if( ($a = EstockTools::getUserAllFlags()) !== null ) {
                $aa = explode("-",$a);
                return $aa[2];
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    public static function getDbname() {
        //return Yii::$app->db->createCommand("SELECT db_name()")->queryScalar();
     return "PGestock";
 }





    /**
     * toto nahradi estock2 tlacove zostavy
     * 
     **/
    private static function odlink($mmove_id,$tpl,$heading=''){
        return 
        ButtonDropdown::widget([
            'label' => $tpl,
            'options' => ['class' => ["btn", "btn-primary"]],
            'dropdown' => [
                'items' => [
                    ['label' => ".ODT", 'url' => ['print/getodt', 'heading'=>$heading, 'xfirma'=>EstockTools::getFirma($mmove_id), 'tpl' => $tpl,'move_id'=>$mmove_id], 'linkOptions' => ['data-pjax' => 0, 'target' => '_blank']],
                    ['label' => ".PDF", 'url' => ['print/getanydoc', 'heading'=>$heading, 'xfirma'=>EstockTools::getFirma($mmove_id), 'tpl' => $tpl,'move_id'=>$mmove_id,'doctype'=>'pdf'], 'linkOptions' => ['data-pjax' => 0, 'target' => '_blank']],
                    ['label' => ".DOC", 'url' => ['print/getanydoc', 'heading'=>$heading, 'xfirma'=>EstockTools::getFirma($mmove_id), 'tpl' => $tpl,'move_id'=>$mmove_id,'doctype'=>'doc'], 'linkOptions' => ['data-pjax' => 0, 'target' => '_blank']],
                    ['label' => ".HTML", 'url' => ['print/getanydoc', 'heading'=>$heading, 'xfirma'=>EstockTools::getFirma($mmove_id), 'tpl' => $tpl,'move_id'=>$mmove_id,'doctype'=>'html'], 'linkOptions' => ['data-pjax' => 0, 'target' => '_blank']],
                ],
            ],
        ]);


            // Html::a("$tpl .ODT",'',['onclick' => "window.open ('".Url::toRoute(['print/getodt', 'tpl' => $tpl,'move_id'=>$mmove_id])."'); return false", 'class' => 'btn btn-warning'])."&nbsp;&nbsp;".
            // Html::a(".PDF",'',['onclick' => "window.open ('".Url::toRoute(['print/getanydoc', 'tpl' => $tpl,'move_id'=>$mmove_id,'doctype'=>'pdf'])."'); return false", 'class' => 'btn btn-success'])."&nbsp;&nbsp;".
            // Html::a(".DOC",'',['onclick' => "window.open ('".Url::toRoute(['print/getanydoc', 'tpl' => $tpl,'move_id'=>$mmove_id,'doctype'=>'doc'])."'); return false", 'class' => 'btn btn-success'])."&nbsp;&nbsp;".
            // Html::a(".HTML",'',['onclick' => "window.open ('".Url::toRoute(['print/getanydoc', 'tpl' => $tpl,'move_id'=>$mmove_id,'doctype'=>'html'])."'); return false", 'class' => 'btn btn-success'])."&nbsp;&nbsp;";

    }

    private static function odXLSlink($mmove_id,$tpl, $heading = ''){
        return "&nbsp;&nbsp;".
        ButtonDropdown::widget([
            'label' => $tpl,
            'options' => ['class' => ["btn", "btn-warning"]],
            'dropdown' => [
                'items' => [
                    ['label' => ".ODS", 'url' => ['print/getanydoc', 'tpl' => $tpl,'move_id'=>$mmove_id,'doctype'=>'ods', 'heading'=>$heading], 'linkOptions' => ['data-pjax' => 0, 'target' => '_blank']],
                    ['label' => ".PDF", 'url' => ['print/getanydoc', 'tpl' => $tpl,'move_id'=>$mmove_id,'doctype'=>'pdf', 'heading'=>$heading], 'linkOptions' => ['data-pjax' => 0, 'target' => '_blank']],
                    ['label' => ".DOC", 'url' => ['print/getanydoc', 'tpl' => $tpl,'move_id'=>$mmove_id,'doctype'=>'xls', 'heading'=>$heading], 'linkOptions' => ['data-pjax' => 0, 'target' => '_blank']],
                ],
            ],
        ]);

            // Html::a("$tpl .ODS",'',['onclick' => "window.open ('".Url::toRoute(['print/getanydoc', 'tpl' => $tpl,'move_id'=>$mmove_id,'doctype'=>'ods', 'heading'=>$heading])."'); return false", 'class' => 'btn btn-warning'])."&nbsp;&nbsp;".
            // Html::a(".PDF",'',['onclick' => "window.open ('".Url::toRoute(['print/getanydoc', 'tpl' => $tpl,'move_id'=>$mmove_id,'doctype'=>'pdf', 'heading'=>$heading])."'); return false", 'class' => 'btn btn-success'])."&nbsp;&nbsp;".
            // Html::a(".XLS",'',['onclick' => "window.open ('".Url::toRoute(['print/getanydoc', 'tpl' => $tpl,'move_id'=>$mmove_id,'doctype'=>'xls', 'heading'=>$heading])."'); return false", 'class' => 'btn btn-success'])."&nbsp;&nbsp;";

    }
    

    
    public static function getprintlinks($mmove_id){

        if ( empty($mmove_id)) {
            return false;
        }

        $printstring = '';
        $this_m_type_id = Yii::$app->db->createCommand("select m_type_id from movement where move_id=:move_id")->bindValue(':move_id',$mmove_id)->queryScalar();
        $xdoc = Yii::$app->db->createCommand("select doc_id, \"name\" as nnn from m_type where m_type_id=:m_type_id")
        ->bindValue(':m_type_id',$this_m_type_id)->queryOne();
        $mdoc_id = $xdoc['doc_id'];
        $user_repli = Yii::$app->user->identity->repli;

        if( in_array($this_m_type_id, [69,131,145,146,138,102,140,168,170,109,23,64,66,88,94,95,115,116,117,118,137,139,167,169,24,
        87,36,132,110,130,147,149,17,150,151,152,153,154,155,156,157,
        180,181,182,183
        ] ) ) {
            $printstring .= EstockTools::odlink($mmove_id,'fa',$xdoc['nnn']);
            $printstring .= EstockTools::odlink($mmove_id,'faqr',$xdoc['nnn']);
            $enname = ['Faktúra' => 'Invoice', 'Dobropis' => 'Credit Note', 'Dodací list' => 'Delivery note' ];
            $enxdoc = $xdoc['nnn'];
            foreach( $enname as $i=>$v ) { if ( mb_strpos($xdoc['nnn'],$i ) !== false ) $enxdoc = $v; }
            $printstring .= EstockTools::odlink($mmove_id,'faEN',$enxdoc);
            $printstring .= EstockTools::odlink($mmove_id,'faENqr',$enxdoc);
        }
        // General model list with currency
        if( $user_repli == 0 ){
            $printstring .= EstockTools::odlink($mmove_id,'fa+dod',$xdoc['nnn']);
            $printstring .= EstockTools::odlink($mmove_id,'fa+dod_c',$xdoc['nnn']);
            $printstring .= EstockTools::odlink($mmove_id,'mat+dod',$xdoc['nnn']);
        }
        else if( $user_repli == 2 ){
            $printstring .= EstockTools::odlink($mmove_id,'inv+lst',$xdoc['nnn']);
        }

                //OPEN DOCUMENTS
        // if( $this_m_type_id == "113") $printstring .= EstockTools::odlink($mmove_id,'gw_faktura');
        // if( $this_m_type_id == "113") $printstring .= EstockTools::odlink($mmove_id,'sw_dodaci');
        if( $this_m_type_id == "102") $printstring .= EstockTools::odlink($mmove_id,'gw_faktura');
        if( $this_m_type_id == "102") $printstring .= EstockTools::odlink($mmove_id,'sw_dodaci');
        if( $this_m_type_id == "87") $printstring .= EstockTools::odlink($mmove_id,'gw_faktura');
        if( $this_m_type_id == "27") $printstring .= EstockTools::odlink($mmove_id,'mat+dodSWD', 'Predfaktúra');
        if( $this_m_type_id == "110"  or $this_m_type_id == "27" ) { $printstring .= EstockTools::odlink($mmove_id,'racio_p6');
        $printstring .= EstockTools::odXLSlink($mmove_id,'p6');
        $printstring .= EstockTools::odXLSlink($mmove_id,'p6b');                
    }
            if( $this_m_type_id == "109" or $this_m_type_id == "69" ) $printstring .= EstockTools::odlink($mmove_id,'racio_p6'); //Novy EU dobropis
            if( $this_m_type_id == "130" or $this_m_type_id == "132" ) {
                $printstring .= EstockTools::odlink($mmove_id,'sw_faktura');
                $printstring .= EstockTools::odlink($mmove_id,'sw_faktura_centrala');
                $printstring .= EstockTools::odlink($mmove_id,'sw_dodaci');
            }
            if( $this_m_type_id == "25" or $this_m_type_id == "26" or $this_m_type_id == "158" ) { 
                //zalohova faktura                                                                                                                           
                $printstring .= EstockTools::odlink($mmove_id,'zal',$xdoc['nnn']);
            }
            if( $this_m_type_id == "137"  or $this_m_type_id == "147" ) {
                $printstring .= EstockTools::odlink($mmove_id,'eshop_sw_faktura');
                $printstring .= EstockTools::odlink($mmove_id,'eshop_sw_faktura_centrala');
                $printstring .= EstockTools::odlink($mmove_id,'eshop_fa-zal',$xdoc['nnn']);
            }
            if( $this_m_type_id == "139" ) {
                $printstring .= EstockTools::odlink($mmove_id,'eshop_sw_ex_faktura');
                $printstring .= EstockTools::odlink($mmove_id,'eshop_sw_ex_faktura_centrala');
            }
            if( EstockTools::isMtypeWithAdvancedPayment($this_m_type_id) ) {
                $printstring .= EstockTools::odlink($mmove_id,'fa-zal',$xdoc['nnn']);
            }
            if( $this_m_type_id == "24" ) {
                $printstring .= EstockTools::odlink($mmove_id,'racio_faktura');
                $printstring .= EstockTools::odlink($mmove_id,'racio_faktura_centrala');
                $printstring .= EstockTools::odlink($mmove_id,'racio_dod_moc');
                $printstring .= EstockTools::odlink($mmove_id,'racio_dod_centrala_moc');
                $printstring .= EstockTools::odlink($mmove_id,'racio_p1');
                $printstring .= EstockTools::odXLSlink($mmove_id,'p1');
            }
            if( in_array($this_m_type_id, [102,17,87,110,149]  ) ) {
                $printstring .= EstockTools::odlink($mmove_id,'racio_faktura');
                $printstring .= EstockTools::odlink($mmove_id,'racio_faktura_centrala');
                $printstring .= EstockTools::odlink($mmove_id,'racio_dod');
                $printstring .= EstockTools::odlink($mmove_id,'racio_dod_centrala');
                $printstring .= EstockTools::odlink($mmove_id,'racio_p1');
                $printstring .= EstockTools::odXLSlink($mmove_id,'p1');
            }

            if( $this_m_type_id == "36"  ) {
                $printstring .= EstockTools::odlink($mmove_id,'sw_fakturacz');
                $printstring .= EstockTools::odlink($mmove_id,'sw_dodacicz');
            }
            if( $this_m_type_id == "71"  ) {
                $printstring .= EstockTools::odlink($mmove_id,'sw_dobropiscz');
                $printstring .= EstockTools::odlink($mmove_id,'sw_dodacicz');
            }

            if( $this_m_type_id == "131" ) {                                                                                                                                                                         
                $printstring .= EstockTools::odlink($mmove_id,'sw_dobropis');
                $printstring .= EstockTools::odlink($mmove_id,'sw_dodaci');
            }
            if( $this_m_type_id == "138"  or $this_m_type_id == "148" ) {                                                                                                                           
                $printstring .= EstockTools::odlink($mmove_id,'eshop_sw_dobropis');
            }
            if( $this_m_type_id == "140" ) {                                                                                                                                                                         
                $printstring .= EstockTools::odlink($mmove_id,'eshop_sw_ex_dobropis');
            }
            if( $user_repli == 2 || $mdoc_id == "Hf" ||  $mdoc_id == "Hp" ||  $mdoc_id == "Ho" ||  $mdoc_id == "HO"  ||  $mdoc_id == "Ha" ||  $mdoc_id == "Hi"  ||  $mdoc_id == "Hj"  ||  $mdoc_id == "Hl"  ||  $mdoc_id == "Hr") {
                $printstring .= EstockTools::odlink($mmove_id,'purchasing_list');
//              $printstring .= EstockTools::odlink($mmove_id,'chrono_invoice');
                $printstring .= EstockTools::odlink($mmove_id,'chrono_list');
                $printstring .= EstockTools::odlink($mmove_id,'chrono_p4');
            }
            //Pictures pre vsetky dokumenty. DOC a HTML nefunguje, preto takto.
            //$printstring .= EstockTools::odXLSlink($mmove_id,'pict');
            //$printstring .= EstockTools::odlink($mmove_id,'pict1');
            $printstring .= ButtonDropdown::widget([
                'label' => "Labels",
                'options' => ['class' => ["btn", "btn-secondary"]],
                'dropdown' => [
                    'items' => [
                        ['label' => "L4731 7x27=189", 'url' => ['print/getpdflabels','id'=>$mmove_id, 'offset'=>0, 'labeltype'=>'L4731', 'first'=>99999,'from'=>1, 'money'=>'no', 'price'=>'d.price' ], 'linkOptions' => ['data-pjax' => 0, 'target' => '_blank']],
                        ['label' => "L4731 p1 price", 'url' => ['print/getpdflabels','id'=>$mmove_id, 'offset'=>0, 'labeltype'=>'L4731', 'first'=>99999,'from'=>1, 'money'=>'no', 'price'=>'p1' ], 'linkOptions' => ['data-pjax' => 0, 'target' => '_blank']],
                        ['label' => "L4731 p6 price", 'url' => ['print/getpdflabels','id'=>$mmove_id, 'offset'=>0, 'labeltype'=>'L4731', 'first'=>99999,'from'=>1, 'money'=>'no', 'price'=>'p6' ], 'linkOptions' => ['data-pjax' => 0, 'target' => '_blank']],
                        ['label' => "L4732 5x16=80", 'url' => ['print/getpdflabels','id'=>$mmove_id, 'offset'=>0, 'labeltype'=>'L4732', 'first'=>99999,'from'=>1, 'money'=>'no' ], 'linkOptions' => ['data-pjax' => 0, 'target' => '_blank']],
                        ['label' => "EU30019 5x13=65", 'url' => ['print/getpdflabels','id'=>$mmove_id, 'offset'=>0, 'labeltype'=>'EU30019', 'first'=>99999,'from'=>1, 'money'=>'no' ], 'linkOptions' => ['data-pjax' => 0, 'target' => '_blank']],
                        // ['label' => "QR CODE list", 'url' => ['eshop/docqrlist', 'd1'=>$this_doc['d1'],'move_id'=>$mmove_id, 'firma'=>$this_doc['firma'], 'doc'=>$this_doc['name'] ], 'linkOptions' => ['data-pjax' => 0, 'target' => '_blank']],
                        // ['label' => "Picture and QR list", 'url' => ['eshop/docpiclist','d1'=>$this_doc['d1'],'move_id'=>$mmove_id, 'firma'=>$this_doc['firma'], 'doc'=>$this_doc['name']], 'linkOptions' => ['data-pjax' => 0, 'target' => '_blank']],
                    ],
                ],
            ]);
          if( $this_m_type_id == "110"  or $this_m_type_id == "109"  or $this_m_type_id == "69" ) { //ceske cislo iban 
            $printstring .= EstockTools::odlink($mmove_id,'racio_fakturaCZ');
            $printstring .= EstockTools::odlink($mmove_id,'racio_faktura_centralaCZ');
            $printstring .= EstockTools::odlink($mmove_id,'racio_dodCZ');
            $printstring .= EstockTools::odlink($mmove_id,'racio_dod_centralaCZ');
        }


    //Vienna
        switch( $this_m_type_id ) {
            case "400":
            $printstring .= EstockTools::odlink($mmove_id,'intrex_uhren','LIEFERSCHEIN');
            $printstring .= EstockTools::odlink($mmove_id,'intrex_modell','LIEFERSCHEIN');
            $printstring .= EstockTools::odlink($mmove_id,'intrex_uhren_mitMwSt','LIEFERSCHEIN');
            $printstring .= EstockTools::odlink($mmove_id,'intrex_modell_mitMwSt','LIEFERSCHEIN');
            break;
            case "401":
            $printstring .= EstockTools::odlink($mmove_id,'intrex_uhren_mitMwSt','RECHNUNG');
            $printstring .= EstockTools::odlink($mmove_id,'intrex_modell_mitMwSt','RECHNUNG');
            break;
            case "403":
            $printstring .= EstockTools::odlink($mmove_id,'intrex_uhren','RECHNUNG');
            $printstring .= EstockTools::odlink($mmove_id,'intrex_modell','RECHNUNG');
            break;
            case "402":
            $printstring .= EstockTools::odlink($mmove_id,'intrex_modell_mitMwSt','Barverkauf lieferschein');
            break;
            case "404":
            $printstring .= EstockTools::odlink($mmove_id,'intrex_modell','WARENANKUNFT');
            $printstring .= EstockTools::odlink($mmove_id,'intrex_modell_mitMwSt','WARENANKUNFT');
            $printstring .= EstockTools::odlink($mmove_id,'intrex_uhren_mitMwSt','WARENANKUNFT');
            break;
            case "405":
            $printstring .= EstockTools::odlink($mmove_id,'intrex_modell','GUTSCHRIFT');
            $printstring .= EstockTools::odlink($mmove_id,'intrex_modell_mitMwSt','Gutschrift');
            $printstring .= EstockTools::odlink($mmove_id,'intrex_uhren','GUTSCHRIFT');
            $printstring .= EstockTools::odlink($mmove_id,'intrex_uhren_mitMwSt','Gutschrift');
            break;
            case "406":
            $printstring .= EstockTools::odlink($mmove_id,'intrex_modell','Bestellung');
            $printstring .= EstockTools::odlink($mmove_id,'intrex_modell_mitMwSt','Bestellung');
            break;
            case "407":
            $printstring .= EstockTools::odlink($mmove_id,'intrex_modell','Preisangebot');
            $printstring .= EstockTools::odlink($mmove_id,'intrex_modell_mitMwSt','Preisangebot');
            break;
            case "408":
            $printstring .= EstockTools::odlink($mmove_id,'intrex_modell_Gutschrift','Gutschrift PA');
            $printstring .= EstockTools::odlink($mmove_id,'intrex_modell_Gutschrift_mitMwSt','Gutschrift PA');
            break;
            case "376":
            case "427":
            $printstring .= EstockTools::odlink($mmove_id,'zafir_modell_mitMwSt','Preisangebot');
            $printstring .= EstockTools::odlink($mmove_id,'zafir_uhren_mitMwSt','LIEFERSCHEIN');
            $printstring .= EstockTools::odlink($mmove_id,'zafir_modell_mitMwSt','LIEFERSCHEIN');
            break;
            case "428":
            $printstring .= EstockTools::odlink($mmove_id,'zafir_uhren_mitMwSt','Gutschrift PA');
            $printstring .= EstockTools::odlink($mmove_id,'intrex_modell_mitMwSt','Gutschrift PA');
            break;
            case "426":
            $printstring .= EstockTools::odlink($mmove_id,'zafir_modell_mitMwSt','Bestellung');
            break;
            case "425":
            $printstring .= EstockTools::odlink($mmove_id,'zafir_uhren_Gutschrift_mitMwSt','Gutschrift');
            $printstring .= EstockTools::odlink($mmove_id,'zafir_modell_Gutschrift_mitMwSt','Gutschrift');
            break;
            case "424":
            $printstring .= EstockTools::odlink($mmove_id,'zafir_uhren_mitMwSt','WARENANKUNFT');
            $printstring .= EstockTools::odlink($mmove_id,'zafir_modell_mitMwSt','WARENANKUNFT');
            break;
            case "422":
            $printstring .= EstockTools::odlink($mmove_id,'zafir_uhren_mitMwSt','Barverkauf');
            $printstring .= EstockTools::odlink($mmove_id,'zafir_modell_mitMwSt','Barverkauf lieferschein');
            break;
            case "423":
            $printstring .= EstockTools::odlink($mmove_id,'zafir_modell_mitMwSt','Rechnung');
            break;
            case "421":
            $printstring .= EstockTools::odlink($mmove_id,'zafir_uhren_mitMwSt','Rechnung');
            $printstring .= EstockTools::odlink($mmove_id,'zafir_modell_mitMwSt','Rechnung');
            break;
            case "420":
            $printstring .= EstockTools::odlink($mmove_id,'zafir_uhren_mitMwSt','LIEFERSCHEIN');
            $printstring .= EstockTools::odlink($mmove_id,'zafir_modell_mitMwSt','LIEFERSCHEIN');
            break;
        }


        // special for isUserW
        if ( EstockTools::isUserW() ) {
            $printstring = EstockTools::odlink($mmove_id,'wfaqr',$xdoc['nnn']);
        }

        return $printstring;

    }

    public static function getRelativeTime($timestamp) {
        $now = new \DateTime();
        $date = \DateTime::createFromFormat('Y-m-d H:i:s.uO', $timestamp);
        
        if (!$date) {
            return '';
        }
        
        $diff = $now->diff($date);
        
        if ($diff->y > 1) {
            return $diff->y . ' years ago';
        } elseif ($diff->y > 0) {
                return $diff->y . 'a year ago';
        } elseif ($diff->m > 1) {
            return $diff->m . ' months ago';
        } elseif ($diff->m > 0) {
           return $diff->m . ' month ago';
        } elseif ($diff->d > 1) {
            return $diff->d . ' days ago';
        } elseif ($diff->d > 0) {
            return $diff->d . ' day ago';
        } elseif ($diff->h > 1) {
            return $diff->h . ' hours ago';
        } elseif ($diff->h > 0) {
            return $diff->h . ' hour ago';
        } elseif ($diff->i > 1) {
            return $diff->i . ' minutes ago';
        } elseif ($diff->i > 0) {
            return $diff->i . ' minute ago';
        } elseif ($diff->s > 0) {
            return $diff->s . ' seconds ago';
        }
        
        return '';
    }

    public static function getGitVersion() {

        $commitVersion = trim(exec('git describe --tags'));

        $commitDate = new \DateTime(trim(exec('git log -n1 --pretty=%ci HEAD')));
        $commitDate->setTimezone(new \DateTimeZone('CET'));

        return sprintf('Version (date): %s (%s)',  $commitVersion, $commitDate->format('Y-m-d H:i:s'));

    }


}
