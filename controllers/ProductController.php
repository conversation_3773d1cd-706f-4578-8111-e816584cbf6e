<?php

namespace app\controllers;

use Yii;
use app\models\Product;
use app\models\ProductSearch;
use app\models\Product2Search;
use app\models\BatchUpdateProductForm;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\helpers\Json;
use yii\data\ActiveDataProvider;
use yii\db\Query;

/**
 * ProductController implements the CRUD actions for Product model.
 */
class ProductController extends Controller
{
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['post'],
                ],
            ],
            'access' => [
                'class' => \yii\filters\AccessControl::class,
                'rules' => [
                    [
                        'allow' => true,
                        'actions' => ['pdf', 'stockcard', 'viewdetailshop', 'productlist', 'indexinshop', 'createinshop', 'updateinshop'],
                        'matchCallback' => function ($rule, $action) {
                            return EstockTools::isUserShop() || EstockTools::isUserStock() || EstockTools::isUserWman() || EstockTools::isUserWstock();
                        }
                    ],
                    [
                        'allow' => true,
                        'actions' => ['productlist'],
                        'matchCallback' => function ($rule, $action) {
                            return EstockTools::isUserB2b();
                        }
                    ],
                    [
                        'allow' => true,
                        'actions' => ['index', 'view', 'create', 'update', 'delete'],
                        'matchCallback' => function ($rule, $action) {
                            return EstockTools::isUserStock() || EstockTools::isUserWman() || EstockTools::isUserWstock();
                        }
                    ],
                    [
                        'allow' => true,
                        'actions' => ['batchupdate'],
                        'matchCallback' => function ($rule, $action) {
                            return EstockTools::isUserDba() || EstockTools::isUserVip() || EstockTools::isUserManagerProd();
                        }
                    ],
                    [
                        'allow' => false
                    ]
                ]
            ]
        ];
    }

    /**
     * Lists all Product models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new Product2Search();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Lists all Product models in shop.
     * @return mixed
     */
    public function actionIndexinshop()
    {
        $searchModel = new ProductSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('indexinshop', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single Product model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        $model = $this->findModel($id);
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * POZOR!!! Nepouzivam to uz, dal som im cely onstock vysledok
     */

    public function actionViewdetailshop()
    {
        if (isset($_POST['expandRowKey']) && EstockTools::isUserShop()) {


            $query = new Query;
            $query->select("kod, model, d1, t.name, t.m_type_id, if  m.stock_id1 is not null and 
                m.stock_id1 like 'sj'  then floor(-d.pcs) else floor(d.pcs) endif pcs, d.price, d.detail_info")
                ->from("movement m, m_detail d, m_type t, product p")
                ->where("m.move_id=d.move_id and m.repli_id=d.m_repli_id and t.m_type_id=m.m_type_id and p.mat_id=d.mat_id and 
                (m.stock_id1 like 'sj' or m.stock_id2 like 'sj') and d.mat_id = " . $_POST['expandRowKey'] . " and 
                year(d1) = year(now()) and
                m.adr_id = " . EstockTools::getUserAdrId())
                ->orderBy(['d1' => SORT_DESC]);

            $dataProvider = new ActiveDataProvider([
                'query' => $query,
                'pagination' => [
                    'pageSize' => 200,
                ],
            ]);

            $sum1 = Yii::$app->db->createCommand("SELECT sum( case when m.stock_id1 is not null and 
                m.stock_id1 like 'sj'  then -d.pcs else d.pcs end ) from movement m, m_detail d
                where m.move_id=d.move_id and (m.stock_id1 like 'sj' or m.stock_id2 like 'sj') and d.mat_id = " . $_POST['expandRowKey'] . " and 
                m.adr_id = " . EstockTools::getUserAdrId())->queryScalar();


            $query2 = new Query;
            $query2->select("(select kod from product where mat_id=shopremains2.mat_id) kod, (select model from product where mat_id=shopremains2.mat_id) model, (select firma from address where adr_id=shopremains2.adr_id) firma, (select city from address where adr_id=shopremains2.adr_id) city, sum(remains) remains")
                ->from("shopremains2")
                ->where("mat_id = " . $_POST['expandRowKey'])
                ->groupBy("shopremains2.remains,shopremains2.mat_id,shopremains2.adr_id")
                ->having("remains>0")
                ->orderBy(['firma' => SORT_ASC]);

            $dataProvider2 = new ActiveDataProvider([
                'query' => $query2,
                'pagination' => [
                    'pageSize' => 200,
                ],
            ]);

            return $this->renderPartial('_stockcard', ['model' => $dataProvider, 'sum1' => $sum1, 'model2' => $dataProvider2]);
        } else {
            return '<div class="alert alert-danger">No data found</div>';
        }
    }

    /**
     * Generates grid for stock card
     */
    public function actionStockcard()
    {

        $query = new Query;
        $stocks = $query->select(" stockname ")
            ->from("
                (select 
                trim(stock_id2) stockname from movement m, m_detail d
                where d.move_id=m.move_id and d.mat_id='" . $_POST['expandRowKey'] . "' and trim(stock_id2) is not null
                union all
                select trim(stock_id1) stockname from movement m, m_detail d
                where d.move_id=m.move_id and d.mat_id='" . $_POST['expandRowKey'] . "' and trim(stock_id1) is not null
            ) as unionTable")
            ->groupBy("stockname")
            ->orderBy(["stockname" => SORT_ASC])
            ->all();

        $out = "";
        if ($stocks !== null) {
            foreach ($stocks as $key => $val) {
                foreach ($val as $key2 => $val2) {
                    if (!empty($val2)) {

                        $q = new Query;
                        $detData = $q->select([
                            "m.move_id",
                            "m.m_type_id",
                            "xname" => "t.name",
                            "number",
                            "d1",
                            "emp_id",
                            "pcs" => "sum(case when m.stock_id1 is not null and m.stock_id1='" . $val2 . "' then -pcs else pcs end)::numeric",
                            "m.stock_id1 as from_stock",
                            "m.stock_id2 as to_stock",
                            "left(m.text1,20) as text1",
                            "left(m.text2,20) as text2",
                            " left(a.firma,20) as firma ",
                            " getmovementserials(" . $_POST['expandRowKey'] . ", m.move_id) as serials "
                        ])
                            ->from("movement m, m_detail d, m_type t, address a ")
                            ->where(" a.adr_id=m.adr_id and m.move_id=d.move_id and
                            t.m_type_id=m.m_type_id and (m.stock_id1 = '" . $val2 . "' or m.stock_id2 = '" . $val2 . "' )
                            and d.mat_id=" . $_POST['expandRowKey'])
                            ->groupBy("m.move_id,m.m_type_id,t.name,number,d1,emp_id,m.stock_id1,a.firma,t.moving")
                            ->orderBy(["d1" => SORT_ASC, "pcs" => SORT_DESC, "t.moving" => SORT_ASC]);


                        $sumData = Yii::$app->db->createCommand("select sum(case when m.stock_id1 is not null and m.stock_id1='" . $val2 . "' then -pcs else pcs end) ss from movement m, m_detail d, m_type t, address a where a.adr_id=m.adr_id and m.move_id=d.move_id and
                            t.m_type_id=m.m_type_id and (m.stock_id1 = '" . $val2 . "' or m.stock_id2 = '" . $val2 . "' )
                            and d.mat_id=" . $_POST['expandRowKey'])->queryScalar();
                        $stockname = \app\models\StockDetail::find()->where(['stock_id' => $val2])->one()->sdescr;
                        $out .= $this->renderPartial('_stockcardtitle', ['infoStockCard' => $stockname . ', remains: ' . $sumData]);
                        $dataProvider = new ActiveDataProvider([
                            'query' => $detData,
                            'pagination' => [
                                'pageSize' => 10000,
                            ],
                        ]);

                        // print_r( $dataProvider->getData());
                        $out .= $this->renderPartial('_stockcard2', ['data1' => $dataProvider, 'idcko' => 'id' . $val2]);
                    }
                }
            }
        }
        return $out;
    }


    /** 
     * Your controller action to fetch the list
     */
    public function actionProductlist($q = null)
    {

        if (EstockTools::isUserB2b()) {
            $fil = "";
            $first = true;
            $kodfilters = explode(',', EstockTools::getB2bParams()['profile']['productWhere']);
            foreach ($kodfilters as $key => $kodlike) {
                if ($first) {
                    $first = false;
                } else {
                    $fil .= " or ";
                }
                $fil .= "kod ilike '" . $kodlike . "%'";
            }

            $query = "SELECT model, mat_id, kod FROM product where (" . $fil . ") and model ILIKE :param order by model limit 10";
        } else {
            $query = "SELECT model, mat_id, kod FROM product where model ILIKE :param order by model limit 10";
        }
        $result = Product::findBySql($query, [':param' => $q . '%'])->all();

        $out = [];
        foreach ($result as $d) {

            $out[] = ['model' => $d['model'], 'mat_id' => $d['mat_id'], 'kod' => $d['kod']];
        }
        return $this->asJson($out);
    }

    /**
     * Creates a new Product model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new Product();
        if ($model->load(Yii::$app->request->post())) {
            if (empty($model->mat_id)) {
                $model->mat_id = Yii::$app->db->createCommand("select max(mat_id)+1 from product")->queryScalar();
            }
            if (empty($model->ean13)) {
                $model->ean13 = $model->mat_id;
            }
            try {
                $model->save(false);
            } catch (\yii\db\Exception $e) {
                Yii::$app->session->setFlash('error', $e->getMessage());
                return $this->render('create', [
                    'model' => $model,
                ]);
            }
            Yii::$app->session->setFlash('success', "Model " . $model->model . " created.");
            return $this->redirect(['index', 'sort' => '-mat_id']);
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }


    /**
     * Creates a new Product model simple only for shop assistants.
     * @return mixed
     */
    public function actionCreateinshop()
    {
        $model = new Product();
        if ($model->load(Yii::$app->request->post())) {
            if (empty($model->mat_id)) {
                $model->mat_id = Yii::$app->db->createCommand("select max(mat_id)+1 from product")->queryScalar();
            }
            if (empty($model->ean13)) {
                $model->ean13 = $model->mat_id;
            }
            try {
                $model->save(false);
            } catch (\yii\db\Exception $e) {
                Yii::$app->session->setFlash('error', $e->getMessage());
                return $this->render('create', [
                    'model' => $model,
                ]);
            }
            Yii::$app->session->setFlash('success', "Model " . $model->model . " created.");
            return $this->redirect(['indexinshop']);
        } else {
            return $this->render('createinshop', [
                'model' => $model,
            ]);
        }
    }


    /**
     * Updates an existing Product model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->mat_id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing Product model - form for shop assistants.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdateinshop($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['indexinshop']);
        } else {
            return $this->render('updateinshop', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing Product model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        try {
            $this->findModel($id)->delete();
        } catch (\yii\db\Exception $e) {

            Yii::$app->session->setFlash('error', $e->getMessage());
        } catch (\Exception $e) {

            Yii::$app->session->setFlash('error', $e);
        }


        return $this->redirect(['index']);
    }

    /**
     * 
     * Export Product information into PDF format.
     * @param integer $id
     * @return mixed
     */
    public function actionPdf($id)
    {
        $model = $this->findModel($id);

        $content = $this->renderAjax('_pdf', [
            'model' => $model,
        ]);

        $pdf = new \kartik\mpdf\Pdf([
            'mode' => \kartik\mpdf\Pdf::MODE_CORE,
            'format' => \kartik\mpdf\Pdf::FORMAT_A4,
            'orientation' => \kartik\mpdf\Pdf::ORIENT_PORTRAIT,
            'destination' => \kartik\mpdf\Pdf::DEST_BROWSER,
            'content' => $content,
            'cssFile' => '@vendor/kartik-v/yii2-mpdf/assets/kv-mpdf-bootstrap.min.css',
            'cssInline' => '.kv-heading-1{font-size:18px}',
            'options' => ['title' => \Yii::$app->name],
            'methods' => [
                'SetHeader' => [\Yii::$app->name],
                'SetFooter' => ['{PAGENO}'],
            ]
        ]);

        return $pdf->render();
    }


    /**
     * Finds the Product model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return Product the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Product::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }




    /**
     * Update product prices
     */
    public function actionBatchupdate()
    {

        $model = new BatchUpdateProductForm();
        if ($model->load(Yii::$app->request->post()) && $model->validate()) {

            try {

                $mactiontxt = "Executed<hr>";
                $csla = explode("\n", $model->updatebox);
                $cols = explode(",", str_replace("\t", "", $csla[0]));
                $colkey = strtolower($cols[0]);
                $whatupdate = "";
                for ($i = 1; $i < count($cols); $i++) {
                    if ($whatupdate !== "") {
                        $whatupdate .= ", ";
                    }
                    $whatupdate .= $cols[$i] . "=:par" . $i;
                }
                $columns = explode(",", str_replace("\t", "", $csla[0]));
                for ($i = 1; $i < count($csla); $i++) {
                    $riadok = explode(",", str_replace("\t", "", $csla[$i]));
                    if (count($riadok) > 1) {
                        $keyval = $riadok[0];
                        $_query = "update product set " . $whatupdate . " where " . $colkey . " = :keypar ";
                        $cmd = Yii::$app->db->createCommand($_query);
                        $cmd->bindValue(':keypar', $keyval);
                        $mactiontxt .= $_query . " <b>" . $keyval . "</b> ";
                        for ($j = 1; $j < count($riadok); $j++) {
                            $cmd->bindValue(':par' . $j, trim($riadok[$j]));
                            $mactiontxt .= " <b>:par" . $j . "</b> " . $riadok[$j];
                        }
                        $mactiontxt .= "<br>\n";
                    }
                    $cmd->query();
                }
            } catch (\yii\db\Exception $e) {
                Yii::$app->session->setFlash('error', $e->getMessage());
            }

            Yii::$app->session->setFlash('success', $mactiontxt);
        } else {
            return $this->render('batchupdate', ['form' => $model]);
        }

        return $this->redirect(['index']);
    }
}
