<?php

namespace app\controllers;

use Yii;
use app\models\Minfo;
use app\models\MinfoSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * MinfoController implements the CRUD actions for Minfo model.
 */
class MinfoController extends Controller
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all Minfo models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new MinfoSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single Minfo model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new Minfo model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new Minfo();

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing Minfo model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing Minfo model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the Minfo model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return Minfo the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Minfo::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }

    /**
     * Searches for Minfo records for autocomplete.
     * @return array JSON response
     */
    public function actionSearch()
    {
        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $term = \Yii::$app->request->get('term');

        $query = Minfo::find()
            ->where(['or',
                ['ilike', 'unaccent(isurname)', '%'. $term . '%', false],
                ['ilike', 'unaccent(icompany)', '%'. $term . '%', false],
                ['ilike', 'isurname', '%'. $term . '%', false],
                ['ilike', 'icompany', '%'. $term . '%', false],
                ['ilike', 'iicdph', $term . '%', false]
            ])
            ->orderBy(['id' => SORT_DESC])
            ->limit(10);

        $models = $query->all();
        
        $results = [];
        foreach ($models as $model) {
            // add total0+total1+total2 to label from movement table where move_id=$model->move_id
            $total = \app\models\Movement::find()->where(['move_id' => $model->move_id])->sum('total0+total1+total2'); 
            $movement = \app\models\Movement::findOne(['move_id' => $model->move_id]);
            $total .= ' ' . $movement->m_type_id . ' ' . $movement->d1;
            $label = $model->isurname . ' - ' . $model->icompany . ' (' . $model->iicdph . ') - ' . $total;
            $results[] = [
                'label' => $label,
                'value' => $label,
                'isurname' => $model->isurname,
                'icompany' => $model->icompany,
                'iicdph' => $model->iicdph,
                'iname' => $model->iname,
                'istreet' => $model->istreet,
                'icity' => $model->icity,
                'izip' => $model->izip,
                'iphone' => $model->iphone,
                'icountry' => $model->icountry,
                'iico' => $model->iico,
                'idic' => $model->idic,
                'dcompany' => $model->dcompany,
                'dname' => $model->dname,
                'dsurname' => $model->dsurname,
                'dstreet' => $model->dstreet,
                'dcity' => $model->dcity,
                'dzip' => $model->dzip,
                'dphone' => $model->dphone,
                'dcountry' => $model->dcountry,

            ];
        }
        
        return $results;
    }

}
