<?php

namespace app\controllers;

use Yii;
use app\models\ShoptetOrders;
use app\models\ShoptetOrdersSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * ShoptetordersController implements the CRUD actions for ShoptetOrders model.
 */
class ShoptetordersController extends Controller
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'access' => [
                'class' => \yii\filters\AccessControl::class,
                'rules' => [
                    [
                        'allow' => true,
                        'actions' => ['index', 'view','create','update','delete', 'shoptetajaxdetail', 'runimport', 'create-movement'],
                        'matchCallback' => function ($rule, $action) {
                            return EstockTools::isUserStock() || EstockTools::isUserWman() || EstockTools::isUserWstock();
                        }
                    ],
                    [
                        'allow' => false
                    ]
                ]
            ]
        ];
    }


    /**
     * Lists all ShoptetOrders models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new ShoptetOrdersSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionRunimport()
    {
        // $result = Yii::$app->runAction('shoptetordersimport/index', []);
        $output = shell_exec('/var/www/vhosts/pgestock/yii shoptetordersimport --appconfig=/var/www/vhosts/pgestock/config/console_wikarska.php');
        $searchModel = new ShoptetOrdersSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Create movement from order
     * 
     * @param integer $id
     */
    public function actionCreateMovement($id)
    {
        $shoptetOrder = ShoptetOrders::findOne($id);
        if( $shoptetOrder->status == 'Vyřízena' && $shoptetOrder->movement_id == null
        && $shoptetOrder->cdate > date('Y-m-d H:i:s', strtotime('-1 month'))){
            $shoptetOrder->createMovement();
        } else {
            // modal info message about order status
            Yii::$app->session->setFlash('error', 'Action Not Allowed or Order is before last Inventory');
        } 
        //  return to the same page with the same sorting and page
        return $this->redirect(Yii::$app->request->referrer);


        
    }
        
    /**
     * Url ajax action - movement-detail
     */
    public function actionShoptetajaxdetail() {
        if (isset($_POST['expandRowKey'])) {
            $model = ShoptetOrders::findOne($_POST['expandRowKey']);
            return $this->renderPartial('_expand', ['model'=>$model]);
        } else {
            return '<div class="alert alert-danger">No data found</div>';
        }
    }


    /**
     * Displays a single ShoptetOrders model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new ShoptetOrders model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new ShoptetOrders();

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->order_id]);
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing ShoptetOrders model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->order_id]);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing ShoptetOrders model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the ShoptetOrders model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return ShoptetOrders the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = ShoptetOrders::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
