<?php

namespace app\controllers;

use Yii;
use app\models\Eshop;
use app\models\EshopSearch;
use app\models\Movement;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\helpers\Json;

/**
 * EshopController implements the CRUD actions for Eshop model.
 */
class EshopController extends Controller
{
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['post'],
                ],
            ],
            'access' => [
                'class' => \yii\filters\AccessControl::className(),
                'rules' => [
                    [
                        'allow' => true,
                        'actions' => ['index', 'view', 'create', 'update', 'delete'],
                        'roles' => ['@']
                    ],
                    [
                        'allow' => false
                    ]
                ]
            ]
        ];
    }

    /**
     * Lists all Eshop models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new EshopSearch();
        $title = "All Eshop orders";
        $mtypes = EstockTools::getUserRepliId() == 2 ? "504" : "137,139,147,167,169";

        if( Yii::$app->request->post('hasEditable')){
            $cId = Yii::$app->request->post('editableKey');
            $cAttr = Yii::$app->request->post('editableAttribute');
            Yii::debug($_POST);
            $c = Eshop::findOne($cId);
            Yii::debug($c);

            $out = Json::encode(['output' => '', 'message' => '']);
            $post = [];
            $posted = current($_POST['Eshop']);
            $post['Eshop'] = $posted;
            if($c->load($post)){
                if($cAttr == 'status' && ($posted['status'] == 'X' || $posted['status'] == 'N' ) ){
                    Yii::$app->db->createCommand("update movement set c_number=null where c_number=:cn")
                        ->bindValue(":cn",$cId)->query();
                }
                if($cAttr == 'status' && $posted['status'] == 'C' ){
                    $mid = Yii::$app->db->createCommand("(select max(move_id) from movement where m_type_id in (".$mtypes.")  and c_number is null and (adr_id=6234 or adr_id between 12065 and 12092) )")->queryScalar();
                    if( isset($mid)){
                        Yii::$app->db->createCommand("update movement set c_number=:cn where move_id=:mid")
                            ->bindValue(":cn",$cId)->bindValue(":mid",$mid)->query();
                        Yii::$app->db->createCommand("update minfo set 
                            icompany = (select icompany from eshop where id=:cn),
                            iico = (select iico from eshop where id=:cn),
                            idic = (select ltrim(idic, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ abcdefghijklmnopqrstuvwxyz')  from eshop where id=:cn),
                            iicdph = (select idic from eshop where id=:cn),
                            iname = (select iname from eshop where id=:cn),
                            isurname = (select isurname from eshop where id=:cn),
                            icity = (select icity from eshop where id=:cn),
                            izip = (select izip from eshop where id=:cn),
                            istreet = (select istreet from eshop where id=:cn),
                            icountry = (select icountry from eshop where id=:cn),
                            dcompany = (select dcompany from eshop where id=:cn),
                            dname = (select dname from eshop where id=:cn),
                            dsurname = (select dsurname from eshop where id=:cn),
                            dcity = (select dcity from eshop where id=:cn),
                            dzip = (select dzip from eshop where id=:cn),
                            dstreet = (select dstreet from eshop where id=:cn),
                            dcountry = (select dcountry from eshop where id=:cn)
                            where move_id=:mid")->bindValue(":cn",$cId)->bindValue(":mid",$mid)->query();                        
                    }




                }
                $c->save();
            }
            return $this->asJson($out);

        }


        if( isset($_GET['statusfilter']) && empty(Yii::$app->request->queryParams[$searchModel->formName()]) ){
            switch ($_GET['statusfilter']) {
                case 'N':
                $dataProvider = $searchModel->search([$searchModel->formName()=>['status' => 'N']]);
                $title = "Not yet connected eshop orders to Movement";
                break;
                case 'C':
                $dataProvider = $searchModel->search([$searchModel->formName()=>['status' => 'C']]);
                $title = "Archived eshop orders";
                break;
                case 'C+Mov':
                $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
                $dataProvider->query->andWhere(['not', ['(select move_id from movement where  m_type_id in ('.$mtypes.') and c_number=orderid order by move_id desc limit 1)' => null]]);
                $title = "Connected eshop orders to Movement";
                break;

                default:
                $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
                break;
            }
            unset($_GET['statusfilter']);
        } else {
            $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        }
        $dataProvider->sort = ['defaultOrder' => ['id'=>SORT_DESC]];

        if(isset($_POST['export_type'])){$dataProvider->pagination = false;}
        else {$dataProvider->pagination->pageSize=100;}

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'title' => $title,
        ]);
    }

    /**
     * Displays a single Eshop model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        $model = $this->findModel($id);
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new Eshop model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new Eshop();

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing Eshop model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing Eshop model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        if ( Movement::find()->where( ['c_number'=>$this->findModel($id)->id] )->one() !== null ) {
            
            Yii::$app->session->setFlash('error','Already connected - first disconnect from movement ');
        } else {
            $this->findModel($id)->delete();
        }

        return $this->redirect(['index']);
    }

    
    /**
     * Finds the Eshop model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return Eshop the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Eshop::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}







//////////////////
/*

    private function isValidJSON($str) {
        json_decode($str);
        return json_last_error() == JSON_ERROR_NONE;
    }

    public function actionShopConnectlist()
    {
        $dbm = new db_sqlany;

        $query = "select alljson from eshop where status='N'";

        $dbm->query($query);
        $rawData = $dbm->getAll();

        $query = "";
        foreach ($rawData as $key => $json_params) {
            # code...
            if (strlen($json_params->alljson) > 0 && $this->isValidJSON($json_params->alljson)){
              $d = json_decode($json_params->alljson);
              foreach ($d->products as $key => $prod) {
                  print_r($d);
                  if( $query !== "" ) {
                    $query .= " union all ";
                  }
                  $query .= "select m.d1,m.m_type_id,d.mat_id,d.price,d.pcs,d.detail_info,m.text1, ".$dbm->quote($d->orderId)." orderId, "
                        .$dbm->quote($d->orderData->email)." email,"
                        .$dbm->quote($d->orderData->invoiceInfo->surname)." surname
                        from dba.m_detail d join dba.movement m on m.move_id=d.move_id
                        where 
                        d1 >= '".substr($d->created, 0, 10)."' and m_type_id in (87,88)
                        and d.mat_id=".$prod->code;
              }
            }
       }
       echo $query;
 
 
    }

    public function actionOrderlist()
    {
    //$this->layout = false;
                        $dbm = new db_sqlany;
 

                $query = "select 
            id,status,orderid,\"order\",created,email,iname,isurname,istreet,icity,izip,iphone,icountry,icompany,iico,idic,dcompany,dname,dsurname,dstreet,dcity,dzip,dphone,dcountry,gcompany,gname,gsurname,gstreet,gcity,gzip,gphone,gcountry,gift,note,shipment,branch,voucher,payment,totalitems,totalitemsnovat,totalshipment,totalpayment,totalclub,total,currency,products
            from dba.eshop where status='N'
            order by id desc";

                $dbm->query($query);
                $rawData = $dbm->getAll();

                $dataProvider=new CArrayDataProvider($rawData, array(
                        'pagination'=>array(
                                'pageSize'=>10000,
                        ),
                ));


                $query = "select 
            id,status,orderid,\"order\",created,email,iname,isurname,istreet,icity,izip,iphone,icountry,icompany,iico,idic
            from dba.eshop where status<>'N'
            order by id desc";

                $dbm->query($query);
                $rawData = $dbm->getAll();

                $dp2=new CArrayDataProvider($rawData, array(
                        'pagination'=>array(
                                'pageSize'=>10000,
                        ),
                ));

        $connectInfo = $dbm->getOne("select top 1 ' Invoice #'||number||' Total: '||total||', date: '||d1 ci from dba.movement m join dba.address a on a.adr_id=m.adr_id and a.repli_id=m.adr_repli_id  where m_type_id in (137,139)  and c_number is null and a.adr_id=6234 order by move_id desc");



        $query = "select alljson from dba.eshop where status='N'";

        $dbm->query($query);
        $rawData = $dbm->getAll();

        $query = "";
        foreach ($rawData as $key => $json_params) {
            # code...
            if (strlen($json_params->alljson) > 0 && $this->isValidJSON($json_params->alljson)){
              $d = json_decode($json_params->alljson);
              foreach ($d->products as $key => $prod) {
                  if( $query !== "" ) {
                    $query .= " union all ";
                  }
                  $query .= "select m.move_id, p.kod,p.model,
                  m.d1,m.m_type_id,d.mat_id,d.price,d.pcs,d.detail_info,m.text1, ".$dbm->quote($d->orderId)." orderId, "
            .$dbm->quote($d->order)." \"order\", "
                        .$dbm->quote($d->orderData->email)." email,"
                        .$dbm->quote($d->orderData->invoiceInfo->surname)." surname
                        from dba.m_detail d join dba.movement m on m.move_id=d.move_id
                        join dba.product p on p.mat_id=d.mat_id
                        where 
                        d1 >= '".substr($d->created, 0, 10)."' and m_type_id in (87,88)
                        and d.mat_id=".$prod->code;
              }
            }
       }



                $dbm->query($query);
                $rawData = $dbm->getAll();

                $dpShop=new CArrayDataProvider($rawData, array(
                        'pagination'=>array(
                                'pageSize'=>10000,
                        ),
                ));


                $this->render('index',array('data1'=>$dataProvider, 'data2'=>$dp2,
                'dataShop'=>$dpShop, 'connInfo'=>$connectInfo));


    }



    public function actionDocqrlist()
    {
        $this->layout = false;
            $dbm = new db_sqlany;
 
        $query = "select th.mat_id,p.model,d.price,th.thserial 
                from dba.thserials th join dba.m_detail d on d.move_id=th.move_id and d.mat_id=th.mat_id join dba.product p on p.mat_id=th.mat_id 
                where d.move_id='".$_REQUEST['move_id']."' group by th.mat_id, p.model, d.price, th.thserial order by p.model";

                $dbm->query($query);
                $rawData = $dbm->getAll();

                $dataProvider=new CArrayDataProvider($rawData, array(
                        'pagination'=>array(
                                'pageSize'=>10000,
                        ),
                ));

                $this->render('docqrlist',array('data1'=>$dataProvider, 'connInfo'=>$connectInfo));

    }

    public function actionDocpiclist()
    {
        $this->layout = false;
            $dbm = new db_sqlany;
 
        $query = "select d.mat_id,p.model,d.price,th.thserial 
                from dba.m_detail d left outer join dba.thserials th  on d.move_id=th.move_id and d.mat_id=th.mat_id join dba.product p on p.mat_id=d.mat_id 
                where d.move_id='".$_REQUEST['move_id']."' order by p.model";

                $dbm->query($query);
                $rawData = $dbm->getAll();

                $dataProvider=new CArrayDataProvider($rawData, array(
                        'pagination'=>array(
                                'pageSize'=>10000,
                        ),
                ));

                $this->render('docpiclist',array('data1'=>$dataProvider, 'connInfo'=>$connectInfo));

    }

    public function actionGetqrpng( )
    {

    Yii::import('application.vendors.phpqrcode.*');
    require_once('qrlib.php');

    header("Mimetype: image/png");
    echo QRcode::png($_REQUEST['text']);
    exit;
    }



    public function actionPrepareFakt()
    {
        $dbm = new db_sqlany();
        $_query = "";
        
        if(isset($_REQUEST['orderid'])){
            $dbm->query("update dba.eshop set status='X' where id='".$_REQUEST['orderid']."'");

        }

        $this->actionOrderlist();

    }


    public function actionResetFakt()
    {
        $dbm = new db_sqlany();
        $_query = "";
        
        if(isset($_REQUEST['orderid'])){
            $dbm->query("update dba.eshop set status='N' where id='".$_REQUEST['orderid']."'");

        }

        $this->actionOrderlist();

    }



    public function actionConnectFakt()
    {
        $dbm = new db_sqlany();
        $_query = "";
        

        if(isset($_REQUEST['orderid'])){
        $_query = "update dba.movement set  c_number='".$_REQUEST['orderid']."' where move_id=(select max(move_id) from dba.movement where m_type_id in (137,139)  and c_number is null and adr_id=6234)";
            $dbm->query($_query);
        Yii::log($_query,'warning','query');
            if( $dbm->std ){
                $dbm->query("update dba.eshop set status='C' where id='".$_REQUEST['orderid']."'");
                $this->actionOrderlist();
            } else {
                Yii::app()->user->setFlash('finish','Error occured - '.$dbm->getError());
                $this->actionOrderlist();
            }

        }

    }


    public function actionConnectShop()
    {
        $dbm = new db_sqlany();
        $_query = "";


        if(isset($_REQUEST['orderid']) && isset($_REQUEST['move_id'])){
        $_query = "update dba.movement set  c_number='".$_REQUEST['orderid']."' where move_id=".$_REQUEST['move_id'];
            $dbm->query($_query);
        Yii::log($_query,'warning','query');
            if( $dbm->std ){
                $dbm->query("update dba.eshop set status='C' where orderId='".$_REQUEST['orderid']."'");
                $this->actionOrderlist();
            } else {
                Yii::app()->user->setFlash('finish','Error occured - '.$dbm->getError());
                $this->actionOrderlist();
            }

        }

    }




}

*/

