<?php

namespace app\controllers;

use app\models\ProductDisc;
use app\models\ProductDiscSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use Yii;
/**
 * ProductdiscController implements the CRUD actions for ProductDisc model.
 */
class ProductdiscController extends Controller
{

    /**
     * @inheritDoc
     */
    public function behaviors()
    {
        return array_merge(
            parent::behaviors(),
            [
                'verbs' => [
                    'class' => VerbFilter::className(),
                    'actions' => [
                        'delete' => ['POST'],
                    ],
                ],
                'access' => [
                'class' => \yii\filters\AccessControl::className(),
                'rules' => [
                    [
                        'allow' => true,
                        'actions' => ['index','view','delete','update'],
                        'matchCallback' => function ($rule, $action) {
                            return EstockTools::isUserDba() || EstockTools::isUserVip() || EstockTools::isUserManagerProd();
                        }
                    ],
                    [
                        'roles' => ['admin'],
                        'allow' => true
                    ],
                    [
                        'allow' => false
                    ]
                ]
            ]
            ]
        );
    }

    /**
     * Lists all ProductDisc models.
     *
     * @return string
     */
    public function actionIndex()
    {
        $searchModel = new ProductDiscSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single ProductDisc model.
     * @param int $cgroup Cgroup
     * @param string $ctype Ctype
     * @return string
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($cgroup, $ctype)
    {
        return $this->render('view', [
            'model' => $this->findModel($cgroup, $ctype),
        ]);
    }

    /**
     * Creates a new ProductDisc model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return string|\yii\web\Response
     */
    public function actionCreate()
    {
        $model = new ProductDisc();

        if (Yii::$app->request->isPost) {
            if ($model->load(Yii::$app->request->post()) && $model->save()) {
                return $this->redirect(['view', 'cgroup' => $model->cgroup, 'ctype' => $model->ctype]);
            }
        } else {
            $model->loadDefaultValues();
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing ProductDisc model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param int $cgroup Cgroup
     * @param string $ctype Ctype
     * @return string|\yii\web\Response
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($cgroup, $ctype)
    {
        $model = $this->findModel($cgroup, $ctype);

        if (Yii::$app->request->isPost && $model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'cgroup' => $model->cgroup, 'ctype' => $model->ctype]);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing ProductDisc model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param int $cgroup Cgroup
     * @param string $ctype Ctype
     * @return \yii\web\Response
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($cgroup, $ctype)
    {
        $this->findModel($cgroup, $ctype)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the ProductDisc model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param int $cgroup Cgroup
     * @param string $ctype Ctype
     * @return ProductDisc the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($cgroup, $ctype)
    {
        if (($model = ProductDisc::findOne(['cgroup' => $cgroup, 'ctype' => $ctype])) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
