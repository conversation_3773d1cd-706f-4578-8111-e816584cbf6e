<?php

namespace app\controllers;

use Yii;
use app\models\WowSales;
use app\models\Product;
use app\models\SearchWowSales;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\helpers\Json;


/**
 * SerialController implements the CRUD? actions for THSerials model.
 */

class SerialController extends CController
{
	
	    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['post'],
                ],
            ],
            'access' => [
                'class' => \yii\filters\AccessControl::className(),
                'rules' => [
                    [
                        'allow' => true,
                        'actions' => ['AddThserial','AddThOneserial','AddThOneMobile','EraseMoveid', 'ShowImported', 'ShowMoveId','DelOne','ShowPossibleThserials','GetMaxId'],
                        'roles' => ['@']
                    ],
                    [
                        'allow' => false
                    ]
                ]
            ]
        ];
    }

    /**
     * ostava aj tu tato funkcia kvoli view
	*/   
    // public function getMyconf()
    // {
    // 	    $dbm = new db_sqlany();
    //         $dbm->getOne( "select * from m_user_config where active=1 and userId='".Yii::$app->user->identity->username."' and db='".Yii::app()->user->getState('dbName')."'");
    //         return $dbm->result;
    // }

    /**
     * Check whether we need refresh...
     */
    public function actionGetMaxId()
    {
		if( isset($_GET['move_id'])){
		    $dbm = new db_sqlany;
		    $dbm->getOneArray("select max(id) mid from thserials where move_id=".$_GET['move_id']);
            header('Content-Type: application/json');
		    $json = json_encode(array("maxid"=>$dbm->result['mid']));
	        echo $json;
	        exit;
		}
	}



    /**
     * Adds New thserial
     */
    public function actionEraseMoveid()
    {
		$thserial=new AddThserialForm;
		if( isset($_GET['move_id'])){
		    $dbm = new db_sqlany;
		    $dbm->query("delete from thserials where move_id=".rawurldecode($_GET['move_id']));
		    if( $dbm->std){
				$infotxt = "OK, deleted <br><br>";
		    } else {
		    	$infotxt = $dbm->getError();
				$infotxt .= "<br><br>";
			    }
			$infotxt .= CHtml::button('TH Serials',array(
            'submit'=>array('Thserial/AddThserial' )
                ));
	    	Yii::app()->user->setFlash('thserial',$infotxt);
		}
		$this->render('AddThserial',array('thserial'=>$thserial));
    }


    /**
     * Adds New thserial from the app
     */
    public function actionAddThOneMobile()
    {

		$thserial=new AddThserialOneForm;
		$detid = "0";
  	    $dbm = new db_sqlany;
	    if( isset($_REQUEST['detId']) && $_REQUEST['detId'] != 'NoData' ){
			$detid = $_REQUEST['detId'];
	    }
	    if( isset($_REQUEST['move_id']) && $_REQUEST['move_id'] != '' ){
			$detid = $_REQUEST['move_id'];
	    }
    	$thserial->move_id = $detid;


		//Cez SCANNER v tabulke
		if(isset($_GET['scanner']) && $_GET['scanner'] > 0 && $thserial->move_id > 0 )
		{
		    $thserial->attributes=$_GET;
	        $thserial->move_id = $detid;
		    //nemame mat_id ani model
            $dbm->getOneArray("Select t.mat_id, model from thserials t join product p on t.mat_id=p.mat_id where thserial='".$thserial->thserial."' limit 1");
            $thserial->mat_id = $dbm->result['mat_id'];
            //ChromePhp::log($dbm);


			    $dbx = new db_sqlany;
			    $xmove = $thserial->move_id;
				try{

					    $dbx->query("insert into thserials (mat_id,move_id,thserial) values (
					    	'".$thserial->mat_id."', '".$xmove."','".$thserial->thserial."')");
			            header('Content-Type: application/json');
					    if( $dbx->std ){
						    $json = json_encode(array("message"=>"Added ".$thserial->mat_id." ".$dbm->result['model']));
					    } else {
						    $json = json_encode(array("message"=>"Error occured ".$dbx->getError()));
					    }
				        echo $json;
				                    //    ChromePhp::log($dbx);


				} catch (CDbException $exc ) {
		            header('Content-Type: application/json');
			        echo json_encode(array("message"=>"Error occured: ".$exc->getMessage()." ... ".$exc->errorInfo));
				                    //    ChromePhp::log($dbx);
			        exit;
				}
		} else {
		            header('Content-Type: application/json');
			        echo json_encode(array("message"=>"Error occured: missing move_id"));
				                    //    ChromePhp::log($dbx);
			        exit;
		}

    }





    /**
     * Adds New thserial
     */
    public function actionAddThOneserial()
    {
//		if(isset($_POST['AddThserialOneForm'])){
//		    $_GET = array();
//		}

		$thserial=new AddThserialOneForm;
                //ChromePhp::log($thserial);
		//ChromePhp::log($_POST);
		//ChromePhp::log($_GET);
		$detid = "0";
    	    $dbm = new db_sqlany;
	    if( isset($_REQUEST['detId']) && $_REQUEST['detId'] != 'NoData' ){
			$detid = $_REQUEST['detId'];
	    }
	    if( isset($_REQUEST['move_id']) && $_REQUEST['move_id'] != '' ){
			$detid = $_REQUEST['move_id'];
	    }
    	$thserial->move_id = $detid;

		if(isset($_POST['AddThserialOneForm']))
		{
		    $thserial->attributes=$_POST['AddThserialOneForm'];

		    if($thserial->validate())
		    {

			    $xmove = $thserial->move_id;
				try{

					    $dbm->query("insert into thserials (mat_id,move_id,thserial) values (
					    	'".$thserial->mat_id."', '".$xmove."','".$thserial->thserial."')");
					    if( $dbm->std ){
					    	$imptxt .= "<B>OK</B><br>";
					    } else {
							$imptxt .= "<br><br><br>".CHtml::link(" TRY AGAIN ",array('Thserial/AddThOneserial','detId'=>$xmove))." ";
							//$imptxt .= "<br><br><br>You can delete all thserials for move_id=".$xmove.CHtml::link(" just click here...",array('Thserial/EraseMoveid','move_id'=>$xmove))." ";
					    	$imptxt .= "<B>ERROR:</B> ".$dbm->getError() .'<br>';
							//$imptxt .= "<br><br>".CHtml::link('TH Serials',array('Thserial/AddThserial'));
							Yii::app()->user->setFlash('thserial',$imptxt);
							$this->refresh();
					    }

				} catch (CDbException $exc ) {
	//			    $dbm->query("rollback");
				    Yii::app()->user->setFlash('thserial','<pre>Error occured. \n'.$exc.'</pre>');
				    $this->refresh();
				}
			}
		}//AddOne

		//Cez URL v tabulke
		else if(isset($_GET['mat_id']))
		{
		    $thserial->attributes=$_GET;
	    	    $thserial->move_id = $detid;
		    if($thserial->validate())
		    {

			    $xmove = $thserial->move_id;
				try{

					    $dbm->query("insert into thserials (mat_id,move_id,thserial) values (
					    	'".$thserial->mat_id."', '".$xmove."','".$thserial->thserial."')");
					    if( $dbm->std ){
					    	$imptxt .= "<B>OK</B><br>";
					    } else {
							$imptxt .= "<br><br><br>".CHtml::link(" TRY AGAIN ",array('Thserial/AddThOneserial','detId'=>$xmove))." ";
							//$imptxt .= "<br><br><br>You can delete all thserials for move_id=".$xmove.CHtml::link(" just click here...",array('Thserial/EraseMoveid','move_id'=>$xmove))." ";
					    	$imptxt .= "<B>ERROR:</B> ".$dbm->getError() .'<br>';
							//$imptxt .= "<br><br>".CHtml::link('TH Serials',array('Thserial/AddThserial'));
							Yii::app()->user->setFlash('thserial',$imptxt);
							$this->refresh();
					    }

				} catch (CDbException $exc ) {
	//			    $dbm->query("rollback");
				    Yii::app()->user->setFlash('thserial','<pre>Error occured. \n'.$exc.'</pre>');
				    $this->refresh();
				}
			}
		}//AddOne

                $query = "
			    Select m.move_id,firma, d1, t.mat_id, p.model, thserial, (select sum(pcs) from m_detail where move_id=t.move_id and mat_id=t.mat_id) pcsInDetail
			 from thserials t join movement m on m.move_id=t.move_id join address a on m.adr_id=a.adr_id and m.adr_repli_id=a.repli_id 
			left outer join product p on t.mat_id=p.mat_id where t.move_id=".$detid." 
			group by m.move_id,firma,d1,t.mat_id,p.model,thserial,t.move_id
			 order by p.model";
                $dbm->query($query);
                $rawData = $dbm->getAll();
                $dataProvider=new CArrayDataProvider($rawData, array(
                        'pagination'=>array(
                                'pageSize'=>1000,
                        ),
                ));


//possible
                //ChromePhp::log($thserial);

            //    ChromePhp::log($query);

            	$missing = "0"; //list of missing mat_ids
				$query = "
					Select d.mat_id||'-'||d.pcs missing, p.model from m_detail d
					join product p on p.mat_id=d.mat_id where (kod ilike 'WTH%' or kod='WLOX') and d.move_id='".$detid.
					"' and d.mat_id||'-'||d.pcs not in (select mat_id||'-'||count(*) from thserials where move_id='".$detid.
					"' and mat_id=d.mat_id
                    group by move_id,mat_id) ";
                $dbm->query($query);
                $rawData = $dbm->getAll();
                foreach ($rawData as $key => $value) {
                	$tempvar = explode("-",$value->missing);
                	$missing .= ",".$tempvar[0];
                }
                //ChromePhp::log($query);
		
		$queryadd = "";
		if( isset( $thserial->repliid ) && $thserial->repliid != "0" ) {
		    $queryadd = "and m.repli_id=".$dbm->getMyconf()->adr_repli_id;
		}
                $query = "
                select thserial,mat_id,(select model from product where mat_id=t.mat_id) xmodel from
                 thserials t join movement m on t.move_id=m.move_id 
                 where
					m.m_type_id in (select m_type_id from m_type where moving='R')
					and thserial not in (select thserial from thserials tt join movement mm
						 on tt.move_id=mm.move_id
						where mm.m_type_id in (select m_type_id from m_type where moving='S')
							and tt.mat_id=t.mat_id and mm.d1>m.d1)
						and mat_id in (".$missing.")
						".$queryadd."
						and thserial not in (select thserial from thserials where move_id='".$detid."')";
                //ChromePhp::log($query);
                $dbm->query($query);
                $rawData = $dbm->getAll();

                $posProvider=new CArrayDataProvider($rawData, array(
                        'pagination'=>array(
                                'pageSize'=>1000,
                        ),
                ));
                //ChromePhp::log($posProvider);
                //ChromePhp::log($dataProvider);


	$this->render('AddThOneserial',array('thserial'=>$thserial, 'data1'=>$dataProvider, 'data2'=>$posProvider));
    }






    /**
     * Adds New thserial
     */
    public function actionAddThserial()
    {


	$thserial=new AddThserialForm;
	if(isset($_POST['AddThserialForm']))
	{
	    $thserial->attributes=$_POST['AddThserialForm'];
	    if($thserial->validate())
	    {

		    $dbm = new db_sqlany;
//		    $dbm->query("begin transaction");
//error_reporting(E_ALL);
		    $xmove = $thserial->move_id;
			try{


	        	$thserial->attributes=$_POST['AddThserialForm']['thfile'];
	        	$thserial->thfile=CUploadedFile::getInstance($thserial,'thfile');
				$inputFileName = '/tmp/'.$_POST['AddThserialForm']['move_id'].'.xlsx';
				$thserial->thfile->saveAs($inputFileName);
				require_once dirname(__FILE__) . '/../vendors/PHPExcel/Classes/PHPExcel/IOFactory.php';
				//Yii::import('application.vendors.PHPExcel.Classes.PHPExcel.IOFactory.phpe') || die('xx');

				$objReader = PHPExcel_IOFactory::createReader('Excel2007');
				$objReader->setReadDataOnly(true);
				$objPHPExcel = $objReader->load($inputFileName);
				$objWorksheet = $objPHPExcel->getActiveSheet();

				$highestRow = $objWorksheet->getHighestRow();
				$highestColumn = $objWorksheet->getHighestColumn();
				$highestColumnIndex = PHPExcel_Cell::columnIndexFromString($highestColumn);
				$rows = array();
				$imptxt = '';

				$inserted = 0;
				for ($row = 2; $row <= $highestRow; ++$row) {
				    $xmodel = $objWorksheet->getCellByColumnAndRow(3, $row)->getValue();
				    $xserial = $objWorksheet->getCellByColumnAndRow(5, $row)->getValue();
				    $xserial = substr($xserial, 0,1) === '*' ? substr($xserial, 1):$xserial;
				    $xserial = trim($xserial);
				    $imptxt .= "<b>TRYING INSERT</b> ".$xmove.", '".$xserial."' ".$xmodel." ".'<br>';
				    $dbm->query("insert into thserials (mat_id,move_id,thserial) select mat_id, ".$xmove.", '".$xserial."' from product where model='".$xmodel."' order by mat_id limit 1");
				    if( $dbm->std ){
				    	$inserted++;
				    	$imptxt .= "<B>OK</B><br>";
				    } else {
				    	$imptxt .= "<B>ERROR:</B> ".$dbm->getError() .'<br>';
				    }
				}

				$check1 = $dbm->getOne('select count(*) pocet from thserials where move_id='.$xmove);
				$imptxt .= "<br><b>THSERIALS FOR ".$xmove.": ".$check1->pocet;
				$imptxt .= "</b><br>NOW INSERTED: ".$inserted;
				$check2 = $dbm->getOne('select sum(pcs) pocet from m_detail where move_id='.$xmove." and mat_id in
						(select mat_id from product where kod ilike 'WTH%' or kod='WLOX')");
				$imptxt .= "<br><b>WTH% WLOX PCS IN ".$xmove.": ".$check2->pocet;
				$imptxt .= "</b>";
				if( $check2->pocet != $check1->pocet){
					$imptxt .= "<br><b>INSERTED IS NOT EQUAL TO PCS IN MOVE_ID!!!";
					$imptxt .= "</b>";
				}

				$imptxt .= "<br><br><br>You can delete all thserials for move_id=".$xmove.CHtml::link(" just click here...",array('Thserial/EraseMoveid','move_id'=>$xmove))." ";
//		    $dbm->query("end transaction");


			$imptxt .= "<br><br>".CHtml::link('TH Serials',array('Thserial/AddThserial'));


			Yii::app()->user->setFlash('thserial',$imptxt);
			$this->refresh();

			} catch (CDbException $exc ) {
//			    $dbm->query("rollback");
			    Yii::app()->user->setFlash('thserial','<pre>Error occured. \n'.$exc.'</pre>');
			    $this->refresh();
			}
	    }
	}//AddFile



	$this->render('AddThserial',array('thserial'=>$thserial));
    }



	/**
	 * show some data...
	 */
	public function actionShowImported()
	{
            $dbm = new db_sqlany;
			if( $_GET['adr_repli_id'] == 2 ){//chrono...
			    $obj = '29';
			    $pohyby = '10,11,67,85';
                $dobropisy = '70,120,121';
                $sklad = 's2';
			} else {//blava, brno
			    $obj = '17';
			    $pohyby = '24,110,130';
                $sklad = 's0';
                $dobropisy = '-9999'; //ToDo - nezadaval som dobropisy na SK
			}
 

                $query = "
					Select t.move_id, firma, d1, count(*) thserials, sum(d.pcs) pcsInDetail from thserials t
					join movement m on m.move_id=t.move_id
					join address a on m.adr_id=a.adr_id and m.adr_repli_id=a.repli_id
					join m_detail d on m.move_id=d.move_id
					join product p on p.mat_id=d.mat_id
					where p.kod ilike 'WTH%' or p.kod='WLOX'
					group by t.move_id,firma,d1
					order by d1,firma";

                $dbm->query($query);
                $rawData = $dbm->getAll();

                $dataProvider=new CArrayDataProvider($rawData, array(
                        'pagination'=>array(
                                'pageSize'=>1000,
                        ),
                ));

                $this->render('showimported',array('data1'=>$dataProvider));


	}


	/**
	 * dekete some data...
	 */
	public function actionDelOne()
	{
            $dbm = new db_sqlany;
                $query = " delete from thserials where move_id=".$_REQUEST['move_id']." and mat_id=".$_REQUEST['mat_id']." and thserial='".$_REQUEST['thserial']."'";
                $dbm->query($query);
	    $_POST['mat_id'] = NULL;
	    $_GET['mat_id'] = NULL;
	    $_POST['thserial'] = NULL;
	    $_GET['thserial'] = NULL;
	    $this->actionAddThOneserial();


	}




	/**
	 * show some data...
	 */
	public function actionShowMoveId()
	{
            $dbm = new db_sqlany;
                $query = "
					Select firma, d1, d.mat_id, p.model, (select count(*) from thserials where move_id=".$_REQUEST['move_id']." 
					  and mat_id=d.mat_id) thserials, sum(d.pcs) pcsInDetail from 
					  movement m
					join address a on m.adr_id=a.adr_id and m.adr_repli_id=a.repli_id
					join m_detail d on m.move_id=d.move_id
					join product p on p.mat_id=d.mat_id
					where (p.kod ilike 'WTH%' or p.kod='WLOX') and d.move_id=".$_REQUEST['move_id']."
					group by firma,d1,d.mat_id,p.model
					order by p.model";
                $dbm->query($query);
                $rawData = $dbm->getAll();

                $dataProvider=new CArrayDataProvider($rawData, array(
                        'pagination'=>array(
                                'pageSize'=>1000,
                        ),
                ));
                $this->render('showMoveId',array('data1'=>$dataProvider));


	}






	/**
	 * special onstock from serials...
	 */
	public function actionShowPossibleThserials()
	{
            $dbm = new db_sqlany;
            	$missing = "0"; //list of missing mat_ids
				$query = "
					Select d.mat_id||'-'||d.pcs missing, p.model from m_detail d
					join product p on p.mat_id=d.mat_id where (kod ilike 'WTH%' or kod='WLOX') and d.move_id='".$_REQUEST['move_id'].
					"' and d.mat_id||'-'||d.pcs not in (select mat_id||'-'||count(*) from thserials where move_id='".$_REQUEST['move_id']."' and mat_id=d.mat_id
                    group by move_id,mat_id) ";
                $dbm->query($query);
                $rawData = $dbm->getAll();
                foreach ($rawData as $key => $value) {
                	$tempvar = explode("-",$value->missing);
                	$missing .= ",".$tempvar[0];
                }


                $query = "
                select thserial,mat_id,(select model from product where mat_id=t.mat_id) from
                 thserials t join movement m on t.move_id=m.move_id 
                 where
					m.m_type_id in (select m_type_id from m_type where moving='R' or m_type_id=14)
					and thserial not in (select thserial from thserials tt join movement mm
						 on tt.move_id=mm.move_id
						where mm.m_type_id in (select m_type_id from m_type where moving='S' or m_type_id=14)
							and tt.mat_id=t.mat_id)
						and mat_id in (".$missing.")";

                $dbm->query($query);
                $rawData = $dbm->getAll();

                $dataProvider=new CArrayDataProvider($rawData, array(
                        'pagination'=>array(
                                'pageSize'=>1000,
                        ),
                ));
                $this->render('showPossibleThserials',array('data1'=>$dataProvider));


	}






}
