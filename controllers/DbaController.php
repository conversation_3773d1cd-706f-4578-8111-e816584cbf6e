<?php

namespace app\controllers;

use Yii;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\helpers\Json;
use app\models\User;
use yii\data\ActiveDataProvider;

/**
 * DbaController implements the CRUD actions for Dba model.
 */
class DbaController extends Controller
{
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['post'],
                ],
            ],
            'access' => [
                'class' => \yii\filters\AccessControl::className(),
                'rules' => [
                    [                    
                        'allow' => true,
                        'actions' => ['create','index','update'],
                        'matchCallback' => function ($rule, $action) {
                            return EstockTools::isUserDba();
                        }
                    ],
                    [                    
                        'allow' => true,
                        'actions' => ['create','update','b2bindex'],
                        'matchCallback' => function ($rule, $action) {
                            return EstockTools::isUserAdminForUsers();
                        }
                    ],
                    [
                        'allow' => false
                    ]
                ]
            ]
        ];
    }

    /**
     * Lists all Dba models.
     * @return mixed
     */
    public function actionIndex()
    {

        $dataProvider = new ActiveDataProvider([
            'query' => User::find(),
        ]);
        $dataProvider->pagination->pageSize=100;

        if( Yii::$app->request->post('hasEditable')){
            $cId = Yii::$app->request->post('editableKey');
            $c = User::findOne($cId);

            $out = Json::encode(['output' => '', 'message' => '']);
            $post = [];
            $posted = current($_POST['User']);
            $post['User'] = $posted;
            if($c->load($post)){
                $c->save();
            }
            return $this->asJson($out);

        }

        return $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }

    
    /**
     * Lists all Dba models.
     * @return mixed
     */
    public function actionB2bindex()
    {

        $dataProvider = new ActiveDataProvider([
            'query' => User::find()->where(['=', 'all_flags', 'b2b']),
        ]);
        $dataProvider->pagination->pageSize=100;

        if( Yii::$app->request->post('hasEditable')){
            $cId = Yii::$app->request->post('editableKey');
            $c = User::findOne($cId);

            $out = Json::encode(['output' => '', 'message' => '']);
            $post = [];
            $posted = current($_POST['User']);
            $post['User'] = $posted;
            if($c->load($post)){
                $c->save();
            }
            return $this->asJson($out);

        }

        return $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->validate() ){
        if( $model->status == 0 ){ // send email if this is 0
            if( !$this->sendpwd($model->username,$model->password_reset_token,$model->email, $model->all_flags) ){
                Yii::$app->session->setFlash('error', "User not updated"); 
                return $this->render('update', [
                'model' => $model,
        	]);
            }
        }    
            $model->updated_at = date('Y-m-d');
            $model->password_hash = md5($model->password_reset_token);
            $model->password_reset_token = " ";
            $model->status = 0; //reset status for default email sending
          
            if(  $model->save()) {
                Yii::$app->session->setFlash('success', "User updated"); 
                return $this->render('update',[ 'model' => $model]);
            } else {
                Yii::$app->session->setFlash('error', "User not updated"); 
                return $this->render('update', [
                'model' => $model,
            ]);
            }
        } else {
                return $this->render('update', [
                'model' => $model,
            ]);

        }
    }


    /**
     * Creates a new User model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new User();
        $model->id = User::find()->max('id') + 1;
        $model->all_flags = "b2b";
        $model->created_at = date('Y-m-d');
        if ($model->load(Yii::$app->request->post()) && $model->validate()) {
            $model->password_hash = md5($model->password_reset_token);
            $model->password_reset_token = " ";
            $model->role = 0; // Role not used for now
            if( $model->status == 0 ){ // send email if this is 0
                if( !$this->sendpwd($model->username,$model->password_reset_token,$model->email, $model->all_flags) ){
                    Yii::$app->session->setFlash('error', "User not created"); 
                    return $this->redirect(['update', 'id' => $model->id]);
                }
            }
            $model->status = 0; //reset status for default email sending    
            if ($model->load(Yii::$app->request->post()) && $model->save()) {
                Yii::$app->session->setFlash('success', "User created"); 
                return $this->redirect(['update', 'id' => $model->id]);
            }
        }
        return $this->render('create', [
            'model' => $model,
        ]);
    }


    /**
     * Updates an existing Customers model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function sendpwd($username,$password,$email,$all_flags)
    {
        //  system( "/usr/bin/htpasswd -bm /var/www/trunk/.htpasswd ".$username." '".$password."'", $resStr );
        //  if( $resStr == 0 ){
            $template = ($all_flags == 'b2b' ? 'layouts/sendb2bpwd' : 'layouts/sendwstockpwd');
            $title = ($all_flags == 'b2b' ? 'B2B login' : 'WSTOCK login');
            if (
                    Yii::$app->mailer->compose($template,
                        [
                            'content' => 'Login name: '.$username."<br>Password: ".$password."<br>",
                    ]) // a view rendering result becomes the message body here
                    ->setFrom('<EMAIL>')
                    ->setTo($email)
                    ->setCc(['<EMAIL>'])
                    ->setSubject($title)
                    ->send()
                )
                {
                  Yii::$app->session->setFlash('success', "Email sent successfully."); 
              } else {
                  Yii::$app->session->setFlash('error', "Email not sent.");
                  return false;
              }

              return true;
        //    } else {
            //  return false;
        //    }

  }



    
    /**
    * Action to load a tabular form grid
    * for Product
    * <AUTHOR> Candrajaya <<EMAIL>>
    * <AUTHOR> Ndaru <<EMAIL>>
    *
    * @return mixed
    */
    public function actionAdduser()
    {
        if (Yii::$app->request->isAjax) {
            $row = Yii::$app->request->post('Product');
            if((Yii::$app->request->post('isNewRecord') && Yii::$app->request->post('_action') == 'load' && empty($row)) || Yii::$app->request->post('_action') == 'add')
                $row[] = [];
            return $this->renderAjax('_formProduct', ['row' => $row]);
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }


    protected function findModel($id)
    {
        if (($model = User::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

}
