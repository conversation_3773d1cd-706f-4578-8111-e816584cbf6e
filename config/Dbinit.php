<?php
namespace app\config;

use Yii;
use yii\base\BootstrapInterface;

/*
/* The base class that you use to retrieve the settings from the database
*/

class Dbinit implements BootstrapInterface {

    private $db;

    public function __construct() {
        $this->db = Yii::$app->db;
    }

    /**
    * Bootstrap method to be called during application bootstrap stage.
    * Loads all the settings into the Yii::$app->params array
    * @param Application $app the application currently running
    */

    public function bootstrap($app) {



        if( \app\controllers\EstockTools::getUserAdrRepliId() == 2 ){
            $this->db->createCommand("set lc_monetary to \"hu_HU.UTF-8\"")->query();
        } else {
          $this->db->createCommand("set lc_monetary to \"en_IE.UTF-8\"")->query();
        }
        // // Get settings from database
        // $sql = $this->db->createCommand("SELECT setting_name,setting_value FROM settings");
        // $settings = $sql->queryAll();

        // // Now let's load the settings into the global params array

        // foreach ($settings as $key => $val) {
        //     Yii::$app->params['settings'][$val['setting_name']] = $val['setting_value'];
        // }

    }

}
