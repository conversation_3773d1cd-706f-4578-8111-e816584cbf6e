<?php
return
 [
    'wstockCustomer' => 'wikarska',
    'adminEmail' => '<EMAIL>',
    'publicProductPrices' => ['p1','p2','p4'], // Pouzijeme ked nenajdeme cenu pre aktualny clipboard
    'query' => [
        'monika' => [

            'OrdersByYearMonth' => [ 'q'=> "SELECT
            s.status,
            year(cdate) as rok,
          month(cdate) as mesiac,
            COUNT(*) AS order_count,
            round(SUM(case when currency_code = 'CZK' then s.total_price_with_vat/25 else s.total_price_with_vat end ),2) AS total_sales
        FROM
            shoptet_orders s
          join shoptet_order_items i on s.order_id=i.order_id
        WHERE cdate >= :p1
        GROUP BY
            s.status, rok, mesiac
        ORDER BY
            s.status, rok, mesiac
                            ",
                                                            'l1' => 'Date from',
                                                            'p1' => '2020-01-01',
                            
                    ],
            'OrdersByYear' => [ 'q' => "SELECT
            s.status,
            year(cdate) as rok,
            COUNT(*) AS order_count,
            round(SUM(case when currency_code = 'CZK' then s.total_price_with_vat/25 else s.total_price_with_vat end ),2) AS total_sales
        FROM
            shoptet_orders s
          join shoptet_order_items i on s.order_id=i.order_id
          WHERE cdate >= :p1
        GROUP BY
            s.status, rok
        ORDER BY
            s.status, rok",
                                                            'l1' => 'Date from',
                                                            'p1' => '2020-01-01',
                ],
        'BestCustomersByOrders' => ['q'=> "
            select customer_email,count(1),sum(total_sales),
            (select max(cdate) from shoptet_orders where customer_email = ss.customer_email) as last_order_date from (
                SELECT Customer_Email,name, s.status, year(cdate) as rok, month(cdate) as mesiac, COUNT(*) AS order_count, round(SUM(case when currency_code = 'CZK' then s.total_price_with_vat/25 else s.total_price_with_vat end ),2) AS total_sales FROM shoptet_orders s join shoptet_order_items i on s.order_id=i.order_id WHERE cdate >= :p1 and otype='product' and s.status <> 'Stornována' GROUP BY Customer_Email, name, s.status, rok, mesiac ORDER BY name,s.status, rok, mesiac) as ss group by customer_email having count(1)>1 order by count desc
            ",
                                'l1' => 'Date from',
                                'p1' => '2020-01-01',
                            ],
        'SalesByProduct' => ['q'=> "
            SELECT name, s.status, year(cdate) as rok, month(cdate) as mesiac, COUNT(*) AS order_count,
             round(SUM(case when currency_code = 'CZK' then s.total_price_with_vat/25 else s.total_price_with_vat end ),2) AS total_sales 
             FROM shoptet_orders s join shoptet_order_items i on s.order_id=i.order_id WHERE cdate >= :p1 and otype='product' and s.status <> 'Stornována'
              GROUP BY name, s.status, rok, mesiac ORDER BY name,s.status, rok, mesiac
            ",
                                'l1' => 'Date from',
                                'p1' => '2020-01-01',
                            ],

        ],
    ]
];
