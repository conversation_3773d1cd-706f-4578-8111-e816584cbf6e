<?php

return [
            'class' => 'yii\db\Connection',
            'dsn' => 'pgsql:host=localhost;dbname=wstock',
            'username' => 'dba',
            'password' => 'Rin1!!!andBeyond',
            'charset' => 'utf8',
            'attributes'=>[
                PDO::ATTR_PERSISTENT => true
            ],
            'schemaMap' => [
                'pgsql' => [
                  'class' => 'yii\db\pgsql\Schema',
                  'defaultSchema' => 'public' //specify your schema here, public is the default schema
                ]
            ], // PostgreSQL
        ];


////$xdsn = "sqlanywhere:ENG={e-stock};SERVERNAME=e-stock;DATABASENAME=e-stock;COMMLINKS=tcpip{host=localhost;port=2637}";


//$xdsn = "sqlanywhere:ENG={e-stock};SERVERNAME=e-stock;DATABASENAME=e-stock;COMMLINKS=tcpip{host=*************;port=2637}";
// if( $_SERVER["HTTP_HOST"] == "dev.klub.wdl.sk" ) {
// 	$xdsn = "sqlanywhere:ENG={blava2018};SERVERNAME=blava2018;DATABASENAME=blava2018;COMMLINKS=tcpip{host=127.0.0.1;port=2638}";
// }

/*
return [
    'class' => 'yii\db\Connection',
    'driverName' => 'sybase',
    'schemaMap' => [
        'sybase' => \websightnl\yii2\sybase\Schema::className(),
    ],
    'dsn' => $xdsn,
    'username' => 'dba',
    'password' => 'Rin1!!!',

];
*/