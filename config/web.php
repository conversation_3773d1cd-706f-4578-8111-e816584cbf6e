<?php


$params = require __DIR__ . '/params.php';
$db = require __DIR__ . '/db.php';

$config = [
    'id' => 'Wstock2020',
    'name' => 'Wstock',
    'basePath' => dirname(__DIR__),
    'bootstrap' => ['log','app\config\Dbinit'],
    'aliases' => [
        '@bower' => '@vendor/bower-asset',
        '@npm'   => '@vendor/npm-asset',
    ],
    'components' => [
        'urlManager' => [
            'class' => 'yii\web\UrlManager',
            // Disable index.php
            'showScriptName' => false,
            // Disable r= routes
            'enablePrettyUrl' => true,
            'rules' => array(
                    '<controller:\w+>/<id:\d+>' => '<controller>/view',
                    '<controller:\w+>/<action:\w+>/<id:\d+>' => '<controller>/<action>',
                    '<controller:\w+>/<action:\w+>' => '<controller>/<action>',
            ),
        ],
        // 'formatter' => [
        //    'dateFormat' => 'Y-M-d',
        //    'datetimeFormat' => 'd-M-Y H:i:s',
        //    'timeFormat' => 'H:i:s',

        //    'locale' => 'en-EN', //your language locale
        //    'defaultTimeZone' => 'Europe/Berlin', // time zone
        // ],

        'request' => [
            // !!! insert a secret key in the following (if it is empty) - this is required by cookie validation
            'cookieValidationKey' => 'YesxvXSnJXSVN9ZZUskwE4jD8K1MTcvU',
        ],
        'cache' => [
            'class' => 'yii\caching\FileCache',
        ],
         'user' => [
            'identityClass' => 'app\models\User',
             'enableAutoLogin' => false,
            'authTimeout' => 3600, // auth expire 
        //     'as mfa' => [
	       //  'class' => 'vxm\mfa\Behavior',
    	   //      'verifyUrl' => '?r=site%2Fmfa-verify' // verify action, see bellow for setup it
    	   //  ]
         ],
        'session' => [
            'class' => 'yii\web\Session',
            'cookieParams' => ['httponly' => true, 'lifetime' => 518400 ],
            'timeout' => 518400, //session expire 6 days
            'useCookies' => true,
        ],


         
        'errorHandler' => [
            'errorAction' => 'site/error',
        ],
        'mailer' => [
            'class' => 'yii\swiftmailer\Mailer',
            // send all mails to a file by default. You have to set
            // 'useFileTransport' to false and configure a transport
            // for the mailer to send real emails.
            'useFileTransport' => false,
            'transport' => [
             'class' => 'Swift_SmtpTransport',
             'host' => 'smtp.gmail.com',  // e.g. smtp.mandrillapp.com or smtp.gmail.com
             'username' => '<EMAIL>',
             'password' => 'napisaneNaServeriOstromAkoHesloAplikacie',
             'port' => '587', // Port 25 is a very common port too
             'encryption' => 'tls', // It is often used, check your provider or mail server specs
            ],
        ],
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['error', 'warning'],
                ],
            ],
        ],
        'db' => $db,
        'view' => [
         'theme' => [
             'pathMap' => [
                '@app/views' => 'views'
             ],
         ],
        ],
        'authClientCollection' => [
            'class' => 'yii\authclient\Collection',
            'clients' => [
                'authentik' => [
                    'class' => 'app\components\AuthentikOAuth2',
                    'clientId' => 'Sa2I3h4U8cFy9aPtpWCSsvSmOiUedQDxz68UVQb0',
                    'clientSecret' => 'H7ShISgefr6xxXil9VlPAPv5DyzM0oeWStGN7Q0aMQnLJdSqRU7iolc3wc0uq1ndsUI5LRQLlrm2gh0wli4rtY36SvWc2ZW7jyCBLoDgXem7OEdv8JoWiiPMdguUNyPX',
                    'tokenUrl' => 'http://casas.grapph.com:9000/application/o/token',
                    'authUrl' => 'http://casas.grapph.com:9000/application/o/authorize',
                    'apiBaseUrl' => 'http://casas.grapph.com:9000/application/o/wstock',
                    'scope' => 'openid profile email',
                    // 'attributeNames' => ['id', 'email', 'username'],
                ],
            ],
        ],

//    'view' => [
//         'theme' => [
//             'pathMap' => [
//                '@app/views' => '@vendor/dmstr/yii2-adminlte-asset/example-views/yiisoft/wdlklub'
    //            '@app/views' => 'app/views'
//             ],
//         ],
//    ],
    // 'assetManager' => [
    //     'bundles' => [
    //         'dmstr\web\AdminLteAsset' => [
    //             'skin' => 'skin-red',
    //         ],
    //     ],
    // ],
        /*
        'urlManager' => [
            'enablePrettyUrl' => true,
            'showScriptName' => false,
            'rules' => [
            ],
        ],
        */
    ],
    'modules' => [
        // 'authy' => [
        //     'class' => 'geoffry304\authy\Module',
        //     'api_key' => '60YBA0Kc5Q7WjO73Q57ecMLsA540jDFO',
        //     'send_mail_from' => '<EMAIL>'
        // ],
        // 'user' => [
        //     'class' => 'amnah\yii2\user\Module',
        //     'modelClasses' => [
        //         'LoginForm' => 'geoffry304\authy\forms\LoginForm'
        //     ]
        // ],
      'gridview' => [
          'class' => 'kartik\grid\Module',
          // see settings on http://demos.krajee.com/grid#module
      ]
    ],
    'params' => $params,
];

if (YII_ENV_DEV) {
    // configuration adjustments for 'dev' environment
    $config['bootstrap'][] = 'debug';
    $config['modules']['debug'] = [
        'class' => 'yii\debug\Module',
        // uncomment the following to add your IP if you are not connecting from localhost.
        'allowedIPs' => ['***************','127.0.0.1', '::1','************','************','**************','************','*************','***********','*************'],
    ];

    $config['bootstrap'][] = 'gii';
    $config['modules']['gii'] = [
        'class' => 'yii\gii\Module',
        'modules' => [
      'gridview' => [
          'class' => 'kartik\grid\Module',
          // see settings on http://demos.krajee.com/grid#module
      ],
      'datecontrol' => [
          'class' => 'kartik\datecontrol\Module',
          // see settings on http://demos.krajee.com/datecontrol#module
      ],
	],
        // uncomment the following to add your IP if you are not connecting from localhost.
        'allowedIPs' => ['***************','127.0.0.1', '::1','************','************','************','*************','***********','*************','*************'],
        'generators' => [ //here
            'crud' => [
                'class' => 'yii\gii\generators\crud\Generator',
                'templates' => [
                    'adminlte' => '@vendor/dmstr/yii2-adminlte-asset/gii/templates/crud/simple',
                ]
            ]
	],
    ];
}

//echo "<pre>";
//print_r($config,false);
//exit;

return $config;

