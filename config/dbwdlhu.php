<?php

$xdsn = "sqlanywhere:ENG={wdl};SERVERNAME=wdl;DATABASENAME=wdl;COMMLINKS=tcpip{host=localhost;port=2638}";
//$xdsn = "sqlanywhere:ENG={e-stock};SERVERNAME=e-stock;DATABASENAME=e-stock;COMMLINKS=tcpip{host=*************;port=2637}";
// if( $_SERVER["HTTP_HOST"] == "dev.klub.wdl.sk" ) {
// 	$xdsn = "sqlanywhere:ENG={blava2018};SERVERNAME=blava2018;DATABASENAME=blava2018;COMMLINKS=tcpip{host=127.0.0.1;port=2638}";
// }

return [
    'class' => 'yii\db\Connection',
    'driverName' => 'sybase',
    'schemaMap' => [
        'sybase' => \websightnl\yii2\sybase\Schema::className(),
    ],
    'dsn' => $xdsn,
    'username' => 'dba',
    'password' => 'Rin1!!!',

];
