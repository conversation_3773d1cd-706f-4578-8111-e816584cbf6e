<?php
return [
            'class' => 'yii\db\Connection',
            'dsn' => 'pgsql:host=localhost;dbname=wikarska',
            'username' => 'dba',
            'password' => 'Rin1!!!andBeyond',
            'charset' => 'utf8',
            'attributes'=>[
                PDO::ATTR_PERSISTENT => true
            ],
            'schemaMap' => [
                'pgsql' => [
                  'class' => 'yii\db\pgsql\Schema',
                  'defaultSchema' => 'public' //specify your schema here, public is the default schema
                ]
            ], // PostgreSQL
	    // Schema cache options (for production environment)
	    'enableSchemaCache' => true,
	    'schemaCacheDuration' => 60,
	    'schemaCache' => 'cache',
];
