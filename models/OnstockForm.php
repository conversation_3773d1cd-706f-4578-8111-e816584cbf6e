<?php

namespace app\models;

use Yii;
use yii\base\Model;

/**
 * ContactForm is the model behind the contact form.
 */
class OnstockForm extends Model
{
    public $model;
    public $mat_id;
    public $kod;
    public $repli;
    public $stock_id;
    public $price1;
    public $price2;
    public $price3;
    public $d1;


    /**
     * @return array the validation rules.
     */
    public function rules()
    {
        return [
            
            [['model', 'mat_id', 'kod','repli', 'stock_id', 'price1', 'price2', 'price3', 'd1'], 'default'],
        ];
    }

    /**
     * @return array customized attribute labels
     */
    public function attributeLabels()
    {
        return [
            'model' => "Model or pdescr like",
            'mat_id' => "mat_id",
            'kod' => "Kod like",
            'repli' => "Country/ repli",
            'stock_id' => "Stock",
            'price1' => "Also show price",
            'price2' => "and price",
            'price3' => "and also price",
            'd1' => "and first date",
        ];
    }

}
