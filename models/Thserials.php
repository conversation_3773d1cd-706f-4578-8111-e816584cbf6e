<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "thserials".
 *
 * @property int $id
 * @property int $mat_id
 * @property int $move_id
 * @property string $thserial
 *
 * @property Product $mat
 * @property Movement $mov
 */
class Thserials extends \yii\db\ActiveRecord
{

    public $multiadd; //Helper for form

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'thserials';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['mat_id', 'move_id'], 'integer'],
            [['move_id'], 'required'],
            [['multiadd'], 'safe'],
            [['thserial'], 'string', 'max' => 30],
            [['move_id', 'mat_id', 'thserial'], 'unique', 'targetAttribute' => ['move_id', 'mat_id', 'thserial']],
            // [['mat_id'], 'unique'],
            // [['move_id'], 'unique'],
            // [['thserial'], 'unique'],
            [['mat_id'], 'exist', 'skipOnError' => true, 'targetClass' => Product::className(), 'targetAttribute' => ['mat_id' => 'mat_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'mat_id' => 'Mat ID',
            'move_id' => 'Move ID',
            'thserial' => 'Thserial',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getMat()
    {
        return $this->hasOne(Product::className(), ['mat_id' => 'mat_id']);
    }


    /**
     * @return \yii\db\ActiveQuery
     */
    public function getMov()
    {
        return $this->hasOne(Movement::className(), ['move_id' => 'move_id']);
    }

    /**
     * {@inheritdoc}
     * @return ThserialsQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new ThserialsQuery(get_called_class());
    }
}
