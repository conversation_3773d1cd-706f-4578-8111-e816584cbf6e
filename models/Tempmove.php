<?php

namespace app\models;

use Yii;
use app\controllers\EstockTools;

/**
 * This is the model class for table "tempmove".
 *
 * @property int $mat_id
 * @property string $price
 * @property string $pcs
 * @property string $discount
 * @property string $tax
 * @property string $act_price
 * @property string $currency
 * @property string $all1
 * @property string $all2
 * @property string $all3
 * @property string $all4
 * @property string $all5
 * @property string $ean13
 * @property string $kod
 * @property string $model
 * @property string $pdescr
 * @property string $unit
 * @property string $user_name
 * @property int $clip_id
 * @property string $d1
 * @property string $d2
 * @property string $d3
 * @property string $text1
 * @property string $text2
 * @property int $m_type_id
 * @property string $stock_id1
 * @property string $stock_id2
 * @property int $adr_id
 * @property string $price_type
 * @property string $tempmove_info
 * @property string $fifo_currency
 * @property string $fifo_price
 * @property int $fifo_move_id
 * @property int $nr_counter
 * @property string $thserial 
 * @property int $onstock
 *
 */
class Tempmove extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'tempmove';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['mat_id', 'price', 'pcs', 'discount', 'tax', 'user_name', 'clip_id'], 'required'],
            [['mat_id', 'clip_id', 'm_type_id', 'adr_id',  'fifo_move_id'], 'integer'],
            [['price', 'pcs', 'discount', 'tax', 'act_price', 'all1', 'all2', 'all3', 'all4', 'all5', 'fifo_price'], 'number'],
            [['d1', 'd2', 'd3'], 'safe'],
            [['currency', 'unit', 'stock_id1', 'stock_id2', 'price_type', 'fifo_currency'], 'string', 'max' => 6],
            [['ean13'], 'string', 'max' => 13],
            [['kod'], 'string', 'max' => 4],
            [['text1', 'text2'], 'string'], 
            [['model', 'tempmove_info', 'thserial'], 'string', 'max' => 255],
            [['pdescr'], 'string', 'max' => 255],
            [['user_name'], 'string', 'max' => 20],
            [['text2', 'tempmove_info'], 'string', 'max' => 255],
            [['mat_id', 'user_name', 'clip_id', 'tempmove_info'], 'unique', 'targetAttribute' => ['mat_id', 'user_name', 'clip_id', 'tempmove_info']],
            [['pcs'], 'checkStock'],
            [['mat_id'], 'checkModel'],
            [['price'], 'checkPrice'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'mat_id' => 'Mat ID',
            'price' => 'Price',
            'pcs' => 'Pcs',
            'discount' => 'Discount',
            'tax' => 'Tax',
            'act_price' => 'Act Price',
            'currency' => 'Currency',
            'all1' => 'All1',
            'all2' => 'All2',
            'all3' => 'All3',
            'all4' => 'All4',
            'all5' => 'All5',
            'ean13' => 'Ean13',
            'kod' => 'Kod',
            'model' => 'Model',
            'pdescr' => 'Pdescr',
            'unit' => 'Unit',
            'user_name' => 'User Name',
            'clip_id' => 'Clip ID',
            'd1' => 'D1',
            'd2' => 'D2',
            'd3' => 'D3',
            'text1' => 'Text1',
            'text2' => 'Text2',
            'm_type_id' => 'M Type ID',
            'stock_id1' => 'Stock Id1',
            'stock_id2' => 'Stock Id2',
            'adr_id' => 'Adr ID',
            'price_type' => 'Price Type',
            'tempmove_info' => 'Info',
            'fifo_currency' => 'Fifo Currency',
            'fifo_price' => 'Fifo Price',
            'fifo_move_id' => 'Fifo Move ID',
            'nr_counter' => 'Nr Counter',
            'thserial' => 'TH Serial #', 
        ];
    }

    /**
     * {@inheritdoc}
     * @return TempmoveQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new TempmoveQuery(get_called_class());
    }

    /**
     * check if enough on stock
     */
    public function checkStock($attribute,$params)
    {

        if(  Yii::$app->db->createCommand("select stock_id1 from m_type where m_type_id=:m_type_id")
                ->bindValue(':m_type_id', $this->m_type_id)
                ->queryScalar() != false ){

            // Rewriten for onstock only - onshop is deprecated
            if( !empty($this->stock_id1)){
                $onstock =  Yii::$app->db->createCommand("select onstock(:mat_id,:stock)")
                    ->bindValue(':mat_id', $this->mat_id)
                    ->bindValue(':stock', $this->stock_id1)
                    ->queryScalar();

                if( $onstock < $this->pcs ){
                      $this->addError($attribute, 'NOT ENOUGH PCS for '.$this->mat_id.' on STOCK '. $this->stock_id1.', only '.$onstock);
                }
            }


        }
            
    }

    /**
     * check if enough on stock
     */
    public function checkModel($attribute,$params)
    {

            
    }

    /**
     * To DO check price according to manager requests...
     */
    public function checkPrice($attribute,$params)
    {

            
    }

    /**
     * To DO test and think about other countries
     */
    //ToDo nejako to nefunguje aj ked to tam da pri save clip to nejde
    public function beforeValidate() {

        if( $this->stock_id1 == '' ) {
            $this->stock_id1 = null;
        }
        if( $this->stock_id2 == '' ) {
            $this->stock_id2 = null;
        }
     //    if( $this->m_type_id == 14 ) {
     //     $this->stock_id1 = 'sj';
     //        if( EstockTools::getUserAdrRepliId() == 2 ){
     //            $this->stock_id2 = 's2';
     //        } else {
     //            $this->stock_id2 = 's0';
     //        }
     // }
     //    else if( $this->m_type_id == 87 ) {
     //        $this->stock_id1 = 'sj';
     //    }
     //    else if( $this->m_type_id == 30 ) {
     //        $this->stock_id1 = 'sj';
     //    }
     //    else if( $this->m_type_id == 97 ) {
     //     $this->stock_id2 = 'sj';
     // }
     //    else if( $this->m_type_id == 99 ) {
     //        $this->stock_id2 = 'sj';
     //    }


        return parent::beforeValidate();

    }



    public function beforeSave($insert) {

        $this->beforeValidate();

        return parent::beforeSave($insert);

    }


    /**
     * @return int
     */
    public function getOnstock()
    {

            // Rewriten for onstock only - onshop is deprecated
            if( !empty($this->stock_id1)){
                return Yii::$app->db->createCommand("select onstock(:mat_id,:stock)")
                    ->bindValue(':mat_id', $this->mat_id)
                    ->bindValue(':stock', $this->stock_id1)
                    ->queryScalar();

            }

            if( !empty($this->stock_id2)){
                return Yii::$app->db->createCommand("select onstock(:mat_id,:stock)")
                    ->bindValue(':mat_id', $this->mat_id)
                    ->bindValue(':stock', $this->stock_id2)
                    ->queryScalar();

            }


        return null;    

    }



}
