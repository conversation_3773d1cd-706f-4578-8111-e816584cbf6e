<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "adr_contact".
 *
 * @property int $adr_id
 * @property string $number
 * @property string $describe
 * @property string $person
 * @property int $repli_id
 *
 * @property Address $adr
 */
class AdrContact extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'adr_contact';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['adr_id', 'number', 'repli_id'], 'required'],
            [['adr_id', 'repli_id'], 'default', 'value' => null],
            [['adr_id', 'repli_id'], 'integer'],
            [['number', 'person'], 'string', 'max' => 20],
            [['describe'], 'string', 'max' => 25],
            [['adr_id', 'number', 'repli_id'], 'unique', 'targetAttribute' => ['adr_id', 'number', 'repli_id']],
            [['adr_id', 'repli_id'], 'exist', 'skipOnError' => true, 'targetClass' => Address::className(), 'targetAttribute' => ['adr_id' => 'adr_id', 'repli_id' => 'repli_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'adr_id' => 'Adr ID',
            'number' => 'Number',
            'describe' => 'Describe',
            'person' => 'Person',
            'repli_id' => 'Repli ID',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getAdr()
    {
        return $this->hasOne(Address::className(), ['adr_id' => 'adr_id', 'repli_id' => 'repli_id']);
    }
}
