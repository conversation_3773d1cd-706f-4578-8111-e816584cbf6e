<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "customers".
 *
 * @property int $id
 * @property string $gender
 * @property string $firstname
 * @property string $lastname
 * @property string $dob
 * @property string $email_address
 * @property string $street_address
 * @property string $suburb
 * @property string $postcode
 * @property string $city
 * @property string $state
 * @property string $telephone
 * @property string $fax
 * @property string $password
 * @property int $country_id
 * @property int $zone_id
 * @property string $nick
 * @property string $card_nr
 * @property string $issue_d
 * @property string $expire_d
 * @property string $card_type
 * @property string $card_discount
 * @property string $cust_currency
 * @property string $card_memo
 * @property WowSales $wowSales
 */
class Customers extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'customers';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['country_id', 'zone_id'], 'integer'],
            [['issue_d', 'expire_d'], 'safe'],
            [['card_discount'], 'number'],
            [['gender'], 'string', 'max' => 1],
            [['firstname', 'lastname', 'suburb', 'city', 'state', 'telephone', 'fax', 'card_nr'], 'string', 'max' => 32],
            [['dob'], 'string', 'max' => 8],
            [['street_address'], 'string', 'max' => 64],
            [['postcode'], 'string', 'max' => 10],
            [['password'], 'string', 'max' => 40],
            [['nick'], 'string', 'max' => 20],
            [['card_type'], 'string', 'max' => 2],
            [['cust_currency'], 'string', 'max' => 3],
            [['card_memo'], 'string', 'max' => 255],
            [['nick'], 'unique'],
            ['email_address', 'email'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'gender' => 'Gender',
            'firstname' => 'Firstname',
            'lastname' => 'Lastname',
            'dob' => 'Dob',
            'email_address' => 'Email Address',
            'street_address' => 'Street Address',
            'suburb' => 'Suburb',
            'postcode' => 'Postcode',
            'city' => 'City',
            'state' => 'State',
            'telephone' => 'Telephone',
            'fax' => 'Fax',
            'password' => 'Password',
            'country_id' => 'Country ID',
            'zone_id' => 'Zone ID',
            'nick' => 'Nick',
            'card_nr' => 'Card Nr',
            'issue_d' => 'Issue Date',
            'expire_d' => 'Expire Date',
            'card_type' => 'Card Type',
            'card_discount' => 'Card Discount',
            'cust_currency' => 'Cust Currency',
            'card_memo' => 'Card Memo',
            'gdpr' => 'DGPR agreement'
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getWowSales()
    {
        return $this->hasOne(WowSales::className(), ['cust_id' => 'id']);
    }

        /**
     * @return \yii\db\ActiveQuery
     */
    public function getCountries()
    {
        return $this->hasOne(Countries::className(), ['numcode' => 'country_id']);
    }

}
