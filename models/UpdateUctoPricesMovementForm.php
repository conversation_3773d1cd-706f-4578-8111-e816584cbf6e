<?php

namespace app\models;

use Yii;
use yii\base\Model;

/**
 * ContactForm is the model behind the contact form.
 */
class UpdateUctoPricesMovementForm extends Model
{
    public $multiaddprices = "";
    public $multiaddpricesLabel = "Zobraz zmeny PRICE riadky a prikazy";
    public $multiaddpricesPlaceholder = "new price,move_id,mat_id, old price, detail_info (detail_info is optional)";


    /**
     * @return array the validation rules.
     */
    public function rules()
    {
        return [
            
            [['multiaddprices','multiaddpricesPlaceholder','multiaddpricesLabel'], 'default'],
        ];
    }

    /**
     * @return array customized attribute labels
     */
    public function attributeLabels()
    {
        return [
            'multiaddpricesLabel' => "Multi PRICES update",

        ];
    }

}
