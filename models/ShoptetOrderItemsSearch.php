<?php

namespace app\models;

use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\ShoptetOrderItems;

/**
 * ShoptetOrderItemsSearch represents the model behind the search form of `app\models\ShoptetOrderItems`.
 */
class ShoptetOrderItemsSearch extends ShoptetOrderItems
{
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['item_id', 'order_id'], 'integer'],
            [['otype', 'name', 'ocode', 'variant_name', 'ean', 'plu', 'manufacturer', 'supplier', 'unit', 'status', 'surcharges'], 'safe'],
            [['amount', 'weight', 'discount', 'unit_price_with_vat', 'unit_price_without_vat', 'unit_price_vat', 'unit_price_vat_rate', 'unit_discount_price_with_vat', 'unit_discount_price_without_vat', 'total_price_with_vat', 'total_price_without_vat', 'total_price_vat', 'total_price_vat_rate'], 'number'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = ShoptetOrderItems::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'item_id' => $this->item_id,
            'order_id' => $this->order_id,
            'amount' => $this->amount,
            'weight' => $this->weight,
            'discount' => $this->discount,
            'unit_price_with_vat' => $this->unit_price_with_vat,
            'unit_price_without_vat' => $this->unit_price_without_vat,
            'unit_price_vat' => $this->unit_price_vat,
            'unit_price_vat_rate' => $this->unit_price_vat_rate,
            'unit_discount_price_with_vat' => $this->unit_discount_price_with_vat,
            'unit_discount_price_without_vat' => $this->unit_discount_price_without_vat,
            'total_price_with_vat' => $this->total_price_with_vat,
            'total_price_without_vat' => $this->total_price_without_vat,
            'total_price_vat' => $this->total_price_vat,
            'total_price_vat_rate' => $this->total_price_vat_rate,
        ]);

        $query->andFilterWhere(['ilike', 'otype', $this->otype])
            ->andFilterWhere(['ilike', 'name', $this->name])
            ->andFilterWhere(['ilike', 'ocode', $this->ocode])
            ->andFilterWhere(['ilike', 'variant_name', $this->variant_name])
            ->andFilterWhere(['ilike', 'ean', $this->ean])
            ->andFilterWhere(['ilike', 'plu', $this->plu])
            ->andFilterWhere(['ilike', 'manufacturer', $this->manufacturer])
            ->andFilterWhere(['ilike', 'supplier', $this->supplier])
            ->andFilterWhere(['ilike', 'unit', $this->unit])
            ->andFilterWhere(['ilike', 'status', $this->status])
            ->andFilterWhere(['ilike', 'surcharges', $this->surcharges]);

        return $dataProvider;
    }
}
