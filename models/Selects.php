<?php

namespace app\models;

define("THISYEAR", date("Y"));
define("PETERSELECT1",  "SELECT 
                    a.adr_id,firma, d.move_id, m_type_id, m.number, d1, d2, d3, d.mat_id,kod, p.price, p.p0, d.currency, model,
                    case when m.stock_id1 is not null then sum(-pcs) else sum(pcs) end as xxpcs,
                    d.price as selprice, p.price, p.price*(case when m.stock_id1 is not null then sum(-pcs) else sum(pcs) end) as price_total,
                    round(d.price*(case when m.stock_id1 is not null then sum(-pcs) else sum(pcs) end)-d.price *(case when m.stock_id1 is not null then sum(-pcs) else sum(pcs) end) * d.discount/100 ,2) as seltotal,
                    text1, text2,stock_id1, emp_id
                    FROM
                    address a, m_detail d, movement m, product p 
                    WHERE
                    a.adr_id=m.adr_id and d.move_id = m.move_id 
                    and d.mat_id = p.mat_id and
                    /* PARAMETER DATUM */
                    d1 between :p1 and :p2 
                    /* PARAMETER M_TYPE */
                    and m_type_id in (xxparamxx)
                    /* PARAMETER MODEL */
                    and p.kod like :p3
                    GROUP BY
                    d.mat_id,firma, d.move_id, m_type_id, d1, d2, d3, kod,p.price, p.p0, model,a.adr_id,
                    d.currency,m.number, m.emp_id,
                    d.price, m.m_type_id, d.tax, p.price , d.discount, text1, text2, m.stock_id1
                    ORDER BY
                    kod asc, model asc");

/**
 * All selects used in SqlController
 */
class Selects
{

    public static $statsquery = "select days, case when (adr_id between 12065 and 12092) then 6234 else adr_id end adr_id_grouped, 
                    case when position('wdl.sk' in shop) > 0 then 'eshop wdl.sk' when position('wow.sk' in shop) > 0 then 'eshop wow.sk' else shop end shopname
        
                    , sum( turnover) turnover,sum(sum_of_pcs) sum_of_pcs, currency, sum(costs) costs, sum(margin) margin, sum(\"Brut profit\") BrutProfit, \"Note\", \"OnePerc\", \"Net profit\"  from (
        
        select days, adr_id, 
        
        shop, turnover::money,sum_of_pcs, currency, costs, margin, margin-costs as \"Brut profit\", ' ' as \"Note\",' ' as \"OnePerc\", ' ' as \"Net profit\"  from (
        SELECT currency,
        'xxparamxx' as days,
        adr_id,
        
        (select firma||' '||city from address where adr_id=m.adr_id ) || case when strpos(t.name,'//') = 0 then '' else substring(t.name from strpos(t.name,'//') )  end as shop,
        floor(sum(case when m.m_type_id in (99,106,98,105,97,104,138,140,148,505) then -md.pcs*(md.price*(1-md.discount/100))*(1+md.tax/100) else md.pcs*(md.price*(1-md.discount/100))*(1+md.tax/100) end)) turnover,
        floor(
            case adr_id
                when 3731 --    G-WATCH PREV. OPTIMA KOSICE         
                    then 9993*xxparamxx/30
                when 3624 --    WATCH DE LUXE G-WATCH WOW-AUPARK BRATISLAVA     
                    then 48000*xxparamxx/30
                when 4370 --    AUPARK2 BRATISLAVA      
                    then 8865*xxparamxx/30
                when 4413 --    RACIO BB        
                    then 12056*xxparamxx/30
                when 3511 --    G-WATCH WOW-POLUS BRATISLAVA            
                    then 8855*xxparamxx/30
                when 4171 --    VALENTINA Slovakia, s.r.o BRATISLAVA    
                    then 0*xxparamxx/30
                when 3512 --    WATCH DE LUXE - POLUS           
                    then 4434*xxparamxx/30
                when 5039 --    eurovea wow         
                    then 31638*xxparamxx/30                  
                when 5040 --    eurovea TH          
                    then 11485*xxparamxx/30
                when 3744 --    CAMPONA Budapest            
                    then 80000*xxparamxx/3
                when 3489 --    MISKOLC - PLÁZA MISKOLC
                    then 100000*xxparamxx/3
                when 3488 --    NYÍRPLÁZA NYIREGYHÁZA       
                    then 140000*xxparamxx/3
                when 4156 --    WDL HU
                    then 600000*xxparamxx/3
                when 4585 --     Arena  HU
                    then 480000*xxparamxx/3
                when 4727 --    TH SHOP HU
                    then 350000*xxparamxx/3
                when 6118 --    WEST WATCH HU
                    then 550000*xxparamxx/3
                when 6487 --    ANDRASSY HU
                    then 2700000*xxparamxx/3
                when 6234 -- ESHOP
                    then 
                        case when 2 = :p3
                                then 100000*xxparamxx/3
                            else 
                                0*xxparamxx/30
                        end
                else 0*xxparamxx/30
                end
            ) costs,
        floor(sum(
            case m.m_type_id
                when 30 then 
                    md.pcs*(md.price*(1-md.discount/100)/1.27-p.p3)
                when 31 then 
                    md.pcs*(md.price*(1-md.discount/100)-p.p3)
                when 90 then md.pcs*(md.price*(1-md.discount/100)/1.21-p.p2)
                when 91 then md.pcs*(md.price*(1-md.discount/100)-p.p2)
                when 87 then 
                    (case m.\"X\" when '9' then 
                        md.pcs*(md.price*(1-md.discount/100)-p.p0)
                    else
                        md.pcs*(md.price*(1-md.discount/100)/1.20-p.p0)
                    end)
                when 88 then md.pcs*(md.price*(1-md.discount/100)-p.p0)

                when 99 then 
                    -md.pcs*(md.price*(1-md.discount/100)/1.27-p.p3)
                when 106 then 
                    -(md.pcs*(md.price*(1-md.discount/100)-p.p3))
                when 98 then -(md.pcs*(md.price*(1-md.discount/100)/1.21-p.p2))
                when 105 then -(md.pcs*(md.price*(1-md.discount/100)-p.p2))
                when 97 then -(md.pcs*(md.price*(1-md.discount/100)/1.20-p.p0))
                when 104 then -(md.pcs*(md.price*(1-md.discount/100)-p.p0))
                when 138 then -(md.pcs*(md.price*(1-md.discount/100)-p.p0))
                when 137 then md.pcs*(md.price*(1-md.discount/100)-p.p0)
                when 148 then -(md.pcs*(md.price*(1-md.discount/100)/1.20-p.p0))
                when 147 then md.pcs*(md.price*(1-md.discount/100)/1.20-p.p0)
                when 149 then md.pcs*(md.price*(1-md.discount/100)/1.20-p.p0)
                when 140 then -(md.pcs*(md.price*(1-md.discount/100)-p.p0))
                when 139 then md.pcs*(md.price*(1-md.discount/100)-p.p0)
                when 167 then md.pcs*(md.price*(1-md.discount/100)-p.p0)
                when 168 then -(md.pcs*(md.price*(1-md.discount/100)-p.p0))
                when 169 then md.pcs*(md.price*(1-md.discount/100)-p.p0)
                when 170 then -(md.pcs*(md.price*(1-md.discount/100)/1.20-p.p0))


                when 505 then -(md.pcs*(md.price*(1-md.discount/100)-p.p3)) -- ToDo .. also chceck WDL hu price
                when 504 then md.pcs*(md.price*(1-md.discount/100)-p.p3)
                
                else 0
            end
            )) margin,
            sum(md.pcs) sum_of_pcs
        FROM
         movement m join m_detail md on m.move_id=md.move_id join product p on p.mat_id=md.mat_id
         join m_type t on t.m_type_id=m.m_type_id
        WHERE
         year(d1)>2018 and 
         d1 between :p1 and :p2 
         and m.m_type_id in (30,31,99,106,90,91,98,105,87,88,97,104,137,138,139,140,167,168,169,170,147,148,149,504,505) 
         and m.repli_id = :p3

         

        GROUP BY
         md.currency,
         (select firma||' '||city from address where adr_id=m.adr_id ) || case when strpos(t.name,'//') = 0 then '' else substring(t.name from strpos(t.name,'//') )  end,
         m.adr_id 
        ORDER BY
         shop ) sss

        ) vvv
group by days, adr_id_grouped,  shopname , currency, \"Note\", \"OnePerc\", \"Net profit\"  ";

    public static $query = [
        'matejek' => [
            'all_OK' => [
                'q' => "select adr_id,firma, move_id, m_type_id, number, d1, mat_id,kod, price, p2, p0, p6, p1, currency, model,
                            xxpcs,
                            (dprice-dprice*discount/100)::numeric(11,2) as selDiscountedprice,
                            discount::numeric(11,4), dprice, dprice*xxpcs as price_total,
                            round(dprice*xxpcs-dprice * xxpcs * discount/100 ,2) as seltotal,
                            text1, text2,stock_id1
                            from (
                            SELECT
                            d.discount,a.adr_id,firma, d.move_id, m_type_id, m.number, d1, d.mat_id,kod, p.price, p.p2, p.p0, p.p6, p.p1, d.currency, model, d.price as dprice,
                            case when m.stock_id1 is not null  then sum(-pcs) else sum(pcs) end as xxpcs,
                            d.price-d.price*d.discount/100 as selDiscountedprice,
                            text1, text2,stock_id1
                            FROM
                            address a, m_detail d, movement m, product p
                            WHERE
                            a.adr_id=m.adr_id and d.move_id = m.move_id and d.m_repli_id=m.repli_id
                            and d.mat_id = p.mat_id and
                            /* PARAMETER DATUM */
                            d1 between :p1 and :p2
                            /* PARAMETER M_TYPE */
                            and m_type_id in (xxparamxx)
                            /* PARAMETER MODEL */
                            and p.kod like :p3
                            GROUP BY
                            d.mat_id,firma, d.move_id, m_type_id, d1, kod,p.price, p.p2, p.p0, p.p6, p.p1, model,a.adr_id,
                            d.currency,m.number,
                            d.price, m.m_type_id, d.tax, p.price , d.discount, text1, text2, m.stock_id1
                            ) ddd
                            ORDER BY
                            kod asc, model asc
                            ",
                'l1' => 'Date from',
                'l2' => 'Date to',
                'l3' => 'Kod like',
                'xxlabxx' => 'M Types',
                'p1' => THISYEAR . '-01-01',
                'p2' => THISYEAR . '-12-31',
                'p3' => '%',
                'xxparamxx' => '24,110,64,69',
            ],
            'eshop' => [
                'q' => "select d.move_id,d.mat_id,kod,model, sum(round(d.price*pcs-d.price * pcs * d.discount/100 ,2)) sum_pcs_price,sum(pcs) sum_pcs,d.currency,c_number,m_type_id, d1,
            icompany, iname, isurname, icity, iico, idic,
             alljson eshopinfo from movement m join m_detail d on m.move_id=d.move_id join product p on p.mat_id=d.mat_id left outer join eshop e on e.id=m.c_number where (adr_id=6234 or adr_id between 12065 and 12092) and 
                            /* PARAMETER DATUM */
                            d1 between :p1 and :p2
                            /* PARAMETER M_TYPE */
                            and m_type_id in (xxparamxx)
                            /* PARAMETER MODEL */
                            and p.kod like :p3

                group by d.move_id,kod,c_number,d.mat_id,model,m_type_id,alljson,d1,d.currency,
                        icompany, iname, isurname, icity, iico, idic
                order by m_type_id,c_number",
                'l1' => 'Date from',
                'l2' => 'Date to',
                'l3' => 'Kod like',
                'xxlabxx' => 'M Types',
                'p1' => THISYEAR . '-01-01',
                'p2' => THISYEAR . '-12-31',
                'p3' => '%',
                'xxparamxx' => '137,139,138,140,167,169,168,170',

            ],
            'wow klub stats' => [
                'q' => "SELECT 
                    c.Id,
                    c.Firstname,
                    c.Lastname,
                    c.Email_Address,
                    c.Card_Nr,
                    c.Card_Type,
                    c.Card_Discount,
                    SUM(s.pcs) as total_pcs,
                    SUM(s.pcs * s.sale_price) as total_sales,
                    COUNT(DISTINCT s.orders_id) as number_of_purchases,
                    MIN(s.sale_date) as first_purchase,
                    MAX(s.sale_date) as last_purchase,
                    (SELECT p2.model 
                     FROM wow_sales s2 
                     JOIN product p2 ON s2.mat_id = p2.mat_id 
                     WHERE s2.cust_id = c.id 
                     AND p2.kod LIKE :p1
                     AND s2.sale_date BETWEEN :p2 AND :p3
                     ORDER BY s2.sale_price DESC 
                     LIMIT 1) as most_expensive_model
                FROM 
                    customers c
                LEFT JOIN 
                    wow_sales s ON c.id = s.cust_id
                LEFT JOIN 
                    product p ON s.mat_id = p.mat_id
                WHERE 
                    p.kod LIKE :p1
                    AND s.sale_date BETWEEN :p2 AND :p3
                GROUP BY 
                    c.Id, c.Firstname, c.Lastname, c.Email_Address, 
                    c.Card_Nr, c.Card_Type, c.Card_Discount
                ORDER BY 
                    total_sales DESC",
                'l1' => 'Product KOD like',
                'l2' => 'Date from',
                'l3' => 'Date to',
                'p2' => THISYEAR . '-01-01',
                'p3' => THISYEAR . '-12-31',
                'p1' => '%',
            ],

        ],

        'veronika' => [

            'eshop' => [
                'q' => "select e.icompany, e.iname, e.isurname, e.istreet,e.icity, e.izip, e.icountry,
                        e.iico,e.idic, e.payment, m.tax2 DPH, m.total2 ZAKLAD, m.total, m_type_id, d1,d2,d3, number, (select currency from m_detail where move_id=m.move_id limit 1) MENA,
                         (select firma from address where adr_id=m.adr_id) FIRMA from movement m left outer join eshop e on m.c_number = e.id
where d1 between :p1 and :p2 and m_type_id in (xxparamxx) order by d1 desc",
                'l1' => 'Date from',
                'l2' => 'Date to',
                'xxlabxx' => 'M Types',
                'p1' => THISYEAR . '-01-01',
                'p2' => THISYEAR . '-12-31',
                'xxparamxx' => '137,138,139,140,167,169,168,170',
            ],

            'zlavyof' => [
                'q' => "select distinct  text2 as faktury, a.firma centrala, (select sum(pcs*price*(discount/100)) from m_detail where move_id=d.move_id)::numeric(11,2) spoluZlava, (select sum(pcs*price*((fifo_price)/100)) from m_detail where move_id=d.move_id)::numeric(11,2) druhaZlava, m.m_type_id,d1, (select currency from m_detail where move_id=d.move_id limit 1) currency, m.number from m_detail d join movement m on m.move_id=d.move_id join address a on a.adr_id=m.adr_id 
             where d1 between :p1 and :p2  and m.m_type_id in (xxparamxx) and 
             (select sum(pcs*price*(discount/100)) from m_detail where move_id=d.move_id)<>0 order by d1",
                'l1' => 'Date from',
                'l2' => 'Date to',
                'xxlabxx' => 'M Types',
                'p1' => THISYEAR . '-01-01',
                'p2' => THISYEAR . '-12-31',
                'xxparamxx' => '24,110,130,145',
            ],
            'movementswithcurr' => [
                'q' => "
                    select firma||' '||city shop, total, total0+total1+total2 zaklad_dane, m_type_id, d1,d2,d3, number,
                    m.text1, m.text2, tax1,tax2, (select currency from m_detail where move_id=m.move_id limit 1) MENA,
                    i.Iname,	i.Isurname,	i.Istreet,	i.Icity,	i.Icountry,	i.Icompany,	i.Iico,	i.Idic,	i.Iicdph,
                    i.Shipment,	i.Branch,	i.Voucher,	i.Payment,	i.Incoterms
                    from movement m left outer join address a on a.adr_id=m.adr_id left outer join minfo i on i.move_id=m.move_id 
                    where 
                            d1 between :p1 and :p2 
                            and m_type_id in (xxparamxx)
                            order by
                             d1 desc",
                'l1' => 'Date from',
                'l2' => 'Date to',
                'xxlabxx' => 'M Types',
                'p1' => THISYEAR . '-01-01',
                'p2' => THISYEAR . '-12-31',
                'xxparamxx' => '14,17,20,23,24,27,64,66,69,73,86,87,88,89,94,95,97,101,102,104,109,110,111,112,114,115,116,117,118,130,131,145,146,132,133,134,135,136,137,138,139,140,167,169,168,170',
            ],
            'intrastats' => [
                'q' => "SELECT (seltotal*kurz)::numeric(11,2) as kurz_spolu, adr_id, firma, m_type_id, \"number\", d2,
                             kurz::numeric(11,2),kurz_cena::numeric(11,2),              mat_id,kod, currency, model, xxpcs, seltotal, text1, text2,stock_id1 from ( 
                            SELECT adr_id, firma, m_type_id, \"number\", d2, kurz,kurz_cena, mat_id,kod, currency, model, xxpcs, round(price-price * discount/100 ,2) as selprice, round(price*xxpcs-price * xxpcs * discount/100 ,2) as seltotal, text1, text2,stock_id1,round(price-price * discount/100 ,2) as selprice from (
                            SELECT a.adr_id, d.price,d.discount,firma, m_type_id, m.number, d2,(select nbs_rate/nbs_multi from nbs_rates where nbs_cur=d.currency and nbs_date <= m.d2 order by nbs_date desc limit 1 ) as kurz, coalesce( d.price* (select nbs_rate/nbs_multi from nbs_rates where nbs_cur=d.currency and nbs_date <= m.d2 order by nbs_date desc limit 1 ), d.price ) as kurz_cena, d.mat_id,kod, d.currency, model, case when m.stock_id1 is not null  then sum(-pcs) else sum(pcs) end as xxpcs, text1, text2,stock_id1 FROM address a, m_detail d, movement m, product p WHERE a.adr_id=m.adr_id and d.move_id = m.move_id and d.mat_id = p.mat_id and d2 between :p1 and :p2 and m_type_id in (xxparamxx) GROUP BY d.mat_id,firma, m_type_id, d2, kod, model,a.adr_id, d.currency,m.number, d.price, m.m_type_id, d.tax, p.price , d.discount, text1, text2, m.stock_id1 ORDER BY kod asc, model asc) sss) ssss",
                'l1' => 'Date from',
                'l2' => 'Date to',
                'xxlabxx' => 'M Types',
                'p1' => THISYEAR . '-01-01',
                'p2' => THISYEAR . '-12-31',
                'xxparamxx' => '69,110,109,130,131,145,146',
            ],


        ],

        'olga' => [

            'kontrola RP' => [
                'q' => "SELECT m.adr_id,firma,doc_id, number,d1,  
                        sum(md.pcs*md.price*(1-md.discount/100))::numeric(11,2) as detailsuma,md.currency, sum(md.pcs) as detailpcs ,   
                        total0+total1+total2+tax1+tax2+rounding totalWithVat,
                        text1,text2,m.m_type_id,m.move_id,m.repli_id,emp_id,stock_id1,stock_id2     FROM movement as m, address as a, m_detail as md    where  m.adr_id = a.adr_id and md.move_id=m.move_id                            
                        /* TYPES OF MOVEMENT  */   
                         and m_type_id in(xxparamxx)    
                        /* DATE FILTER  */
                        and d1 between :p1 and :p2   
                        group by  m.adr_id,firma,doc_id, md.currency,number,text1,text2,emp_id,d1,m.m_type_id,stock_id1,stock_id2,m.move_id
                        /* SORTING */   
                        order by d1",
                'l1' => 'Date from',
                'l2' => 'Date to',
                'xxlabxx' => 'M Types',
                'p1' => THISYEAR . '-01-01',
                'p2' => THISYEAR . '-12-31',
                'xxparamxx' => '87,97,147,148,149',
            ],
            'Invoice list' => [
                'q' => "select
                            m.move_id,
                            (select adr_id from address where adr_id=a.firma_id limit 1) c_adr_id,
                            (select repli_id from address where adr_id=a.firma_id limit 1 ) c_repli_id,
                            (select firma from address where adr_id=a.firma_id limit 1 ) c_firma,
                            (select street from address where adr_id=a.firma_id limit 1 ) c_street,
                            (select city from address where adr_id=a.firma_id limit 1 ) c_city,
                            (select zip from address where adr_id=a.firma_id limit 1 ) c_zip,
                            (select ico from address where adr_id=a.firma_id limit 1 ) centrala_ico,
                            (select drc1 from address where adr_id=a.firma_id limit 1 ) c_drc1,
                            (select drc2 from address where adr_id=a.firma_id limit 1 ) c_drc2,

                            m.adr_id,
                            a.firma,
                            a.street,
                            a.city,
                            a.zip,
                            a.ico,
                            a.drc1,
                            a.drc2,
                            total,
                            total0+total1+total2+rounding zaklad,
                            tax1+tax2 dph,
                            total0+total1+total2+rounding+tax1+tax2-total to_ma_byt_nula,
                            m_type_id,
                            d1,d2,d3,
                            number,
                            m.text1,
                            m.text2,
                            tax1,tax2,

                            (select currency from m_detail where move_id=m.move_id limit 1) MENA,
                            (select shipment from minfo where move_id=m.move_id limit 1) sposob_prepravy
                            from
                            movement m join address a on m.adr_id=a.adr_id 
                            where
                            /***********  Edituj tieto datumy a adresu *************/
                            d1 between :p1 and :p2
                            and m_type_id in (xxparamxx)
                            order by
                                 d1 desc",
                'l1' => 'Date from',
                'l2' => 'Date to',
                'xxlabxx' => 'M Types',
                'p1' => THISYEAR . '-01-01',
                'p2' => THISYEAR . '-12-31',
                'xxparamxx' => '24,102,110,112,114,69,109',
            ],

        ],

        'peter' => [

            'whole buying by kod' => [
                'q' => PETERSELECT1,
                'l1' => 'Date from',
                'l2' => 'Date to',
                'l3' => 'Kod like',
                'xxlabxx' => 'M Types',
                'p1' => THISYEAR . '-01-01',
                'p2' => THISYEAR . '-12-31',
                'p3' => '%',
                'xxparamxx' => '87,88,97,104',

            ],

            'Komis' => [
                'q' => PETERSELECT1,
                'l1' => 'Date from',
                'l2' => 'Date to',
                'l3' => 'Kod like',
                'xxlabxx' => 'M Types',
                'p1' => THISYEAR . '-01-01',
                'p2' => THISYEAR . '-12-31',
                'p3' => '%',
                'xxparamxx' => '87,88,24,64,137,139,110,130,145,146,147, 97,104,138,140,69,109,131,148,101,167,169,168,170',

            ],
            'Stats TH' => [
                'q' => PETERSELECT1,
                'l1' => 'Date from',
                'l2' => 'Date to',
                'l3' => 'Kod like',
                'xxlabxx' => 'M Types',
                'p1' => THISYEAR . '-01-01',
                'p2' => THISYEAR . '-12-31',
                'p3' => '%',
                'xxparamxx' => '110,24,130,118,64, 20,69,109,101,131, 145,146',
            ],

            'vedu' => [
                'q' => "SELECT 
                    a.adr_id,firma, d.move_id, m_type_id, m.number, d1,  d.mat_id,kod,  p.p0, p.p1,d.currency, model,
                    case when m.stock_id1 is not null  then sum(-pcs) else sum(pcs) end as xxpcs,
                    d.price as selprice, p.price, p.price*(case when m.stock_id1 is not null  then sum(-pcs) else sum(pcs) end) as price_total,
                    round(d.price*(case when m.stock_id1 is not null  then sum(-pcs) else sum(pcs) end)-d.price * (case when m.stock_id1 is not null  then sum(-pcs) else sum(pcs) end) * d.discount/100 ,2) as seltotal,
                    text1, text2,stock_id1
                    FROM
                    address a, m_detail d, movement m, product p 
                    WHERE
                    a.adr_id=m.adr_id and d.move_id = m.move_id
                    and d.mat_id = p.mat_id and
                    /* PARAMETER DATUM */
                    d1 between :p1 and :p2 
                    /* PARAMETER M_TYPE */
                    and m_type_id in (xxparamxx)
                    /* PARAMETER MODEL */
                    and p.kod ilike :p3
                    GROUP BY
                    d.mat_id,firma, d.move_id, m_type_id, d1, d2, d3, kod,p.price, p.p0, p.p1, model,a.adr_id,
                    d.currency,m.number,
                    d.price, m.m_type_id, d.tax, p.price , d.discount, text1, text2, m.stock_id1
                    ORDER BY
                    kod asc, model asc",
                'l1' => 'Date from',
                'l2' => 'Date to',
                'l3' => 'Kod like',
                'xxlabxx' => 'M Types',
                'p1' => THISYEAR . '-01-01',
                'p2' => THISYEAR . '-12-31',
                'p3' => 'OMEG%',
                'xxparamxx' => '87,88,97,104,109,69,97,104,101,102,110,64,24,137,138,139,140,167,169,168,170',
            ],

        ], //peter

        'andrej' => [

            'AT customers by Months' => [
                'q' => "
                    SELECT 
                    d.detail_info,
                    a.adr_id,firma, d.move_id, m_type_id, m.number, d1, d2, d3, d.mat_id,kod, p.price, p.p0, d.currency, model,
                    case when m.stock_id1 is not null  then sum(-pcs) else sum(pcs) end as xxpcs,
                    d.price as selprice, p.price, p.price*(case when m.stock_id1 is not null  then sum(-pcs) else sum(pcs) end) as price_total,
                     round(d.price*(case when m.stock_id1 is not null  then sum(-pcs) else sum(pcs) end)-d.price * (case when m.stock_id1 is not null  then sum(-pcs) else sum(pcs) end) * d.discount/100 ,2) as seltotal,
                     
                    text1, text2,stock_id1
                    FROM
                    address a, m_detail d, movement m, product p 
                    WHERE
                    a.adr_id=m.adr_id and d.move_id = m.move_id
                    and d.mat_id = p.mat_id and
                    /* PARAMETER DATUM */
                    d1 between :p1 and :p2 
                    /* PARAMETER M_TYPE */
                    and m_type_id in (xxparamxx)
                    /* PARAMETER MODEL */
                    and p.kod ilike :p3
                    GROUP BY
                    d.mat_id,firma, d.move_id, m_type_id, d1, d2, d3, kod,p.price, p.p0, model,a.adr_id,
                    d.currency,m.number,
                    d.price, m.m_type_id, d.tax, p.price , d.discount, text1, text2, m.stock_id1, d.detail_info
                    ORDER BY
                    kod asc, model asc",
                'l1' => 'Date from',
                'l2' => 'Date to',
                'l3' => 'Kod like',
                'xxlabxx' => 'M Types',
                'p1' => THISYEAR . '-01-01',
                'p2' => THISYEAR . '-12-31',
                'p3' => '%',
                'xxparamxx' => '69,101,110,64,131,130,145,146,66',
            ],
        ], //andrej
        'gabor o' => [

            'x sales' => [
                'q' => "

                SELECT firma, m_type_id, d1, kod, model, 
                (select thserial from thserials where mat_id=m_detail.mat_id and move_id=m_detail.move_id limit 1) firstserial,
                 case when movement.stock_id1 is not null  then sum(-pcs) else sum(pcs) end as Qty, m_detail.price as selprice, sum(m_detail.price*pcs-m_detail.price * pcs* m_detail.discount/100)::numeric(11,2) as seltotal, stock_id1, stock_id2, text1, text2 from address, m_detail, movement, product where address.adr_id=movement.adr_id and m_detail.move_id = movement.move_id and m_detail.mat_id = product.mat_id and d1 between :p1 and :p2 and kod like :p3 and m_type_id in (xxparamxx) group by firma, m_type_id, d1, kod, m_detail.move_id,m_detail.mat_id, model, product.price, p2i, p3,p4, m_detail.price, movement.m_type_id, stock_id1, stock_id2, text1, text2 order by kod asc, model asc

            ",
                'l1' => 'Date from',
                'l2' => 'Date to',
                'l3' => 'Kod like',
                'xxlabxx' => 'M Types',
                'p1' => THISYEAR . '-01-01',
                'p2' => THISYEAR . '-12-31',
                'p3' => 'TUD%',
                'xxparamxx' => '5,56,30,99',

            ]

        ], //orosz
        'OG kk' => [

            '30,99,504,505 report' => [
                'q' => "
            SELECT  firma, m_type_id, d1, kod, model,
                case when movement.m_type_id in (99, 505) then sum(-pcs) else sum(pcs) end as Qty,
                p3, p4, m_detail.price as selprice,
                 sum(m_detail.price*pcs-m_detail.price * pcs* m_detail.discount/100) as seltotal,
                 stock_id1, stock_id2, text1, text2
                from address, m_detail, movement, product where address.adr_id=movement.adr_id and m_detail.move_id = movement.move_id and m_detail.mat_id = product.mat_id and
                d1 between  :p1 and :p2
                and m_type_id in (30,99, 504,505)  group by firma, m_type_id, d1, kod, model, product.price, p9, p3,p4, m_detail.price, movement.m_type_id, stock_id1, stock_id2, text1, text2
                order by kod asc, model asc
            ",
                'l1' => 'Date from',
                'l2' => 'Date to',
                'p1' => THISYEAR . '-01-01',
                'p2' => THISYEAR . '-12-31',

            ]

        ], //ordered report by janos

        'milos w' => [

            'check document header tax' => [
                'q' => "select 
total0+total1+total2 -
( select sum(pcs*(price*(1-discount/100))) from m_detail where move_id=m.move_id) as inDetail,
 * from movement m where abs(total0+total1+total2 -
( select sum(pcs*(price*(1-discount/100))) from m_detail where move_id=m.move_id)) > 0.01 and year(d1) = :p1",
                'l1' => 'Year',
                'p1' => THISYEAR,

            ],
            'special sales by kod' => [
                'q' => "

                SELECT a.adr_id,firma,d1, model, case when m.stock_id1 is not null  then sum(-pcs) else sum(pcs) end as xxpcs,
                    sum(d.pcs) over (partition by firma,m_type_id) as sumPcsByAdrMtype,
                     kod, m_type_id, p.p0, d.price as selprice, d.mat_id, d.currency, d.detail_info, text1, text2,(select stock_id||' '||sdescr from stock_detail where stock_id=stock_id1) fromstock FROM address a, m_detail d, movement m, product p WHERE a.adr_id=m.adr_id and d.move_id = m.move_id  and d.mat_id = p.mat_id and /* PARAMETER DATUM */ d1 between :p1 and :p2 /* PARAMETER M_TYPE */ and m_type_id in (xxparamxx) /* PARAMETER MODEL */ and p.kod similar to :p3 GROUP BY d.pcs,d.mat_id,firma, d.move_id, m_type_id, d1,kod, p.p0, model,a.adr_id, d.currency,m.number, m.m_type_id, d.tax, d.price, d.discount, text1, text2, m.stock_id1, d.detail_info ORDER BY kod asc, model asc",
                'l1' => 'Date from',
                'l2' => 'Date to',
                'l3' => 'Kod similar to',
                'xxlabxx' => 'M Types',
                'p1' => THISYEAR . '-01-01',
                'p2' => THISYEAR . '-12-31',
                'p3' => '%(SEID|FCAS|GANT|BMN)%',
                'xxparamxx' => '87,88,97,104,64,110,24,69,101,14',

            ],
            'special sales by shop' => [
                'q' => "

                SELECT a.adr_id,firma,d1, model, case when m.stock_id1 is not null  then sum(-pcs) else sum(pcs) end as xxpcs,
                    sum(d.pcs) over (partition by firma,m_type_id) as sumPcsByAdrMtype, kod, m_type_id, p.p0, d.price as selprice, d.mat_id, d.currency, d.detail_info, text1, text2,(select stock_id||' '||sdescr from stock_detail where stock_id=stock_id1) fromstock FROM address a, m_detail d, movement m, product p WHERE a.adr_id=m.adr_id and d.move_id = m.move_id  and d.mat_id = p.mat_id and /* PARAMETER DATUM */ d1 between :p1 and :p2 /* PARAMETER M_TYPE */ and m_type_id in (xxparamxx) /* PARAMETER MODEL */ and p.kod similar to :p3 GROUP BY d.pcs,d.mat_id,firma, d.move_id, m_type_id, d1,kod, p.p0, model,a.adr_id, d.currency,m.number, m.m_type_id, d.tax, d.price, d.discount, text1, text2, m.stock_id1, d.detail_info ORDER BY stock_id1 asc, kod, model asc",
                'l1' => 'Date from',
                'l2' => 'Date to',
                'l3' => 'Kod similar to',
                'xxlabxx' => 'M Types',
                'p1' => THISYEAR . '-01-01',
                'p2' => THISYEAR . '-12-31',
                'p3' => '%(SEID|FCAS|GANT|BMN)%',
                'xxparamxx' => '87,88,97,104,64,110,24,69,101,14',

            ],
            'stats by mat_id' => [
                'q' => "

                    select *,
                        sum(price_sold - price_purchased) over(order by rn) as profit 
                    from
                        (
                            select 
                                row_number() over(order by d1, mat_id) as rn,
                                mat_id, move_id,d1, moving, pcs, xprice as price_purchased
                            from fifoview, generate_series(1, abs(pcs))
                            where moving = 'R' and mat_id=:p1
                        ) p
                        full join
                        (
                            select
                                row_number() over(order by d1, mat_id) as rn,
                                mat_id, move_id, d1, moving, pcs, xprice as price_sold
                            from fifoview, generate_series(1, abs(pcs))
                            where moving = 'S' and mat_id=:p1
                        ) s using (rn, mat_id)
                    ",
                'l1' => 'mat_id',
                'p1' => '128388',

            ],


            'sel moves' => [
                'q' => "
                select adr_id,firma, move_id, m_type_id, \"number\", d1, d2, d3, mat_id,kod, prod_price, discount,p0,p1,p2,p6,tax,currency, model, xxpcs, selprice,selprice*xxpcs as price_total, round(selprice*xxpcs-selprice * xxpcs * discount/100 ,2) as seltotal, text1, text2,stock_id1,emp_id, detail_info
                    ,i.Iname,	i.Isurname,	i.Istreet,	i.Icity,	i.Icountry,	i.Icompany,	i.Iico,	i.Idic,	i.Iicdph,	i.Shipment,	i.Branch,	i.Voucher,	i.Payment,	i.Incoterms

                    from ( SELECT d.detail_info, a.adr_id,firma, d.move_id, m_type_id, m.number, d1, d2, d3, d.mat_id,kod, p.price as prod_price, d.discount,p.p0,p.p1,p.p2,p.p6,d.tax,d.currency, model, case when m.stock_id1 is not null then sum(-pcs) else sum(pcs) end as xxpcs, d.price as selprice, text1, text2,stock_id1, emp_id 
                    ,i.Iname,	i.Isurname,	i.Istreet,	i.Icity,	i.Icountry,	i.Icompany,	i.Iico,	i.Idic,	i.Iicdph,	i.Shipment,	i.Branch,	i.Voucher,	i.Payment,	i.Incoterms
        FROM  movement m full join address a on a.adr_id=m.adr_id  full join m_detail d on d.move_Id=m.move_id full join  product p on p.mat_id=d.mat_id full join minfo i on  i.move_id=d.move_id 
            WHERE 
                                /* PARAMETER DATUM */
                     
                    d1 between :p1 and :p2
                     
                    /* PARAMETER M_TYPE */
                     
                    and m_type_id in (xxparamxx)
                     
                    /* PARAMETER MODEL */
                     
                    and p.kod similar to :p3

                     
                    GROUP BY
            d.mat_id,firma, d.move_id, m_type_id, d1, d2, d3, kod,p.price, p.p0, model,a.adr_id, d.currency,m.number, m.emp_id, d.price, m.m_type_id, d.tax, p.price ,p.p1,p.p2,p.p6, d.discount, text1, text2, m.stock_id1, d.detail_info
            ,i.Iname,	i.Isurname,	i.Istreet,	i.Icity,	i.Icountry,	i.Icompany,	i.Iico,	i.Idic,	i.Iicdph,	i.Shipment,	i.Branch,	i.Voucher,	i.Payment,	i.Incoterms
                ) as i ORDER BY kod asc, model asc;
                    ",
                'l1' => 'Date from',
                'l2' => 'Date to',
                'l3' => 'Kod similar to',
                'xxlabxx' => 'M Types',
                'p1' => THISYEAR . '-01-01',
                'p2' => THISYEAR . '-12-31',
                'p3' => '%',
                'xxparamxx' => '69,97,101,104,131,146,132,133,24,64,87,88,110,130,145,66,109,71,98,100,105,138,140,35,36,83,90,91,94,95,132,137,139,65,103,102,14,20,167,169,168,170',

            ],

        ], //milos w

        'zuzana' => [

            'Stock Prijem Vydaj by KOD' => [
                'q' => "select *,
            sum(pcstotal) OVER ( order by rok,mesiac,m_type_id )  zostatok
                from (
            select year(d1) rok,month(d1) mesiac,sum(case when m.stock_id2=:p2 then d.pcs else -d.pcs end ) pcstotal,sum( case when m.stock_id2=:p2 then d.pcs*d.price else -d.pcs*d.price end) total, 

            p.kod,m.m_type_id,t.name from movement m join m_detail d on m.move_id=d.move_id join product p on p.mat_id=d.mat_id join m_type t on m.m_type_id=t.m_type_id where (m.stock_id2 = :p2 or m.stock_id1 = :p2 ) and p.kod ilike :p1 group by year(d1),month(d1), p.kod, m.m_type_id,t.name order by rok,mesiac,m.m_type_id,kod ) s
        ",
                'l1' => 'KOD',
                'l2' => 'StockId',
                'p1' => '%',
                'p2' => 'WAUP',
            ],

            'Incoming to stock' => [
                'q' => "select sum(d.pcs) pcstotal,sum(d.pcs*d.price) total,d.mat_id,p.kod,p.model,m.m_type_id from movement m join m_detail d on m.move_id=d.move_id join product p on p.mat_id=d.mat_id where year(d1)=:p1 and month(d1)=:p2 and stock_id2 = :p3 group by d.mat_id,p.model,p.kod,m.m_type_id order by m_type_id,kod,model",
                'l1' => 'Year',
                'l2' => 'Month',
                'l3' => 'StockId',
                'p1' => '2020',
                'p2' => '1',
                'p3' => 'WAUP',
            ],
            'Incoming to stock total' => [
                'q' => "select sum(d.pcs) pcstotal,sum(d.pcs*d.price) total,p.kod,m.m_type_id from movement m join m_detail d on m.move_id=d.move_id join product p on p.mat_id=d.mat_id where year(d1)=:p1 and month(d1)=:p2 and stock_id2 = :p3 group by p.kod,m.m_type_id order by m_type_id,kod",
                'l1' => 'Year',
                'l2' => 'Month',
                'l3' => 'StockId',
                'p1' => '2020',
                'p2' => '1',
                'p3' => 'WAUP',
            ],
            'Out from stock' => [
                'q' => "select sum(d.pcs) pcstotal,sum(d.pcs*d.price) total,d.mat_id,p.kod,p.model,m.m_type_id from movement m join m_detail d on m.move_id=d.move_id join product p on p.mat_id=d.mat_id where year(d1)=:p1 and month(d1)=:p2 and stock_id1 = :p3 group by d.mat_id,p.model,p.kod,m.m_type_id order by m_type_id,kod,model",
                'l1' => 'Year',
                'l2' => 'Month',
                'l3' => 'StockId',
                'p1' => '2020',
                'p2' => '1',
                'p3' => 'WAUP',
            ],
            'Out from stock total' => [
                'q' => "select sum(d.pcs) pcstotal,sum(d.pcs*d.price) total,p.kod,m.m_type_id from movement m join m_detail d on m.move_id=d.move_id join product p on p.mat_id=d.mat_id where year(d1)=:p1 and month(d1)=:p2 and stock_id1 = :p3 group by p.kod,m.m_type_id order by m_type_id,kod",
                'l1' => 'Year',
                'l2' => 'Month',
                'l3' => 'StockId',
                'p1' => '2020',
                'p2' => '1',
                'p3' => 'WAUP',
            ],

        ], //zuzana


        'novakova' => [
            'intrastat1' => [
                'q' => "SELECT 
                    a.adr_id,
                    a.firma,
                    i.iicdph,
                    m.m_type_id,
                    d1,
                    d.mat_id,
                    p.kod,
                    p.model,
                    CASE WHEN m.stock_id1 IS NOT NULL THEN SUM(-pcs) ELSE SUM(pcs) END AS xxpcs,
                    ROUND(d.price * (CASE WHEN m.stock_id1 IS NOT NULL THEN SUM(-pcs) ELSE SUM(pcs) END) - d.price * (CASE WHEN m.stock_id1 IS NOT NULL THEN SUM(-pcs) ELSE SUM(pcs) END) * d.discount/100, 2) AS seltotal,
                    d.currency,
                    m.text1,
                    m.text2,
                    p.hs_code,
                    p.coo,
                    i.incoterms
                FROM
                    address a
                    JOIN movement m ON a.adr_id = m.adr_id
                    JOIN m_detail d ON d.move_id = m.move_id
                    JOIN product p ON d.mat_id = p.mat_id
                    FULL JOIN minfo i ON i.move_id = m.move_id
                WHERE
                    m.m_type_id in (xxparamxx)
                    AND d1 BETWEEN :p1 AND :p2
                GROUP BY
                    a.adr_id, a.firma, i.iicdph, m.m_type_id, d1, d.mat_id, p.kod, p.model, d.price, d.discount, d.currency, m.text1, m.text2, p.hs_code, p.coo, m.stock_id1, i.incoterms
                ORDER BY
                    d1, a.adr_id, p.kod",
                'l1' => 'Date from',
                'l2' => 'Date to',
                'xxlabxx' => 'M Type ID',
                'p1' => THISYEAR . '-01-01',
                'p2' => THISYEAR . '-12-31',
                'xxparamxx' => '20',
            ],
            'intrastat2' => [
                'q' => "SELECT 
                    a.adr_id,
                    a.firma,
                    i.iicdph,
                    m.m_type_id,
                    d1,
                    d.mat_id,
                    p.kod,
                    p.model,
                    CASE WHEN m.stock_id1 IS NOT NULL THEN SUM(-pcs) ELSE SUM(pcs) END AS xxpcs,
                    ROUND(d.price * (CASE WHEN m.stock_id1 IS NOT NULL THEN SUM(-pcs) ELSE SUM(pcs) END) - d.price * (CASE WHEN m.stock_id1 IS NOT NULL THEN SUM(-pcs) ELSE SUM(pcs) END) * d.discount/100, 2) AS seltotal,
                    d.currency,
                    m.text1,
                    m.text2,
                    p.hs_code,
                    p.coo,
                    i.incoterms
                FROM
                    address a
                    JOIN movement m ON a.adr_id = m.adr_id
                    JOIN m_detail d ON d.move_id = m.move_id
                    JOIN product p ON d.mat_id = p.mat_id
                    FULL JOIN minfo i ON i.move_id = m.move_id
                WHERE
                    m.m_type_id IN (xxparamxx)
                    AND d1 BETWEEN :p1 AND :p2
                GROUP BY
                    a.adr_id, a.firma, i.iicdph, m.m_type_id, d1, d.mat_id, p.kod, p.model, d.price, d.discount, d.currency, m.text1, m.text2, p.hs_code, p.coo, m.stock_id1, i.incoterms
                ORDER BY
                    d1, a.adr_id, p.kod",
                'l1' => 'Date from',
                'l2' => 'Date to',
                'xxlabxx' => 'M Type ID',
                'p1' => THISYEAR . '-01-01',
                'p2' => THISYEAR . '-12-31',
                'xxparamxx' => '86,89,134,116,117,135',
            ],
        ], //novakova




    ];

    public static $selectsbyname = [
        'm.demko' => [
            'wdl klub by KOD' =>
            [
                'q' => "select 
                Id,Firstname,   Lastname,Email_Address,Street_Address,Postcode,City,State,Telephone,Card_Nr,Issue_D,Card_Type,Card_Discount,Card_Memo,
                (select sum(pcs) from wow_sales s join product p on p.mat_id=s.mat_id where cust_id=c.id and kod ilike :p1) pcs,
                (select sum(pcs*sale_price) from wow_sales s join product p on p.mat_id=s.mat_id where cust_id=c.id and kod ilike :p1) totaleur
                from customers c where id in (
                    select cust_id from wow_sales s join product p on p.mat_id=s.mat_id where p.kod ilike :p1  group by s.cust_id,p.kod
                    )
                    order by id desc",
                'l1' => 'Kod like',
                'p1' => 'WTH%',

            ],
            'predaj Eshop' => [
                'q' => "
                select  Firma, Emp_Id, m_type_Id,  m.Number, d1, kod,  p.mat_id , model, 
                case when m.m_type_id in (138,140, 148,168,170) then -pcs else pcs end case
                from movement m join m_detail d on m.move_id=d.move_id join product p on p.mat_id=d.mat_id
                join address a on a.adr_id=m.adr_id
                where d1 between :p1 and :p2
                and m_type_id in (137,139,147,149,167,169,138,140, 148,168,170) 
                ",
                'l1' => 'Date from',
                'l2' => 'Date to',
                'p1' => THISYEAR . '-01-01',
                'p2' => THISYEAR . '-12-31',

            ],
            'predaj predajne' => [
                'q' => "
                select  Firma, m_type_Id, d1, kod,  p.mat_id , model, 
                case when m.m_type_id in (97) then -pcs else pcs end case
                from movement m join m_detail d on m.move_id=d.move_id join product p on p.mat_id=d.mat_id
                join address a on a.adr_id=m.adr_id
                where d1 between :p1 and :p2
                and m_type_id in (87,97) 
                ",
                'l1' => 'Date from',
                'l2' => 'Date to',
                'p1' => THISYEAR . '-01-01',
                'p2' => THISYEAR . '-12-31',
            ]

        ], // martin d

    ];

    //
    // list of all m_types for user
    //
    // public function getMymtypes() {
    //     $mt = "";
    //     foreach (\app\controllers\EstockTools::getUserMtypes() as $key => $value) {
    //         if( !empty($mt)){
    //             $mt .= ",";
    //         }
    //         $mt .= $value['m_type_id'];
    //     }

    //     return $mt;
    // }

}
