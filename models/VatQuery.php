<?php

namespace app\models;

/**
 * This is the ActiveQuery class for [[Vat]].
 *
 * @see Vat
 */
class VatQuery extends \yii\db\ActiveQuery
{
    /*public function active()
    {
        return $this->andWhere('[[status]]=1');
    }*/

    /**
     * {@inheritdoc}
     * @return Vat[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * {@inheritdoc}
     * @return Vat|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
