<?php

namespace app\models;

/**
 * This is the ActiveQuery class for [[PriceDetail]].
 *
 * @see PriceDetail
 */
class PriceDetailQuery extends \yii\db\ActiveQuery
{
    /*public function active()
    {
        return $this->andWhere('[[status]]=1');
    }*/

    /**
     * {@inheritdoc}
     * @return PriceDetail[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * {@inheritdoc}
     * @return PriceDetail|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
