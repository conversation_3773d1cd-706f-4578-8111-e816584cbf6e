<?php

namespace app\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\MDetail;

/**
 * app\models\MDetailSearch represents the model behind the search form about `app\models\MDetail`.
 */
 class MDetailSearch extends MDetail
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['move_id', 'mat_id', 'pcs', 'm_repli_id', 'p_repli_id', 'fifo_move_id', 'fifo_repli_id', 'all1'], 'integer'],
            [['price', 'discount', 'tax', 'fifo_price'], 'number'],
            [['currency', 'fifo_currency', 'detail_info'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = MDetail::find();

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        $query->andFilterWhere([
            'move_id' => $this->move_id,
            'mat_id' => $this->mat_id,
            'price' => $this->price,
            'pcs' => $this->pcs,
            'discount' => $this->discount,
            'tax' => $this->tax,
            'm_repli_id' => $this->m_repli_id,
            'p_repli_id' => $this->p_repli_id,
            'fifo_price' => $this->fifo_price,
            'fifo_move_id' => $this->fifo_move_id,
            'fifo_repli_id' => $this->fifo_repli_id,
            'all1' => $this->all1,
        ]);

        $query->andFilterWhere(['ilike', 'currency', $this->currency])
            ->andFilterWhere(['ilike', 'fifo_currency', $this->fifo_currency])
            ->andFilterWhere(['ilike', 'detail_info', $this->detail_info]);

        return $dataProvider;
    }
}
