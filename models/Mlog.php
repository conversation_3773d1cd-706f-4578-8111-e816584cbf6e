<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "mlog".
 *
 * @property int $id
 * @property string $created_at
 * @property int $move_id
 * @property string|null $flags
 * @property string|null $note
 * @property string|null $user_name
 * @property Movement $move
 */
class Mlog extends \yii\db\ActiveRecord
{

    const FLAG_ON_THE_WAY = 'OnTheWay';
    const FLAG_RECEIVED = 'Received';
    const FLAGS = [ 'Note' => 'Note', 'Tracking#' => 'Tracking#Url', 'Warning' => 'Warning', 'Error' => 'Error'];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'mlog';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['created_at'], 'safe'],
            [['move_id','note','flags'], 'required'],
            [['move_id'], 'default', 'value' => null],
            [['move_id'], 'integer'],
            [['flags', 'note', 'user_name'], 'string', 'max' => 255],
            [['move_id'], 'exist', 'skipOnError' => true, 'targetClass' => Movement::class, 'targetAttribute' => ['move_id' => 'move_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'created_at' => 'Created At',
            'move_id' => 'Move ID',
            'flags' => 'Flags',
            'note' => 'Note',
            'user_name' => 'User Name',
        ];
    }

    /**
     * Gets query for [[Move]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMove()
    {
        return $this->hasOne(Movement::class, ['move_id' => 'move_id']);
    }
}
