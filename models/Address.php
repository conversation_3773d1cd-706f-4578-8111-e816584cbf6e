<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "address".
 *
 * @property string $firma
 * @property string $street
 * @property string $city
 * @property string $zip
 * @property string $ico
 * @property string $drc1
 * @property string $drc2
 * @property string $account
 * @property string $bankcode
 * @property string $memo
 * @property int $adr_id
 * @property int $firma_id
 * @property string $info
 * @property string $credit_complet
 * @property string $discount
 * @property string $expire
 * @property string $akc_disc
 * @property string $currency
 * @property string $owner_name
 * @property int $area
 * @property string $iso
 *
 * @property AdrContact[] $adrContacts
 * @property Movement[] $movements
 */
class Address extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'address';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['firma_id', 'area' ], 'default', 'value' => null],
            [['firma_id', 'area'], 'integer'],
            [['credit_complet', 'discount', 'expire', 'akc_disc'], 'number'],
            [['firma', 'info'], 'string', 'max' => 255],
            [['street', 'city'], 'string', 'max' => 33],
            [['zip'], 'string', 'max' => 6],
            [['ico'], 'string', 'max' => 8],
            [['drc1'], 'string', 'max' => 16],
            [['drc2', 'currency'], 'string', 'max' => 16],
            [['account'], 'string', 'max' => 25],
            [['bankcode'], 'string', 'max' => 4],
            [['memo'], 'string', 'max' => 40],
            [['owner_name'], 'string', 'max' => 26],
            [['iso'], 'string', 'max' => 2],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'firma' => 'Firma',
            'street' => 'Street',
            'city' => 'City',
            'zip' => 'Zip',
            'ico' => 'Ico',
            'drc1' => 'Drc1: IČ DPH',
            'drc2' => 'Drc2: DIČ',
            'account' => 'Account',
            'bankcode' => 'Bankcode',
            'memo' => 'Memo',
            'adr_id' => 'Adr ID',
            'firma_id' => 'Firma ID',
            'info' => 'Info',
            'credit_complet' => 'Credit Complet',
            'discount' => 'Discount',
            'expire' => 'Expire',
            'akc_disc' => 'Akc Disc',
            'currency' => 'Currency',
            'owner_name' => 'Owner Name',
            'area' => 'Area',
            'iso' => 'Iso',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getAdrContacts()
    {
        return $this->hasMany(AdrContact::className(), ['adr_id' => 'adr_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getMovements()
    {
        return $this->hasMany(Movement::className(), ['adr_id' => 'adr_id']);
    }

    /**
    * {@inheritdoc}
    * @return AddressQuery the active query used by this AR class.
    */
   public static function find()
   {
       return new AddressQuery(get_called_class());
   }

}
