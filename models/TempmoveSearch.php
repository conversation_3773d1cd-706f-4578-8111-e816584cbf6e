<?php

namespace app\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\Tempmove;

/**
 * app\models\TempmoveSearch represents the model behind the search form about `app\models\Tempmove`.
 */
 class TempmoveSearch extends Tempmove
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['mat_id', 'clip_id', 'm_type_id', 'adr_id', 'fifo_move_id', 'nr_counter'], 'integer'],
            [['price', 'pcs', 'discount', 'tax', 'act_price', 'all1', 'all2', 'all3', 'all4', 'all5', 'fifo_price'], 'number'],
            [['currency', 'ean13', 'kod', 'model', 'pdescr', 'unit', 'user_name', 'd1', 'd2', 'd3', 'text1', 'text2', 'stock_id1', 'stock_id2', 'price_type', 'tempmove_info','thserial', 'fifo_currency'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = Tempmove::find();

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        $query->andFilterWhere([
            'mat_id' => $this->mat_id,
            'price' => $this->price,
            'pcs' => $this->pcs,
            'discount' => $this->discount,
            'tax' => $this->tax,
            'act_price' => $this->act_price,
            'all1' => $this->all1,
            'all2' => $this->all2,
            'all3' => $this->all3,
            'all4' => $this->all4,
            'all5' => $this->all5,
            'clip_id' => $this->clip_id,
            'd1' => $this->d1,
            'd2' => $this->d2,
            'd3' => $this->d3,
            'm_type_id' => $this->m_type_id,
            'adr_id' => $this->adr_id,
            'fifo_price' => $this->fifo_price,
            'fifo_move_id' => $this->fifo_move_id,
            'nr_counter' => $this->nr_counter,
            'thserial' => $this->thserial,
        ]);

        $query->andFilterWhere(['ilike', 'currency', $this->currency])
            ->andFilterWhere(['ilike', 'ean13', $this->ean13])
            ->andFilterWhere(['ilike', 'kod', $this->kod])
            ->andFilterWhere(['ilike', 'model', $this->model])
            ->andFilterWhere(['ilike', 'pdescr', $this->pdescr])
            ->andFilterWhere(['ilike', 'unit', $this->unit])
            ->andFilterWhere(['ilike', 'user_name', $this->user_name])
            ->andFilterWhere(['ilike', 'text1', $this->text1])
            ->andFilterWhere(['ilike', 'text2', $this->text2])
            ->andFilterWhere(['ilike', 'stock_id1', $this->stock_id1])
            ->andFilterWhere(['ilike', 'stock_id2', $this->stock_id2])
            ->andFilterWhere(['ilike', 'price_type', $this->price_type])
            ->andFilterWhere(['ilike', 'tempmove_info', $this->tempmove_info])
            ->andFilterWhere(['ilike', 'thserial', $this->thserial])
            ->andFilterWhere(['ilike', 'fifo_currency', $this->fifo_currency]);

        return $dataProvider;
    }
}
