<?php

namespace app\models;

use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\AdrContact;

/**
 * AdrcontactSearch represents the model behind the search form of `app\models\AdrContact`.
 */
class AdrcontactSearch extends AdrContact
{
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['adr_id', 'repli_id'], 'integer'],
            [['number', 'describe', 'person'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = AdrContact::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'adr_id' => $this->adr_id,
            'repli_id' => $this->repli_id,
        ]);

        $query->andFilterWhere(['ilike', 'number', $this->number])
            ->andFilterWhere(['ilike', 'describe', $this->describe])
            ->andFilterWhere(['ilike', 'person', $this->person]);

        return $dataProvider;
    }
}
