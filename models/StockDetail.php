<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "stock_detail".
 *
 * @property string $stock_id
 * @property string $sdescr
 * @property int $adr_id
 */
class StockDetail extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'stock_detail';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['stock_id', 'sdescr'], 'required'],
            [['adr_id'], 'default', 'value' => null],
            [['adr_id'], 'integer'],
            [['stock_id'], 'string', 'max' => 6],
            [['sdescr'], 'string', 'max' => 255],
            [['stock_id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'stock_id' => 'Stock ID',
            'sdescr' => 'Sdescr',
            'adr_id' => 'Adr ID',
        ];
    }
}
