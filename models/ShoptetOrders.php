<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "shoptet_orders".
 *
 * @property int $order_id
 * @property string|null $code
 * @property string|null $cdate
 * @property string|null $status
 * @property string|null $currency_code
 * @property float|null $currency_exchange_rate
 * @property string|null $customer_email
 * @property string|null $customer_phone
 * @property string|null $bill_name
 * @property string|null $bill_company
 * @property string|null $bill_street
 * @property string|null $bill_house_number
 * @property string|null $bill_city
 * @property string|null $bill_zip
 * @property string|null $bill_country
 * @property string|null $bill_company_id
 * @property string|null $bill_vat_id
 * @property string|null $customer_identification_number
 * @property string|null $delivery_name
 * @property string|null $delivery_company
 * @property string|null $delivery_street
 * @property string|null $delivery_house_number
 * @property string|null $delivery_city
 * @property string|null $delivery_zip
 * @property string|null $delivery_country
 * @property string|null $customer_ip_address
 * @property string|null $remark
 * @property string|null $shop_remark
 * @property string|null $referer
 * @property string|null $package_number
 * @property string|null $varchar1
 * @property string|null $varchar2
 * @property string|null $varchar3
 * @property string|null $text1
 * @property string|null $text2
 * @property string|null $text3
 * @property float|null $weight
 * @property float|null $total_price_with_vat
 * @property float|null $total_price_without_vat
 * @property float|null $total_price_vat
 * @property float|null $total_price_rounding
 * @property float|null $total_price_to_pay
 * @property bool|null $paid
 * @property int|null $movement_id
 *
 * @property ShoptetOrderItems[] $shoptetOrderItems
 */
class ShoptetOrders extends \yii\db\ActiveRecord
{


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'shoptet_orders';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['code', 'cdate', 'status', 'currency_code', 'currency_exchange_rate', 'customer_email', 'customer_phone', 'bill_name', 'bill_company', 'bill_street', 'bill_house_number', 'bill_city', 'bill_zip', 'bill_country', 'bill_company_id', 'bill_vat_id', 'customer_identification_number', 'delivery_name', 'delivery_company', 'delivery_street', 'delivery_house_number', 'delivery_city', 'delivery_zip', 'delivery_country', 'customer_ip_address', 'remark', 'shop_remark', 'referer', 'package_number', 'varchar1', 'varchar2', 'varchar3', 'text1', 'text2', 'text3', 'weight', 'total_price_with_vat', 'total_price_without_vat', 'total_price_vat', 'total_price_rounding', 'total_price_to_pay', 'paid', 'movement_id'], 'default', 'value' => null],
            [['cdate'], 'safe'],
            [['currency_exchange_rate', 'weight', 'total_price_with_vat', 'total_price_without_vat', 'total_price_vat', 'total_price_rounding', 'total_price_to_pay'], 'number'],
            [['remark', 'shop_remark', 'referer', 'text1', 'text2', 'text3'], 'string'],
            [['paid'], 'boolean'],
            [['movement_id'], 'default', 'value' => null],
            [['movement_id'], 'integer'],
            [['code', 'status', 'customer_phone', 'bill_house_number', 'bill_company_id', 'bill_vat_id', 'customer_identification_number', 'delivery_house_number', 'customer_ip_address', 'package_number'], 'string', 'max' => 50],
            [['currency_code'], 'string', 'max' => 10],
            [['customer_email', 'bill_name', 'bill_company', 'bill_street', 'delivery_name', 'delivery_company', 'delivery_street', 'varchar1', 'varchar2', 'varchar3'], 'string', 'max' => 255],
            [['bill_city', 'bill_country', 'delivery_city', 'delivery_country'], 'string', 'max' => 100],
            [['bill_zip', 'delivery_zip'], 'string', 'max' => 20],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'order_id' => 'Order ID',
            'code' => 'Code',
            'cdate' => 'Cdate',
            'status' => 'Status',
            'currency_code' => 'Currency Code',
            'currency_exchange_rate' => 'Currency Exchange Rate',
            'customer_email' => 'Customer Email',
            'customer_phone' => 'Customer Phone',
            'bill_name' => 'Bill Name',
            'bill_company' => 'Bill Company',
            'bill_street' => 'Bill Street',
            'bill_house_number' => 'Bill House Number',
            'bill_city' => 'Bill City',
            'bill_zip' => 'Bill Zip',
            'bill_country' => 'Bill Country',
            'bill_company_id' => 'Bill Company ID',
            'bill_vat_id' => 'Bill Vat ID',
            'customer_identification_number' => 'Customer Identification Number',
            'delivery_name' => 'Delivery Name',
            'delivery_company' => 'Delivery Company',
            'delivery_street' => 'Delivery Street',
            'delivery_house_number' => 'Delivery House Number',
            'delivery_city' => 'Delivery City',
            'delivery_zip' => 'Delivery Zip',
            'delivery_country' => 'Delivery Country',
            'customer_ip_address' => 'Customer Ip Address',
            'remark' => 'Remark',
            'shop_remark' => 'Shop Remark',
            'referer' => 'Referer',
            'package_number' => 'Package Number',
            'varchar1' => 'Varchar1',
            'varchar2' => 'Varchar2',
            'varchar3' => 'Varchar3',
            'text1' => 'Text1',
            'text2' => 'Text2',
            'text3' => 'Text3',
            'weight' => 'Weight',
            'total_price_with_vat' => 'Total Price With Vat',
            'total_price_without_vat' => 'Total Price Without Vat',
            'total_price_vat' => 'Total Price Vat',
            'total_price_rounding' => 'Total Price Rounding',
            'total_price_to_pay' => 'Total Price To Pay',
            'paid' => 'Paid',
            'movement_id' => 'Movement ID',
        ];
    }

    /**
     * Gets query for [[ShoptetOrderItems]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getShoptetOrderItems()
    {
        return $this->hasMany(ShoptetOrderItems::class, ['order_id' => 'order_id']);
    }

    /**
     * Create movement for order if not exists
     * 
     * returns mat_id of the new movement
     * @return int
     */
    public function createMovement()
    {
        $uname = 'eshopBot';
        $invoicemtype = 24; //faktura
        $ordermtypeid = 17; //faktura
        $fromstock = 'snik';
        $cdate = date("Y-m-d");
        $adrid = 3;
        $finalclip = 50;

        try {

        $stmt = Yii::$app->db->createCommand("DELETE FROM tempmove WHERE user_name='".$uname."' and clip_id = $finalclip")->query();

            foreach ($this->shoptetOrderItems as $d) {
                $pdescr = $d->ocode;
                $stmt = Yii::$app->db->createCommand("SELECT mat_id FROM product where pdescr='".$pdescr."' limit 1")->queryScalar();
                if( empty($stmt) ){
                    Yii::debug("Product not found: ".$pdescr);
                    Yii::$app->session->setFlash('error', 'Product not found: '.$pdescr);
                    return -1;                    
                } else {
                    $cdate = date("Y-m-d", strtotime($this->cdate));
                    
                    $query = "call pridajasuser(:adr_id, :mat_id, :clip_id, :pcs, :xx, :price, :d1, :d2, :d3, :m_type_id, :price2, :stock_id1,:stock_id2,:text1,:text2,:tempmove_info, :thserial, :user)";
                    $res = Yii::$app->db->createCommand($query)
                    ->bindValue(':adr_id', $adrid)
                    ->bindValue(':mat_id', $stmt)
                    ->bindValue(':clip_id', $finalclip)
                    ->bindValue(':pcs', $d->amount)
                    ->bindValue(':price', $d->unit_discount_price_with_vat)
                    ->bindValue(':xx', 'xx')
                    ->bindValue(':d1', $cdate)
                    ->bindValue(':d2', $cdate)
                    ->bindValue(':d3', $cdate)
                    ->bindValue(':m_type_id', $ordermtypeid)
                    ->bindValue(':price2', '0')
                    ->bindValue(':stock_id1', $fromstock)
                    ->bindValue(':stock_id2', null)
                    ->bindValue(':text1', null)
                    ->bindValue(':text2', null)
                    ->bindValue(':tempmove_info', '')
                    ->bindValue(':thserial', null )
                    ->bindValue(':user', $uname)
                    ->query();
                }
            }

            // set tax = 0
            $stmt = Yii::$app->db->createCommand("update tempmove set tax=0 where user_name='".$uname."' and clip_id = $finalclip")->query();

            // make tempmove simplified
            $stmt = Yii::$app->db->createCommand("select tempmove_by_mapping(:user,:clip_id)")->bindValue(':user', $uname)->bindValue(':clip_id', $finalclip)->queryScalar();
            Yii::$app->session->setFlash('success', 'Nr of mapping items used: '.$stmt);

            // set currency according to shoptet_orders currency_code
            $stmt = Yii::$app->db->createCommand("update tempmove set currency = '".$this->currency_code."' where user_name='".$uname."' and clip_id = $finalclip")->query();

            try {

                Yii::$app->db->createCommand("call newpaper("
                    .$invoicemtype.",'x','"
                    .$cdate."','"
                    .$cdate."','"
                    .$cdate."',"
                    ."0,'"
                    .($d->order_id ?? 'OrderNrUnknown' )."','"
                    ."created: ".( $cdate ?? "n/a")."',"
                    .$adrid.",'"
                    .$uname."',"
                    ."'".$fromstock."',"
                    ."'',"
                    ."'x'".",0,"
                    .$finalclip.")")->query();
                
                    //get the move_id of the new movement
                    $res = Yii::$app->db->createCommand("SELECT max(move_id) FROM movement WHERE emp_id='".$uname."' and m_type_id = $invoicemtype ")->queryScalar();
                    $this->movement_id = $res;
                    $this->save();
                    return $res;

            }  catch (\yii\db\Exception $e) {

                Yii::debug($e->getMessage());
                Yii::$app->session->setFlash('error', 'Database error: '.$e->getMessage());

                // Yii::$app->db->createCommand("call newpaper("
                // .$ordermtypeid.",'x','"
                // .$cdate."','"
                // .$cdate."','"
                // .$cdate."',"
                // ."0,'"
                // .($d->order_id ?? 'OrderNrUnknown' )."',:msg,"
                // .$adrid.",'"
                // .$uname."',"
                // ."'',"
                // ."'',"
                // ."'x'".",0,"
                // .$finalclip.")")
                // ->bindValue(':msg',"CREATE INVOICE FAILED!!! ".substr($e->getMessage(),43,190))
                // ->query();
          }

                return -1;



    } catch (\yii\db\Exception $e) {
        Yii::debug($e->getMessage());
        Yii::$app->session->setFlash('error', 'Database error: '.$e->getMessage());                
    }

}

}
