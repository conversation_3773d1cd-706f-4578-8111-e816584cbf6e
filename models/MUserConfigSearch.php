<?php

namespace app\models;

use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\MUserConfig;
use Yii;
/**
 * MUserConfigSearch represents the model behind the search form of `app\models\MUserConfig`.
 */
class MUserConfigSearch extends MUserConfig
{
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['id', 'userid', 'clip_id', 'adr_id', 'm_type_id', 'active'], 'integer'],
            [['db', 'pictures', 'price', 'stock_id1', 'stock_id2', 'text1', 'text2', 'd1', 'd2', 'd3'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = MUserConfig::find()->where(['userid'=>Yii::$app->user->id]);

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'userid' => $this->userid,
            'clip_id' => $this->clip_id,
            'adr_id' => $this->adr_id,
            'm_type_id' => $this->m_type_id,
            'd1' => $this->d1,
            'd2' => $this->d2,
            'd3' => $this->d3,
            'active' => $this->active,
        ]);

        $query->andFilterWhere(['ilike', 'db', $this->db])
            ->andFilterWhere(['ilike', 'pictures', $this->pictures])
            ->andFilterWhere(['ilike', 'price', $this->price])
            ->andFilterWhere(['ilike', 'stock_id1', $this->stock_id1])
            ->andFilterWhere(['ilike', 'stock_id2', $this->stock_id2])
            ->andFilterWhere(['ilike', 'text1', $this->text1])
            ->andFilterWhere(['ilike', 'text2', $this->text2]);

        return $dataProvider;
    }
}
