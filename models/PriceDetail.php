<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "price_detail".
 *
 * @property string $price_id
 * @property string $desc
 */
class PriceDetail extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'price_detail';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['price_id', 'desc'], 'required'],
            [['price_id'], 'string', 'max' => 3],
            [['desc'], 'string', 'max' => 255],
            [['price_id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'price_id' => 'Price ID',
            'desc' => 'Desc',
        ];
    }

    /**
     * {@inheritdoc}
     * @return PriceDetailQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new PriceDetailQuery(get_called_class());
    }
}
