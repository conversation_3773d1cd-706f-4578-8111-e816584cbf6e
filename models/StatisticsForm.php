<?php

namespace app\models;

use Yii;
use yii\base\Model;

/**
 * ContactForm is the model behind the contact form.
 */
class StatisticsForm extends Model
{
    public $d1;
    public $d2;
    public $repli;


    /**
     * @return array the validation rules.
     */
    public function rules()
    {
        return [
            // name, email, subject and body are required
            [['d1', 'd2', 'repli'], 'required'],
        ];
    }

    /**
     * @return array customized attribute labels
     */
    public function attributeLabels()
    {
        return [
            'd1' => 'Date from',
            'd2' => 'Date to'
        ];
    }

}
