<?php

namespace app\models;

use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\MType;

/**
 * MTypeSearch represents the model behind the search form of `app\models\MType`.
 */
class MTypeSearch extends MType
{
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['m_type_id', 'flag'], 'integer'],
            [['name', 'mdescr', 'doc_id', 'usergroup', 'moving', 'stock_id1', 'stock_id2', 'fifogroup', 'mdescr2', 'changeprice', 'taxable'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = MType::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'm_type_id' => $this->m_type_id,
            'flag' => $this->flag,
        ]);

        $query->andFilterWhere(['ilike', 'name', $this->name])
            ->andFilterWhere(['ilike', 'mdescr', $this->mdescr])
            ->andFilterWhere(['ilike', 'doc_id', $this->doc_id])
            ->andFilterWhere(['ilike', 'usergroup', $this->usergroup])
            ->andFilterWhere(['ilike', 'moving', $this->moving])
            ->andFilterWhere(['ilike', 'stock_id1', $this->stock_id1])
            ->andFilterWhere(['ilike', 'stock_id2', $this->stock_id2])
            ->andFilterWhere(['ilike', 'fifogroup', $this->fifogroup])
            ->andFilterWhere(['ilike', 'mdescr2', $this->mdescr2])
            ->andFilterWhere(['ilike', 'changeprice', $this->changeprice])
            ->andFilterWhere(['ilike', 'taxable', $this->taxable]);

        return $dataProvider;
    }
}
