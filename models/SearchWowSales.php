<?php

namespace app\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\WowSales;

/**
 * SearchWowSales represents the model behind the search form of `app\models\WowSales`.
 */
class SearchWowSales extends WowSales
{
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['orders_id', 'mat_id', 'pcs', 'cust_id'], 'integer'],
            [['sale_price', 'sale_spare'], 'number'],
            [['card_nr', 'sale_date', 'sale_type', 'sale_info'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = WowSales::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'orders_id' => $this->orders_id,
            'mat_id' => $this->mat_id,
            'pcs' => $this->pcs,
            'sale_price' => $this->sale_price,
            'sale_date' => $this->sale_date,
            'sale_spare' => $this->sale_spare,
            'cust_id' => $this->cust_id,
        ]);

        $query->andFilterWhere(['ilike', 'card_nr', $this->card_nr])
            ->andFilterWhere(['ilike', 'sale_type', $this->sale_type])
            ->andFilterWhere(['ilike', 'sale_info', $this->sale_info]);

        return $dataProvider;
    }
}
