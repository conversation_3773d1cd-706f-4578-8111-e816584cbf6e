<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "wow_cards".
 *
 * @property string $card_nr
 * @property string $parent_nr
 * @property string $issue_d
 * @property string $expire_d
 * @property int $cust_id
 * @property string $card_type
 * @property string $card_discount
 * @property string $cust_currency
 * @property string $card_memo
 */
class WowCards extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'wow_cards';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['issue_d', 'expire_d'], 'safe'],
            [['cust_id'], 'integer'],
            [['card_discount'], 'number'],
            [['card_nr', 'parent_nr'], 'string', 'max' => 32],
            [['card_type'], 'string', 'max' => 2],
            [['cust_currency'], 'string', 'max' => 3],
            [['card_memo'], 'string', 'max' => 255],
            [['parent_nr'], 'unique'],
            [['card_nr'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'card_nr' => 'Card Nr',
            'parent_nr' => 'Parent Nr',
            'issue_d' => 'Issue Date',
            'expire_d' => 'Expire Date',
            'cust_id' => 'Cust ID',
            'card_type' => 'Card Type',
            'card_discount' => 'Card Discount',
            'cust_currency' => 'Cust Currency',
            'card_memo' => 'Card Memo',
        ];
    }
}
