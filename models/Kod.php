<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "kod".
 *
 * @property string $kod
 * @property string|null $name0
 * @property string|null $name1
 * @property string|null $name2
 * @property string|null $name3
 * @property string|null $label1
 * @property string|null $name4
 * @property string|null $cur
 * @property int $cgroup
 *
 * @property Product[] $products
 */
class Kod extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'kod';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['kod'], 'required'],
            [['cgroup'], 'default', 'value' => 1],
            [['cgroup'], 'integer'],
            [['kod'], 'string', 'max' => 4],
            [['name0', 'name1', 'name2', 'name3', 'label1', 'name4'], 'string', 'max' => 60],
            [['cur'], 'string', 'max' => 3],
            [['kod'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'kod' => 'Kod',
            'name0' => 'Name0',
            'name1' => 'Name1',
            'name2' => 'Name2',
            'name3' => 'Name3',
            'label1' => 'Label1',
            'name4' => 'Name4',
            'cur' => 'Cur',
            'cgroup' => 'Cgroup',
        ];
    }

    /**
     * Gets query for [[Products]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getProducts()
    {
        return $this->hasMany(Product::class, ['kod' => 'kod']);
    }
}
