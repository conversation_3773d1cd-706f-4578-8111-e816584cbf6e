<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "product_disc".
 *
 * @property int $perc
 * @property int $cgroup
 * @property string $ctype
 * @property string|null $pcolumn
 */
class ProductDisc extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'product_disc';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['perc', 'cgroup'], 'default', 'value' => null],
            [['perc', 'cgroup'], 'integer'],
            [['cgroup', 'ctype'], 'required'],
            [['ctype'], 'string', 'max' => 1],
            [['cgroup', 'ctype'], 'unique', 'targetAttribute' => ['cgroup', 'ctype']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'perc' => 'Perc',
            'cgroup' => 'Cgroup',
            'ctype' => 'Ctype',
        ];
    }
}
