<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "vat".
 *
 * @property int $id
 * @property string $country iso code
 * @property string $countryname
 * @property int $perc
 * @property int $category 1 reduced 2 default 3 other vat rate
 * @property string $vatname
 */
class Vat extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'vat';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['country'], 'required'],
            [['perc', 'category'], 'default', 'value' => null],
            [['perc', 'category'], 'integer'],
            [['country', 'countryname', 'vatname'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'country' => 'Country',
            'countryname' => 'Countryname',
            'perc' => 'Perc',
            'category' => 'Category',
            'vatname' => 'Vatname',
        ];
    }

    /**
     * {@inheritdoc}
     * @return VatQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new VatQuery(get_called_class());
    }
}
