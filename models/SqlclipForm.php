<?php

namespace app\models;

use Yii;
use yii\base\Model;

/**
 * SQLclipForm is the model behind the form in clipboard for changing opened clipboard.
 */
class SqlclipForm extends Model
{
    public $sqlCommand;
    public $sqlWhere;
    public $isUpdate = "update";
    public $isErase = false;


    /**
     * @return array the validation rules.
     */
    public function rules()
    {
        return [
            
            [['sqlCommand', 'sqlWhere', 'isUpdate','isErase'], 'default'],
        ];
    }


    /**
     * @return array customized attribute labels
     */
    public function attributeLabels()
    {
        return [
            'sqlCommand' => 'SQL Command',
            'sqlWhere' => 'Where',
            'isUpdate' => 'Update',
            'isErase' => 'Erase all rows'
        ];
    }

}
