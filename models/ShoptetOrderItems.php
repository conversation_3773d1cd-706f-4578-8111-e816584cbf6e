<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "shoptet_order_items".
 *
 * @property int $item_id
 * @property int $order_id
 * @property string $otype
 * @property string $name
 * @property string $amount
 * @property string $ocode
 * @property string $variant_name
 * @property string $ean
 * @property string $plu
 * @property string $manufacturer
 * @property string $supplier
 * @property string $unit
 * @property string $weight
 * @property string $status
 * @property string $discount
 * @property string $unit_price_with_vat
 * @property string $unit_price_without_vat
 * @property string $unit_price_vat
 * @property string $unit_price_vat_rate
 * @property string $unit_discount_price_with_vat
 * @property string $unit_discount_price_without_vat
 * @property string $total_price_with_vat
 * @property string $total_price_without_vat
 * @property string $total_price_vat
 * @property string $total_price_vat_rate
 * @property string $surcharges
 *
 * @property ShoptetOrders $order
 */
class ShoptetOrderItems extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'shoptet_order_items';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['order_id'], 'default', 'value' => null],
            [['order_id'], 'integer'],
            [['amount', 'weight', 'discount', 'unit_price_with_vat', 'unit_price_without_vat', 'unit_price_vat', 'unit_price_vat_rate', 'unit_discount_price_with_vat', 'unit_discount_price_without_vat', 'total_price_with_vat', 'total_price_without_vat', 'total_price_vat', 'total_price_vat_rate'], 'number'],
            [['surcharges'], 'string'],
            [['otype', 'ocode', 'ean', 'plu', 'unit', 'status'], 'string', 'max' => 50],
            [['name'], 'string', 'max' => 255],
            [['variant_name', 'manufacturer', 'supplier'], 'string', 'max' => 100],
            [['order_id'], 'exist', 'skipOnError' => true, 'targetClass' => ShoptetOrders::className(), 'targetAttribute' => ['order_id' => 'order_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'item_id' => 'Item ID',
            'order_id' => 'Order ID',
            'otype' => 'Otype',
            'name' => 'Name',
            'amount' => 'Amount',
            'ocode' => 'Ocode',
            'variant_name' => 'Variant Name',
            'ean' => 'Ean',
            'plu' => 'Plu',
            'manufacturer' => 'Manufacturer',
            'supplier' => 'Supplier',
            'unit' => 'Unit',
            'weight' => 'Weight',
            'status' => 'Status',
            'discount' => 'Discount',
            'unit_price_with_vat' => 'Unit Price With Vat',
            'unit_price_without_vat' => 'Unit Price Without Vat',
            'unit_price_vat' => 'Unit Price Vat',
            'unit_price_vat_rate' => 'Unit Price Vat Rate',
            'unit_discount_price_with_vat' => 'Unit Discount Price With Vat',
            'unit_discount_price_without_vat' => 'Unit Discount Price Without Vat',
            'total_price_with_vat' => 'Total Price With Vat',
            'total_price_without_vat' => 'Total Price Without Vat',
            'total_price_vat' => 'Total Price Vat',
            'total_price_vat_rate' => 'Total Price Vat Rate',
            'surcharges' => 'Surcharges',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getOrder()
    {
        return $this->hasOne(ShoptetOrders::className(), ['order_id' => 'order_id']);
    }
}
