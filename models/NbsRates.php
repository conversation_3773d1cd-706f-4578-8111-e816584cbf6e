<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "nbs_rates".
 *
 * @property string $nbs_date
 * @property string $nbs_cur
 * @property string $nbs_rate
 * @property string $nbs_multi
 */
class NbsRates extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'nbs_rates';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['nbs_date', 'nbs_cur', 'nbs_rate', 'nbs_multi'], 'required'],
            [['nbs_date'], 'safe'],
            [['nbs_rate', 'nbs_multi'], 'number'],
            [['nbs_cur'], 'string', 'max' => 3],
            [['nbs_date', 'nbs_cur'], 'unique', 'targetAttribute' => ['nbs_date', 'nbs_cur']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'nbs_date' => 'Nbs Date',
            'nbs_cur' => 'Nbs Cur',
            'nbs_rate' => 'Nbs Rate',
            'nbs_multi' => 'Nbs Multi',
        ];
    }

    /**
     * {@inheritdoc}
     * @return NbsRatesQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new NbsRatesQuery(get_called_class());
    }
}
