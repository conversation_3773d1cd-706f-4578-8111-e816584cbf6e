<?php

namespace app\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\WowCards;

/**
 * WowCardsSearch represents the model behind the search form of `app\models\WowCards`.
 */
class WowCardsSearch extends WowCards
{
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['card_nr', 'parent_nr', 'issue_d', 'expire_d', 'card_type', 'cust_currency', 'card_memo'], 'safe'],
            [['cust_id'], 'integer'],
            [['card_discount'], 'number'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = WowCards::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'issue_d' => $this->issue_d,
            'expire_d' => $this->expire_d,
            'cust_id' => $this->cust_id,
            'card_discount' => $this->card_discount,
        ]);

        $query->andFilterWhere(['ilike', 'card_nr', $this->card_nr])
            ->andFilterWhere(['ilike', 'parent_nr', $this->parent_nr])
            ->andFilterWhere(['ilike', 'card_type', $this->card_type])
            ->andFilterWhere(['ilike', 'cust_currency', $this->cust_currency])
            ->andFilterWhere(['ilike', 'card_memo', $this->card_memo]);

        return $dataProvider;
    }
}
