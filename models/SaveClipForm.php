<?php

namespace app\models;

use Yii;
use yii\base\Model;

/**
 * SaveClipForm is the model behind the SaveClip form.
 *
 * @property User|null $user This property is read-only.
 *
 */
class SaveClipForm extends Model
{
    public $text1;
    public $text2;
    public $mtype;
    public $clip_id;
    public $d1;
    public $x;
    public $final_stock;
    public $adr_id;
    public $also_invoice;
    public $also_creditnote;


    /**
     * @return array the validation rules.
     */
    public function rules()
    {
        return [
            [['mtype','clip_id','d1'], 'required'],
            [['text1','text2','x','adr_id', 'final_stock', 'also_invoice', 'also_creditnote'],'default']
        ];
    }


    public function attributeLabels()
    {
        return [
            'text1' => "Please write text1 info about this document",
            'text2' => "... and text2 also",
	         'd1' => "Date",
             'x' => "DPH?",
             'adr_id' => "Address from",
             'final_stock' => "Final stock",
             'also_invoice' => "Urobiť aj faktúru pohyb " . (Yii::$app->user->identity->username === 'aupark' ? ' GW  182 ?' : ' SW 180 ?'),
             'also_creditnote' => "Urobiť aj dobropis pohyb " . (Yii::$app->user->identity->username === 'aupark' ? ' GW  183 ?' : ' SW 181 ?')
        ];
    }


}
