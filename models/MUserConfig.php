<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "m_user_config".
 *
 * @property int $id
 * @property int $userid
 * @property string $db
 * @property int $clip_id
 * @property string $pictures
 * @property string $price
 * @property int $adr_id
 * @property string $stock_id1
 * @property string $stock_id2
 * @property int $m_type_id
 * @property string $text1
 * @property string $text2
 * @property string $d1
 * @property string $d2
 * @property string $d3
 * @property int $active
 *
 * @property Address $adr
 * @property MType $mtype
 */
class MUserConfig extends \yii\db\ActiveRecord
{

    const SCENARIO_CREATE = 'create';
    const SCENARIO_UPDATE = 'update';
    const SCENARIO_DEFAULT = 'default';


    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        $scenarios = parent::scenarios();
        $scenarios[self::SCENARIO_CREATE] = $scenarios[self::SCENARIO_DEFAULT];
        $scenarios[self::SCENARIO_UPDATE] = $scenarios[self::SCENARIO_DEFAULT];
        return $scenarios;
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_user_config';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['userid', 'db', 'clip_id','price', 'adr_id', 'm_type_id'], 'required'],
            [['stock_id1','stock_id2'],  'checkStockId','on'=>self::SCENARIO_CREATE, 'skipOnEmpty' => false, 'skipOnError' => false ],
            [['userid', 'clip_id', 'adr_id', 'm_type_id', 'active'], 'default', 'value' => null],
            [['userid', 'clip_id', 'adr_id', 'm_type_id', 'active'], 'integer'],
            [['text1', 'text2'], 'string'],
            [['d1', 'd2', 'd3','stock_id1','stock_id2'], 'safe'],
            [['db'], 'string', 'max' => 255],
            [['pictures', 'stock_id1', 'stock_id2'], 'string', 'max' => 4],
            [['price'], 'string', 'max' => 5],
            [['userid', 'clip_id'], 'unique', 'targetAttribute' => ['userid', 'clip_id']],
            [['adr_id'], 'exist', 'skipOnError' => true, 'targetClass' => Address::className(), 'targetAttribute' => ['adr_id' => 'adr_id']],
        ];
    }



    /**
     * check if the stock_ids are OK
     */
    public function checkStockId($attribute, $params, $validator)
    {


        $moving = Yii::$app->db->createCommand("select moving from
                    m_type where m_type_id=:mid")->bindValue(':mid',$this->m_type_id)->queryScalar();

        switch($moving){
            case 'M':    
                if($this->stock_id1==$this->stock_id2)
                  $this->addError($attribute, 'stock_ids can not be the same');
                elseif(empty($this->$attribute))
                  $this->addError($attribute, 'stock_ids must be set');
                break;
            case 'S':
                if(empty($this->stock_id1))
                  $this->addError('stock_id1', 'stock_id1 must be set');
                break;
            case 'R':
                if(empty($this->stock_id2))
                  $this->addError('stock_id2', 'stock_id2 must be set');
                break;
            }
            
    }


    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'userid' => 'Userid',
            'db' => 'Db',
            'clip_id' => 'Clip ID',
            'pictures' => 'Pictures',
            'price' => 'Price',
            'adr_id' => 'Adr ID',
            'stock_id1' => 'From stock',
            'stock_id2' => 'To stock',
            'm_type_id' => 'M Type ID',
            'text1' => 'Text1',
            'text2' => 'Text2',
            'd1' => 'D1',
            'd2' => 'D2',
            'd3' => 'D3',
            'active' => 'Active',
        ];
    }

    //POZOR TOTO vyfiltruje vsetky akcie nad modelom, budeme vidiet len svoje a na specialnu "db"
    //TODO zmazat z modelu db 
    public function init() {
        parent::init();
        if ($this->scenario == $this::SCENARIO_CREATE ) {
            $this->userid = Yii::$app->user->identity->id;
            $this->db = "estock";
            $this->clip_id = Yii::$app->db->createCommand("SELECT coalesce(max(clip_id)+1,1) from m_user_config where userid=:userid")->bindValue(':userid',Yii::$app->user->identity->id)->queryScalar();
            $this->d1 = date("Y-m-d");
            $this->d2 = date("Y-m-d");
            $this->d3 = date("Y-m-d");
        }
    }


    /**
     * @return \yii\db\ActiveQuery
     */
    public function getAdr()
    {
        return $this->hasOne(Address::className(), ['adr_id' => 'adr_id']);
    }


    /**
     * @return \yii\db\ActiveQuery
     */
    public function getMtype()
    {
        return $this->hasOne(MType::className(), ['m_type_id' => 'm_type_id']);
    }


    /**
     * {@inheritdoc}
     * @return MUserConfigQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new MUserConfigQuery(get_called_class());
    }
}
