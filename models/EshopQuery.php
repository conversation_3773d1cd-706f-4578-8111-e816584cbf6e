<?php

namespace app\models;

/**
 * This is the ActiveQuery class for [[Eshop]].
 *
 * @see Eshop
 */
class EshopQuery extends \yii\db\ActiveQuery
{
    /*public function active()
    {
        return $this->andWhere('[[status]]=1');
    }*/

    /**
     * {@inheritdoc}
     * @return Eshop[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * {@inheritdoc}
     * @return Eshop|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
