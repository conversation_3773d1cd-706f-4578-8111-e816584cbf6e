<?php

namespace app\models;

use Yii;
use yii\base\Model;

/**
 * ImportForm is the model behind the import models in clipboard form.
 */
class MultiaddProductForm extends Model
{
    public $multiadd = "";
    public $multiaddLabel = "New products import from csv";
    public $multiaddPlaceholder = "kod,model,pdescr,price,p0,p1,p2,p6\nWATM,Example for Model,Can be also pdescr,11.00,22.10,33.20,5500000\nWATX,Another,Withpdescr,1.00,2,122,50000";
    public $multiaddSwitch = '0';


    /**
     * @return array the validation rules.
     */
    public function rules()
    {
        return [
            
            [['multiadd','multiaddPlaceholder','multiaddLabel'], 'default'],
        ];
    }

    /**
     * @return array customized attribute labels
     */
    public function attributeLabels()
    {
        return [
            'multiaddLabel' => "Multiadd",

        ];
    }

}
