<?php

namespace app\models;

use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\ShoptetOrders;

/**
 * ShoptetOrdersSearch represents the model behind the search form of `app\models\ShoptetOrders`.
 */
class ShoptetOrdersSearch extends ShoptetOrders
{
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['order_id'], 'integer'],
            [['code', 'cdate', 'status', 'currency_code', 'customer_email', 'customer_phone', 'bill_name', 'bill_company', 'bill_street', 'bill_house_number', 'bill_city', 'bill_zip', 'bill_country', 'bill_company_id', 'bill_vat_id', 'customer_identification_number', 'delivery_name', 'delivery_company', 'delivery_street', 'delivery_house_number', 'delivery_city', 'delivery_zip', 'delivery_country', 'customer_ip_address', 'remark', 'shop_remark', 'referer', 'package_number', 'varchar1', 'varchar2', 'varchar3', 'text1', 'text2', 'text3'], 'safe'],
            [['currency_exchange_rate', 'weight', 'total_price_with_vat', 'total_price_without_vat', 'total_price_vat', 'total_price_rounding', 'total_price_to_pay'], 'number'],
            [['paid'], 'boolean'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = ShoptetOrders::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'order_id' => $this->order_id,
            'cdate' => $this->cdate,
            'currency_exchange_rate' => $this->currency_exchange_rate,
            'weight' => $this->weight,
            'total_price_with_vat' => $this->total_price_with_vat,
            'total_price_without_vat' => $this->total_price_without_vat,
            'total_price_vat' => $this->total_price_vat,
            'total_price_rounding' => $this->total_price_rounding,
            'total_price_to_pay' => $this->total_price_to_pay,
            'paid' => $this->paid,
        ]);

        $query->andFilterWhere(['ilike', 'code', $this->code])
            ->andFilterWhere(['ilike', 'status', $this->status])
            ->andFilterWhere(['ilike', 'currency_code', $this->currency_code])
            ->andFilterWhere(['ilike', 'customer_email', $this->customer_email])
            ->andFilterWhere(['ilike', 'customer_phone', $this->customer_phone])
            ->andFilterWhere(['ilike', 'bill_name', $this->bill_name])
            ->andFilterWhere(['ilike', 'bill_company', $this->bill_company])
            ->andFilterWhere(['ilike', 'bill_street', $this->bill_street])
            ->andFilterWhere(['ilike', 'bill_house_number', $this->bill_house_number])
            ->andFilterWhere(['ilike', 'bill_city', $this->bill_city])
            ->andFilterWhere(['ilike', 'bill_zip', $this->bill_zip])
            ->andFilterWhere(['ilike', 'bill_country', $this->bill_country])
            ->andFilterWhere(['ilike', 'bill_company_id', $this->bill_company_id])
            ->andFilterWhere(['ilike', 'bill_vat_id', $this->bill_vat_id])
            ->andFilterWhere(['ilike', 'customer_identification_number', $this->customer_identification_number])
            ->andFilterWhere(['ilike', 'delivery_name', $this->delivery_name])
            ->andFilterWhere(['ilike', 'delivery_company', $this->delivery_company])
            ->andFilterWhere(['ilike', 'delivery_street', $this->delivery_street])
            ->andFilterWhere(['ilike', 'delivery_house_number', $this->delivery_house_number])
            ->andFilterWhere(['ilike', 'delivery_city', $this->delivery_city])
            ->andFilterWhere(['ilike', 'delivery_zip', $this->delivery_zip])
            ->andFilterWhere(['ilike', 'delivery_country', $this->delivery_country])
            ->andFilterWhere(['ilike', 'customer_ip_address', $this->customer_ip_address])
            ->andFilterWhere(['ilike', 'remark', $this->remark])
            ->andFilterWhere(['ilike', 'shop_remark', $this->shop_remark])
            ->andFilterWhere(['ilike', 'referer', $this->referer])
            ->andFilterWhere(['ilike', 'package_number', $this->package_number])
            ->andFilterWhere(['ilike', 'varchar1', $this->varchar1])
            ->andFilterWhere(['ilike', 'varchar2', $this->varchar2])
            ->andFilterWhere(['ilike', 'varchar3', $this->varchar3])
            ->andFilterWhere(['ilike', 'text1', $this->text1])
            ->andFilterWhere(['ilike', 'text2', $this->text2])
            ->andFilterWhere(['ilike', 'text3', $this->text3]);

        return $dataProvider;
    }
}
