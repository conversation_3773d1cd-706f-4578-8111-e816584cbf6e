<?php

namespace app\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
//use app\models\Product;
use app\models\ProductWithModel2;


/**
 * app\models\ProductSearch represents the model behind the search form about `app\models\Product`.
 */
class Product2Search extends ProductWithModel2
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['mat_id'], 'integer'],
            [['ean13', 'kod', 'model', 'pdescr', 'unit', 'model2', 'hs_code', 'coo'], 'safe'],
            [['price', 'p0', 'p1', 'p2', 'p3', 'p4', 'p5', 'p6', 'p8', 'p9', 'tax', 'min', 'max', 'p7', 'p0a', 'p0b', 'p0c', 'p0d', 'p0e', 'p0f', 'p0g', 'p0h', 'p0i', 'p0j', 'p1b', 'p1c', 'p1d', 'p1e', 'p1f', 'p1g', 'p1h', 'p1i', 'p1j', 'p2b', 'p2c', 'p2d', 'p2e', 'p2f', 'p2g', 'p2h', 'p2i', 'p2j', 'p1a', 'p2a'], 'number'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = ProductWithModel2::find();

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        $query->andFilterWhere([
            'mat_id' => $this->mat_id,
            'price' => $this->price,
            'p0' => $this->p0,
            'p1' => $this->p1,
            'p2' => $this->p2,
            'p3' => $this->p3,
            'p4' => $this->p4,
            'p5' => $this->p5,
            'p6' => $this->p6,
            'p8' => $this->p8,
            'p9' => $this->p9,
            'tax' => $this->tax,
            'min' => $this->min,
            'max' => $this->max,
            'p7' => $this->p7,
            'p0a' => $this->p0a,
            'p0b' => $this->p0b,
            'p0c' => $this->p0c,
            'p0d' => $this->p0d,
            'p0e' => $this->p0e,
            'p0f' => $this->p0f,
            'p0g' => $this->p0g,
            'p0h' => $this->p0h,
            'p0i' => $this->p0i,
            'p0j' => $this->p0j,
            'p1b' => $this->p1b,
            'p1c' => $this->p1c,
            'p1d' => $this->p1d,
            'p1e' => $this->p1e,
            'p1f' => $this->p1f,
            'p1g' => $this->p1g,
            'p1h' => $this->p1h,
            'p1i' => $this->p1i,
            'p1j' => $this->p1j,
            'p2b' => $this->p2b,
            'p2c' => $this->p2c,
            'p2d' => $this->p2d,
            'p2e' => $this->p2e,
            'p2f' => $this->p2f,
            'p2g' => $this->p2g,
            'p2h' => $this->p2h,
            'p2i' => $this->p2i,
            'p2j' => $this->p2j,
            'p1a' => $this->p1a,
            'p2a' => $this->p2a,
            'hs_code' => $this->hs_code,
            'coo' => strtoupper($this->coo ? $this->coo : ''),
        ]);

        $query->andFilterWhere(['ilike', 'ean13', $this->ean13 . '%', false])
            ->andFilterWhere(['ilike', 'kod', $this->kod . '%', false])
            ->andFilterWhere(['ilike', 'model', $this->model . '%', false])
            ->andFilterWhere(['ilike', 'pdescr', $this->pdescr . '%', false])
            ->andFilterWhere(['ilike', 'unit', $this->unit . '%', false])
            ->andFilterWhere(['ilike', 'model2', '%' . $this->model2 . '%', false]);

        return $dataProvider;
    }
}
