<?php

namespace app\models;

/**
 * This is the ActiveQuery class for [[MDetail]].
 *
 * @see MDetail
 */
class MDetailQuery extends \yii\db\ActiveQuery
{
    /*public function active()
    {
        return $this->andWhere('[[status]]=1');
    }*/

    /**
     * {@inheritdoc}
     * @return MDetail[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * {@inheritdoc}
     * @return MDetail|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
