<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "m_detail".
 *
 * @property int $move_id
 * @property int $mat_id
 * @property string $price
 * @property int $pcs
 * @property string $discount
 * @property string $tax
 * @property int $m_repli_id
 * @property string $currency
 * @property int $p_repli_id
 * @property string $fifo_currency
 * @property string $fifo_price
 * @property int $fifo_move_id
 * @property string $detail_info
 * @property int $fifo_repli_id
 * @property int $all1
 * @property Product $product
 * @property Movement $movement
 */
class MDetail extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_detail';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['move_id', 'mat_id', 'detail_info', 'fifo_repli_id'], 'required'],
            [['move_id', 'mat_id', 'pcs', 'm_repli_id', 'p_repli_id', 'fifo_move_id', 'fifo_repli_id', 'all1'], 'integer'],
            [['price', 'discount', 'tax', 'fifo_price'], 'number'],
            [['currency', 'fifo_currency'], 'string', 'max' => 3],
            [['detail_info'], 'string', 'max' => 255],
            [['mat_id'], 'unique'],
            [['move_id', 'mat_id', 'm_repli_id', 'fifo_move_id', 'detail_info', 'fifo_repli_id'], 'unique', 'targetAttribute' => ['move_id', 'mat_id', 'm_repli_id', 'fifo_move_id', 'detail_info', 'fifo_repli_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'move_id' => 'Move ID',
            'mat_id' => 'Mat ID',
            'price' => 'Price',
            'pcs' => 'Pcs',
            'discount' => 'Discount',
            'tax' => 'Tax',
            'm_repli_id' => 'M Repli ID',
            'currency' => 'Currency',
            'p_repli_id' => 'P Repli ID',
            'fifo_currency' => 'Fifo Currency',
            'fifo_price' => 'Fifo Price',
            'fifo_move_id' => 'Fifo Move ID',
            'detail_info' => 'Detail Info',
            'fifo_repli_id' => 'Fifo Repli ID',
            'all1' => 'All1',
        ];
    }


    /**
     * @return \yii\db\ActiveQuery
     */
    public function getMovement()
    {
        return $this->hasOne(Movement::className(), ['move_id' => 'move_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getProduct()
    {
        return $this->hasOne(Product::className(), ['mat_id' => 'mat_id']);
    }

    /**
     * {@inheritdoc}
     * @return MDetailQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new MDetailQuery(get_called_class());
    }
}
