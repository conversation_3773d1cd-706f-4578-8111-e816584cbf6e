<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "m_type".
 *
 * @property int $m_type_id
 * @property string $name nickname of movetype
 * @property string $mdescr more text if desired
 * @property string $doc_id which number of document it changes
 * @property int $flag for which programm code it is needed
 * @property string $usergroup who can handle this type of movement
 * @property string $moving <S>ell <R>eceive <M>ove <N>othing <X>special /pr. pokl. doklad
 * @property string $stock_id1 From/ To which stock - default value
 * @property string $stock_id2 If moving, into which stock
 * @property string $fifogroup <Y>es <N>o
 * @property string $mdescr2
 * @property string $changeprice
 * @property string $taxable
 *
 * @property Document $doc
 * @property Movement[] $movements
 */
class MType extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_type';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['flag'], 'default', 'value' => null],
            [['flag'], 'integer'],
            [['name', 'usergroup'], 'string', 'max' => 40],
            [['mdescr'], 'string', 'max' => 100],
            [['doc_id'], 'string', 'max' => 5],
            [['moving', 'changeprice', 'taxable'], 'string', 'max' => 1],
            [['stock_id1', 'stock_id2'], 'string', 'max' => 6],
            [['fifogroup'], 'string', 'max' => 2],
            [['mdescr2'], 'string', 'max' => 20],
            [['doc_id'], 'exist', 'skipOnError' => true, 'targetClass' => Document::class, 'targetAttribute' => ['doc_id' => 'doc_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'm_type_id' => 'M Type ID',
            'name' => 'Name',
            'mdescr' => 'Mdescr',
            'doc_id' => 'Doc ID',
            'flag' => 'Flag',
            'usergroup' => 'Usergroup',
            'moving' => 'Moving',
            'stock_id1' => 'Stock Id1',
            'stock_id2' => 'Stock Id2',
            'fifogroup' => 'Fifogroup',
            'mdescr2' => 'Mdescr2',
            'changeprice' => 'Changeprice',
            'taxable' => 'Taxable',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getDoc()
    {
        return $this->hasOne(Document::class, ['doc_id' => 'doc_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getMovements()
    {
        return $this->hasMany(Movement::class, ['m_type_id' => 'm_type_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public static function getMyMTypes($adr_id=-1)
    {
        $mytypes = Movement::find()->select('m_type_id')->where(['emp_id' => Yii::$app->user->identity->username ])->orWhere(['adr_id' => $adr_id])->distinct()->all();
        return MType::find()->where(['m_type_id' => $mytypes]);
    }
     
}
