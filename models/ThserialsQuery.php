<?php

namespace app\models;

/**
 * This is the ActiveQuery class for [[Thserials]].
 *
 * @see Thserials
 */
class ThserialsQuery extends \yii\db\ActiveQuery
{
    /*public function active()
    {
        return $this->andWhere('[[status]]=1');
    }*/

    /**
     * {@inheritdoc}
     * @return Thserials[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * {@inheritdoc}
     * @return Thserials|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
