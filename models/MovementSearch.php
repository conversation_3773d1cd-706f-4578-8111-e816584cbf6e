<?php

namespace app\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\Movement;

/**
 * app\models\MovementSearch represents the model behind the search form about `app\models\Movement`.
 */
 class MovementSearch extends Movement
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['move_id', 'adr_id', 'number', 'c_number', 'm_type_id', 'parent_move_id'], 'integer'],
            [['d1', 'd2', 'd3', 'doc_id', 'emp_id', 'payment', 'X', 'text1', 'text2', 'stock_id1', 'stock_id2'], 'safe'],
            [['total', 'total0', 'total1', 'total2', 'rounding', 'tax1', 'tax2', 'discount'], 'number'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = Movement::find();

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $dataProvider->sort->defaultOrder = ['move_id' => SORT_DESC];

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        $query->andFilterWhere([
            'move_id' => $this->move_id,
            'parent_move_id' => $this->parent_move_id,
            'adr_id' => $this->adr_id,
//            'd1' => $this->d1,
            // 'd2' => $this->d2,
            // 'd3' => $this->d3,
            'doc_id' => $this->doc_id,
            'number' => $this->number,
            'total' => $this->total,
            'c_number' => $this->c_number,
            'total0' => $this->total0,
            'total1' => $this->total1,
            'total2' => $this->total2,
            'rounding' => $this->rounding,
            'tax1' => $this->tax1,
            'tax2' => $this->tax2,
            'discount' => $this->discount,
            'm_type_id' => $this->m_type_id,
        ]);

        //$query->andFilterWhere(['ilike', 'doc_id', $this->doc_id])
            $query->andFilterWhere(['ilike', 'emp_id', $this->emp_id])
            ->andFilterWhere(['ilike', 'payment', $this->payment])
            ->andFilterWhere(['ilike', 'X', $this->X])
            ->andFilterWhere(['ilike', 'text1', $this->text1])
            ->andFilterWhere(['ilike', 'text2', $this->text2])
            ->andFilterWhere(['ilike', 'stock_id1', $this->stock_id1])
            ->andFilterWhere(['ilike', 'stock_id2', $this->stock_id2]);


            // if(isset ($this->d1)&&$this->d1!=''){ //you dont need the if function if yourse sure you have a not null date
            //   $date_explode=explode(" - ",$this->d1);
            //   $date1=trim($date_explode[0]);
            //   $date2=trim($date_explode[1]);
            //   $query->andFilterWhere(['between','d1',$date1,$date2]);
            // }

            if ( ! is_null($this->d1) && strpos($this->d1, ' - ') !== false ) {
                list($start_date, $end_date) = explode(' - ', $this->d1);
                $query->andFilterWhere(['between', 'd1', $start_date, $end_date]);
                $this->d1 = null;
            }
            if ( ! is_null($this->d2) && strpos($this->d2, ' - ') !== false ) {
                list($start_date, $end_date) = explode(' - ', $this->d2);
                $query->andFilterWhere(['between', 'd2', $start_date, $end_date]);
                $this->d2 = null;
            }
            if ( ! is_null($this->d3) && strpos($this->d3, ' - ') !== false ) {
                list($start_date, $end_date) = explode(' - ', $this->d3);
                $query->andFilterWhere(['between', 'd3', $start_date, $end_date]);
                $this->d3 = null;
            }

        return $dataProvider;
    }
}
