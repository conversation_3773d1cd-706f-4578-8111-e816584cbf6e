<?php

namespace app\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\Kod;

/**
 * app\models\KodSearch represents the model behind the search form about `app\models\Kod`.
 */
 class KodSearch extends Kod
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['kod', 'name0', 'name1', 'name2', 'name3', 'label1', 'name4', 'cur'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = Kod::find();

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        $query->andFilterWhere(['ilike', 'kod', $this->kod])
            ->andFilterWhere(['ilike', 'name0', $this->name0])
            ->andFilterWhere(['ilike', 'name1', $this->name1])
            ->andFilterWhere(['ilike', 'name2', $this->name2])
            ->andFilterWhere(['ilike', 'name3', $this->name3])
            ->andFilterWhere(['ilike', 'label1', $this->label1])
            ->andFilterWhere(['ilike', 'name4', $this->name4])
            ->andFilterWhere(['ilike', 'cur', $this->cur]);

        return $dataProvider;
    }
}
