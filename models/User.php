<?php

namespace app\models;
use Yii;

use yii\db\ActiveRecord;
use yii\web\IdentityInterface;

class User extends ActiveRecord implements IdentityInterface
{
    public static function tableName()
    {
        return 'wuser';
    }

        /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['username', 'repli'], 'required'],
            [['repli'], 'integer'],
            [['id','email','status','profile','repli','password_hash','password_reset_token','banned_at'],'default'],
            [['username'], 'string', 'max' => 20],
            [['all_flags'], 'string', 'max' => 255],
//            [['authentik_id'], 'string', 'max' => 64],
            [['username'], 'unique'],
        ];
    }

    /**
     * Finds an identity by the given ID.
     *
     * @param string|int $id the ID to be looked for
     * @return IdentityInterface|null the identity object that matches the given ID.
     */
    public static function findIdentity($id)
    {
        return static::findOne($id);
    }

    /**
     * Finds an identity by the given token.
     *
     * @param string $token the token to be looked for
     * @return IdentityInterface|null the identity object that matches the given token.
     */
    public static function findIdentityByAccessToken($token, $type = null)
    {
        return static::findOne(['access_token' => $token]);
    }

    /**
     * @return int|string current user ID
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @return string current user auth key
     */
    public function getAuthKey()
    {
        return $this->auth_key;
    }


    /**
     * @param string $authKey
     * @return bool if auth key is valid for current user
     */
    public function validateAuthKey($authKey)
    {
        return $this->getAuthKey() === $authKey;
    }


    /**
     * @param string $authKey
     * @return bool if auth key is valid for current user
     */
    public function validatePassword($password)
    {
        //return Yii::$app->security->validatePassword($password,$this->password);
        return $this->password_hash === md5($password) && !isset($this->banned_at);
    }

    public function beforeSave($insert)
    {
        if (parent::beforeSave($insert)) {
            if ($this->isNewRecord) {
                $this->auth_key = \Yii::$app->security->generateRandomString();
            }
            return true;
        }
        return false;
    }


    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'username' => 'Username for login',
            'repli' => 'office repli (0, 1, 2)',
            'status' => 'Send login info to user\'s email?',
            'profile' => 'Profile for b2b user',
            'banned_at' => 'Banned since (if not set, user is active)',
        ];
    }
}
