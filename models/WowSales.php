<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "wow_sales".
 *
 * @property int $orders_id
 * @property int $mat_id
 * @property int $pcs
 * @property string $sale_price
 * @property string $card_nr
 * @property string $sale_date
 * @property string $sale_spare
 * @property string $sale_type
 * @property string $sale_info
 * @property int $cust_id
 *
 * @property Customers $cust
 */
class WowSales extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'wow_sales';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['orders_id', 'mat_id', 'pcs', 'cust_id'], 'integer'],
            [['sale_price','mat_id', 'pcs'], 'required'],
            [['sale_price', 'sale_spare'], 'number'],
            [['sale_date'], 'safe'],
            [['card_nr'], 'string', 'max' => 32],
            [['sale_type'], 'string', 'max' => 2],
            [['sale_info'], 'string', 'max' => 255],
            [['orders_id', 'card_nr', 'mat_id'], 'unique', 'targetAttribute' => ['orders_id', 'card_nr', 'mat_id']],
            [['cust_id'], 'exist', 'skipOnError' => true, 'targetClass' => Customers::className(), 'targetAttribute' => ['cust_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'orders_id' => 'ESHOP Order ID',
            'mat_id' => 'Mat ID',
            'pcs' => 'Pcs',
            'sale_price' => 'Sale Price',
            'card_nr' => 'Card Nr',
            'sale_date' => 'Sale Date',
            'sale_spare' => 'Sale Spare',
            'sale_type' => 'Sale Type',
            'sale_info' => 'Sale Info',
            'cust_id' => 'Cust ID',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getCust()
    {
        return $this->hasOne(Customers::className(), ['id' => 'cust_id']);
    }


    /**
     * @return \yii\db\ActiveQuery
     */
    public function getProduct()
    {
        return $this->hasOne(Product::className(), ['mat_id' => 'mat_id']);
    }


    public function beforeSave($insert) {

            if ($insert) {

                $this->sale_date = new \yii\db\Expression('NOW()');

            }

            return parent::beforeSave($insert);

        }




}
