<?php

namespace app\models;

use Yii;
use yii\base\Model;

/**
 * ContactForm is the model behind the contact form.
 */
class UpdateUctoMovementForm extends Model
{
    public $multiadd = "";
    public $multiaddLabel = "Zobraz zmeny MAT_ID riadky a prikazy";
    public $multiaddPlaceholder = "new mat_id,move_id,old mat_id,price, detail_info (price or price,detail_info is optional)";


    /**
     * @return array the validation rules.
     */
    public function rules()
    {
        return [
            
            [['multiadd','multiaddPlaceholder','multiaddLabel'], 'default'],
        ];
    }

    /**
     * @return array customized attribute labels
     */
    public function attributeLabels()
    {
        return [
            'multiaddLabel' => "Multi update",

        ];
    }

}
