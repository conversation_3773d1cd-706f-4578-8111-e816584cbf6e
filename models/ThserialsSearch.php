<?php

namespace app\models;

use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\Thserials;

/**
 * ThserialsSearch represents the model behind the search form of `app\models\Thserials`.
 */
class ThserialsSearch extends Thserials
{
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['id', 'mat_id', 'move_id'], 'integer'],
            [['thserial'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = Thserials::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'mat_id' => $this->mat_id,
            'move_id' => $this->move_id,
        ]);

        $query->andFilterWhere(['ilike', 'thserial', $this->thserial]);

        return $dataProvider;
    }
}
