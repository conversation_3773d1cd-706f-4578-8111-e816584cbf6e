<?php

namespace app\models;
use yii;

/**
 * This is the ActiveQuery class for [[Tempmove]].
 *
 * @see Tempmove
 */
class TempmoveQuery extends \yii\db\ActiveQuery
{
    public function my()
    {
        return $this->andWhere(['user_name' => Yii::$app->user->identity->username]);
    }

    /**
     * {@inheritdoc}
     * @return Tempmove[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * {@inheritdoc}
     * @return Tempmove|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
