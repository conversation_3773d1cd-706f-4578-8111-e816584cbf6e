<?php

namespace app\models;

use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\Address;

/**
 * AddressSearch represents the model behind the search form of `app\models\Address`.
 */
class AddressSearch extends Address
{
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['firma', 'street', 'city', 'zip', 'ico', 'drc1', 'drc2', 'account', 'bankcode', 'memo', 'info', 'currency', 'owner_name', 'iso'], 'safe'],
            [['adr_id', 'firma_id', 'area'], 'integer'],
            [['credit_complet', 'discount', 'expire', 'akc_disc'], 'number'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = Address::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'adr_id' => $this->adr_id,
            'firma_id' => $this->firma_id,
            'credit_complet' => $this->credit_complet,
            'discount' => $this->discount,
            'expire' => $this->expire,
            'akc_disc' => $this->akc_disc,
            'area' => $this->area,
        ]);

        $query->andFilterWhere(['ilike', 'firma', $this->firma])
            ->andFilterWhere(['ilike', 'street', $this->street])
            ->andFilterWhere(['ilike', 'city', $this->city])
            ->andFilterWhere(['ilike', 'zip', $this->zip])
            ->andFilterWhere(['ilike', 'ico', $this->ico])
            ->andFilterWhere(['ilike', 'drc1', $this->drc1])
            ->andFilterWhere(['ilike', 'drc2', $this->drc2])
            ->andFilterWhere(['ilike', 'account', $this->account])
            ->andFilterWhere(['ilike', 'bankcode', $this->bankcode])
            ->andFilterWhere(['ilike', 'memo', $this->memo])
            ->andFilterWhere(['ilike', 'info', $this->info])
            ->andFilterWhere(['ilike', 'currency', $this->currency])
            ->andFilterWhere(['ilike', 'owner_name', $this->owner_name])
            ->andFilterWhere(['ilike', 'iso', $this->iso]);

        return $dataProvider;
    }
}
