<?php

namespace app\models;

use Yii;
use yii\base\Model;

/**
 * ContactForm is the model behind the contact form.
 */
class SqlForm extends Model
{
    public $sqllabel;
    public $param1;
    public $param2;
    public $param3;
    public $xxparamxx;
    public $sql = "N/A";
    public $label1 = "N/A";
    public $label2 = "N/A";
    public $label3 = "N/A";
    public $xxlabxx = "N/A";



    /**
     * @return array the validation rules.
     */
    public function rules()
    {
        return [
            
            [['sqllabel', 'param1', 'param2','param3','sql','xxparamxx'], 'default'],
        ];
    }

    /**
     * @return array customized attribute labels
     */
    public function attributeLabels()
    {
        return [
            'param1' => $this->label1,
            'param2' => $this->label2,
            'param3' => $this->label3,
            'xxparamxx' => $this->xxlabxx,
            'sql' => "Any select",

        ];
    }

}
