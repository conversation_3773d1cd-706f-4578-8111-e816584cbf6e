<?php

namespace app\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\Mdetailprod;

/**
 * app\models\MdetailprodSearch represents the model behind the search form about `app\models\Mdetailprod`.
 */
 class MdetailprodSearch extends Mdetailprod
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['move_id', 'repli_id', 'mat_id', 'pcs'], 'integer'],
            [['kod', 'model', 'currency', 'detail_info'], 'safe'],
            [['price', 'discount', 'tax'], 'number'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = Mdetailprod::find();

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        $query->andFilterWhere([
            'move_id' => $this->move_id,
            'repli_id' => $this->repli_id,
            'mat_id' => $this->mat_id,
            'price' => $this->price,
            'pcs' => $this->pcs,
            'discount' => $this->discount,
            'tax' => $this->tax,
        ]);

        $query->andFilterWhere(['ilike', 'kod', $this->kod.'%', false])
            ->andFilterWhere(['ilike', 'model', $this->model.'%', false])
            ->andFilterWhere(['ilike', 'currency', $this->currency.'%', false])
            ->andFilterWhere(['ilike', 'detail_info', $this->detail_info]);

        return $dataProvider;
    }
}
