<?php

namespace app\models;

use Yii;
use yii\base\Model;

/**
 * ContactForm is the model behind the contact form.
 */
class MultiaddForm extends Model
{
    public $multiadd = "";
    public $multiaddLabel = "Multiadd";
    public $multiaddPlaceholder = "mat_id or ean13 or model, pcs, price, info";
    public $multiaddSwitch = '0';


    /**
     * @return array the validation rules.
     */
    public function rules()
    {
        return [
            //multiadd is required
            [['multiadd'], 'required'],
            [['multiaddPlaceholder','multiaddLabel', 'multiaddSwitch'], 'default'],
        ];
    }

    /**
     * @return array customized attribute labels
     */
    public function attributeLabels()
    {
        return [
            'multiaddLabel' => "Multiadd",
            'multiaddSwitch' => 'Analyse first column: ',

        ];
    }

}
