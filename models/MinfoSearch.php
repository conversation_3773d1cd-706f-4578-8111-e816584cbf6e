<?php

namespace app\models;

use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\Minfo;

/**
 * MinfoSearch represents the model behind the search form of `app\models\Minfo`.
 */
class MinfoSearch extends Minfo
{
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['id', 'move_id', 'orderid'], 'integer'],
            [['status', 'email', 'iname', 'isurname', 'istreet', 'icity', 'izip', 'iphone', 'icountry', 'icompany', 'iico', 'idic', 'iicdph', 'dcompany', 'dname', 'dsurname', 'dstreet', 'dcity', 'dzip', 'dphone', 'dcountry', 'mnote', 'shipment', 'branch', 'voucher', 'payment'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = Minfo::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'move_id' => $this->move_id,
            'orderid' => $this->orderid,
        ]);

        $query->andFilterWhere(['ilike', 'status', $this->status])
            ->andFilterWhere(['ilike', 'email', $this->email])
            ->andFilterWhere(['ilike', 'iname', $this->iname])
            ->andFilterWhere(['ilike', 'isurname', $this->isurname])
            ->andFilterWhere(['ilike', 'istreet', $this->istreet])
            ->andFilterWhere(['ilike', 'icity', $this->icity])
            ->andFilterWhere(['ilike', 'izip', $this->izip])
            ->andFilterWhere(['ilike', 'iphone', $this->iphone])
            ->andFilterWhere(['ilike', 'icountry', $this->icountry])
            ->andFilterWhere(['ilike', 'icompany', $this->icompany])
            ->andFilterWhere(['ilike', 'iico', $this->iico])
            ->andFilterWhere(['ilike', 'idic', $this->idic])
            ->andFilterWhere(['ilike', 'iicdph', $this->iicdph])
            ->andFilterWhere(['ilike', 'dcompany', $this->dcompany])
            ->andFilterWhere(['ilike', 'dname', $this->dname])
            ->andFilterWhere(['ilike', 'dsurname', $this->dsurname])
            ->andFilterWhere(['ilike', 'dstreet', $this->dstreet])
            ->andFilterWhere(['ilike', 'dcity', $this->dcity])
            ->andFilterWhere(['ilike', 'dzip', $this->dzip])
            ->andFilterWhere(['ilike', 'dphone', $this->dphone])
            ->andFilterWhere(['ilike', 'dcountry', $this->dcountry])
            ->andFilterWhere(['ilike', 'mnote', $this->mnote])
            ->andFilterWhere(['ilike', 'shipment', $this->shipment])
            ->andFilterWhere(['ilike', 'branch', $this->branch])
            ->andFilterWhere(['ilike', 'voucher', $this->voucher])
            ->andFilterWhere(['ilike', 'payment', $this->payment]);

        return $dataProvider;
    }
}
