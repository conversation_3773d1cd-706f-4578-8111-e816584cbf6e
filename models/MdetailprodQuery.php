<?php

namespace app\models;

/**
 * This is the ActiveQuery class for [[Mdetailprod]].
 *
 * @see Mdetailprod
 */
class MdetailprodQuery extends \yii\db\ActiveQuery
{
    /*public function active()
    {
        return $this->andWhere('[[status]]=1');
    }*/

    /**
     * {@inheritdoc}
     * @return Mdetailprod[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * {@inheritdoc}
     * @return Mdetailprod|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
