<?php

namespace app\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\Customers;

/**
 * CustomersSearch represents the model behind the search form of `app\models\Customers`.
 */
class CustomersSearch extends Customers
{
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['id', 'country_id', 'zone_id'], 'integer'],
            [['gender', 'firstname', 'lastname', 'dob', 'email_address', 'street_address', 'suburb', 'postcode', 'city', 'state', 'telephone', 'fax', 'password', 'nick', 'card_nr', 'issue_d', 'expire_d', 'card_type', 'cust_currency', 'card_memo'], 'safe'],
            [['card_discount'], 'number'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = Customers::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'country_id' => $this->country_id,
            'zone_id' => $this->zone_id,
            'issue_d' => $this->issue_d,
            'expire_d' => $this->expire_d,
            'card_discount' => $this->card_discount,
        ]);

        $query->andFilterWhere(['ilike', 'gender', $this->gender])
            ->andFilterWhere(['ilike', 'firstname', $this->firstname])
            ->andFilterWhere(['ilike', 'lastname', $this->lastname])
            ->andFilterWhere(['ilike', 'dob', $this->dob])
            ->andFilterWhere(['ilike', 'email_address', $this->email_address])
            ->andFilterWhere(['ilike', 'street_address', $this->street_address])
            ->andFilterWhere(['ilike', 'suburb', $this->suburb])
            ->andFilterWhere(['ilike', 'postcode', $this->postcode])
            ->andFilterWhere(['ilike', 'city', $this->city])
            ->andFilterWhere(['ilike', 'state', $this->state])
            ->andFilterWhere(['ilike', 'telephone', $this->telephone])
            ->andFilterWhere(['ilike', 'fax', $this->fax])
            ->andFilterWhere(['ilike', 'password', $this->password])
            ->andFilterWhere(['ilike', 'nick', $this->nick])
            ->andFilterWhere(['ilike', 'card_nr', $this->card_nr])
            ->andFilterWhere(['ilike', 'card_type', $this->card_type])
            ->andFilterWhere(['ilike', 'cust_currency', $this->cust_currency])
            ->andFilterWhere(['ilike', 'card_memo', $this->card_memo]);

        return $dataProvider;
    }
}
