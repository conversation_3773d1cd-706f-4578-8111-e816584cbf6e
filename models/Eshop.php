<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "eshop".
 *
 * @property int $id
 * @property string $status
 * @property int $orderid
 * @property int $order
 * @property string $created
 * @property string $email
 * @property string $iname
 * @property string $isurname
 * @property string $istreet
 * @property string $icity
 * @property string $izip
 * @property string $iphone
 * @property string $icountry
 * @property string $icompany
 * @property string $iico
 * @property string $idic
 * @property string $dcompany
 * @property string $dname
 * @property string $dsurname
 * @property string $dstreet
 * @property string $dcity
 * @property string $dzip
 * @property string $dphone
 * @property string $dcountry
 * @property string $gcompany
 * @property string $gname
 * @property string $gsurname
 * @property string $gstreet
 * @property string $gcity
 * @property string $gzip
 * @property string $gphone
 * @property string $gcountry
 * @property string $gift
 * @property string $note
 * @property string $shipment
 * @property string $branch
 * @property string $voucher
 * @property string $payment
 * @property string $totalitems
 * @property string $totalitemsnovat
 * @property string $totalshipment
 * @property string $totalpayment
 * @property string $totalclub
 * @property string $total
 * @property string $currency
 * @property string $products
 * @property string $alljson
 *
 * @property Movement $movement
 */
class Eshop extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'eshop';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['orderid'], 'required'],
            [['orderid', 'order'], 'default', 'value' => null],
            [['orderid', 'order'], 'integer'],
            [['created'], 'safe'],
            [['totalitems', 'totalitemsnovat', 'totalshipment', 'totalpayment', 'totalclub', 'total'], 'number'],
            [['products', 'alljson'], 'string'],
            [['status'], 'string', 'max' => 4],
            [['email', 'iname', 'isurname', 'istreet', 'icity', 'izip', 'iphone', 'icountry', 'icompany', 'iico', 'idic', 'dcompany', 'dname', 'dsurname', 'dstreet', 'dcity', 'dzip', 'dphone', 'dcountry', 'gcompany', 'gname', 'gsurname', 'gstreet', 'gcity', 'gzip', 'gphone', 'gcountry', 'note', 'shipment', 'branch', 'voucher', 'payment'], 'string', 'max' => 255],
            [['gift'], 'string', 'max' => 1],
            [['currency'], 'string', 'max' => 11],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'status' => 'Status',
            'orderid' => 'Orderid',
            'order' => 'Order',
            'created' => 'Created',
            'email' => 'Email',
            'iname' => 'Iname',
            'isurname' => 'Isurname',
            'istreet' => 'Istreet',
            'icity' => 'Icity',
            'izip' => 'Izip',
            'iphone' => 'Iphone',
            'icountry' => 'Icountry',
            'icompany' => 'Icompany',
            'iico' => 'Iico',
            'idic' => 'Idic',
            'dcompany' => 'Dcompany',
            'dname' => 'Dname',
            'dsurname' => 'Dsurname',
            'dstreet' => 'Dstreet',
            'dcity' => 'Dcity',
            'dzip' => 'Dzip',
            'dphone' => 'Dphone',
            'dcountry' => 'Dcountry',
            'gcompany' => 'Gcompany',
            'gname' => 'Gname',
            'gsurname' => 'Gsurname',
            'gstreet' => 'Gstreet',
            'gcity' => 'Gcity',
            'gzip' => 'Gzip',
            'gphone' => 'Gphone',
            'gcountry' => 'Gcountry',
            'gift' => 'Gift',
            'note' => 'Note',
            'shipment' => 'Shipment',
            'branch' => 'Branch',
            'voucher' => 'Voucher',
            'payment' => 'Payment',
            'totalitems' => 'Totalitems',
            'totalitemsnovat' => 'Totalitemsnovat',
            'totalshipment' => 'Totalshipment',
            'totalpayment' => 'Totalpayment',
            'totalclub' => 'Totalclub',
            'total' => 'Total',
            'currency' => 'Currency',
            'products' => 'Products',
            'alljson' => 'Alljson',
        ];
    }

    /**
     * {@inheritdoc}
     * @return EshopQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new EshopQuery(get_called_class());
    }


    /**
     * @return \yii\db\ActiveQuery
     */
    public function getMovement()
    {
        return $this->hasOne(Movement::className(), ['c_number' => 'id']);
    }

}
