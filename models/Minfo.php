<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "minfo".
 *
 * @property int $id
 * @property int $move_id
 * @property string $status
 * @property int $orderid
 * @property string $email
 * @property string $iname
 * @property string $isurname
 * @property string $istreet
 * @property string $icity
 * @property string $izip
 * @property string $iphone
 * @property string $icountry
 * @property string $icompany
 * @property string $iico
 * @property string $idic
 * @property string $iicdph
 * @property string $dcompany
 * @property string $dname
 * @property string $dsurname
 * @property string $dstreet
 * @property string $dcity
 * @property string $dzip
 * @property string $dphone
 * @property string $dcountry
 * @property string $mnote
 * @property string $shipment
 * @property string $branch
 * @property string $voucher
 * @property string $payment
 * @property string $incoterms
 */
class Minfo extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'minfo';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['move_id'], 'required'],
            [['move_id', 'orderid'], 'default', 'value' => null],
            [['move_id', 'orderid'], 'integer'],
            [['mnote'], 'string'],
            [['status'], 'string', 'max' => 4],
            [['email', 'iname', 'isurname', 'istreet', 'icity', 'izip', 'iphone', 'icountry', 'icompany', 'iico', 'idic', 'iicdph', 'dcompany', 'dname', 'dsurname', 'dstreet', 'dcity', 'dzip', 'dphone', 'dcountry', 'shipment', 'branch', 'voucher', 'payment', 'incoterms'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'move_id' => 'Move ID',
            'status' => 'Status',
            'orderid' => 'Orderid',
            'email' => 'Email',
            'iname' => 'Iname',
            'isurname' => 'Isurname',
            'istreet' => 'Istreet',
            'icity' => 'Icity',
            'izip' => 'Izip',
            'iphone' => 'Iphone',
            'icountry' => 'Icountry',
            'icompany' => 'Icompany',
            'iico' => 'Iico',
            'idic' => 'Idic',
            'iicdph' => 'Iicdph',
            'dcompany' => 'Dcompany',
            'dname' => 'Dname',
            'dsurname' => 'Dsurname',
            'dstreet' => 'Dstreet',
            'dcity' => 'Dcity',
            'dzip' => 'Dzip',
            'dphone' => 'Dphone',
            'dcountry' => 'Dcountry',
            'mnote' => 'Mnote',
            'shipment' => 'Shipment',
            'branch' => 'Branch',
            'voucher' => 'Voucher',
            'payment' => 'Payment',
            'incoterms' => 'Incoterms',
        ];
    }
}
