<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "m_detail_prod".
 *
 * @property int $move_id
 * @property int $repli_id
 * @property int $mat_id
 * @property string $kod
 * @property string $model
 * @property string $price
 * @property int $pcs
 * @property string $discount
 * @property string $tax
 * @property string $currency
 * @property string $detail_info
 * @property Movement $movement
 * @property Mlog $mlog
 * @property Thserials[] $thserials
 * @property Firstthserial $firstthserial
 */
class Mdetailprod extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_detail_prod';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['move_id', 'repli_id', 'mat_id', 'kod', 'model', 'detail_info'], 'required'],
            [['move_id', 'repli_id', 'mat_id', 'pcs'], 'integer'],
            [['price', 'discount', 'tax'], 'number'],
            [['kod'], 'string', 'max' => 4],
            [['model'], 'string', 'max' => 30],
            [['currency'], 'string', 'max' => 3],
            [['detail_info'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc$primaryKey
     */
    public static function primaryKey()
    {
        return ["move_id",'mat_id','detail_info'];
    }


    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'move_id' => 'Move ID',
            'repli_id' => 'Repli ID',
            'mat_id' => 'Mat ID',
            'kod' => 'Kod',
            'model' => 'Model',
            'price' => 'Price',
            'pcs' => 'Pcs',
            'discount' => 'Discount',
            'tax' => 'Tax',
            'currency' => 'Currency',
            'detail_info' => 'Detail Info',
        ];
    }

    /**
     * {@inheritdoc}
     * @return MdetailprodQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new MdetailprodQuery(get_called_class());
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getMovement()
    {
        return $this->hasOne(Movement::class, ['move_id' => 'move_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getMlog()
    {
        return $this->hasMany(Mlog::class, ['move_id' => 'move_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getThserials()
    {
        return $this->hasMany(Thserials::class, ['move_id' => 'move_id', 'mat_id' => 'mat_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFirstthserial()
    {
        return $this->getThserials()->one()->thserial;
    }

}
