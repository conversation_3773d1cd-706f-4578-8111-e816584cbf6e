<?php

namespace app\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\Eshop;

/**
 * app\models\EshopSearch represents the model behind the search form about `app\models\Eshop`.
 */
 class EshopSearch extends Eshop
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'orderid', 'order'], 'integer'],
            [['status', 'created', 'email', 'iname', 'isurname', 'istreet', 'icity', 'izip', 'iphone', 'icountry', 'icompany', 'iico', 'idic', 'dcompany', 'dname', 'dsurname', 'dstreet', 'dcity', 'dzip', 'dphone', 'dcountry', 'gcompany', 'gname', 'gsurname', 'gstreet', 'gcity', 'gzip', 'gphone', 'gcountry', 'gift', 'note', 'shipment', 'branch', 'voucher', 'payment', 'currency', 'products', 'alljson'], 'safe'],
            [['totalitems', 'totalitemsnovat', 'totalshipment', 'totalpayment', 'totalclub', 'total'], 'number'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = Eshop::find();

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        $query->andFilterWhere([
            'id' => $this->id,
            'orderid' => $this->orderid,
            'order' => $this->order,
            'created' => $this->created,
            'totalitems' => $this->totalitems,
            'totalitemsnovat' => $this->totalitemsnovat,
            'totalshipment' => $this->totalshipment,
            'totalpayment' => $this->totalpayment,
            'totalclub' => $this->totalclub,
            'total' => $this->total,
        ]);

        $query->andFilterWhere(['ilike', 'status', $this->status])
            ->andFilterWhere(['ilike', 'email', $this->email])
            ->andFilterWhere(['ilike', 'iname', $this->iname])
            ->andFilterWhere(['ilike', 'isurname', $this->isurname])
            ->andFilterWhere(['ilike', 'istreet', $this->istreet])
            ->andFilterWhere(['ilike', 'icity', $this->icity])
            ->andFilterWhere(['ilike', 'izip', $this->izip])
            ->andFilterWhere(['ilike', 'iphone', $this->iphone])
            ->andFilterWhere(['ilike', 'icountry', $this->icountry])
            ->andFilterWhere(['ilike', 'icompany', $this->icompany])
            ->andFilterWhere(['ilike', 'iico', $this->iico])
            ->andFilterWhere(['ilike', 'idic', $this->idic])
            ->andFilterWhere(['ilike', 'dcompany', $this->dcompany])
            ->andFilterWhere(['ilike', 'dname', $this->dname])
            ->andFilterWhere(['ilike', 'dsurname', $this->dsurname])
            ->andFilterWhere(['ilike', 'dstreet', $this->dstreet])
            ->andFilterWhere(['ilike', 'dcity', $this->dcity])
            ->andFilterWhere(['ilike', 'dzip', $this->dzip])
            ->andFilterWhere(['ilike', 'dphone', $this->dphone])
            ->andFilterWhere(['ilike', 'dcountry', $this->dcountry])
            ->andFilterWhere(['ilike', 'gcompany', $this->gcompany])
            ->andFilterWhere(['ilike', 'gname', $this->gname])
            ->andFilterWhere(['ilike', 'gsurname', $this->gsurname])
            ->andFilterWhere(['ilike', 'gstreet', $this->gstreet])
            ->andFilterWhere(['ilike', 'gcity', $this->gcity])
            ->andFilterWhere(['ilike', 'gzip', $this->gzip])
            ->andFilterWhere(['ilike', 'gphone', $this->gphone])
            ->andFilterWhere(['ilike', 'gcountry', $this->gcountry])
            ->andFilterWhere(['ilike', 'gift', $this->gift])
            ->andFilterWhere(['ilike', 'note', $this->note])
            ->andFilterWhere(['ilike', 'shipment', $this->shipment])
            ->andFilterWhere(['ilike', 'branch', $this->branch])
            ->andFilterWhere(['ilike', 'voucher', $this->voucher])
            ->andFilterWhere(['ilike', 'payment', $this->payment])
            ->andFilterWhere(['ilike', 'currency', $this->currency])
            ->andFilterWhere(['ilike', 'products', $this->products])
            ->andFilterWhere(['ilike', 'alljson', $this->alljson]);

        return $dataProvider;
    }
}
