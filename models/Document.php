<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "document".
 *
 * @property string $doc_id
 * @property string $name
 * @property int $number
 *
 * @property MType[] $mTypes
 */
class Document extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'document';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['doc_id'], 'required'],
            [['number'], 'default', 'value' => null],
            [['number'], 'integer'],
            [['doc_id'], 'string', 'max' => 5],
            [['name'], 'string', 'max' => 40],
            [['doc_id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'doc_id' => 'Doc ID',
            'name' => 'Name',
            'number' => 'Number',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getMTypes()
    {
        return $this->hasMany(MType::className(), ['doc_id' => 'doc_id']);
    }
}
