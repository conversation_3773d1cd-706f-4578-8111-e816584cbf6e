<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "Product".
 *
 * @property int $mat_id
 * @property string $ean13
 * @property string $kod
 * @property string $model
 * @property string $model2
 * @property string $price
 * @property string $p0
 * @property string $p1
 * @property string $p2
 * @property string $p3
 * @property string $p4
 * @property string $p5
 * @property string $p6
 * @property string $p8
 * @property string $p9
 * @property string $pdescr
 * @property string $tax
 * @property string $unit
 * @property string $min minimum on the stock...
 * @property string $max maximum on the stock...
 * @property string $p7
 * @property string $p0a
 * @property string $p0b
 * @property string $p0c
 * @property string $p0d
 * @property string $p0e
 * @property string $p0f
 * @property string $p0g
 * @property string $p0h
 * @property string $p0i
 * @property string $p0j
 * @property string $p1b
 * @property string $p1c
 * @property string $p1d
 * @property string $p1e
 * @property string $p1f
 * @property string $p1g
 * @property string $p1h
 * @property string $p1i
 * @property string $p1j
 * @property string $p2b
 * @property string $p2c
 * @property string $p2d
 * @property string $p2e
 * @property string $p2f
 * @property string $p2g
 * @property string $p2h
 * @property string $p2i
 * @property string $p2j
 * @property string $p1a
 * @property string $p2a
 * @property string $hs_code Harmonized System code
 * @property string $coo Country of origin
 *
 * @property Thserials $thserials
 * @property MDetails[] $mdetails
 * @property int $sumonstocks
 * @property string $stockstatus
 */
class ProductWithModel2 extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'product_with_model2';
    }


    public static function primaryKey()
    {
        return ['mat_id'];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['mat_id', 'sumonstocks'], 'integer'],
            [['kod', 'model'], 'required'],
            [['price', 'p0', 'p1', 'p2', 'p3', 'p4', 'p5', 'p6', 'p8', 'p9', 'tax', 'min', 'max', 'p7', 'p0a', 'p0b', 'p0c', 'p0d', 'p0e', 'p0f', 'p0g', 'p0h', 'p0i', 'p0j', 'p1b', 'p1c', 'p1d', 'p1e', 'p1f', 'p1g', 'p1h', 'p1i', 'p1j', 'p2b', 'p2c', 'p2d', 'p2e', 'p2f', 'p2g', 'p2h', 'p2i', 'p2j', 'p1a', 'p2a'], 'number'],
            [['ean13'], 'string', 'max' => 13],
            [['kod'], 'string', 'max' => 4],
            [['model', 'model2'], 'string', 'max' => 255],
            [['pdescr'], 'string', 'max' => 120],
            [['unit'], 'string', 'max' => 3],
            [['hs_code'], 'string', 'max' => 10],
            [['coo'], 'string', 'max' => 2],
            [['ean13'], 'unique'],
            [['mat_id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'mat_id' => 'mat_id',
            'ean13' => 'Ean13',
            'kod' => 'Kod',
            'model' => "model...........................",
            'price' => 'Price',
            'p0' => 'P0',
            'p1' => 'P1',
            'p2' => 'P2',
            'p3' => 'P3',
            'p4' => 'P4',
            'p5' => 'P5',
            'p6' => 'P6',
            'p8' => 'P8',
            'p9' => 'P9',
            'pdescr' => 'Pdescr',
            'tax' => 'Tax',
            'unit' => 'Unit',
            'min' => 'Min',
            'max' => 'Max',
            'p7' => 'P7',
            'p0a' => 'P0a B%',
            'p0b' => 'P0b G%',
            'p0c' => 'P0c P%',
            'p0d' => 'P0d',
            'p0e' => 'P0e',
            'p0f' => 'P0f',
            'p0g' => 'P0g',
            'p0h' => 'P0h',
            'p0i' => 'P0i',
            'p0j' => 'P0j',
            'p1b' => 'P1b',
            'p1c' => 'P1c',
            'p1d' => 'P1d',
            'p1e' => 'P1e',
            'p1f' => 'P1f',
            'p1g' => 'P1g',
            'p1h' => 'P1h',
            'p1i' => 'P1i',
            'p1j' => 'P1j',
            'p2b' => 'P2b',
            'p2c' => 'P2c',
            'p2d' => 'P2d',
            'p2e' => 'P2e',
            'p2f' => 'P2f',
            'p2g' => 'P2g',
            'p2h' => 'P2h',
            'p2i' => 'P2i',
            'p2j' => 'P2j',
            'p1a' => 'P1a',
            'p2a' => 'P2a',
            'hs_code' => 'HS Code',
            'coo' => 'CoO',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getMdetail()
    {
        return $this->hasMany(MDetail::class, ['mat_id' => 'mat_id']);
    }


    /**
     * @return \yii\db\ActiveQuery
     */
    public function getThserials()
    {
        return $this->hasMany(Thserials::class, ['mat_id' => 'mat_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getSumonstocks()
    {
        return Yii::$app->db->createCommand("select sum(remains) from dyninfox where mat_id=:mat_id")
            ->bindValue(':mat_id', isset($this->mat_id) ? $this->mat_id : 0)->queryScalar();
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getStockstatus()
    {
        if (isset($this->kod) && $this->kod <> 'WTHS' && $this->kod <> 'WLON') {
            return Yii::$app->db->createCommand("select case when sum(remains) > 3 then 'Urgent' when sum(remains) > 0 then 'OnDemand' else 'N/A' end from dyninfox where shop in ('s0', 's0j', 'sc', 'THS', 'WAUP', 'WBB', 'WDEU', 'WOPT') and mat_id=:mat_id")
                ->bindValue(':mat_id', isset($this->mat_id) ? $this->mat_id : 0)->queryScalar();
        } elseif (isset($this->kod) && $this->kod == 'WLON') {
            return Yii::$app->db->createCommand("select case when sum(remains) > 3 then 'Urgent' when sum(remains) > 0 then 'OnDemand' else 'N/A' end from dyninfox where shop in ('s0') and mat_id=:mat_id")
                ->bindValue(':mat_id', isset($this->mat_id) ? $this->mat_id : 0)->queryScalar();
        } else {
            return Yii::$app->db->createCommand("select case when sum(remains) > 3 then 'Urgent' when sum(remains) > 0 then 'OnDemand' else 'N/A' end from dyninfox where shop in ('s0', 's0j') and mat_id=:mat_id")
                ->bindValue(':mat_id', isset($this->mat_id) ? $this->mat_id : 0)->queryScalar();
        }
    }
}
