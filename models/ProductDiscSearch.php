<?php

namespace app\models;

use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\ProductDisc;

/**
 * ProductDiscSearch represents the model behind the search form of `app\models\ProductDisc`.
 */
class ProductDiscSearch extends ProductDisc
{
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['perc', 'cgroup'], 'integer'],
            [['ctype'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = ProductDisc::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'perc' => $this->perc,
            'cgroup' => $this->cgroup,
        ]);

        $query->andFilterWhere(['ilike', 'ctype', $this->ctype]);

        return $dataProvider;
    }
}
