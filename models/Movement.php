<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "movement".
 *
 * @property int $move_id
 * @property int $adr_id
 * @property string $d1
 * @property string $d2
 * @property string $d3
 * @property string $doc_id
 * @property int $number
 * @property string $emp_id
 * @property string $total
 * @property string $payment
 * @property string $X if 9 than type 87 is intended WITHOUT VAT
 * @property int $c_number
 * @property string $total0 total with tax 0%
 * @property string $total1
 * @property string $total2
 * @property string $rounding
 * @property string $tax1 tax amount nr. 1
 * @property string $tax2 tax amount nr. 2
 * @property string $discount sum of pcs*price minus (total0+total1+total2)
 * @property string $text1 add info for paper if needed (nr of order, etc.)
 * @property string $text2 text in paper (short sentences)
 * @property int $m_type_id exact type of movement
 * @property int $parent_move_id 
 * @property string $stock_id1
 * @property string $stock_id2
 * @property MDetail[] $mDetails
 * @property MDetail $firstMDetail
 * @property Minfo $firstMinfo
 * @property Mlog $mlog
 * @property Address $adr
 * @property MType $mType
 * @property Thserials[] $thserials
 * @property int $parent_move_id
 */
class Movement extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'movement';
    }


    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['adr_id', 'number', 'c_number', 'm_type_id', 'parent_move_id'], 'default', 'value' => null],
            [['adr_id', 'number', 'c_number', 'm_type_id', 'parent_move_id'], 'integer'],
            [['d1', 'd2', 'd3'], 'safe'],
            [['total', 'total0', 'total1', 'total2', 'rounding', 'tax1', 'tax2', 'discount'], 'number'],
            [['text1', 'text2'], 'string'],
            [['doc_id'], 'string', 'max' => 5],
            [['emp_id'], 'string', 'max' => 20],
            [['payment', 'X'], 'string', 'max' => 1],
            [['stock_id1', 'stock_id2'], 'string', 'max' => 6],
            [['adr_id'], 'exist', 'skipOnError' => true, 'targetClass' => Address::class, 'targetAttribute' => ['adr_id' => 'adr_id']],
            [['parent_move_id'], 'exist', 'skipOnError' => true, 'targetClass' => Movement::class, 'targetAttribute' => ['parent_move_id' => 'move_id']],
            [['m_type_id'], 'exist', 'skipOnError' => true, 'targetClass' => MType::class, 'targetAttribute' => ['m_type_id' => 'm_type_id']],
                       [['stock_id1'], 'exist', 'skipOnError' => true, 'targetClass' => StockDetail::class, 'targetAttribute' => ['stock_id1' => 'stock_id']], 
           [['stock_id2'], 'exist', 'skipOnError' => true, 'targetClass' => StockDetail::class, 'targetAttribute' => ['stock_id2' => 'stock_id']],    
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'move_id' => 'Move ID',
            'adr_id' => 'Adr ID',
            'd1' => 'D1',
            'd2' => 'D2',
            'd3' => 'D3',
            'doc_id' => 'Doc ID',
            'number' => 'Number',
            'emp_id' => 'Emp ID',
            'total' => 'Total+VAT',
            'payment' => 'Payment',
            'X' => 'X',
            'c_number' => 'C Number',
            'total0' => 'Total0',
            'total1' => 'Total1',
            'total2' => 'Total2',
            'rounding' => 'Rounding',
            'tax1' => 'Tax1',
            'tax2' => 'Tax2',
            'discount' => 'Discount',
            'text1' => 'Text1',
            'text2' => 'Text2',
            'm_type_id' => 'M Type ID',
            'stock_id1' => 'From Stock',
            'stock_id2' => 'To Stock',
            'parent_move_id' => 'Advanced payment move_id'
        ];
    }

    public function getSumpcs()
    {
        return Yii::$app->db->createCommand("select sum(pcs) from m_detail where move_id=:move_id")
                 ->bindValue(':move_id',$this->move_id)->queryScalar();
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getMDetails()
    {
        return $this->hasMany(MDetail::class, ['move_id' => 'move_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFirstMDetail()
    {
        return $this->hasOne(MDetail::class, ['move_id' => 'move_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFirstMinfo()
    {
        return $this->hasOne(Minfo::class, ['move_id' => 'move_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getAdr()
    {
        return $this->hasOne(Address::class, ['adr_id' => 'adr_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getMType()
    {
        return $this->hasOne(MType::class, ['m_type_id' => 'm_type_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getMlog()
    {
        return $this->hasMany(Mlog::class, ['move_id' => 'move_id']);
    }


    /**
     * @return \yii\db\ActiveQuery
     */
    public function getParent()
    {
        return $this->hasOne(Movement::class, ['move_id' => 'parent_move_id']);
    }


    /**
     * @return \yii\db\ActiveQuery
     */
    public function getThserials()
    {
        return $this->hasMany(Thserials::class, ['move_id' => 'move_id']);
    }

    public function getStockdetail1()
    {
        return $this->hasOne(StockDetail::class, ['stock_id' => 'stock_id1']);
    }

    public function getStockdetail2()
    {
        return $this->hasOne(StockDetail::class, ['stock_id' => 'stock_id2']);
    }

    public function isOnTheWay()
    {
        return Yii::$app->db->createCommand("select created_at from mlog where move_id=:move_id and flags like 'OnTheWay%'")
        ->bindValue(':move_id',$this->move_id)->queryScalar();
    }    

    public function isOnTheWayWhere()
    {
        return Yii::$app->db->createCommand("select substring(flags, strpos(flags, ',') + 1)  from mlog where move_id=:move_id and flags like 'OnTheWay,%'")
        ->bindValue(':move_id',$this->move_id)->queryScalar();
    }    

    public function isReceived()
    {
        return Yii::$app->db->createCommand("select created_at from mlog where move_id=:move_id and flags='Received'")
        ->bindValue(':move_id',$this->move_id)->queryScalar();
    }    

    public function getNrOfMessages()
    {
        return Yii::$app->db->createCommand("select count(*) from mlog where move_id=:move_id and flags not in ('Received','OnTheWay') and flags not like 'Sys%'")
        ->bindValue(':move_id',$this->move_id)->queryScalar();
    }    

    public function hasTracking()
    {
        return Yii::$app->db->createCommand("select note from mlog where move_id=:move_id and flags='Tracking#'")
        ->bindValue(':move_id',$this->move_id)->queryScalar();
    }    

    /**
     * {@inheritdoc}
     * @return MovementQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new MovementQuery(get_called_class());
    }
}
