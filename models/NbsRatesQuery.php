<?php

namespace app\models;

/**
 * This is the ActiveQuery class for [[NbsRates]].
 *
 * @see NbsRates
 */
class NbsRatesQuery extends \yii\db\ActiveQuery
{
    /*public function active()
    {
        return $this->andWhere('[[status]]=1');
    }*/

    /**
     * {@inheritdoc}
     * @return NbsRates[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * {@inheritdoc}
     * @return NbsRates|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
