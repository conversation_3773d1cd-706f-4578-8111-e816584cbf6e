<?php

namespace app\models;

use Yii;
use yii\base\Model;

/**
 * BatchUpdateForm is the model for updating product by csv string
 */
class BatchUpdateProductForm extends Model
{
    public $updatebox = "";
    public $updateLabel = "Update product prices by comma delimited string.";
    public $updatePlaceholder = "model,p5,p0,price,p1,p2,p6\nWTB439.34.883.4432,11.99,234.00,88,23432,4444323.92,44432";


    /**
     * @return array the validation rules.
     */
    public function rules()
    {
        return [
            
            [['updatebox','updatePlaceholder','updateLabel'], 'default'],
        ];
    }

    /**
     * @return array customized attribute labels
     */
    public function attributeLabels()
    {
        return [
            'updateLabel' => "Update more models",

        ];
    }

}
