<?php

namespace app\models;

/**
 * This is the ActiveQuery class for [[MUserConfig]].
 *
 * @see MUserConfig
 */
class MUserConfigQuery extends \yii\db\ActiveQuery
{
    /*public function active()
    {
        return $this->andWhere('[[status]]=1');
    }*/

    /**
     * {@inheritdoc}
     * @return MUserConfig[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * {@inheritdoc}
     * @return MUserConfig|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
