<?php

namespace app\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\Nbsrates;

/**
 * app\models\NbsratesSearch represents the model behind the search form about `app\models\Nbsrates`.
 */
 class NbsratesSearch extends Nbsrates
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['nbs_date', 'nbs_cur'], 'safe'],
            [['nbs_rate', 'nbs_multi'], 'number'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = Nbsrates::find();

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        $query->andFilterWhere([
            'nbs_date' => $this->nbs_date,
            'nbs_rate' => $this->nbs_rate,
            'nbs_multi' => $this->nbs_multi,
        ]);

        $query->andFilterWhere(['ilike', 'nbs_cur', $this->nbs_cur]);

        return $dataProvider;
    }
}
