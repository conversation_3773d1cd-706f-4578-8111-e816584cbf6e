<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model app\models\WowCards */

$this->title = $model->card_nr;
$this->params['breadcrumbs'][] = ['label' => 'Wow Cards', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="wow-cards-view">

    <h1><?= Html::encode($this->title) ?></h1>

    <p>
        <?= Html::a('Update', ['update', 'id' => $model->card_nr], ['class' => 'btn btn-primary']) ?>
        <?= Html::a('Delete', ['delete', 'id' => $model->card_nr], [
            'class' => 'btn btn-danger',
            'data' => [
                'confirm' => 'Are you sure you want to delete this item?',
                'method' => 'post',
            ],
        ]) ?>
    </p>

    <?= DetailView::widget([
        'model' => $model,
        'attributes' => [
            'card_nr',
            'parent_nr',
            'issue_d',
            'expire_d',
            'cust_id',
            'card_type',
            'card_discount',
            'cust_currency',
            'card_memo',
        ],
    ]) ?>

</div>
