<?php

use yii\helpers\Html;
use yii\grid\GridView;

/* @var $this yii\web\View */
/* @var $searchModel app\models\WowCardsSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Wow Cards';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="wow-cards-index">

    <h1><?= Html::encode($this->title) ?></h1>
    <?php // echo $this->render('_search', ['model' => $searchModel]); ?>

    <p>
        <?= Html::a('Create Wow Cards', ['create'], ['class' => 'btn btn-success']) ?>
    </p>

    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => [
            ['class' => 'yii\grid\SerialColumn'],

            'card_nr',
            'parent_nr',
            'issue_d',
            'expire_d',
            'cust_id',
            //'card_type',
            //'card_discount',
            //'cust_currency',
            //'card_memo',

            ['class' => 'yii\grid\ActionColumn'],
        ],
    ]); ?>
</div>
