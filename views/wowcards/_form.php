<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model app\models\WowCards */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="wow-cards-form">

    <?php $form = ActiveForm::begin(); ?>

    <?= $form->field($model, 'card_nr')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'parent_nr')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'issue_d')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'expire_d')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'cust_id')->textInput() ?>

    <?= $form->field($model, 'card_type')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'card_discount')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'cust_currency')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'card_memo')->textInput(['maxlength' => true]) ?>

    <div class="form-group">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
