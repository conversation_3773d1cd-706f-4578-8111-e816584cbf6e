<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model app\models\EshopSearch */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="form-eshop-search">

    <?php $form = ActiveForm::begin([
        'action' => ['index'],
        'method' => 'get',
    ]); ?>

    <?= $form->field($model, 'id', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'status')->textInput(['maxlength' => true, 'placeholder' => 'Status']) ?>

    <?= $form->field($model, 'orderid')->textInput(['placeholder' => 'Orderid']) ?>

    <?= $form->field($model, 'order')->textInput(['placeholder' => 'Order']) ?>

    <?= $form->field($model, 'email')->textInput(['maxlength' => true, 'placeholder' => 'Email']) ?>

    <?php /* echo $form->field($model, 'iname')->textInput(['maxlength' => true, 'placeholder' => 'Iname']) */ ?>

    <?php /* echo $form->field($model, 'isurname')->textInput(['maxlength' => true, 'placeholder' => 'Isurname']) */ ?>

    <?php /* echo $form->field($model, 'istreet')->textInput(['maxlength' => true, 'placeholder' => 'Istreet']) */ ?>

    <?php /* echo $form->field($model, 'icity')->textInput(['maxlength' => true, 'placeholder' => 'Icity']) */ ?>

    <?php /* echo $form->field($model, 'izip')->textInput(['maxlength' => true, 'placeholder' => 'Izip']) */ ?>

    <?php /* echo $form->field($model, 'iphone')->textInput(['maxlength' => true, 'placeholder' => 'Iphone']) */ ?>

    <?php /* echo $form->field($model, 'icountry')->textInput(['maxlength' => true, 'placeholder' => 'Icountry']) */ ?>

    <?php /* echo $form->field($model, 'icompany')->textInput(['maxlength' => true, 'placeholder' => 'Icompany']) */ ?>

    <?php /* echo $form->field($model, 'iico')->textInput(['maxlength' => true, 'placeholder' => 'Iico']) */ ?>

    <?php /* echo $form->field($model, 'idic')->textInput(['maxlength' => true, 'placeholder' => 'Idic']) */ ?>

    <?php /* echo $form->field($model, 'dcompany')->textInput(['maxlength' => true, 'placeholder' => 'Dcompany']) */ ?>

    <?php /* echo $form->field($model, 'dname')->textInput(['maxlength' => true, 'placeholder' => 'Dname']) */ ?>

    <?php /* echo $form->field($model, 'dsurname')->textInput(['maxlength' => true, 'placeholder' => 'Dsurname']) */ ?>

    <?php /* echo $form->field($model, 'dstreet')->textInput(['maxlength' => true, 'placeholder' => 'Dstreet']) */ ?>

    <?php /* echo $form->field($model, 'dcity')->textInput(['maxlength' => true, 'placeholder' => 'Dcity']) */ ?>

    <?php /* echo $form->field($model, 'dzip')->textInput(['maxlength' => true, 'placeholder' => 'Dzip']) */ ?>

    <?php /* echo $form->field($model, 'dphone')->textInput(['maxlength' => true, 'placeholder' => 'Dphone']) */ ?>

    <?php /* echo $form->field($model, 'dcountry')->textInput(['maxlength' => true, 'placeholder' => 'Dcountry']) */ ?>

    <?php /* echo $form->field($model, 'gcompany')->textInput(['maxlength' => true, 'placeholder' => 'Gcompany']) */ ?>

    <?php /* echo $form->field($model, 'gname')->textInput(['maxlength' => true, 'placeholder' => 'Gname']) */ ?>

    <?php /* echo $form->field($model, 'gsurname')->textInput(['maxlength' => true, 'placeholder' => 'Gsurname']) */ ?>

    <?php /* echo $form->field($model, 'gstreet')->textInput(['maxlength' => true, 'placeholder' => 'Gstreet']) */ ?>

    <?php /* echo $form->field($model, 'gcity')->textInput(['maxlength' => true, 'placeholder' => 'Gcity']) */ ?>

    <?php /* echo $form->field($model, 'gzip')->textInput(['maxlength' => true, 'placeholder' => 'Gzip']) */ ?>

    <?php /* echo $form->field($model, 'gphone')->textInput(['maxlength' => true, 'placeholder' => 'Gphone']) */ ?>

    <?php /* echo $form->field($model, 'gcountry')->textInput(['maxlength' => true, 'placeholder' => 'Gcountry']) */ ?>

    <?php /* echo $form->field($model, 'gift')->textInput(['maxlength' => true, 'placeholder' => 'Gift']) */ ?>

    <?php /* echo $form->field($model, 'note')->textInput(['maxlength' => true, 'placeholder' => 'Note']) */ ?>

    <?php /* echo $form->field($model, 'shipment')->textInput(['maxlength' => true, 'placeholder' => 'Shipment']) */ ?>

    <?php /* echo $form->field($model, 'branch')->textInput(['maxlength' => true, 'placeholder' => 'Branch']) */ ?>

    <?php /* echo $form->field($model, 'voucher')->textInput(['maxlength' => true, 'placeholder' => 'Voucher']) */ ?>

    <?php /* echo $form->field($model, 'payment')->textInput(['maxlength' => true, 'placeholder' => 'Payment']) */ ?>

    <?php /* echo $form->field($model, 'totalitems')->textInput(['placeholder' => 'Totalitems']) */ ?>

    <?php /* echo $form->field($model, 'totalitemsnovat')->textInput(['placeholder' => 'Totalitemsnovat']) */ ?>

    <?php /* echo $form->field($model, 'totalshipment')->textInput(['placeholder' => 'Totalshipment']) */ ?>

    <?php /* echo $form->field($model, 'totalpayment')->textInput(['placeholder' => 'Totalpayment']) */ ?>

    <?php /* echo $form->field($model, 'totalclub')->textInput(['placeholder' => 'Totalclub']) */ ?>

    <?php /* echo $form->field($model, 'total')->textInput(['placeholder' => 'Total']) */ ?>

    <?php /* echo $form->field($model, 'currency')->textInput(['maxlength' => true, 'placeholder' => 'Currency']) */ ?>

    <?php /* echo $form->field($model, 'products')->textarea(['rows' => 6]) */ ?>

    <?php /* echo $form->field($model, 'alljson')->textarea(['rows' => 6]) */ ?>

    <div class="form-group">
        <?= Html::submitButton('Search', ['class' => 'btn btn-primary']) ?>
        <?= Html::resetButton('Reset', ['class' => 'btn btn-default']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
