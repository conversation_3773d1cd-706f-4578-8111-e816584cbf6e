<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use kartik\grid\GridView;

/* @var $this yii\web\View */
/* @var $model app\models\Eshop */

$this->title = $model->id;
$this->params['breadcrumbs'][] = ['label' => 'Eshop', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="eshop-view">

    <div class="row">
        <div class="col-sm-9">
            <h2><?= 'Eshop'.' '. Html::encode($this->title) ?></h2>
        </div>
        <div class="col-sm-3" style="margin-top: 15px">
            
            <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary']) ?>
            <?= Html::a('Delete', ['delete', 'id' => $model->id], [
                'class' => 'btn btn-danger',
                'data' => [
                    'confirm' => 'Are you sure you want to delete this item?',
                    'method' => 'post',
                ],
            ])
            ?>
        </div>
    </div>

    <div class="row">
<?php 
    $gridColumn = [
        ['attribute' => 'id', 'visible' => false],
        'status',
        'orderid',
        'order',
        'email:email',
        'iname',
        'isurname',
        'istreet',
        'icity',
        'izip',
        'iphone',
        'icountry',
        'icompany',
        'iico',
        'idic',
        'dcompany',
        'dname',
        'dsurname',
        'dstreet',
        'dcity',
        'dzip',
        'dphone',
        'dcountry',
        'gcompany',
        'gname',
        'gsurname',
        'gstreet',
        'gcity',
        'gzip',
        'gphone',
        'gcountry',
        'gift',
        'note',
        'shipment',
        'branch',
        'voucher',
        'payment',
        'totalitems',
        'totalitemsnovat',
        'totalshipment',
        'totalpayment',
        'totalclub',
        'total',
        'currency',
        'products:ntext',
        'alljson:ntext',
    ];
    echo DetailView::widget([
        'model' => $model,
        'attributes' => $gridColumn
    ]);
?>
    </div>
</div>
