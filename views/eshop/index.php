<?php

/* @var $this yii\web\View */
/* @var $searchModel app\models\EshopSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

use yii\helpers\Html;
use kartik\export\ExportMenu;
use kartik\grid\GridView;
use yii\helpers\Url;

$this->title = empty($title)?'Eshop':$title;
$this->params['breadcrumbs'][] = $this->title;
$search = "$('.search-button').click(function(){
	$('.search-form').toggle(1000);
	return false;
});";
$this->registerJs($search);
?>
<div class="eshop-index">

    <?php // echo $this->render('_search', ['model' => $searchModel]); ?>

    <p>
<!--         <?= Html::a('Create Eshop', ['create'], ['class' => 'btn btn-success']) ?> -->
        <?= Html::a('Advance Search', '#', ['class' => 'btn btn-info search-button']) ?>
        <?php
        echo Html::a('All', Url::current(), ['class' => 'btn btn-success', 'id' =>'statusall']);
        echo "&nbsp;&nbsp;&nbsp;";
        echo Html::a('Not connected (N)', Url::current(['statusfilter'=>'N' ]), ['class' => 'btn btn-success', 'id' =>'statusfilter1']);
        echo "&nbsp;&nbsp;&nbsp;";
        echo Html::a('Connected (C+Mov)', Url::current(['statusfilter'=>'C+Mov' ]), ['class' => 'btn btn-success', 'id' =>'statusfilter1']);
        echo "&nbsp;&nbsp;&nbsp;";
        echo Html::a('Archived (C)', Url::current(['statusfilter'=>'C' ]), ['class' => 'btn btn-success', 'id' =>'statusfilter1']);
   ?>
    </p>
    <div class="search-form" style="display:none">
        <?=  $this->render('_search', ['model' => $searchModel]); ?>
    </div>
    <?php 
    $gridColumn = [
        // ['class' => 'yii\grid\SerialColumn'],
        ['attribute' => 'id', 'visible' => false],
        [
            'class' => 'kartik\grid\ExpandRowColumn',
            'width' => '50px',
            'value' => function ($model, $key, $index, $column) {
                if( !empty($model->movement)){
                    return GridView::ROW_COLLAPSED;
                } else {
                    return "";
                }
            },
            'detail' => function ($model, $key, $index, $column) {
                    return Yii::$app->controller->renderPartial('/movement/_expand', ['model' => $model->movement]);
            },
            'headerOptions' => ['class' => 'kartik-sheet-style'],
            'expandOneOnly' => true
        ],
        [
           'class' => 'kartik\grid\EditableColumn',
           'attribute' => 'status',
           'refreshGrid' => true,
           'editableOptions' => [
               'inputType' => kartik\editable\Editable::INPUT_DROPDOWN_LIST,
               'data' => ['C' => 'C = Connect to last movement 137,139,167,169,147,504', 'N' => 'N = Disconnect from movement', 'X' => 'X = Set as test or canceled'],
               'options' => ['class'=>'form-control', 'prompt'=>'Set status...'],
                'displayValueConfig'=> [
                'C' => '<i class="fa fa-thumbs-up"></i> C',
                'N' => '<i class="fa fa-hourglass"></i> N',
                'X' => '<i class="fa fa-thumbs-down"></i> X',
                ],
            ],
        ],
        // [
        //    'class' => 'kartik\grid\EditableColumn',
        //    'attribute' => 'movement',
        //    'readonly' => true,
        //    'value' => function($model){ return $model->movement->move_id; }, // assign value from getProfileCompany method
        //    'editableOptions' => [
        //         'header' => 'Connected_move_id',
        //         'inputType' => kartik\editable\Editable::INPUT_TEXT,
        //         'options' => [
        //             'pluginOptions' => [

        //             ]
        //         ]
        //     ],
        // ],
        //'orderid',
         'order',
        //'email:email',
        'iname',
        'isurname',
        'istreet',
        'icity',
        // 'izip',
        'iphone',
        'icountry',
        // 'icompany',
        // 'iico',
        // 'idic',
        // 'dcompany',
        // 'dname',
        // 'dsurname',
        // 'dstreet',
        // 'dcity',
        // 'dzip',
        // 'dphone',
        // 'dcountry',
        // 'gcompany',
        // 'gname',
        // 'gsurname',
        // 'gstreet',
        // 'gcity',
        // 'gzip',
        // 'gphone',
        // 'gcountry',
         'gift',
         'note',
         'shipment',
         'branch',
         'voucher',
         'payment',
        // 'totalitems',
        // 'totalitemsnovat',
        // 'totalshipment',
        // 'totalpayment',
        // 'totalclub',
        'total',
        'currency',
        'products:ntext',
        // 'alljson:ntext',
        [
            'class' => 'yii\grid\ActionColumn',
        ],
    ]; 
    ?>
    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => $gridColumn,
        'pjax' => true,
        'pjaxSettings' => ['options' => ['id' => 'kv-pjax-container-eshop']],
        'panel' => [
            'type' => GridView::TYPE_PRIMARY,
            'heading' => '<span class="glyphicon glyphicon-book"></span>  ' . Html::encode($this->title),
        ],
        'export' => false,
        // your toolbar can include the additional full export menu
        'toolbar' => [
            ExportMenu::widget([
                'dataProvider' => $dataProvider,
                'columns' => $gridColumn,
                'target' => ExportMenu::TARGET_BLANK,
                'fontAwesome' => true,
                'pjax' => false,
                'clearBuffers' => true,
                'showConfirmAlert' => false,
                'dropdownOptions' => [
                    'label' => 'Full',
                    'class' => 'btn btn-default',
                    'itemsBefore' => [
                        '<li class="dropdown-header">Export All Data</li>',
                    ],
                ],
                'exportConfig' => [
                    ExportMenu::FORMAT_PDF => false
                ]
            ]) ,
        ],
    ]); ?>

</div>
