<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model app\models\Eshop */
/* @var $form yii\widgets\ActiveForm */

?>

<div class="eshop-form">

    <?php $form = ActiveForm::begin(); ?>

    <?= $form->errorSummary($model); ?>

    <?= $form->field($model, 'id', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'status')->textInput(['maxlength' => true, 'placeholder' => 'Status']) ?>

    <?= $form->field($model, 'orderid')->textInput(['placeholder' => 'Orderid']) ?>

    <?= $form->field($model, 'order')->textInput(['placeholder' => 'Order']) ?>

    <?= $form->field($model, 'email')->textInput(['maxlength' => true, 'placeholder' => 'Email']) ?>

    <?= $form->field($model, 'iname')->textInput(['maxlength' => true, 'placeholder' => 'Iname']) ?>

    <?= $form->field($model, 'isurname')->textInput(['maxlength' => true, 'placeholder' => 'Isurname']) ?>

    <?= $form->field($model, 'istreet')->textInput(['maxlength' => true, 'placeholder' => 'Istreet']) ?>

    <?= $form->field($model, 'icity')->textInput(['maxlength' => true, 'placeholder' => 'Icity']) ?>

    <?= $form->field($model, 'izip')->textInput(['maxlength' => true, 'placeholder' => 'Izip']) ?>

    <?= $form->field($model, 'iphone')->textInput(['maxlength' => true, 'placeholder' => 'Iphone']) ?>

    <?= $form->field($model, 'icountry')->textInput(['maxlength' => true, 'placeholder' => 'Icountry']) ?>

    <?= $form->field($model, 'icompany')->textInput(['maxlength' => true, 'placeholder' => 'Icompany']) ?>

    <?= $form->field($model, 'iico')->textInput(['maxlength' => true, 'placeholder' => 'Iico']) ?>

    <?= $form->field($model, 'idic')->textInput(['maxlength' => true, 'placeholder' => 'Idic']) ?>

    <?= $form->field($model, 'dcompany')->textInput(['maxlength' => true, 'placeholder' => 'Dcompany']) ?>

    <?= $form->field($model, 'dname')->textInput(['maxlength' => true, 'placeholder' => 'Dname']) ?>

    <?= $form->field($model, 'dsurname')->textInput(['maxlength' => true, 'placeholder' => 'Dsurname']) ?>

    <?= $form->field($model, 'dstreet')->textInput(['maxlength' => true, 'placeholder' => 'Dstreet']) ?>

    <?= $form->field($model, 'dcity')->textInput(['maxlength' => true, 'placeholder' => 'Dcity']) ?>

    <?= $form->field($model, 'dzip')->textInput(['maxlength' => true, 'placeholder' => 'Dzip']) ?>

    <?= $form->field($model, 'dphone')->textInput(['maxlength' => true, 'placeholder' => 'Dphone']) ?>

    <?= $form->field($model, 'dcountry')->textInput(['maxlength' => true, 'placeholder' => 'Dcountry']) ?>

    <?= $form->field($model, 'gcompany')->textInput(['maxlength' => true, 'placeholder' => 'Gcompany']) ?>

    <?= $form->field($model, 'gname')->textInput(['maxlength' => true, 'placeholder' => 'Gname']) ?>

    <?= $form->field($model, 'gsurname')->textInput(['maxlength' => true, 'placeholder' => 'Gsurname']) ?>

    <?= $form->field($model, 'gstreet')->textInput(['maxlength' => true, 'placeholder' => 'Gstreet']) ?>

    <?= $form->field($model, 'gcity')->textInput(['maxlength' => true, 'placeholder' => 'Gcity']) ?>

    <?= $form->field($model, 'gzip')->textInput(['maxlength' => true, 'placeholder' => 'Gzip']) ?>

    <?= $form->field($model, 'gphone')->textInput(['maxlength' => true, 'placeholder' => 'Gphone']) ?>

    <?= $form->field($model, 'gcountry')->textInput(['maxlength' => true, 'placeholder' => 'Gcountry']) ?>

    <?= $form->field($model, 'gift')->textInput(['maxlength' => true, 'placeholder' => 'Gift']) ?>

    <?= $form->field($model, 'note')->textInput(['maxlength' => true, 'placeholder' => 'Note']) ?>

    <?= $form->field($model, 'shipment')->textInput(['maxlength' => true, 'placeholder' => 'Shipment']) ?>

    <?= $form->field($model, 'branch')->textInput(['maxlength' => true, 'placeholder' => 'Branch']) ?>

    <?= $form->field($model, 'voucher')->textInput(['maxlength' => true, 'placeholder' => 'Voucher']) ?>

    <?= $form->field($model, 'payment')->textInput(['maxlength' => true, 'placeholder' => 'Payment']) ?>

    <?= $form->field($model, 'totalitems')->textInput(['placeholder' => 'Totalitems']) ?>

    <?= $form->field($model, 'totalitemsnovat')->textInput(['placeholder' => 'Totalitemsnovat']) ?>

    <?= $form->field($model, 'totalshipment')->textInput(['placeholder' => 'Totalshipment']) ?>

    <?= $form->field($model, 'totalpayment')->textInput(['placeholder' => 'Totalpayment']) ?>

    <?= $form->field($model, 'totalclub')->textInput(['placeholder' => 'Totalclub']) ?>

    <?= $form->field($model, 'total')->textInput(['placeholder' => 'Total']) ?>

    <?= $form->field($model, 'currency')->textInput(['maxlength' => true, 'placeholder' => 'Currency']) ?>

    <?= $form->field($model, 'products')->textarea(['rows' => 6]) ?>

    <?= $form->field($model, 'alljson')->textarea(['rows' => 6]) ?>

    <div class="form-group">
        <?= Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
