<?php

use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\ActiveForm;
use yii\widgets\MaskedInput;
use kartik\password\PasswordInput;
use kartik\date\DatePicker;
use app\controllers\EstockTools;


/* @var $this yii\web\View */
/* @var $model app\models\Customers */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="customers-form">

    <?php $form = ActiveForm::begin(); ?>

<!--     <?= $form->field($model, 'gender')->textInput(['maxlength' => true]) ?> -->

    <?= $form->field($model, 'firstname')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'lastname')->textInput(['maxlength' => true]) ?>

<!--    <?= $form->field($model, 'dob')->textInput(['maxlength' => true]) ?> -->

    <?= $form->field($model, 'email_address')->widget(MaskedInput::className(), [
           'clientOptions' => [
        'alias' =>  'email'
    ]
]) ?>

    <?= $form->field($model, 'street_address')->textInput(['maxlength' => true]) ?>

<!--      <?= $form->field($model, 'suburb')->textInput(['maxlength' => true]) ?> -->

    <?= $form->field($model, 'postcode')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'city')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'state')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'telephone')->textInput(['maxlength' => true]) ?>

    <?php
    if (!$model->password) $model->password=EstockTools::generateStrongPassword();
    if (!$model->nick) $model->nick=EstockTools::generateStrongPassword(3,false,'u').EstockTools::generateStrongPassword(3,false,'d');
    if (!$model->card_nr) $model->card_nr=$model->nick;
    if (!$model->issue_d) $model->issue_d = date('Y-m-d');
    if (!$model->card_type) $model->card_type = 'B';
    if (!$model->card_discount) $model->card_discount = '2';
    if (!$model->cust_currency) $model->cust_currency = 'EUR';
    ?>

<!--      <?= $form->field($model, 'fax')->textInput(['maxlength' => true]) ?> -->
    <?= $form->field($model, 'password')->widget(PasswordInput::className(), [
            'togglePlacement' => 'left',
    ]);

     ?>
    <p>
        <?= Html::a(Yii::t('app', 'Generate Pwd'), null, ['class' => 'btn btn-success', 'id' => 'genpwdbtn']) ?>
    </p>
<!--    <?= $form->field($model, 'country_id')->textInput() ?> -->



        <?php 
        echo $form->field($model, 'country_id')
            ->dropDownList(yii\helpers\ArrayHelper::map($cmodel, 'numcode', 'printable_name'), 
                [
                   'options' => $model->country_id?null:['703'=>['selected'=>true]]
                ]
        ) ?>


<!--      <?= $form->field($model, 'zone_id')->textInput() ?> -->

    <?= $form->field($model, 'nick')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'card_nr')->textInput(['maxlength' => true, 'readonly' => Yii::$app->user->identity->username!=='ferkovake'?true:false]) ?>

    <?= $form->field($model, 'issue_d')->widget(DatePicker::className(), [

        'options' => ['placeholder' => 'Start date'],
        'options2' => ['placeholder' => 'End date'],
        'type' => DatePicker::TYPE_COMPONENT_PREPEND,
        'pluginOptions' => [
            'format' => 'yyyy-mm-dd',
            'autoclose' => true,
        ]

    ]);


     ?>

<!--    <?= $form->field($model, 'expire_d')->textInput(['maxlength' => true]) ?> -->

    <?= $form->field($model, 'card_type')->textInput(['maxlength' => true, 'readonly' => Yii::$app->user->identity->username!=='ferkovake'?true:false]) ?>

    <?= $form->field($model, 'card_discount')->textInput(['maxlength' => true, 'readonly' => Yii::$app->user->identity->username!=='ferkovake'?true:false]) ?>

    <?= $form->field($model, 'cust_currency')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'card_memo')->textInput(['maxlength' => true]) ?>

    <br />
    <?= Html::checkbox('GDPR',false, ['label'=> 'By submitting this form I AGREE with ', 'id'=>'gdprCheck']) ?>
    <?= Html::a('GDPR',  Url::to('https://www.wdl.sk/obchodne-informacie/zasady-ochrany-spracovania-osobnych-udajov/', true)) ?>
    <br />    <br />
    <br />

    <div class="form-group">
       <?= Html::submitButton($model->isNewRecord ? 'Create new WDL member' : 'Update member', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary', 'id' => 'submitBtn', 'disabled' => 'true']) ?>
    </div>

    <?php ActiveForm::end(); ?>

    <?php 
    $script = <<< JS

    function setPwd(){
        $.get('index.php?r=customers/getpwd',function(data){
            var pwd = $.parseJSON(data);
            $('#customers-password').val(pwd.pwd);
            })       
    }

    $("#genpwdbtn").on('click',function(){
        setPwd();
        });

    setPwd();

    $("#gdprCheck").on('click',function(){
        var checked = $("#gdprCheck").is(':checked');
        if (checked) {
            $("#submitBtn").prop('disabled', false);
        } else {
            $("#submitBtn").prop('disabled', true);
        }
        });


JS;
$this->registerJs($script);
?>

</div>
