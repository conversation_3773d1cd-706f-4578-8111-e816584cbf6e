<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use kartik\grid\GridView;

/* @var $this yii\web\View */
/* @var $model app\models\Customers */

$this->title = $model->firstname.' '.$model->lastname;
$this->params['breadcrumbs'][] = ['label' => 'Customers', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="customers-view">

    <div class="col-xs-6">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary']) ?>
        <?= Html::a('Delete', ['delete', 'id' => $model->id], [
            'class' => 'btn btn-danger',
            'data' => [
                'confirm' => 'Are you sure you want to delete this item?',
                'method' => 'post',
            ],
        ]) ?>
    </div>

    <div class="col-xs-6 text-right">
        <?= Html::a('Send Password', ['sendpwd', 'id' => $model->id], ['class'=>'btn btn-warning',
                'data' => [
                'confirm' => 'Are you sure you want to send password to '.$model->email_address.'?',
                'method' => 'post',
            ],
            ]) ?>
    </div>

    <div class="col-xs-6">
    <?= DetailView::widget([
        'model' => $model,
        'attributes' => [
            'id',
            'firstname',
            'lastname',
            'email_address:email',
            'street_address',
            'postcode',
            'city',
            'telephone',
        ],
    ]) ?>
    </div>
    <div class="col-xs-6">
    <?= DetailView::widget([
        'model' => $model,
        'attributes' => [
            'countries.printable_name',
            'nick',
            'issue_d',
            'card_nr',
            'card_type',
            'card_discount',
            'cust_currency',
            'card_memo',
        ],
    ]) ?>
    </div>

    <h2>Sales </h2>


    <p>
        <?= Html::a(Yii::t('app', 'New Sale'), ['sales/create', 'cust_id'=>$model->id,
        'card_nr'=>$model->card_nr,'card_discount'=>$model->card_discount], ['class' => 'btn btn-success']) ?>
    </p>


    <?= GridView::widget([
        'dataProvider' => $salesdata,
            'responsive'=>true,
    'hover'=>true,
    'export' => false,
        'columns' => [
            ['class' => 'yii\grid\ActionColumn', 'controller' => 'sales', 'template' => '{view}'],
            'mat_id',
            'product.kod',
            'product.model',
            'pcs',
            'sale_price',
            'card_nr',
            'sale_spare',
            'sale_type',
            'sale_info',
            'sale_date',

        ],
    ]); ?>
</div>
