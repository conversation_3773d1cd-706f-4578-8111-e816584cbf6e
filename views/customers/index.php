<?php

use yii\helpers\Html;
use kartik\grid\GridView;
use yii\widgets\MaskedInput;
use kartik\export\ExportMenu;

/* @var $this yii\web\View */
/* @var $searchModel app\models\CustomersSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = Yii::$app->request->get('q')?'Search results':'WDL Klub / members';
$this->params['breadcrumbs'][] = $this->title;

ini_set("memory_limit","128M");

?>
<div class="customers-index">

    <h1><?= Html::encode($this->title) ?></h1>
    <p>Note: Eshop checks for <b>Nick</b> or <b>Card Nr</b> if member writes "Číslo vernostnej karty WDL klubu"</p>
    <p>
        <?= Html::a('Register new member', ['create'], ['class' => 'btn btn-success']) ?>
    </p>

    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'export' => false,

    'columns' => [
            ['class' => 'yii\grid\ActionColumn','template' => '{view}'],
            'id',

            ['class' => 'kartik\grid\EditableColumn', 'attribute' => 'firstname' ],
            ['class' => 'kartik\grid\EditableColumn', 'attribute' => 'lastname' ],
            ['class' => 'kartik\grid\EditableColumn', 'attribute' => 'email_address'],
            ['class' => 'kartik\grid\EditableColumn', 'attribute' => 'street_address' ],
            ['class' => 'kartik\grid\EditableColumn', 'attribute' => 'city' ],
            ['class' => 'kartik\grid\EditableColumn', 'attribute' => 'postcode' ],
            ['class' => 'kartik\grid\EditableColumn', 'attribute' => 'telephone' ],
            ['class' => 'kartik\grid\EditableColumn', 'attribute' => 'nick' ],
            // ['class' => 'kartik\grid\EditableColumn', 'attribute' => 'password', 'editableOptions' => [
            //     'asPopover' => true,
            //     'displayValue' => '***'
            //     ]],
    
//            'id',
//            'gender',
//            'firstname',
//            'lastname',
//            'dob',
//            'email_address:email',
//            'street_address',
            //'suburb',
//            'postcode',
//            'city',
            //'state',
//            'telephone',
            //'fax',
            //'password',
            //'country_id',
            //'zone_id',
//            'nick',
            'card_type',
            'card_discount',
            'card_nr',
            //['class' => 'kartik\grid\EditableColumn', 'attribute' => 'card_nr' ],
            // ['class' => 'kartik\grid\EditableColumn', 'attribute' => 'card_type', 'editableOptions' => [
            //     'asPopover' => false
            //     ] ],
            // ['class' => 'kartik\grid\EditableColumn', 'attribute' => 'card_discount', 'editableOptions' => [
            //     'asPopover' => false
            //     ] ],
            ['class' => 'yii\grid\ActionColumn','template' => '{update}'],
        ],
        'panel' => [
            'type' => GridView::TYPE_PRIMARY,
            'heading' => '<span class="glyphicon glyphicon-book"></span>  ' . Html::encode($this->title),
        ],
        // your toolbar can include the additional full export menu
        'toolbar' => [
            ExportMenu::widget([
                'dataProvider' => $dataProvider,
                'columns' => ['id','email_address','firstname','lastname','street_address','postcode','city'],
                'target' => ExportMenu::TARGET_BLANK,
                'fontAwesome' => true,
                'pjax' => false,
                'clearBuffers' => true,
                'showConfirmAlert' => false,
                'dropdownOptions' => [
                    'label' => 'Full',
                    'class' => 'btn btn-default',
                    'itemsBefore' => [
                        '<li class="dropdown-header">Export All Data</li>',
                    ],
                ],
            ]) ,
        ],
    ]); ?>

    <?php if(!Yii::$app->request->get('q')) echo "<h1>Search</h1>".$this->render('_search', ['model' => $searchModel]); ?>


</div>
