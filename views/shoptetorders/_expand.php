<?php
use yii\helpers\Html;
use kartik\tabs\TabsX;
use yii\helpers\Url;
use yii\data\ActiveDataProvider;
use app\models\ShoptetOrderItems;

    $dataProvider = new ActiveDataProvider([
        'query' => ShoptetOrderItems::find()->where([ 'order_id' => $model->order_id]),
    ]);
    // $dataProvider->pagination = false;
    $items = [
        [
            'label' => '<i class="glyphicon glyphicon-eur"></i> '. Html::encode('Items'),
            'content' => $this->render( '//shoptetorderitems/index' , [
                'dataProvider' => $dataProvider,
            ]),
        ],
        [
            'label' => '<i class="glyphicon glyphicon-book"></i> '. Html::encode('Details'),
            'content' =>  $this->render('//sql/stat1', ['form' => null, 'pall'=>$dataProvider, 'title' => 'Select * from shoptet_order_items where order_id = '.$model->order_id, 'sql' => '']),
        ],
    ];

echo TabsX::widget([
    'items' => $items,
    'position' => TabsX::POS_ABOVE,
    'encodeLabels' => false,
    'class' => 'tes',
    'pluginOptions' => [
        'bordered' => true,
        'sideways' => true,
        'enableCache' => false
    ],
]);
?>
