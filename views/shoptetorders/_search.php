<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model app\models\ShoptetOrdersSearch */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="shoptet-orders-search">

    <?php $form = ActiveForm::begin([
        'action' => ['index'],
        'method' => 'get',
    ]); ?>

    <?= $form->field($model, 'order_id') ?>

    <?= $form->field($model, 'code') ?>

    <?= $form->field($model, 'cdate') ?>

    <?= $form->field($model, 'status') ?>

    <?= $form->field($model, 'currency_code') ?>

    <?php // echo $form->field($model, 'currency_exchange_rate') ?>

    <?php // echo $form->field($model, 'customer_email') ?>

    <?php // echo $form->field($model, 'customer_phone') ?>

    <?php // echo $form->field($model, 'bill_name') ?>

    <?php // echo $form->field($model, 'bill_company') ?>

    <?php // echo $form->field($model, 'bill_street') ?>

    <?php // echo $form->field($model, 'bill_house_number') ?>

    <?php // echo $form->field($model, 'bill_city') ?>

    <?php // echo $form->field($model, 'bill_zip') ?>

    <?php // echo $form->field($model, 'bill_country') ?>

    <?php // echo $form->field($model, 'bill_company_id') ?>

    <?php // echo $form->field($model, 'bill_vat_id') ?>

    <?php // echo $form->field($model, 'customer_identification_number') ?>

    <?php // echo $form->field($model, 'delivery_name') ?>

    <?php // echo $form->field($model, 'delivery_company') ?>

    <?php // echo $form->field($model, 'delivery_street') ?>

    <?php // echo $form->field($model, 'delivery_house_number') ?>

    <?php // echo $form->field($model, 'delivery_city') ?>

    <?php // echo $form->field($model, 'delivery_zip') ?>

    <?php // echo $form->field($model, 'delivery_country') ?>

    <?php // echo $form->field($model, 'customer_ip_address') ?>

    <?php // echo $form->field($model, 'remark') ?>

    <?php // echo $form->field($model, 'shop_remark') ?>

    <?php // echo $form->field($model, 'referer') ?>

    <?php // echo $form->field($model, 'package_number') ?>

    <?php // echo $form->field($model, 'varchar1') ?>

    <?php // echo $form->field($model, 'varchar2') ?>

    <?php // echo $form->field($model, 'varchar3') ?>

    <?php // echo $form->field($model, 'text1') ?>

    <?php // echo $form->field($model, 'text2') ?>

    <?php // echo $form->field($model, 'text3') ?>

    <?php // echo $form->field($model, 'weight') ?>

    <?php // echo $form->field($model, 'total_price_with_vat') ?>

    <?php // echo $form->field($model, 'total_price_without_vat') ?>

    <?php // echo $form->field($model, 'total_price_vat') ?>

    <?php // echo $form->field($model, 'total_price_rounding') ?>

    <?php // echo $form->field($model, 'total_price_to_pay') ?>

    <?php // echo $form->field($model, 'paid')->checkbox() ?>

    <div class="form-group">
        <?= Html::submitButton('Search', ['class' => 'btn btn-primary']) ?>
        <?= Html::resetButton('Reset', ['class' => 'btn btn-default']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
