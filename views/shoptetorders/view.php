<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model app\models\ShoptetOrders */

$this->title = $model->order_id;
$this->params['breadcrumbs'][] = ['label' => 'Shoptet Orders', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
\yii\web\YiiAsset::register($this);
?>
<div class="shoptet-orders-view">

    <h1><?= Html::encode($this->title) ?></h1>

    <p>
        <?= Html::a('Update', ['update', 'id' => $model->order_id], ['class' => 'btn btn-primary']) ?>
        <?= Html::a('Delete', ['delete', 'id' => $model->order_id], [
            'class' => 'btn btn-danger',
            'data' => [
                'confirm' => 'Are you sure you want to delete this item?',
                'method' => 'post',
            ],
        ]) ?>
    </p>

    <?= DetailView::widget([
        'model' => $model,
        'attributes' => [
            'order_id',
            'code',
            'cdate',
            'status',
            'currency_code',
            'currency_exchange_rate',
            'customer_email:email',
            'customer_phone',
            'bill_name',
            'bill_company',
            'bill_street',
            'bill_house_number',
            'bill_city',
            'bill_zip',
            'bill_country',
            'bill_company_id',
            'bill_vat_id',
            'customer_identification_number',
            'delivery_name',
            'delivery_company',
            'delivery_street',
            'delivery_house_number',
            'delivery_city',
            'delivery_zip',
            'delivery_country',
            'customer_ip_address',
            'remark:ntext',
            'shop_remark:ntext',
            'referer:ntext',
            'package_number',
            'varchar1',
            'varchar2',
            'varchar3',
            'text1:ntext',
            'text2:ntext',
            'text3:ntext',
            'weight',
            'total_price_with_vat',
            'total_price_without_vat',
            'total_price_vat',
            'total_price_rounding',
            'total_price_to_pay',
            'paid:boolean',
        ],
    ]) ?>

</div>
