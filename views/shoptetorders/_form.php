<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model app\models\ShoptetOrders */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="shoptet-orders-form">

    <?php $form = ActiveForm::begin(); ?>

    <?= $form->field($model, 'code')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'cdate')->textInput() ?>

    <?= $form->field($model, 'status')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'currency_code')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'currency_exchange_rate')->textInput() ?>

    <?= $form->field($model, 'customer_email')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'customer_phone')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'bill_name')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'bill_company')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'bill_street')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'bill_house_number')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'bill_city')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'bill_zip')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'bill_country')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'bill_company_id')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'bill_vat_id')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'customer_identification_number')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'delivery_name')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'delivery_company')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'delivery_street')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'delivery_house_number')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'delivery_city')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'delivery_zip')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'delivery_country')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'customer_ip_address')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'remark')->textarea(['rows' => 6]) ?>

    <?= $form->field($model, 'shop_remark')->textarea(['rows' => 6]) ?>

    <?= $form->field($model, 'referer')->textarea(['rows' => 6]) ?>

    <?= $form->field($model, 'package_number')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'varchar1')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'varchar2')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'varchar3')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'text1')->textarea(['rows' => 6]) ?>

    <?= $form->field($model, 'text2')->textarea(['rows' => 6]) ?>

    <?= $form->field($model, 'text3')->textarea(['rows' => 6]) ?>

    <?= $form->field($model, 'weight')->textInput() ?>

    <?= $form->field($model, 'total_price_with_vat')->textInput() ?>

    <?= $form->field($model, 'total_price_without_vat')->textInput() ?>

    <?= $form->field($model, 'total_price_vat')->textInput() ?>

    <?= $form->field($model, 'total_price_rounding')->textInput() ?>

    <?= $form->field($model, 'total_price_to_pay')->textInput() ?>

    <?= $form->field($model, 'paid')->checkbox() ?>

    <div class="form-group">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
