<?php

use yii\helpers\Html;
use kartik\grid\GridView;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $searchModel app\models\ShoptetOrdersSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Shoptet Orders';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="shoptet-orders-index">

    <h1><?= Html::encode($this->title) ?></h1>
    <?php // echo $this->render('_search', ['model' => $searchModel]); ?>

    <p>
        <?php echo $result ?? ''; ?>
    </p>
    <p>
        <?php
            // Button with commands/shoptetordersimport
            echo Html::a('Shoptet Orders Import', ['runimport'], ['class' => 'btn btn-success']);
        ?>
    </p>

    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'pjax' => true,
        'pjaxSettings' => ['options' => ['id' => 'kv-pjax-container-shoptetorders']],
        'columns' => [
            [
                'class' => 'kartik\grid\ExpandRowColumn',
                'value' => function ($model, $key, $index, $column) {
                    return GridView::ROW_COLLAPSED;
                },
                'expandOneOnly' => false,
                'enableCache' => false,
                'detailUrl' => Url::to(['shoptetajaxdetail']),
            ],

            'order_id',
            [
                'attribute' => 'movement_id',
                'value' => function ($model) {
                    return $model->movement_id ? 
                    // link to movement detail
                    Html::a($model->movement_id, ['movement/index', 'MovementSearch[move_id]' => $model->movement_id]) :
                    // link to create movement, class btn
    ($model->status == 'Vyřízena' ?
                    Html::a('📎 Create', ['shoptetorders/create-movement', 'id' => $model->order_id], ['class' => 'btn btn-warning'])
                    : '');
                    
                },
                'format' => 'raw',
                
            ],
            'code',
            'cdate',
            'status',
            'currency_code',
            //'currency_exchange_rate',
            'customer_email:email',
            'customer_phone',
            //'bill_name',
            //'bill_company',
            //'bill_street',
            //'bill_house_number',
            //'bill_city',
            //'bill_zip',
            //'bill_country',
            //'bill_company_id',
            //'bill_vat_id',
            //'customer_identification_number',
            //'delivery_name',
            //'delivery_company',
            //'delivery_street',
            //'delivery_house_number',
            //'delivery_city',
            //'delivery_zip',
            //'delivery_country',
            //'customer_ip_address',
            'remark:ntext',
            //'shop_remark:ntext',
            //'referer:ntext',
            //'package_number',
            //'varchar1',
            //'varchar2',
            //'varchar3',
            //'text1:ntext',
            //'text2:ntext',
            //'text3:ntext',
            //'weight',
            'total_price_with_vat',
            //'total_price_without_vat',
            //'total_price_vat',
            //'total_price_rounding',
            //'total_price_to_pay',
            'paid:boolean',

            ['class' => 'yii\grid\ActionColumn'],
        ],
    ]); ?>
</div>
