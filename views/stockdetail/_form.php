<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model app\models\StockDetail */
/* @var $form yii\widgets\ActiveForm */

?>

<div class="stock-detail-form">

    <?php $form = ActiveForm::begin(); ?>

    <?= $form->errorSummary($model); ?>

    <?= $form->field($model, 'stock_id')->textInput(['maxlength' => true, 'placeholder' => 'Stock']) ?>

    <?= $form->field($model, 'sdescr')->textInput(['maxlength' => true, 'placeholder' => 'Sdescr']) ?>

        <?= $form->field($model, 'adr_id')->widget(\kartik\select2\Select2::class, [
        'data' => \yii\helpers\ArrayHelper::map(\app\models\Address::find()->asArray()->all(), 'adr_id', 'firma'),
        'options' => ['placeholder' => 'Firma filter'],
        'pluginOptions' => [
            'allowClear' => true
        ],
        'pluginEvents' => [
          "select2:select" => "function() { 
                var label=$('#muserconfig-adr :selected').parent().attr('label');
                document.getElementById('muserconfig-adr_id').value=this.value;
           }",
        ]
    ]); ?>

    <div class="form-group">
        <?= Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
