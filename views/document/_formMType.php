<div class="form-group" id="add-mtype">
<?php
use kartik\grid\GridView;
use kartik\builder\TabularForm;
use yii\data\ArrayDataProvider;
use yii\helpers\Html;
use yii\widgets\Pjax;

$dataProvider = new ArrayDataProvider([
    'allModels' => $row,
    'pagination' => [
        'pageSize' => -1
    ]
]);
echo TabularForm::widget([
    'dataProvider' => $dataProvider,
    'formName' => 'MType',
    'checkboxColumn' => false,
    'actionColumn' => false,
    'attributeDefaults' => [
        'type' => TabularForm::INPUT_TEXT,
    ],
    'attributes' => [
        'm_type_id' => ['type' => TabularForm::INPUT_HIDDEN],
        'name' => ['type' => TabularForm::INPUT_TEXT],
        'mdescr' => ['type' => TabularForm::INPUT_TEXT],
        'flag' => ['type' => TabularForm::INPUT_TEXT],
        'usergroup' => ['type' => TabularForm::INPUT_TEXT],
        'moving' => ['type' => TabularForm::INPUT_TEXT],
        'stock_id1' => ['type' => TabularForm::INPUT_TEXT],
        'stock_id2' => ['type' => TabularForm::INPUT_TEXT],
        'fifogroup' => ['type' => TabularForm::INPUT_TEXT],
        'mdescr2' => ['type' => TabularForm::INPUT_TEXT],
        'changeprice' => ['type' => TabularForm::INPUT_TEXT],
        'del' => [
            'type' => 'raw',
            'label' => '',
            'value' => function($model, $key) {
                return
                    Html::hiddenInput('Children[' . $key . '][id]', (!empty($model['id'])) ? $model['id'] : "") .
                    Html::a('<i class="glyphicon glyphicon-trash"></i>', '#', ['title' =>  'Delete', 'onClick' => 'delRowMType(' . $key . '); return false;', 'id' => 'mtype-del-btn']);
            },
        ],
    ],
    'gridSettings' => [
        'panel' => [
            'heading' => false,
            'type' => GridView::TYPE_DEFAULT,
            'before' => false,
            'footer' => false,
            'after' => Html::button('<i class="glyphicon glyphicon-plus"></i>' . 'Add M Type', ['type' => 'button', 'class' => 'btn btn-success kv-batch-create', 'onClick' => 'addRowMType()']),
        ]
    ]
]);
echo  "    </div>\n\n";
?>

