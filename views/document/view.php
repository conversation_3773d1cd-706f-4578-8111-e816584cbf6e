<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use kartik\grid\GridView;

/* @var $this yii\web\View */
/* @var $model app\models\Document */

$this->title = $model->name;
$this->params['breadcrumbs'][] = ['label' => 'Document', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="document-view">

    <div class="row">
        <div class="col-sm-9">
            <h2><?= 'Document'.' '. Html::encode($this->title) ?></h2>
        </div>
        <div class="col-sm-3" style="margin-top: 15px">
            
            <?= Html::a('Update', ['update', 'id' => $model->doc_id], ['class' => 'btn btn-primary']) ?>
            <?= Html::a('Delete', ['delete', 'id' => $model->doc_id], [
                'class' => 'btn btn-danger',
                'data' => [
                    'confirm' => 'Are you sure you want to delete this item?',
                    'method' => 'post',
                ],
            ])
            ?>
        </div>
    </div>

    <div class="row">
<?php 
    $gridColumn = [
        'doc_id',
        'name',
        'number',
    ];
    echo DetailView::widget([
        'model' => $model,
        'attributes' => $gridColumn
    ]);
?>
    </div>
    
    <div class="row">
<?php
if($providerMType->totalCount){
    $gridColumnMType = [
        ['class' => 'yii\grid\SerialColumn'],
            'm_type_id',
            'name',
            'mdescr',
                        'flag',
            'usergroup',
            'moving',
            'stock_id1',
            'stock_id2',
            'fifogroup',
            'mdescr2',
            'changeprice',
    ];
    echo Gridview::widget([
        'dataProvider' => $providerMType,
        'pjax' => true,
        'pjaxSettings' => ['options' => ['id' => 'kv-pjax-container-m-type']],
        'panel' => [
            'type' => GridView::TYPE_PRIMARY,
            'heading' => '<span class="glyphicon glyphicon-book"></span> ' . Html::encode('M Type'),
        ],
        'export' => false,
        'columns' => $gridColumnMType
    ]);
}
?>

    </div>
</div>
