<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model app\models\MDetail */
/* @var $form yii\widgets\ActiveForm */

?>

<div class="mdetail-form">

    <?php $form = ActiveForm::begin(); ?>

    <?= $form->errorSummary($model); ?>

    <?= $form->field($model, 'move_id')->textInput(['placeholder' => 'Move']) ?>

    <?= $form->field($model, 'mat_id')->textInput(['placeholder' => 'Mat']) ?>

    <?= $form->field($model, 'price')->textInput(['maxlength' => true, 'placeholder' => 'Price']) ?>

    <?= $form->field($model, 'pcs')->textInput(['placeholder' => 'Pcs']) ?>

    <?= $form->field($model, 'discount')->textInput(['maxlength' => true, 'placeholder' => 'Discount']) ?>

    <?= $form->field($model, 'tax')->textInput(['maxlength' => true, 'placeholder' => 'Tax']) ?>

    <?= $form->field($model, 'm_repli_id')->textInput(['placeholder' => 'M Repli']) ?>

    <?= $form->field($model, 'currency')->textInput(['maxlength' => true, 'placeholder' => 'Currency']) ?>

    <?= $form->field($model, 'p_repli_id')->textInput(['placeholder' => 'P Repli']) ?>

    <?= $form->field($model, 'fifo_currency')->textInput(['maxlength' => true, 'placeholder' => 'Fifo Currency']) ?>

    <?= $form->field($model, 'fifo_price')->textInput(['maxlength' => true, 'placeholder' => 'Fifo Price']) ?>

    <?= $form->field($model, 'fifo_move_id')->textInput(['placeholder' => 'Fifo Move']) ?>

    <?= $form->field($model, 'detail_info')->textInput(['maxlength' => true, 'placeholder' => 'Detail Info']) ?>

    <?= $form->field($model, 'fifo_repli_id')->textInput(['placeholder' => 'Fifo Repli']) ?>

    <?= $form->field($model, 'all1')->textInput(['placeholder' => 'All1']) ?>

    <div class="form-group">
        <?= Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
