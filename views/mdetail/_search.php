<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model app\models\MDetailSearch */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="form-mdetail-search">

    <?php $form = ActiveForm::begin([
        'action' => ['index'],
        'method' => 'get',
    ]); ?>

    <?= $form->field($model, 'move_id')->textInput(['placeholder' => 'Move']) ?>

    <?= $form->field($model, 'mat_id')->textInput(['placeholder' => 'Mat']) ?>

    <?= $form->field($model, 'price')->textInput(['maxlength' => true, 'placeholder' => 'Price']) ?>

    <?= $form->field($model, 'pcs')->textInput(['placeholder' => 'Pcs']) ?>

    <?= $form->field($model, 'discount')->textInput(['maxlength' => true, 'placeholder' => 'Discount']) ?>

    <?php /* echo $form->field($model, 'tax')->textInput(['maxlength' => true, 'placeholder' => 'Tax']) */ ?>

    <?php /* echo $form->field($model, 'm_repli_id')->textInput(['placeholder' => 'M Repli']) */ ?>

    <?php /* echo $form->field($model, 'currency')->textInput(['maxlength' => true, 'placeholder' => 'Currency']) */ ?>

    <?php /* echo $form->field($model, 'p_repli_id')->textInput(['placeholder' => 'P Repli']) */ ?>

    <?php /* echo $form->field($model, 'fifo_currency')->textInput(['maxlength' => true, 'placeholder' => 'Fifo Currency']) */ ?>

    <?php /* echo $form->field($model, 'fifo_price')->textInput(['maxlength' => true, 'placeholder' => 'Fifo Price']) */ ?>

    <?php /* echo $form->field($model, 'fifo_move_id')->textInput(['placeholder' => 'Fifo Move']) */ ?>

    <?php /* echo $form->field($model, 'detail_info')->textInput(['maxlength' => true, 'placeholder' => 'Detail Info']) */ ?>

    <?php /* echo $form->field($model, 'fifo_repli_id')->textInput(['placeholder' => 'Fifo Repli']) */ ?>

    <?php /* echo $form->field($model, 'all1')->textInput(['placeholder' => 'All1']) */ ?>

    <div class="form-group">
        <?= Html::submitButton('Search', ['class' => 'btn btn-primary']) ?>
        <?= Html::resetButton('Reset', ['class' => 'btn btn-default']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
