<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use kartik\grid\GridView;

/* @var $this yii\web\View */
/* @var $model app\models\MDetail */

$this->title = $model->move_id;
$this->params['breadcrumbs'][] = ['label' => 'M Detail', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="mdetail-view">

    <div class="row">
        <div class="col-sm-9">
            <h2><?= 'M Detail'.' '. Html::encode($this->title) ?></h2>
        </div>
    </div>

    <div class="row">
<?php 
    $gridColumn = [
        'move_id',
        'mat_id',
        'price',
        'pcs',
        'discount',
        'tax',
        'm_repli_id',
        'currency',
        'p_repli_id',
        'fifo_currency',
        'fifo_price',
        'fifo_move_id',
        'detail_info',
        'fifo_repli_id',
        'all1',
    ];
    echo DetailView::widget([
        'model' => $model,
        'attributes' => $gridColumn
    ]); 
?>
    </div>
</div>
