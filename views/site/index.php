<?php
use yii\helpers\Html;
use miloschuman\highcharts\Highcharts;
use miloschuman\highcharts\SeriesDataHelper;
use yii\web\JsExpression;
use app\controllers\EstockTools;
/* @var $this yii\web\View */

if( EstockTools::isUserB2b()) {
    $wtitle = "B2B.RACIO.COM";
    $isfb = false;
    $welcometext = "Welcome to b2b.racio.com. This version allows you to create orders, see your invoices, fresh product list and also backorder report.";
} else {
    $wtitle = "WSTOCK";
    $isfb = EstockTools::getUserRepliId() == 2 ? "https://www.facebook.com/wdl.hu":
        ( isset(Yii::$app->params['wstockCustomer']) && Yii::$app->params['wstockCustomer'] == 'wikarska' ? "https://www.facebook.com/wikarska.symbiotics" : "https://www.facebook.com/watchdeluxe" );

    $welcometext = "";
}

$this->title = '';
?>

<script src="https://code.highcharts.com/highcharts.js"></script>
<script src="https://code.highcharts.com/highcharts-more.js"></script>
<script src="https://code.highcharts.com/modules/exporting.js"></script>
<div class="site-index">
    <div class="body-content">

    <div class="row">
        <div class="col-sm-6">
             <div id="clock" style="min-width: 310px; margin: 0 auto"></div>
        </div>
        <div class="col-sm-6">
<!--             <video width="100%" controls>
  <source src="/video/wstockDev.mov" type="video/mp4">
Your browser does not support the video tag.
</video>
</div>
</div>
<div class="row">
        <div class="col-sm-12"> -->
            <?php if ($isfb ) {
            ?>
            <div id="fb-root"></div>
<script async defer crossorigin="anonymous" src="https://connect.facebook.net/sk_SK/sdk.js#xfbml=1&version=v5.0&appId=225704418947&autoLogAppEvents=1"></script>

<div class="fb-page" data-href="<?php echo $isfb; ?>" data-tabs="timeline" data-width="510" data-height="" data-small-header="true" data-adapt-container-width="true" data-hide-cover="true" data-show-facepile="false"><blockquote cite="<?php echo $isfb; ?>" class="fb-xfbml-parse-ignore"><a href="<?php echo $isfb; ?>">Watch de Luxe</a></blockquote></div>
        <?php } else { echo "<h3>".$welcometext."<h3>"; }
        ?>

        </div>
    </div>


<?php 
    $script = <<< JS

/**
 * Get the current time
 */
function getNow() {
    var now = new Date();

    return {
        hours: now.getHours() + now.getMinutes() / 60,
        minutes: now.getMinutes() * 12 / 60 + now.getSeconds() * 12 / 3600,
        seconds: now.getSeconds() * 12 / 60
    };
}

/**
 * Pad numbers
 */
function pad(number, length) {
    // Create an array of the remaining length + 1 and join it with 0's
    return new Array((length || 2) + 1 - String(number).length).join(0) + number;
}

var now = getNow();

// Create the chart
Highcharts.chart('clock', {

    chart: {
        type: 'gauge',
        plotBackgroundColor: null,
        plotBackgroundImage: null,
        plotBorderWidth: 0,
        plotShadow: false,
        height: '92%'
    },

    credits: {
        enabled: false
    },

    title: {
        text: ''
    },

    pane: {
        background: [{
            // default background
        }, {
            // reflex for supported browsers
            backgroundColor: Highcharts.svg ? {
                radialGradient: {
                    cx: 0.5,
                    cy: -0.4,
                    r: 1.9
                },
                stops: [
                    [0.5, 'rgba(255, 255, 255, 0.2)'],
                    [0.1, 'rgba(255, 255, 255, 0.2)']
                ]
            } : null
        }]
    },

    yAxis: {
        labels: {
            distance: -20
        },
        min: 0,
        max: 12,
        lineWidth: 0,
        showFirstLabel: false,

        minorTickInterval: 'auto',
        minorTickWidth: 1,
        minorTickLength: 5,
        minorTickPosition: 'inside',
        minorGridLineWidth: 0,
        minorTickColor: '#666',

        tickInterval: 1,
        tickWidth: 2,
        tickPosition: 'inside',
        tickLength: 10,
        tickColor: '#666',
        title: {
            text: '$wtitle',
            style: {
                color: '#BBB',
                fontWeight: 'bold',
                fontSize: '18px',
                lineHeight: '10px'
            },
            y: 10
        }
    },

    tooltip: {
        formatter: function () {
            return this.series.chart.tooltipText;
        }
    },

    series: [{
        data: [{
            id: 'hour',
            y: now.hours,
            dial: {
                radius: '60%',
                baseWidth: 4,
                baseLength: '95%',
                rearLength: 0
            }
        }, {
            id: 'minute',
            y: now.minutes,
            dial: {
                baseLength: '95%',
                rearLength: 0
            }
        }, {
            id: 'second',
            y: now.seconds,
            dial: {
                radius: '100%',
                baseWidth: 1,
                rearLength: '20%'
            }
        }],
        animation: false,
        dataLabels: {
            enabled: false
        }
    }]
},

// Move
function (chart) {
    setInterval(function () {

        now = getNow();

        if (chart.axes) { // not destroyed
            var hour = chart.get('hour'),
                minute = chart.get('minute'),
                second = chart.get('second'),
                // run animation unless we're wrapping around from 59 to 0
                animation = now.seconds === 0 ?
                    false : {
                        easing: 'easeOutBounce'
                    };

            // Cache the tooltip text
            chart.tooltipText =
                    pad(Math.floor(now.hours), 2) + ':' +
                    pad(Math.floor(now.minutes * 5), 2) + ':' +
                    pad(now.seconds * 5, 2);


            hour.update(now.hours, true, animation);
            minute.update(now.minutes, true, animation);
            second.update(now.seconds, true, animation);
        }

    }, 1000);

});

/**
 * Easing function from https://github.com/danro/easing-js/blob/master/easing.js
 */
Math.easeOutBounce = function (pos) {
    if ((pos) < (1 / 2.75)) {
        return (7.5625 * pos * pos);
    }
    if (pos < (2 / 2.75)) {
        return (7.5625 * (pos -= (1.5 / 2.75)) * pos + 0.75);
    }
    if (pos < (2.5 / 2.75)) {
        return (7.5625 * (pos -= (2.25 / 2.75)) * pos + 0.9375);
    }
    return (7.5625 * (pos -= (2.625 / 2.75)) * pos + 0.984375);
};


JS;
$this->registerJs($script);
?>


