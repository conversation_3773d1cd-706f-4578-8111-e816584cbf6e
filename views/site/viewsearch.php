<?php

use yii\helpers\Html;
use kartik\grid\GridView;
use yii\widgets\Pjax;
use yii\helpers\Url;


$this->title = 'Search results for '.$q;
$this->params['breadcrumbs'][] = $this->title;
?>
<div>

    <?php Pjax::begin(); ?>
    <?php // echo $this->render('_search', ['model' => $searchModel]); ?>

    <h4>Movement</h4>

    <?= GridView::widget([
        'dataProvider' => $m1,
        'columns' => [
            ['class' => 'yii\grid\ActionColumn', 'template' => '{info}',
			'buttons' => [
			        'info' => function ($url, $model) {
			            return Html::a('<span class="glyphicon glyphicon-info-sign"></span>', $url, [
			                        'title' => Yii::t('app', 'Info'),
			            ]);
			        }
			],
			'urlCreator' => function ($action, $model, $key, $index) {
        			if ($action === 'info') {
            			$url = Yii::$app->urlManager->createUrl(['movement/index',"MovementSearch[move_id]"=>$model['move_id']]);
            			            			// $url = Yii::$app->urlManager->createUrl(['movement/index?MovementSearch%5Bmove_id%5D='.$model['move_id']]);
            			return $url;
        			}
    			}
        ],

            'move_id',
            'number',
            'firma',
            'stock_id1',
            'stock_id2',
            'm_type_id',
            'd1',
            'text1',
            'text2'
        ],
    ]); ?>
    <?php Pjax::end(); ?>
</div>

<div>

    <?php Pjax::begin(); ?>
    <?php // echo $this->render('_search', ['model' => $searchModel]); ?>

    <h4>Movement details and product</h4>

    <?= GridView::widget([
        'dataProvider' => $m2, //move_id, mat_id, price, pcs, detail_info 
        'columns' => [
            ['class' => 'yii\grid\ActionColumn', 'template' => '{info}',
			'buttons' => [
			        'info' => function ($url, $model) {
			            return Html::a('<span class="glyphicon glyphicon-info-sign"></span>', $url, [
			                        'title' => Yii::t('app', 'Info'),
			            ]);
			        }
			],
			'urlCreator' => function ($action, $model, $key, $index) {
        			if ($action === 'info') {
            			$url = Yii::$app->urlManager->createUrl(['movement/index',"MovementSearch[move_id]"=>$model['move_id']]);
            			            			// $url = Yii::$app->urlManager->createUrl(['movement/index?MovementSearch%5Bmove_id%5D='.$model['move_id']]);
            			return $url;
        			}
    			}
        ],

            'move_id',
            'mat_id',
            'kod',
            'model',
            'price',
            'pcs',
            'detail_info',
            'thserial'
        ],
    ]); ?>
    <?php Pjax::end(); ?>
</div>

