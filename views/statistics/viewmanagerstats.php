<?php

use yii\helpers\Html;
use yii\helpers\Url;
use kartik\grid\GridView;
use kartik\export\ExportMenu;
use yii\widgets\ActiveForm;
use kartik\date\DatePicker;
use miloschuman\highcharts\SeriesDataHelper;
use miloschuman\highcharts\Highcharts;

// use miloschuman\highcharts\Highcharts;
// use miloschuman\highcharts\SeriesDataHelper;
// use miloschuman\highcharts\Highstock;


/* @var $this yii\web\View */
/* @var $model app\models\MDetail */

$this->title = "Manager statistics";
$this->params['breadcrumbs'][] = ['label' => 'Statistics', 'url' => ['viewmanagerstats']];
$this->params['breadcrumbs'][] = $this->title;
// echo "<pre>";
// print_r($pmonths);
// exit;

$f = ActiveForm::begin(); ?>
<div class="row">
	<?php echo Html::a('Today ', Url::current(['datefilter'=>'today' ]), ['class' => 'btn btn-success', 'id' =>'datefilterToday']);
    echo "&nbsp;&nbsp;&nbsp;";
	echo Html::a('This week ', Url::current(['datefilter'=>'thisweek' ]), ['class' => 'btn btn-success', 'id' =>'datefilterWeek']);
    echo "&nbsp;&nbsp;&nbsp;";
	echo Html::a('This month ', Url::current(['datefilter'=>'thismonth' ]), ['class' => 'btn btn-success', 'id' =>'datefilter1']);
    echo "&nbsp;&nbsp;&nbsp;";
    echo Html::a('Last month', Url::current(['datefilter'=>'lastmonth' ]), ['class' => 'btn btn-success', 'id' =>'datefilter2']);
    echo "&nbsp;&nbsp;&nbsp;";
    echo Html::a('This year', Url::current(['datefilter'=>'thisyear' ]), ['class' => 'btn btn-success', 'id' =>'datefilter3']);
    echo "&nbsp;&nbsp;&nbsp;";
    echo Html::a('Last year', Url::current(['datefilter'=>'lastyear' ]), ['class' => 'btn btn-success', 'id' =>'datefilter4']);
?>

<div class="row">
	<div class="col-md-4">
<?php
echo $f->field($form, 'd1')->widget(DatePicker::classname(),[
    'value' => Date('Y-m-d'),
    'options' => ['placeholder' => 'Date from...'],
    'pluginOptions' => [
        'todayHighlight' => true,
        'todayBtn' => true,
        'format' => 'yyyy-mm-dd',
        'autoclose' => true,
    ]
	]);
echo '</div><div class="col-md-4">';

echo $f->field($form, 'd2')->widget(DatePicker::classname(),[
    'value' => Date('Y-m-d'),
    'options' => ['placeholder' => 'Date to...'],
    'pluginOptions' => [
        'todayHighlight' => true,
        'todayBtn' => true,
        'format' => 'yyyy-mm-dd',
        'autoclose' => true,
    ]
	]);
echo '</div><div class="col-md-4">';
  echo $f->field($form, 'repli')->radioList( [0=>'SK', 2 => 'HU'],['separator'=>'&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'] );
echo '</div></div>';

?>
        <div class="form-group">
            <?= Html::submitButton('Show report ', ['class' => 'btn btn-danger']) ?>
        </div>
    <?php ActiveForm::end(); 


if(isset($pmonths)){

$seriesdata = [];
foreach ($pselect->getModels() as $key => $value) {
	$seriesdata[] = [
		'type' => 'column',
		'name' => $value['shopname'],
		'data' => [ intval(preg_replace('/[^0-9]/', '', $value['turnover']))/100,intval($value['costs']),intval($value['margin']),intval($value['brutprofit']),intval($value['Net profit'])],
		];
}
// echo "<pre>";
// var_dump($seriesdata);
// exit;
$cats = [];
foreach ($seriesdata as $key => $value) {
	$cats[] = $value['name'];
}

$tdata = [];
foreach ($pselect->getModels() as $key => $value) {
	$tdata[] = [
		'name' => $value['shopname'],
		'y' => intval(preg_replace('/[^0-9]/', '', $value['turnover']))/100,
		];
}
$seriesdata[] = 
[
                'type' => 'pie',
                'name' => 'Total shops',
                'data' => $tdata,
                'center' => [300, 20],
                'size' => 100,
                'showInLegend' => false,
                'dataLabels' => [
                    'enabled' => false,
                ],
];

echo Highcharts::widget([
    'scripts' => [
        'modules/exporting',
        'themes/grid-light',
    ],
    'options' => [
    	'xAxis' => [
            'categories' => ['Turnover', 'Costs', 'Margin', 'Brut profit', 'Net profit'],
        ],
        'title' => [
            'text' => 'Sales all',
        ],
        'series' => $seriesdata,
    
    ]
]);

// echo Highstock::widget([
//    'options' => [
//       'title' => ['text' => 'movements'],
//       'type' => 'line',
//         'yAxis' => [
//             ['title' => ['text' => 'OHLC'], 'height' => '60%'],
//             ['title' => ['text' => 'Volume'], 'top' => '65%', 'height' => '35%', 'offset' => 0],
//         ],
//       'series' => [

//             [
//                 'type' => 'line',
//                 'name' => 'Volume',
//                 'data' => new SeriesDataHelper($pmonths, ['days:date', 'turnover:int']),
//                 'yAxis' => 1,
//             ],
//         ]

//    ]
// ]);


// $gridColumn = [
// 	['attribute'=>'days', 'label'=>'Time'],
// 	'adr_id',
// 	'turnover',
// 	'shop',
// 	'costs',
// 	'margin',
// 	[
// 		'label' => 'turnover-margin',
// 		'content'=>function($data){
//             	//print_r($data);
// 			if( $data['margin']<$data['costs']){
// 				return "<span style=\"color:red\">".($data['margin']-$data['costs'])."</style>";
// 			}
// 			return "<span style=\"color:blue\">".($data['margin']-$data['costs'])."</style>";
// 		}
// 	],
// ]; 
?>

<?= GridView::widget([
	'dataProvider' => $pselect,
	// 'columns' => $gridColumn,
	'panel' => [
		'type' => GridView::TYPE_PRIMARY,
		'heading' => '<span class="glyphicon glyphicon-book"></span> SALES ALL',
	],
	'toolbar' => [
		'{export}',
	]
]); 
?>

<?= GridView::widget([
	'dataProvider' => $pmonths,
	// 'columns' => $gridColumn,
	'panel' => [
		'type' => GridView::TYPE_PRIMARY,
		'heading' => '<span class="glyphicon glyphicon-book"></span> SALES by MONTHS',
	],
	'toolbar' => [
		'{export}',
	]
]); 
?>


<!-- <?= GridView::widget([
	'dataProvider' => $peshop,
	// 'columns' => $gridColumn,
	'panel' => [
		'type' => GridView::TYPE_PRIMARY,
		'heading' => '<span class="glyphicon glyphicon-book"></span> Eshop sales in shop',
	],
	'toolbar' => [
		'{export}',
	]
]); 
?> -->


<?= GridView::widget([
	'dataProvider' => $pmov,
	// 'columns' => $gridColumn,
	'panel' => [
		'type' => GridView::TYPE_PRIMARY,
		'heading' => '<span class="glyphicon glyphicon-book"></span> Turnover by type of movement',
	],
	// 'export' => false,
	'toolbar' => [
		'{export}',
	]
	// 	ExportMenu::widget([
	// 		'dataProvider' => $pmov,
	// 		// 'columns' => $gridColumn,
	// 		'target' => ExportMenu::TARGET_BLANK,
	// 		'fontAwesome' => true,
	// 		'dropdownOptions' => [
	// 			'label' => 'Export',
	// 			'class' => 'btn btn-default',
	// 			'itemsBefore' => [
	// 				'<li class="dropdown-header">Export All Data</li>',
	// 			],
	// 		],
	// 	]) ,
	// ],
]); 
?>

<?= GridView::widget([
	'dataProvider' => $pbykod,
	// 'columns' => $gridColumn,
	'panel' => [
		'type' => GridView::TYPE_PRIMARY,
		'heading' => '<span class="glyphicon glyphicon-book"></span> Sales by kod',
	],
	'toolbar' => [
		'{export}',
	]
]); 
?>

<?php
// echo "<pre>";
// print_r($pbykod);
// echo "</pre>";

$seriesdata = [];
foreach ($pkod->getModels() as $key => $value) {
	$seriesdata[] = [
		'type' => 'column',
		'name' => $value['kod'],
		'data' => [ intval(preg_replace('/[^0-9]/', '', $value['turnover']))/100],
		];
}
// echo "<pre>";
// var_dump($seriesdata);
// exit;
$cats = [];
foreach ($seriesdata as $key => $value) {
	$cats[] = isset($value['kod'])?$value['kod']:'';
}


echo Highcharts::widget([
    'scripts' => [
        'modules/exporting',
        'themes/grid-light',
    ],
    'options' => [
    	'xAxis' => [
            'categories' => ['Turnover'],
        ],
        'title' => [
            'text' => 'Sales all shops by KOD',
        ],
        'series' => $seriesdata,
    
    ]
]);

?>


<?= GridView::widget([
	'dataProvider' => $pkod,
	// 'columns' => $gridColumn,
	'panel' => [
		'type' => GridView::TYPE_PRIMARY,
		'heading' => '<span class="glyphicon glyphicon-book"></span> Sum all shops by kod',
	],
	'toolbar' => [
		'{export}',
	]
]); 

}//isset data model provider
?>