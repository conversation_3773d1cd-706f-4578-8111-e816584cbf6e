<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use kartik\grid\GridView;
use kartik\export\ExportMenu;
use yii\widgets\ActiveForm;
use kartik\date\DatePicker;
use miloschuman\highcharts\Highcharts;
use miloschuman\highcharts\SeriesDataHelper;
use miloschuman\highcharts\Highstock;


/* @var $this yii\web\View */
/* @var $model app\models\MDetail */

$this->title = "Retail statistics";
$this->params['breadcrumbs'][] = ['label' => 'Statistics', 'url' => ['view']];
$this->params['breadcrumbs'][] = $this->title;
// echo "<pre>";
// print_r($pmonths);
// exit;

$f = ActiveForm::begin(); ?>

<div class="row">
	<div class="col-md-4">
<?php
echo $f->field($form, 'd1')->widget(DatePicker::classname(),[
    'value' => Date('Y-m-d'),
    'options' => ['placeholder' => 'Date from...'],
    'pluginOptions' => [
        'todayHighlight' => true,
        'todayBtn' => true,
        'format' => 'yyyy-mm-dd',
        'autoclose' => true,
    ]
	]);
echo '</div><div class="col-md-4">';

echo $f->field($form, 'd2')->widget(DatePicker::classname(),[
    'value' => Date('Y-m-d'),
    'options' => ['placeholder' => 'Date to...'],
    'pluginOptions' => [
        'todayHighlight' => true,
        'todayBtn' => true,
        'format' => 'yyyy-mm-dd',
        'autoclose' => true,
    ]
	]);
echo '</div><div class="col-md-4">';
  echo $f->field($form, 'repli')->radioList( [0=>'SK', 2 => 'HU'],['separator'=>'&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'] );
echo '</div></div>';

?>
        <div class="form-group">
            <?= Html::submitButton('Show report ', ['class' => 'btn btn-danger']) ?>
        </div>
    <?php ActiveForm::end(); 


if(isset($pmonths)){

// $xdata = [
//     ['date' => '2006-05-14T20:00:00-0400', 'open' => 67.37, 'high' => 68.38, 'low' => 67.12, 'close' => 67.79, 'volume' => 18921051],
//     ['date' => '2006-05-15T20:00:00-0400', 'open' => 68.1, 'high' => 68.25, 'low' => 64.75, 'close' => 64.98, 'volume' => 33470860],
//     ['date' => '2006-05-16T20:00:00-0400', 'open' => 64.7, 'high' => 65.7, 'low' => 64.07, 'close' => 65.26, 'volume' => 26941146],
//     ['date' => '2006-05-17T20:00:00-0400', 'open' => 65.68, 'high' => 66.26, 'low' => 63.12, 'close' => 63.18, 'volume' => 23524811],
//     ['date' => '2006-05-18T20:00:00-0400', 'open' => 63.26, 'high' => 64.88, 'low' => 62.82, 'close' => 64.51, 'volume' => 35221586],
//     ['date' => '2006-05-21T20:00:00-0400', 'open' => 63.87, 'high' => 63.99, 'low' => 62.77, 'close' => 63.38, 'volume' => 25680800],
//     ['date' => '2006-05-22T20:00:00-0400', 'open' => 64.86, 'high' => 65.19, 'low' => 63, 'close' => 63.15, 'volume' => 24814061],
//     ['date' => '2006-05-23T20:00:00-0400', 'open' => 62.99, 'high' => 63.65, 'low' => 61.56, 'close' => 63.34, 'volume' => 32722949],
//     ['date' => '2006-05-24T20:00:00-0400', 'open' => 64.26, 'high' => 64.45, 'low' => 63.29, 'close' => 64.33, 'volume' => 16563319],
//     ['date' => '2006-05-25T20:00:00-0400', 'open' => 64.31, 'high' => 64.56, 'low' => 63.14, 'close' => 63.55, 'volume' => 15464811],
// ];

// $xdataProvider = new \yii\data\ArrayDataProvider(['allModels' => $xdata]);

// echo Highstock::widget([
//     'options' => [
//         'title' => ['text' => 'Basic Example'],
//         'yAxis' => [
//             ['title' => ['text' => 'OHLC'], 'height' => '60%'],
//             ['title' => ['text' => 'Volume'], 'top' => '65%', 'height' => '35%', 'offset' => 0],
//         ],
//         'series' => [
//             [
//                 'type' => 'candlestick',
//                 'name' => 'OHLC',
//                 'data' => new SeriesDataHelper($xdataProvider, ['date:datetime', 'open', 'high', 'low', 'close']),
//             ],
//             [
//                 'type' => 'column',
//                 'name' => 'Volume',
//                 'data' => new SeriesDataHelper($xdataProvider, ['date:datetime', 'volume:int']),
//                 'yAxis' => 1,
//             ],
//         ]
//     ]
// ]);


// // this is the same data as above but without string keys and using a Unix timestamp for the date
// $xdata = [
//     [**********,67.37,68.38,67.12,67.79,18921051],
//     [**********,68.10,68.25,64.75,64.98,33470860],
//     [**********,64.70,65.70,64.07,65.26,26941146],
//     [**********,65.68,66.26,63.12,63.18,23524811],
//     [**********,63.26,64.88,62.82,64.51,35221586],
//     [**********,63.87,63.99,62.77,63.38,25680800],
//     [**********,64.86,65.19,63.00,63.15,24814061],
//     [**********,62.99,63.65,61.56,63.34,32722949],
//     [**********,64.26,64.45,63.29,64.33,16563319],
//     [**********,64.31,64.56,63.14,63.55,15464811],
// ];

// echo Highstock::widget([
//     'options' => [
//         'title' => ['text' => 'Numerically Indexed'],
//         'yAxis' => [
//             ['title' => ['text' => 'OHLC'], 'height' => '60%'],
//             ['title' => ['text' => 'Volume'], 'top' => '65%', 'height' => '35%', 'offset' => 0],
//         ],
//         'series' => [
//             [
//                 'type' => 'candlestick',
//                 'name' => 'OHLC',
//                 // just like before, only now the columns are referenced by array offset
//                 'data' => new SeriesDataHelper($xdata, ['0:timestamp', 1, 2, 3, 4]),
//             ],
//             [
//                 'type' => 'column',
//                 'name' => 'Volume',
//                 'data' => new SeriesDataHelper($xdata, ['0:timestamp', '5:int']),
//                 'yAxis' => 1,
//             ],
//         ]
//     ]
// ]);


// 	$series = [
// 		[ 'name'=>$data[0]['shop'], 'data' => new SeriesDataHelper($data, ['shop:date', 'turnover:int']), ]
// 	];
// 	$idx = $data[0]['shop'];
// 	foreach ($data as $key => $value) {
// 		if( $value['shop'] != $idx){
// 			$idx = $value['shop'];
// 			array_push($series, [ 'name'=>$idx, 'data' => new SeriesDataHelper($data, ['shop:date', 'turnover:int']), ]);
// 		}
// 	}

echo Highstock::widget([
   'options' => [
      'title' => ['text' => 'movements'],
      'type' => 'line',
        'yAxis' => [
            ['title' => ['text' => 'OHLC'], 'height' => '60%'],
            ['title' => ['text' => 'Volume'], 'top' => '65%', 'height' => '35%', 'offset' => 0],
        ],
      'series' => [

            [
                'type' => 'line',
                'name' => 'Volume',
                'data' => new SeriesDataHelper($pmonths, ['days:date', 'turnover:int']),
                'yAxis' => 1,
            ],
        ]

   ]
]);


$gridColumn = [
	['attribute'=>'days', 'label'=>'Time'],
	'adr_id',
	'turnover',
	'shop',
	'costs',
	'margin',
	[
		'label' => 'turnover-margin',
		'content'=>function($data){
            	//print_r($data);
			if( $data['margin']<$data['costs']){
				return "<span style=\"color:red\">".($data['margin']-$data['costs'])."</style>";
			}
			return "<span style=\"color:blue\">".($data['margin']-$data['costs'])."</style>";
		}
	],
]; 
?>
<?= GridView::widget([
	'dataProvider' => $pmonths,
	'columns' => $gridColumn,
	'panel' => [
		'type' => GridView::TYPE_PRIMARY,
		'heading' => '<span class="glyphicon glyphicon-book"></span> SALES by MONTHS',
	],
	'toolbar' => [
		ExportMenu::widget([
			'dataProvider' => $pmonths,
			'columns' => $gridColumn,
			'target' => ExportMenu::TARGET_BLANK,
			'fontAwesome' => true,
            'pjax' => false,
            'clearBuffers' => true,
			'dropdownOptions' => [
				'label' => 'Full',
				'class' => 'btn btn-default',
				'itemsBefore' => [
					'<li class="dropdown-header">Export All Data</li>',
				],
			],
		]) ,
	],
]); 
?>

<?= GridView::widget([
	'dataProvider' => $pall,
	'columns' => $gridColumn,
	'panel' => [
		'type' => GridView::TYPE_PRIMARY,
		'heading' => '<span class="glyphicon glyphicon-book"></span> SALES ALL',
	],
	'toolbar' => [
		ExportMenu::widget([
			'dataProvider' => $pall,
			'columns' => $gridColumn,
			'target' => ExportMenu::TARGET_BLANK,
            'pjax' => false,
            'clearBuffers' => true,
			'fontAwesome' => true,
			'dropdownOptions' => [
				'label' => 'Full',
				'class' => 'btn btn-default',
				'itemsBefore' => [
					'<li class="dropdown-header">Export All Data</li>',
				],
			],
		]) ,
	],
]); 

}//isset data model provider
?>
