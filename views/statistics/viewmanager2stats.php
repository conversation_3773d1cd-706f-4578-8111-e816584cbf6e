<?php

use yii\helpers\Html;
use yii\helpers\Url;
use kartik\grid\GridView;
use kartik\export\ExportMenu;
use yii\widgets\ActiveForm;
use kartik\date\DatePicker;
use miloschuman\highcharts\SeriesDataHelper;
use miloschuman\highcharts\Highcharts;

// use miloschuman\highcharts\Highcharts;
// use miloschuman\highcharts\SeriesDataHelper;
// use miloschuman\highcharts\Highstock;


/* @var $this yii\web\View */
/* @var $model app\models\MDetail */

$this->title = "Retail statistics  ".($_GET['datefilter'] ?? '');
$this->params['breadcrumbs'][] = ['label' => 'Statistics', 'url' => ['viewmanager2stats']];
$this->params['breadcrumbs'][] = $this->title;
// echo "<pre>";
// print_r($pmonths);
// exit;

$f = ActiveForm::begin(); ?>
<div class="row">
	<?php echo Html::a('Today ', Url::current(['datefilter'=>'today' ]), ['class' => 'btn btn-success', 'id' =>'datefilterToday']);
    echo "&nbsp;&nbsp;&nbsp;";
	echo Html::a('This week ', Url::current(['datefilter'=>'thisweek' ]), ['class' => 'btn btn-success', 'id' =>'datefilterWeek']);
    echo "&nbsp;&nbsp;&nbsp;";
	echo Html::a('This month ', Url::current(['datefilter'=>'thismonth' ]), ['class' => 'btn btn-success', 'id' =>'datefilter1']);
    echo "&nbsp;&nbsp;&nbsp;";
    echo Html::a('Last month', Url::current(['datefilter'=>'lastmonth' ]), ['class' => 'btn btn-success', 'id' =>'datefilter2']);
    echo "&nbsp;&nbsp;&nbsp;";
    echo Html::a('This year', Url::current(['datefilter'=>'thisyear' ]), ['class' => 'btn btn-success', 'id' =>'datefilter3']);
    echo "&nbsp;&nbsp;&nbsp;";
    echo Html::a('Last year', Url::current(['datefilter'=>'lastyear' ]), ['class' => 'btn btn-success', 'id' =>'datefilter4']);
?>

</div>
<div class="row">
	<div class="col-md-4">
<?php
echo $f->field($form, 'd1')->widget(DatePicker::classname(),[
    'value' => Date('Y-m-d'),
    'options' => ['placeholder' => 'Date from...'],
    'pluginOptions' => [
        'todayHighlight' => true,
        'todayBtn' => true,
        'format' => 'yyyy-mm-dd',
        'autoclose' => true,
    ]
	]);
echo '</div><div class="col-md-4">';

echo $f->field($form, 'd2')->widget(DatePicker::classname(),[
    'value' => Date('Y-m-d'),
    'options' => ['placeholder' => 'Date to...'],
    'pluginOptions' => [
        'todayHighlight' => true,
        'todayBtn' => true,
        'format' => 'yyyy-mm-dd',
        'autoclose' => true,
    ]
	]);
echo '</div><div class="col-md-4">';
  echo $f->field($form, 'repli')->radioList( [0=>'SK', 2 => 'HU'],['separator'=>'&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'] );
echo '</div></div>';
?>
        <div class="form-group">
            <?= Html::submitButton('Show report ', ['class' => 'btn btn-danger']) ?>
        </div>
    <?php ActiveForm::end(); 

if(isset($pall)){
?>


<?= GridView::widget([
	'dataProvider' => $pall,
	// 'columns' => $gridColumn,
	'panel' => [
		'type' => GridView::TYPE_PRIMARY,
		'heading' => '<span class="glyphicon glyphicon-book"></span> SALES grouped',
	],
	'toolbar' => [
		'{export}',
	]
]); 
?>

<?= GridView::widget([
	'dataProvider' => $pall2,
	// 'columns' => $gridColumn,
	'panel' => [
		'type' => GridView::TYPE_PRIMARY,
		'heading' => '<span class="glyphicon glyphicon-book"></span> SALES by model',
	],
	'toolbar' => [
		'{export}',
	]
]); 
}
?>
