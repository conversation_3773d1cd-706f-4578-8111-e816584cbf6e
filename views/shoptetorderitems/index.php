<?php

use yii\helpers\Html;
use yii\grid\GridView;

/* @var $this yii\web\View */
/* @var $searchModel app\models\ShoptetOrderItemsSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Shoptet Order Items';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="shoptet-order-items-index">

    <!-- <h1><?= Html::encode($this->title) ?></h1> -->
    <?php // echo $this->render('_search', ['model' => $searchModel]); ?>

    <!-- <p> -->
        <!-- <?= Html::a('Create Shoptet Order Items', ['create'], ['class' => 'btn btn-success']) ?> -->
    <!-- </p> -->

    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'columns' => [
            'otype',
            'name',
            'amount',
            'ocode',
            'variant_name',
            //'ean',
            //'plu',
            //'manufacturer',
            //'supplier',
            //'unit',
            //'weight',
            'status',
            // 'discount',
            // 'unit_price_with_vat',
            //'unit_price_without_vat',
            //'unit_price_vat',
            //'unit_price_vat_rate',
            'unit_discount_price_with_vat',
            //'unit_discount_price_without_vat',
            //'total_price_with_vat',
            //'total_price_without_vat',
            //'total_price_vat',
            //'total_price_vat_rate',
            //'surcharges:ntext',
        ],
    ]); ?>
</div>
