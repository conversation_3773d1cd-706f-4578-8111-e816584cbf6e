<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model app\models\ShoptetOrderItems */

$this->title = $model->name;
$this->params['breadcrumbs'][] = ['label' => 'Shoptet Order Items', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
\yii\web\YiiAsset::register($this);
?>
<div class="shoptet-order-items-view">

    <h1><?= Html::encode($this->title) ?></h1>

    <p>
        <?= Html::a('Update', ['update', 'id' => $model->item_id], ['class' => 'btn btn-primary']) ?>
        <?= Html::a('Delete', ['delete', 'id' => $model->item_id], [
            'class' => 'btn btn-danger',
            'data' => [
                'confirm' => 'Are you sure you want to delete this item?',
                'method' => 'post',
            ],
        ]) ?>
    </p>

    <?= DetailView::widget([
        'model' => $model,
        'attributes' => [
            'item_id',
            'order_id',
            'otype',
            'name',
            'amount',
            'ocode',
            'variant_name',
            'ean',
            'plu',
            'manufacturer',
            'supplier',
            'unit',
            'weight',
            'status',
            'discount',
            'unit_price_with_vat',
            'unit_price_without_vat',
            'unit_price_vat',
            'unit_price_vat_rate',
            'unit_discount_price_with_vat',
            'unit_discount_price_without_vat',
            'total_price_with_vat',
            'total_price_without_vat',
            'total_price_vat',
            'total_price_vat_rate',
            'surcharges:ntext',
        ],
    ]) ?>

</div>
