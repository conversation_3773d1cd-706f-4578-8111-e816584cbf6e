<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model app\models\ShoptetOrderItems */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="shoptet-order-items-form">

    <?php $form = ActiveForm::begin(); ?>

    <?= $form->field($model, 'order_id')->textInput() ?>

    <?= $form->field($model, 'otype')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'name')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'amount')->textInput() ?>

    <?= $form->field($model, 'ocode')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'variant_name')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'ean')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'plu')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'manufacturer')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'supplier')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'unit')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'weight')->textInput() ?>

    <?= $form->field($model, 'status')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'discount')->textInput() ?>

    <?= $form->field($model, 'unit_price_with_vat')->textInput() ?>

    <?= $form->field($model, 'unit_price_without_vat')->textInput() ?>

    <?= $form->field($model, 'unit_price_vat')->textInput() ?>

    <?= $form->field($model, 'unit_price_vat_rate')->textInput() ?>

    <?= $form->field($model, 'unit_discount_price_with_vat')->textInput() ?>

    <?= $form->field($model, 'unit_discount_price_without_vat')->textInput() ?>

    <?= $form->field($model, 'total_price_with_vat')->textInput() ?>

    <?= $form->field($model, 'total_price_without_vat')->textInput() ?>

    <?= $form->field($model, 'total_price_vat')->textInput() ?>

    <?= $form->field($model, 'total_price_vat_rate')->textInput() ?>

    <?= $form->field($model, 'surcharges')->textarea(['rows' => 6]) ?>

    <div class="form-group">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
