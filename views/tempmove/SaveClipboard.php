<?php

use app\controllers\EstockTools;
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use kartik\date\DatePicker;
//use kartikorm\ActiveForm;

$this->title = 'FINISH, SAVE Clipboard to Movement';
$this->params['breadcrumbs'][] = $this->title;

/* @var $this yii\web\View */
/* @var $model app\models\SaveClipForm */
/* @var $form ActiveForm */
?>
<div class="SaveClipboard">

    <?php $form = ActiveForm::begin(); ?>

    <?= $model->mtype == 20 && EstockTools::isUserShop() ? $form->field($model, 'adr_id')
        ->dropDownList( EstockTools::getShopParams()['shopAdrList'], ['prompt' => 'Vyberte adresu dodavatela'] )
    : '' ?>

<?= $form->field($model, 'd1')->widget(DatePicker::class,[
    'name' => 'd1',
    'value' => Date('Y-m-d'),
    'options' => ['placeholder' => 'Enter date ...'],
    'pluginOptions' => [
        'todayHighlight' => true,
        'todayBtn' => true,
        'format' => 'yyyy-mm-dd',
        'autoclose' => true,
    ]
]);?>

        <?= $form->field($model, 'text1') ?>
        <?= $form->field($model, 'text2') ?>
		<?= $form->field($model, 'mtype',['inputOptions' => ['value' => Yii::$app->request->get('mtype', 1)]])->hiddenInput()->label(false) ?>    
		<?= $form->field($model, 'clip_id',['inputOptions' => ['value' => Yii::$app->request->get('clip_id')]])->hiddenInput()->label(false) ?>
        <?php
            if( $model->mtype == 87 or $model->mtype == 147 ){
                echo $form->field($model, 'x')->radioList(['0'=>"DPH zahrnutá v cenách",'9'=>'Ceny chcem BEZ DPH (export)']);
                echo $form->field($model, 'also_invoice')->checkbox();
            };
            if( $model->mtype == 97 ){
                echo $form->field($model, 'x')->radioList(['0'=>"DPH zahrnutá v cenách",'9'=>'Ceny chcem BEZ DPH (export)']);
                echo $form->field($model, 'also_creditnote')->checkbox();
            };
        ?>
        <?php
            if( in_array($model->mtype,[14,27, 16]) ){ // 14 - prevod, 27 - predfaktura, 16 - proforma szamla
                $rblist = [];
                if( EstockTools::getUserRepliId() == 0 ){
                    $shopmsg = "<p class='alert alert-warning'>Nastavte finálny sklad, alebo nechajte svoj, ak sa to má vrátiť k vám</p>";
                } else {
                    $shopmsg = "<p class='alert alert-warning'>Please set final stock, or leave setting to your stock, if you will commit receiving back to you.</p>";
                }    

                if( isset(EstockTools::getShopParams()['shopWhereCanTransfer'])) { //Obchody maju specialne nastavenia
                    // HU nechce final stock, lebo to ide do skladu, kde to je potrebne ak to je SERH
                    if( Yii::$app->request->get('stock_id2') == 'SERH' or Yii::$app->request->get('stock_id1') == 'SERH' ) {
                        $rblist['x'] = 'Final stock is '. Yii::$app->request->get('stock_id2');
                        $model->final_stock = 'x';    
                        $shopmsg = '';
                    // SK nechce final stock, ak je to pohyb zo SERV na ich sklad
                    } elseif( Yii::$app->request->get('stock_id1') == 'SERV' ) {
                        $rblist['x'] = 'Final stock is '. Yii::$app->request->get('stock_id2');
                        $model->final_stock = 'x';    
                        $shopmsg = '';
                    // HU nechce final stock, lebo to ide do skladu, kde to je potrebne ak to je S2 alebo S4
                    } elseif( Yii::$app->request->get('stock_id1') == 's2' 
                        or Yii::$app->request->get('stock_id2') == 's2' 
                        or Yii::$app->request->get('stock_id1') == 's4' 
                        or Yii::$app->request->get('stock_id2') == 's4' ) {
                        $rblist['x'] = 'Final stock is '. Yii::$app->request->get('stock_id2');
                        $model->final_stock = 'x';    
                        $shopmsg = '';
                    } else {
                        foreach( EstockTools::getShopParams()['shopWhereCanTransfer'] as $key => $value ){
                            if ( $value['mystock'] == 1 ) {
                                $model->final_stock = $value['stock_id'];
                            }
                            $rblist[$value['stock_id']] = $value['stock_id'] . '  ' . $value['sdescr'];
                        }    
                    }
                } else { //regular wstock nastavenia
                    //HU chce final stock pre stock_id2=OTW
                    if( Yii::$app->request->get('stock_id2') == 'OTW' &&  EstockTools::getUserRepliId() == 2 ) {
                        $rblist['s2'] ='s2 H office raktár';
                        $rblist['s4'] ='s4 HU office RACIO Hungary';
                        $rblist['SWE'] = 'SWE West-watch';
                        $rblist['SWA'] = 'SWA Watch Arena';
                        $rblist['SRH1'] = 'SRH1 WDL Andrassy';
                        $rblist['STHH'] = 'STHH TH Shop Kft'; 
                    }
                    $rblist['x'] = 'Final stock is '. Yii::$app->request->get('stock_id2') . ' and you should confirm receiving later';
                    $rblist[Yii::$app->request->get('stock_id1')] = 'Will go back to '. Yii::$app->request->get('stock_id1') . ' (reservation, this movement will be DELETED after confirm!)';
                    $model->final_stock = 'x';
                }
            //    print_r($rblist);
                echo $form->field($model, 'final_stock')->radioList($rblist,['separator'=>'&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;']);
                if ( EstockTools::isUserShop() ){
                    echo $shopmsg;
                }
            };
        ?>     
        <div class="form-group">
            <?= Html::submitButton('Save clipboard '.Yii::$app->request->get('mtypename'), ['class' => 'btn btn-danger']) ?>
        </div>
    <?php ActiveForm::end(); ?>

</div><!-- SaveClipboard -->
