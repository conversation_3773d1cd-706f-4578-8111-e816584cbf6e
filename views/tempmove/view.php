<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use kartik\grid\GridView;

/* @var $this yii\web\View */
/* @var $model app\models\Tempmove */

$this->title = $model->mat_id;
$this->params['breadcrumbs'][] = ['label' => 'Tempmove', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="tempmove-view">

    <div class="row">
        <div class="col-sm-9">
            <h2><?= 'Tempmove'.' '. Html::encode($this->title) ?></h2>
        </div>
        <div class="col-sm-3" style="margin-top: 15px">
<?=             
             Html::a('<i class="fa glyphicon glyphicon-hand-up"></i> ' . 'PDF', 
                ['pdf', 'mat_id' => $model->mat_id, 'user_name' => $model->user_name, 'clip_id' => $model->clip_id, 'tempmove_info' => $model->tempmove_info],
                [
                    'class' => 'btn btn-danger',
                    'target' => '_blank',
                    'data-toggle' => 'tooltip',
                    'title' => 'Will open the generated PDF file in a new window'
                ]
            )?>
            
            <?= Html::a('Update', ['update', 'mat_id' => $model->mat_id, 'user_name' => $model->user_name, 'clip_id' => $model->clip_id, 'tempmove_info' => $model->tempmove_info], ['class' => 'btn btn-primary']) ?>
            <?= Html::a('Delete', ['delete', 'mat_id' => $model->mat_id, 'user_name' => $model->user_name, 'clip_id' => $model->clip_id, 'tempmove_info' => $model->tempmove_info], [
                'class' => 'btn btn-danger',
                'data' => [
                    'confirm' => 'Are you sure you want to delete this item?',
                    'method' => 'post',
                ],
            ])
            ?>
        </div>
    </div>

    <div class="row">
<?php 
    $gridColumn = [
        'mat_id',
        'price',
        'pcs',
        'discount',
        'tax',
        ['attribute' => 'act_price', 'visible' => false],
        'currency',
        ['attribute' => 'all1', 'visible' => false],
        ['attribute' => 'all2', 'visible' => false],
        ['attribute' => 'all3', 'visible' => false],
        ['attribute' => 'all4', 'visible' => false],
        ['attribute' => 'all5', 'visible' => false],
        'ean13',
        'kod',
        'model',
        'pdescr',
        'unit',
        'thserial',
        ['attribute' => 'user_name', 'visible' => false],
        ['attribute' => 'clip_id', 'visible' => false],
        ['attribute' => 'd1', 'visible' => false],
        ['attribute' => 'd2', 'visible' => false],
        ['attribute' => 'd3', 'visible' => false],
        ['attribute' => 'text1', 'visible' => false],
        ['attribute' => 'text2', 'visible' => false],
        ['attribute' => 'm_type_id', 'visible' => false],
        ['attribute' => 'stock_id1', 'visible' => false],
        ['attribute' => 'stock_id2', 'visible' => false],
        ['attribute' => 'adr_id', 'visible' => false],
        ['attribute' => 'price_type', 'visible' => false],
        ['attribute' => 'tempmove_info', 'visible' => false],
        ['attribute' => 'fifo_currency', 'visible' => false],
        ['attribute' => 'fifo_price', 'visible' => false],
        ['attribute' => 'fifo_move_id', 'visible' => false],
        ['attribute' => 'nr_counter', 'visible' => false],
    ];
    echo DetailView::widget([
        'model' => $model,
        'attributes' => $gridColumn
    ]);
?>
    </div>
</div>
