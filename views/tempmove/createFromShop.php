<?php

use yii\helpers\Html;


/* @var $this yii\web\View */
/* @var $model app\models\Tempmove */

$this->title = 'Add to '.Yii::$app->request->queryParams['mtypename'];
$this->params['breadcrumbs'][] = ['label' => 'Clipboards', 'url' => ['shop']];
$this->params['breadcrumbs'][] = $this->title;
// $model->m_type_id=Yii::$app->request->queryParams['mtype'];
// $model->clip_id=Yii::$app->request->queryParams['clip_id'];

?>
<div class="tempmove-create">

    <?= $this->render('_form', [
        'model' => $model,
        'shopPrice' => $shopPrice
    ]) ?>

</div>

<div class="tempmove-create">

    <?= $this->render('../product/indexclipsearch', [
    	'searchModel' => ($searchModel ?? null),
        'dataProvider' => ($dataProvider ?? null),
    ]) ?>

</div>
