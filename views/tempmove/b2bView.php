<?php

/* @var $this yii\web\View */
/* @var $searchModel app\models\TempmoveSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

use yii\helpers\Html;
use kartik\export\ExportMenu;
use kartik\grid\GridView;
use kartik\tabs\TabsX;


$this->title = 'Clipboards';
$this->params['breadcrumbs'][] = $this->title;
// $search = "$('.search-button').click(function(){
// 	$('.search-form').toggle(1000);
// 	return false;
// });";
// $this->registerJs($search);

?>
<div class="tempmove-index">

    <?php 
    $gridColumn = [
        [
            'class' => 'yii\grid\ActionColumn',
            'template' => '{update}'
        ],        
        'mat_id',
        'kod',
        'model',
        'pdescr',
        'price',
        'pcs',
        'discount',
        // 'tax',
        ['attribute' => 'act_price', 'visible' => false],
        'currency',
        'tempmove_info',
        ['attribute' => 'all1', 'visible' => false],
        ['attribute' => 'all2', 'visible' => false],
        ['attribute' => 'all3', 'visible' => false],
        ['attribute' => 'all4', 'visible' => false],
        ['attribute' => 'all5', 'visible' => false],
        // 'ean13',
        // 'unit',
        // ['attribute' => 'user_name', 'visible' => true],
        // ['attribute' => 'clip_id', 'visible' => true],
        ['attribute' => 'd1', 'visible' => false],
        ['attribute' => 'd2', 'visible' => false],
        ['attribute' => 'd3', 'visible' => false],
        ['attribute' => 'text1', 'visible' => false],
        ['attribute' => 'text2', 'visible' => false],
        // ['attribute' => 'm_type_id', 'visible' => true],
        // ['attribute' => 'stock_id1', 'visible' => true],
        // ['attribute' => 'stock_id2', 'visible' => true],
        ['attribute' => 'adr_id', 'visible' => false],
        ['attribute' => 'price_type', 'visible' => false],
        ['attribute' => 'tempmove_info', 'visible' => false],
        ['attribute' => 'fifo_currency', 'visible' => false],
        ['attribute' => 'fifo_price', 'visible' => false],
        ['attribute' => 'fifo_move_id', 'visible' => false],
        ['attribute' => 'nr_counter', 'visible' => false],
        [
            'class' => 'yii\grid\ActionColumn',
            'template' => '{delete}'
        ],        

    ]; 

    function echoGrid($dataProvider,$dataSumProvider,$searchModel,$gridColumn,$mtypename,$mtype,$fromstock,$tostock){
        $ret = "";
        if( empty($dataProvider) ){
//        if( sizeof($dataProvider) == 0 ){
            $ret .= '<i class="glyphicon glyphicon-info-sign"></i> Empty' ;
            $ret .= "<div>". Html::a('Add one model to '.$mtypename, ['createfromb2b','mtype'=>$mtype, 'stock_id1' =>$fromstock, 'stock_id2' =>$tostock, 'mtypename' =>$mtypename, 'clip_id'=> '-1' ], ['class' => 'btn btn-success', 'id' =>'createBtn'.$mtypename])."</div>";
            $oneclip = null;
        } else {
               $ret .= GridView::widget([
                'dataProvider' => $dataProvider,
                'filterModel' => $searchModel,
                'columns' => $gridColumn,
                'pjax' => true,
                'pjaxSettings' => ['options' => ['id' => 'kv-pjax-container-tempmove']],
                'panel' => [
                    'type' => GridView::TYPE_PRIMARY,
                    'heading' => '<span class="glyphicon glyphicon-book"></span>  ' . $mtypename . ' PCS: <b>' . $dataSumProvider['xpcs'] 
                . '</b> TOTAL: <b>' . $dataSumProvider['xtotal'] 
                . '</b> With VAT: <b>' . $dataSumProvider['xtotaltax'] .'</b>' ,

                ],
                            // your toolbar can include the additional full export menu
                'toolbar' => [
                    ExportMenu::widget([
                        'dataProvider' => $dataProvider,
                        'columns' => $gridColumn,
                        'target' => ExportMenu::TARGET_BLANK,
                        'pjax' => false,
                        'clearBuffers' => true,
                        'fontAwesome' => true,
                        'dropdownOptions' => [
                            'label' => 'Full',
                            'class' => 'btn btn-default',
                            'itemsBefore' => [
                                '<li class="dropdown-header">Export All Data</li>',
                            ],
                        ],
                    ]) ,
                ],
            ]);

                      $ret .= "<div class=\"col-sm-6\">". Html::a('Add one model to '.$mtypename, ['createfromb2b','mtype'=>$mtype, 'mtypename' =>$mtypename, 'clip_id'=>$dataProvider === null ? '-1' :$dataProvider->getModels()[0]->clip_id, 'stock_id1' => $dataProvider->getModels()[0]->stock_id1, 'stock_id2' => $dataProvider->getModels()[0]->stock_id2], ['class' => 'btn btn-success', 'id' =>'createBtn'.$mtypename])."</div>";
       $ret .= "<div class=\"col-sm-6\">". Html::a('Save '.$mtypename, ['saveclipboard','mtype'=>$mtype, 'mtypename' =>$mtypename, 'clip_id'=>$dataProvider === null ? '-1' :$dataProvider->getModels()[0]->clip_id, 'stock_id1' => $dataProvider->getModels()[0]->stock_id1, 'stock_id2' => $dataProvider->getModels()[0]->stock_id2], ['class' => 'btn btn-danger', 'id' =>'createBtn'.$mtypename])."</div>";

       }

       return $ret;

   }

    $items = [];

    foreach ($mtypes as $key => $value) {
        if( strval($tabIndex) === strval($value[0].$value[2].$value[3]) || $tabIndex === 0 ) {
            $active = true;
            $tabIndex = -1;
        } else {
            $active = false;
        }
        $items[] = [
            'label'=> isset($dataSumProvider[$key]['xpcs']) ?  '<span class="badge btn-success">' . $dataSumProvider[$key]['xpcs']. '</span> '.$value[1] : $value[1],   
            'content'=>  echoGrid($dataProvider[$key],$dataSumProvider[$key],$searchModel,$gridColumn,$value[1],$value[0],$value[2],$value[3]),
             'active'=>$active,
        ];
    };
// exit;
    


// Above
echo TabsX::widget([
    'items'=>$items,
    'position'=>TabsX::POS_ABOVE,
    'bordered'=>true,
    'encodeLabels'=>false
]);

?>

</div>
