<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use kartik\typeahead\Typeahead;
use yii\helpers\Url;

    if( isset($form)){
		$f = ActiveForm::begin();
    	echo $f->errorSummary($form);

?>
    <?= $f->field($form, 'dradio')->radioList(['rewrite'=>"Rewrite clipboard   ",'add'=>'Add to clipboard']); ?>

<div class="row"><div class="col-sm-8">
<?php
echo '<label class="control-label">Movement</label>';

echo Typeahead::widget([
    'name' => 'movementsearch',
    'options' => ['placeholder' => 'Search by firma or move_id as you type ...', 'label' => "xx"],
    'pluginOptions' => ['highlight'=>true],
    'pluginEvents' => [
        "typeahead:selected" => "function(e,d) { $(\"#sqlclipcopyform-dmoveid\").val(d.move_id); }",
        "typeahead:autocompleted" => "function() { console.log(\"typeahead:autocompleted\"); }",
    ],
    'dataset' => [
        [
            'display' => 'mtitle',
            'remote' => [
                'url' => Url::to(['movement/movementlist']) . '?q=%QUERY',
                'wildcard' => '%QUERY'
            ]
        ]
    ]
]);
?>


</div>
<div class="col-sm-4">
    <?= $f->field($form, 'dmoveid')->textInput(['maxlength' => true]) ?>
    </div>
</div>
	
        <div class="form-group">
            <?= Html::submitButton("Copy or Add to clipboard", ['class' => 'btn btn-danger',
        'data' => [ 'confirm' => 'Are you sure you want to get data from movement?']]) ?>
        </div>
    <?php ActiveForm::end(); 
	}
    ?>