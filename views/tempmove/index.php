<?php

/* @var $this yii\web\View */
/* @var $searchModel app\models\TempmoveSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

use yii\helpers\Html;
use kartik\export\ExportMenu;
use kartik\grid\GridView;
use yii\helpers\Url;


$this->title = 'Tempmove / templates / clipboard system';
$this->params['breadcrumbs'][] = $this->title;
$search = "$('.search-button').click(function(){
	$('.search-form').toggle(1000);
	return false;
});";
$this->registerJs($search);
?>
<div class="tempmove-index">

    <h1><?= Html::encode($this->title) ?></h1>
    <?php // echo $this->render('_search', ['model' => $searchModel]); ?>

    <p>
        <?= Html::a('Create Tempmove', ['create'], ['class' => 'btn btn-success']) ?>
        <?= Html::a('Advance Search', '#', ['class' => 'btn btn-info search-button']) ?>
    </p>
    <div class="search-form" style="display:none">
        <?=  $this->render('_search', ['model' => $searchModel]); ?>
    </div>
    <?php 
    $gridColumn = [
        'mat_id',
        'price',
        'pcs',
        'discount',
        'tax',
        ['attribute' => 'act_price', 'visible' => false],
        'currency',
        ['attribute' => 'all1', 'visible' => false],
        ['attribute' => 'all2', 'visible' => false],
        ['attribute' => 'all3', 'visible' => false],
        ['attribute' => 'all4', 'visible' => false],
        ['attribute' => 'all5', 'visible' => false],
        'ean13',
        'kod',
        'model',
        'pdescr',
        'unit',
        'thserial',
        ['attribute' => 'user_name', 'visible' => false],
        ['attribute' => 'clip_id', 'visible' => false],
        ['attribute' => 'd1', 'visible' => false],
        ['attribute' => 'd2', 'visible' => false],
        ['attribute' => 'd3', 'visible' => false],
        ['attribute' => 'text1', 'visible' => false],
        ['attribute' => 'text2', 'visible' => false],
        ['attribute' => 'm_type_id', 'visible' => false],
        ['attribute' => 'stock_id1', 'visible' => false],
        ['attribute' => 'stock_id2', 'visible' => false],
        ['attribute' => 'adr_id', 'visible' => false],
        ['attribute' => 'price_type', 'visible' => false],
        ['attribute' => 'tempmove_info', 'visible' => false],
        ['attribute' => 'fifo_currency', 'visible' => false],
        ['attribute' => 'fifo_price', 'visible' => false],
        ['attribute' => 'fifo_move_id', 'visible' => false],
        ['attribute' => 'nr_counter', 'visible' => false],
        [
            'class' => 'yii\grid\ActionColumn',
        ],
    ]; 
    ?>
    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => $gridColumn,
        'pjax' => true,
        'pjaxSettings' => ['options' => ['id' => 'kv-pjax-container-tempmove']],
        'panel' => [
            'type' => GridView::TYPE_PRIMARY,
            'heading' => '<span class="glyphicon glyphicon-book"></span>  ' . Html::encode($this->title),
        ],
        // your toolbar can include the additional full export menu
        'toolbar' => [
            ExportMenu::widget([
                'dataProvider' => $dataProvider,
                'columns' => $gridColumn,
                'pjax' => false,
                'clearBuffers' => true,
                'target' => ExportMenu::TARGET_BLANK,
                'fontAwesome' => true,
                'dropdownOptions' => [
                    'label' => 'Full',
                    'class' => 'btn btn-default',
                    'itemsBefore' => [
                        '<li class="dropdown-header">Export All Data</li>',
                    ],
                ],
            ]) ,
        ],
    ]); ?>

</div>
