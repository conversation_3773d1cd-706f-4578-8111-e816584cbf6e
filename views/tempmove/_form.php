<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use kartik\typeahead\Typeahead;
use yii\helpers\Url;
use app\controllers\EstockTools;
use kartik\date\DatePicker;
/* @var $this yii\web\View */
/* @var $model app\models\Tempmove */
/* @var $form yii\widgets\ActiveForm */

?>

<h4 id="maintitle">&nbsp;</h4>
<?php
if( $model->isNewRecord ) {
    echo "<h4>Search by model</h4>";
    echo Typeahead::widget([
        'name' => 'modelsearch',
        'options' => ['placeholder' => 'Search model as you type...'],
        'pluginOptions' => ['highlight'=>true],
        'pluginEvents' => [
            "typeahead:selected" => "function(e,d) { $(\"#tempmove-mat_id\").val(d.mat_id); $('#maintitle').text(d.kod+ ' ' + d.model ); setprice(); }",
            "typeahead:autocompleted" => "function() { console.log(\"typeahead:autocompleted\"); }",
        ],
        'dataset' => [
            [
                'datumTokenizer' => "Bloodhound.tokenizers.obj.whitespace('value')",
                'display' => 'model',
    //            'prefetch' => $baseUrl . '/samples/countries.json',
                'remote' => [
                    'url' => Url::to(['product/productlist']) . '?q=%QUERY',
                    'wildcard' => '%QUERY'
                ]
            ]
        ]
    ]);

}

?>

<div class="tempmove-form">

    <?php $form = ActiveForm::begin(); ?>

    <?= $form->errorSummary($model); ?>

    <?= $model->isNewRecord?$form->field($model, 'mat_id')->textInput(['placeholder' => 'Mat']):'' ?>

    <?= EstockTools::isUserB2b()?
        ($model->isNewRecord?$form->field($model, 'price')->textInput(['maxlength' => true, 'placeholder' => 'Price', 'readonly' => true]):'')
        :$form->field($model, 'price')->textInput(['maxlength' => true, 'placeholder' => 'Price']) ?>

    <?= $model->isNewRecord?$form->field($model, 'pcs')->textInput(['maxlength' => true, 'placeholder' => 'Pcs', 'value' => '1']):
            $form->field($model, 'pcs')->textInput(['maxlength' => true, 'placeholder' => 'Pcs']) ?>

    <?= $form->field($model, 'tempmove_info')->textInput(['maxlength' => true, 'placeholder' => 'info']) ?>

    <?= ! ( EstockTools::isUserB2b() || EstockTools::isUserWman() )  ?$form->field($model, 'thserial')->textInput(['maxlength' => true, 'placeholder' => 'Optional']):'' ?>
    <?php if( EstockTools::isUserWman() || EstockTools::isUserWstock() ) {
      echo  $form->field($model, 'd1')->widget(DatePicker::class,[
            'name' => 'd1',
            'value' => '9999-12-31',
            'options' => ['placeholder' => 'Exp date ...'],
            'pluginOptions' => [
                'todayHighlight' => true,
                'todayBtn' => true,
                'format' => 'yyyy-mm-dd',
                'autoclose' => true,
            ]
            ]);
    }
    ?>
    <?= $form->field($model, 'discount')->hiddenInput(['value' => 0 ])->label(false) ?>

    <!-- <?= $form->field($model, 'tax')->hiddenInput(['value' => 0 ])->label(false) ?> -->

    <?= $form->field($model, 'clip_id')->hiddenInput(['value' => Yii::$app->request->get('clip_id')])->label(false) ?>
    <?= $form->field($model, 'm_type_id')->hiddenInput(['value' => Yii::$app->request->get('m_type_id')])->label(false) ?>

    <?= $form->field($model, 'user_name')->hiddenInput(['value' => Yii::$app->user->identity->username])->label(false) ?>

    <?= $form->field($model, 'stock_id1')->hiddenInput(['value' => Yii::$app->request->get('stock_id1')])->label(false) ?>

    <?= $form->field($model, 'stock_id2')->hiddenInput(['value' => Yii::$app->request->get('stock_id2')])->label(false) ?>

<!--
    <?= $form->field($model, 'discount')->textInput(['maxlength' => true, 'placeholder' => 'Discount','style' => 'display:none']) ?>

    <?= $form->field($model, 'tax')->textInput(['maxlength' => true, 'placeholder' => 'Tax','style' => 'display:none']) ?>

    <?= $form->field($model, 'act_price', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'currency')->textInput(['maxlength' => true, 'placeholder' => 'Currency','style' => 'display:none']) ?>

    <?= $form->field($model, 'all1', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'all2', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'all3', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'all4', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'all5', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'ean13')->textInput(['maxlength' => true, 'placeholder' => 'Ean13','style' => 'display:none']) ?>

    <?= $form->field($model, 'kod')->textInput(['maxlength' => true, 'placeholder' => 'Kod','style' => 'display:none']) ?>

    <?= $form->field($model, 'model')->textInput(['maxlength' => true, 'placeholder' => 'Model','style' => 'display:none']) ?>

    <?= $form->field($model, 'pdescr')->textInput(['maxlength' => true, 'placeholder' => 'Pdescr']) ?>

    <?= $form->field($model, 'unit')->textInput(['maxlength' => true, 'placeholder' => 'Unit','style' => 'display:none']) ?>

    <?= $form->field($model, 'user_name', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'clip_id', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'd1', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'd2', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'd3', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'text1', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'text2', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'm_type_id', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'stock_id1', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'stock_id2', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'adr_id', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>


    <?= $form->field($model, 'price_type', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'tempmove_info', ['template' => '{input}'])->textInput(); ?>

    <?= $form->field($model, 'fifo_currency', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'fifo_price', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'fifo_move_id', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>


    <?= $form->field($model, 'nr_counter', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>
-->
    <div class="form-group">
        <?= Html::submitButton($model->isNewRecord ? 'Add' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
    </div>

    <?php ActiveForm::end(); ?>

    <?php 

    $script = "
    function setprice() {
        var m = $(\"#tempmove-mat_id\").val();
        var cd = 0; //discount
        var p = \"".$shopPrice."\";
        $.get('". Url::to(['/sales/getmodel']) ."',{mat_id:m,price:p},function(model){
            $('#maintitle').text(model.kod+ ' ' + model.model + ' ".$shopPrice.": ' + model.".$shopPrice."  );
            $('#tempmove-price').val(model.".$shopPrice.");
            });
        };

    $(\"#tempmove-mat_id\").on('keyup change', setprice );

    ";

$this->registerJs($script);
?>