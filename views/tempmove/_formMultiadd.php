<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

    if( isset($form)){
		$f = ActiveForm::begin();
    	echo $f->errorSummary($form);
		echo $f->field($form,'multiaddSwitch')->radioList(['0'=>"   Automatic   ",'1'=>'  Model is first column    ','2'=>'  mat_id is first column     '],[ 'id' => 'multiaddSwitch', 'class' => 'btn-group', 'value' => '0']);
		echo $f->field($form, 'multiadd')->textarea(['rows' => '6','placeholder'=>$form->multiaddPlaceholder])->label($form->multiaddLabel);
	?>


        <div class="form-group">
            <?= Html::submitButton($form->multiaddLabel, ['class' => 'btn btn-danger']) ?>
        </div>
    <?php ActiveForm::end(); 
	} else {
		echo "No form defined";
	}
