<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use kartik\typeahead\Typeahead;
use yii\helpers\Url;
use app\controllers\EstockTools;
use kartik\date\DatePicker;


/* @var $this yii\web\View */
/* @var $model app\models\Tempmove */
/* @var $form yii\widgets\ActiveForm */


?>
<div class="tempmove-form">

    <?php $form = ActiveForm::begin(); ?>

    <?= $form->errorSummary($model); ?>

    
    <div id="maintitle"></div>

    <div class="row"><div class="col-sm-4">
    <?= $form->field($model, 'mat_id')->textInput(['placeholder' => '111111']) ?>
    </div><div class="col-sm-8">

<?php
echo '<label class="control-label">Model</label>';

echo Typeahead::widget([
    'name' => 'modelsearch',
    'options' => ['placeholder' => 'Search model as you type ...', 'label' => "xx"],
    'pluginOptions' => ['highlight'=>true],
    'pluginEvents' => [
        "typeahead:selected" => "function(e,d) { $(\"#tempmove-mat_id\").val(d.mat_id); $('#maintitle').text(d.kod+ ' ' + d.model ); setprice(); }",
        "typeahead:autocompleted" => "function() { console.log(\"typeahead:autocompleted\"); }",
    ],
    'dataset' => [
        [
            'datumTokenizer' => "Bloodhound.tokenizers.obj.whitespace('value')",
            'display' => 'model',
//            'prefetch' => $baseUrl . '/samples/countries.json',
            'remote' => [
                'url' => Url::to(['product/productlist']) . '?q=%QUERY',
                'wildcard' => '%QUERY'
            ]
        ]
    ]
]);
?>

    </div></div>
    <div class="row"><div class="col-sm-4">
    <?= $form->field($model, 'price')->textInput(['maxlength' => true, 'placeholder' => 'Price']) ?>
    </div><div class="col-sm-2">
    <?= $form->field($model, 'pcs')->textInput(['maxlength' => true, 'placeholder' => 'Pcs', 'value' => "1"]) ?>
    </div><div class="col-sm-3">
    <?= $form->field($model, 'tempmove_info')->textInput(['maxlength' => true, 'placeholder' => 'info']) ?>
    </div><div class="col-sm-3">
    <?= 
    // Expiration date is only for W users
    // Other users can use thserial for serial number
    EstockTools::isUserW() ?
        $form->field($model, 'd1')->widget(DatePicker::class,[
            'name' => 'd1',
            'value' => '9999-12-31',
            'options' => ['placeholder' => 'Exp date ...'],
            'pluginOptions' => [
                'todayHighlight' => true,
                'todayBtn' => true,
                'format' => 'yyyy-mm-dd',
                'autoclose' => true,
            ]
    ])
    :
    $form->field($model, 'thserial')->textInput(['maxlength' => true, 'placeholder' => 'Optional']) ?>
    </div></div>
    <?= $form->field($model, 'discount')->hiddenInput(['value' => "0"])->label(false) ?>
    <?= $form->field($model, 'tax')->hiddenInput(['value' => "0"])->label(false) ?>
    

    <?= $form->field($model, 'clip_id')->hiddenInput(['value' => $modelconfig->clip_id])->label(false) ?>
    <?= $form->field($model, 'm_type_id')->hiddenInput(['value' => $modelconfig->m_type_id])->label(false) ?>

    <?= $form->field($model, 'user_name')->hiddenInput(['value' => Yii::$app->user->identity->username])->label(false) ?>


<!--
    <?= $form->field($model, 'discount')->textInput(['maxlength' => true, 'placeholder' => 'Discount','style' => 'display:none']) ?>

    <?= $form->field($model, 'tax')->textInput(['maxlength' => true, 'placeholder' => 'Tax','style' => 'display:none']) ?>

    <?= $form->field($model, 'act_price', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'currency')->textInput(['maxlength' => true, 'placeholder' => 'Currency','style' => 'display:none']) ?>

    <?= $form->field($model, 'all1', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'all2', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'all3', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'all4', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'all5', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'ean13')->textInput(['maxlength' => true, 'placeholder' => 'Ean13','style' => 'display:none']) ?>

    <?= $form->field($model, 'kod')->textInput(['maxlength' => true, 'placeholder' => 'Kod','style' => 'display:none']) ?>

    <?= $form->field($model, 'model')->textInput(['maxlength' => true, 'placeholder' => 'Model','style' => 'display:none']) ?>

    <?= $form->field($model, 'pdescr')->textInput(['maxlength' => true, 'placeholder' => 'Pdescr']) ?>

    <?= $form->field($model, 'unit')->textInput(['maxlength' => true, 'placeholder' => 'Unit','style' => 'display:none']) ?>

    <?= $form->field($model, 'user_name', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'clip_id', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'd1', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'd2', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'd3', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'text1', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'text2', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'm_type_id', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'stock_id1', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'stock_id2', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'adr_id', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>


    <?= $form->field($model, 'price_type', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'tempmove_info', ['template' => '{input}'])->textInput(); ?>

    <?= $form->field($model, 'fifo_currency', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'fifo_price', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'fifo_move_id', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>


    <?= $form->field($model, 'nr_counter', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>
-->
    <div class="form-group">
        <?= Html::submitButton($model->isNewRecord ? 'Add' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
    </div>

    <?php ActiveForm::end(); ?>

        <?php 

    $script = "
    function setprice() {
        var m = $(\"#tempmove-mat_id\").val();
        var cd = 0; //discount
        var p = \"".$modelconfig->price."\";
        $.get('". Url::to(['/sales/getmodel']) ."',{mat_id:m,price:p},function(model){
            $('#maintitle').text(model.kod+ ' ' + model.model + ' ".$modelconfig->price.": ' + model.".$modelconfig->price."  );
            //$('#tempmove-price').val(Math.round((model.p1-(model.p1*cd/100))*100)/100);
            $('#tempmove-price').val(model.".$modelconfig->price.");
            });
        };

    $(\"#tempmove-mat_id\").on('keyup change', setprice );

    ";

$this->registerJs($script);
?>

</div>
