<?php

/* @var $this yii\web\View */
/* @var $searchModel app\models\TempmoveSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

use yii\helpers\Html;
use kartik\export\ExportMenu;
use kartik\grid\GridView;
use kartik\tabs\TabsX;
use app\controllers\EstockTools;
use yii\web\View;



$this->title = "Clipboard";
$this->params['breadcrumbs'][] = $this->title;

if (empty($modelconfig)) {
    return;
}

echo "<h4>" . $modelconfig->d1 . " " . $modelconfig->text1
    . $modelconfig->adr->firma . ", " . $modelconfig->mtype->name . ", <b>From:</b> " . $modelconfig->stock_id1 . " <b>To:</b> " . $modelconfig->stock_id2 .
    " <b>Text1,2:</b> " . $modelconfig->text1 . ", " . $modelconfig->text2 . "</h4>";
$this->params['breadcrumbs'][] = $modelconfig->clip_id . " " . $modelconfig->m_type_id;

?>

<div class="tempmove-index">

    <?php
    $this->registerJs("
        function showQrCode(event, element) {
            // Remove any existing QR code popups
            $('.qr-popup').remove();
            
            // Create QR code popup
            var qrUrl = $(element).data('qr-url');
            var popup = $('<div class=\"qr-popup\"><img src=\"/print/qrcode?text=' + qrUrl + '\" width=\"250\" height=\"250\"></div>');
            
            // Position near mouse
            popup.css({
                position: 'absolute',
                left: event.pageX + 10 + 'px',
                top: event.pageY + 10 + 'px',
                zIndex: 1000,
                background: '#fff',
                padding: '5px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                boxShadow: '0 0 10px rgba(0,0,0,0.2)'
            });
            
            // Add to body and set click outside to close
            $('body').append(popup);
            
            $(document).one('click', function() {
                popup.remove();
            });
            
            // Prevent event propagation
            event.stopPropagation();
        }
        
        // Also support hover if desired
        $(document).on('mouseenter', '.qr-code-link', function(e) {
            showQrCode(e, this);
        });
        
        $(document).on('mouseleave', '.qr-code-link', function() {
            $('.qr-popup').remove();
        });
    ");
    ?>

    <?php
    $gridColumn = [
        [
            'class' => 'yii\grid\ActionColumn',
            'template' => '{update}'
        ],
        // add column - qr code with text: https://grapph.com/allcoderelay/webhook_config.php?webhook_url=https%3A%2F%2Fwstock.racio.com%2Ftempmove%2Fqrupdate%3Fuser_name%3Dzzz%26mat_id%3Dxxx%26clip_id%3D22&webhook_title=xxx%20Serial
        //  where xxx is mat_id, zzz is user_name, 22 is clip_id
        [
            'label' => 'QR',
            'format' => 'raw',
            'value' => function ($model) {
                $qrUrl = 'allcoderelay://setwebhookurl?url=' . urlencode(Yii::$app->urlManager->hostInfo . '/tempmove/qrupdate?data=' . $model->user_name . '§' . $model->mat_id . '§' . $model->clip_id) . '&title=' . urlencode($model->model);
                return Html::a('<span class="glyphicon glyphicon-qrcode"></span>', '#', [
                    'class' => 'qr-code-link',
                    'data-qr-url' => $qrUrl,
                    'onclick' => 'showQrCode(event, this); return false;',
                ]);
            },
            'contentOptions' => [
                'style' => 'position: relative;'
            ],
        ],
        'mat_id',
        'kod',
        'model',
        'price',
        'pcs',
        'onstock',
        'discount',
        'tax',
        ['attribute' => 'act_price', 'visible' => false],
        'currency',
        ['attribute' => 'all1', 'visible' => false],
        ['attribute' => 'all2', 'visible' => false],
        ['attribute' => 'all3', 'visible' => false],
        ['attribute' => 'all4', 'visible' => false],
        ['attribute' => 'all5', 'visible' => false],
        'tempmove_info',
        ['attribute' => 'thserial', 'visible' => EstockTools::isUserW() ? false : true],
        ['attribute' => 'd1', 'visible' => EstockTools::isUserW() ? true : false],
        [
            'class' => 'yii\grid\ActionColumn',
            'template' => '{delete}'
        ],
        ['attribute' => 'user_name', 'visible' => false],
        ['attribute' => 'clip_id', 'visible' => false],
        ['attribute' => 'd2', 'visible' => false],
        ['attribute' => 'd3', 'visible' => false],
        ['attribute' => 'text1', 'visible' => false],
        ['attribute' => 'text2', 'visible' => false],
        ['attribute' => 'm_type_id', 'visible' => false],
        ['attribute' => 'stock_id1', 'visible' => false],
        ['attribute' => 'stock_id2', 'visible' => false],
        ['attribute' => 'adr_id', 'visible' => false],
        ['attribute' => 'price_type', 'visible' => false],
        ['attribute' => 'fifo_currency', 'visible' => false],
        ['attribute' => 'fifo_price', 'visible' => false],
        ['attribute' => 'fifo_move_id', 'visible' => false],
        ['attribute' => 'nr_counter', 'visible' => false],

    ];

    function echoGrid($dataProvider, $dataSumProvider, $searchModel, $gridColumn, $cid, $config_id)
    {
        $ret = "";
        $mtype = !empty($dataProvider->getModels()) ? $dataProvider->getModels()[0]->m_type_id : null;
        $text1 = !empty($dataProvider->getModels()) ? $dataProvider->getModels()[0]->text1 : null;
        $text2 = !empty($dataProvider->getModels()) ? $dataProvider->getModels()[0]->text2 : null;
        $ret .= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'columns' => $gridColumn,
            'export' => false,
            'toggleData' => false,
            'pjax' => true,
            'pjaxSettings' => ['options' => ['id' => 'kv-pjax-container-tempmove']],
            'panel' => [
                'type' => GridView::TYPE_PRIMARY,
                'heading' => '<span class="glyphicon glyphicon-book"></span>   PCS: <b>' . $dataSumProvider['xpcs']
                    . '</b> TOTAL: <b>' . $dataSumProvider['xtotal']
                    . '</b> With VAT: <b>' . $dataSumProvider['xtotaltax'] . '</b>',
            ],
            'toolbar' => [
                ExportMenu::widget([
                    'dataProvider' => $dataProvider,
                    'columns' => $gridColumn,
                    'target' => ExportMenu::TARGET_BLANK,
                    'pjax' => false,
                    'clearBuffers' => true,
                    'fontAwesome' => true,
                    'showConfirmAlert' => false,
                    'dropdownOptions' => [
                        'label' => 'Full',
                        'class' => 'btn btn-default',
                        'itemsBefore' => [
                            '<li class="dropdown-header">Export All Data</li>',
                        ],
                    ],
                ]),
            ],
        ]);

        // $ret .= "<div class=\"col-sm-6\">". Html::a('Add one model '.$cid, ['createfromconfig','config_id'=>$config_id], ['class' => 'btn btn-success', 'id' =>'createBtn'.$mtypename])."</div>";
        if (!empty($dataProvider->getModels())) {
            $mtypename = isset($mtypename) ? $mtypename : "";
            $ret .= "<div class=\"col-sm-12\">" . Html::a('Save ' . $mtypename, ['saveclipboard2', 'mtype' => $mtype, 'mtypename' => $mtypename, 'clip_id' => $cid, 'text1' =>  $text1, 'text2' => $text2, 'stock_id1' => $dataProvider->getModels()[0]->stock_id1, 'stock_id2' => $dataProvider->getModels()[0]->stock_id2], ['class' => 'btn btn-danger', 'id' => 'createBtn' . $mtypename]) . "</div><div>&nbsp;</div>";
        }

        return $ret;
    }

    $items = [
        [
            'label' => '<i class="glyphicon glyphicon-plus"></i> Add',
            'content' =>  $this->render('_formClip', ['model' => $model, 'modelconfig' => $modelconfig]),
        ],
        [
            'label' => '<i class="glyphicon glyphicon-book"></i> MultiAdd',
            'content' =>  $this->render('_formMultiadd', ['form' => $modelMultiadd]),
        ],
        [
            'label' => '<i class="glyphicon glyphicon-book"></i> SQL Actions',
            'content' =>  $this->render('_formSqlclip', ['form' => $sqlclipForm]),
        ],
        EstockTools::isUserW() ?
            [
                'label' => '<i class="glyphicon glyphicon-book"></i> Formulas',
                'content' =>  $this->render('_formWik', ['form' => $sqlclipformulaForm]),
            ] :
            [
                'label' => '<i class="glyphicon glyphicon-book"></i> Discounts',
                'content' =>  $this->render('_formSqlclipdisc', ['form' => $sqlclipdiscForm]),
            ],
        [
            'label' => '<i class="glyphicon glyphicon-book"></i> Copy from Movement',
            'content' =>  $this->render('_formSqlclipcopy', ['form' => $sqlclipcopyForm]),
        ],
        [
            'label' => '<i class="fa fa-cubes"></i> Products',
            'content' =>  $this->render('../product/indexclipsearch', [
                'searchModel' => $productsearchModel,
                'dataProvider' => $dataProvider,
            ])
        ],
        [
            'label' => '<i class="glyphicon glyphicon-star"></i> New product',
            'content' =>  $this->render('../product/create', [
                'model' => $newProduct,
            ])
        ],
        [
            'label' => '<i class="glyphicon glyphicon-star"></i> New product import',
            'content' =>  $this->render('_formMultiadd', ['form' => $modelMultiaddProduct, 'idprep' => 'modelimport']),
        ]


    ];



    // Above
    echo TabsX::widget([
        'items' => $items,
        'position' => TabsX::POS_ABOVE,
        'bordered' => true,
        'encodeLabels' => false
    ]);

    echo "<br>";
    echo echoGrid($clipdataProvider, $dataSumProvider, $searchModel, $gridColumn, $clip_id, $config_id);

    ?>

</div>