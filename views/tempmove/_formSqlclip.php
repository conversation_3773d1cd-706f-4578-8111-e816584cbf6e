<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;


    if( isset($form)){
		$f = ActiveForm::begin();
    	echo $f->errorSummary($form);


?>
<div class="row"><div class="col-sm-6" id="sqlCommandField">
    <?= $f->field($form, 'sqlCommand')->textInput(['maxlength' => true, 'placeholder' => 'price=price*1.12']) ?>
    </div><div class="col-sm-6">
    <?= $f->field($form, 'sqlWhere')->textInput(['maxlength' => true, 'placeholder' => "kod ilike 'WAT%'"]) ?>
    </div>
</div>
	<?= $f->field($form, 'isUpdate')->radioList(['update'=>"Update   ",'delete'=>'delete'],[ 'id' => 'isUpdateField']); ?>


	<?= $f->field($form, 'isErase')->checkbox(); ?>
	
        <div class="form-group">
            <?= Html::submitButton("Update or Delete ", ['class' => 'btn btn-danger']) ?>
        </div>
    <?php ActiveForm::end(); 
	}


$js = "

$('#isUpdateField :radio').change(function(){
    var x = $(this).val();
    if( x == 'delete'){
        $(\"#sqlCommandField\").hide();
    } else {
        $(\"#sqlCommandField\").show();

    }
});


";
$this->registerJs($js);

