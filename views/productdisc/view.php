<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

/** @var yii\web\View $this */
/** @var app\models\ProductDisc $model */

$this->title = $model->cgroup;
$this->params['breadcrumbs'][] = ['label' => 'Product Discs', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
\yii\web\YiiAsset::register($this);
?>
<div class="product-disc-view">

    <h1><?= Html::encode($this->title) ?></h1>

    <p>
        <?= Html::a('Update', ['update', 'cgroup' => $model->cgroup, 'ctype' => $model->ctype], ['class' => 'btn btn-primary']) ?>
        <?= Html::a('Delete', ['delete', 'cgroup' => $model->cgroup, 'ctype' => $model->ctype], [
            'class' => 'btn btn-danger',
            'data' => [
                'confirm' => 'Are you sure you want to delete this item?',
                'method' => 'post',
            ],
        ]) ?>
    </p>

    <?= DetailView::widget([
        'model' => $model,
        'attributes' => [
            'perc',
            'cgroup',
            'ctype',
        ],
    ]) ?>

</div>
