<?php

use app\models\ProductDisc;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\grid\ActionColumn;
use yii\grid\GridView;
use yii\widgets\Pjax;
/** @var yii\web\View $this */
/** @var app\models\ProductDiscSearch $searchModel */
/** @var yii\data\ActiveDataProvider $dataProvider */

$this->title = 'Product Discs';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="product-disc-index">

    <h1><?= Html::encode($this->title) ?></h1>

    <p>
        <?= Html::a('Create Product Disc', ['create'], ['class' => 'btn btn-success']) ?>
    </p>

    <?php Pjax::begin(); ?>
    <?php // echo $this->render('_search', ['model' => $searchModel]); ?>

    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => [
            ['class' => 'yii\grid\SerialColumn'],

            'perc',
            'cgroup',
            'ctype',
            [
                'class' => ActionColumn::className(),
                'urlCreator' => function ($action, ProductDisc $model, $key, $index, $column) {
                    return Url::toRoute([$action, 'cgroup' => $model->cgroup, 'ctype' => $model->ctype]);
                 }
            ],
        ],
    ]); ?>

    <?php Pjax::end(); ?>

</div>
