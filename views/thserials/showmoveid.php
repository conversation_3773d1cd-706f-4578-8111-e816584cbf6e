                <div class="grid_12">
                    <div id="content">


<h4>Already written Serials</h4>
<?php
$this->widget('zii.widgets.grid.CGridView', array(
    'dataProvider'=>$data1,
    'columns'=>array(
	'firma',
    'd1',  // display the 'name' attribute of the 'category' relation
    'mat_id',
    'model',
    'thserial',   // display the 'content' attribute as purified HTML
    'pcsInDetail',
    array(            // display a column with "view", "update" and "delete" buttons
	    'header' => 'delete',
                    'type' => 'raw',
	    'name'=>'xxx',	    'value' => 'CHtml::link("DELETE this line",Yii::app()->createUrl("Thserial/delOne",array("mat_id"=>$data->mat_id,"move_id"=>$data->move_id,"thserial"=>$data->thserial)))'
// 'CHtml::ajaxLink($data1->mat_id,Yii::app()->createUrl("product/addFromOnstock",array("mat_id"=>$data1->mat_id, "move_id"=>$data1->move_id)),
			/* array(
			        "update"=>"#req_res",
			        "complete"=>"function(){
			    	$('."'#req_res'".').text('."'Deleted mat_id: '+".'$data->mat_id'.').fadeOut(300).fadeIn(1000);
			        }"
			    )
		)'*/)
)));
?>
<div id="req_res"></div>

                    </div><!-- content -->
                </div>
