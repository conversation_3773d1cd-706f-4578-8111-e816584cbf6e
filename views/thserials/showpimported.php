                <div class="grid_12">
                    <div id="content">


<h2>All thserials imported movements</h2>
<?php
$this->widget('zii.widgets.grid.CGridView', array(
    'dataProvider'=>$data1,
    'columns'=>array(
	'move_id',
	'firma',
    'd1',  // display the 'name' attribute of the 'category' relation
    'thserials',   // display the 'content' attribute as purified HTML
	'pcsInDetail',
    array(            // display a column with "view", "update" and "delete" buttons
			'header' => 'Detail/Edit',
	         'type' => 'raw',
			'name'=>$data->move_id,
			'value' => 'CHtml::link($data->move_id,Yii::app()->createUrl("Thserial/AddThOneserial",array("move_id"=>$data->move_id)))',
	))
));

?>


                    </div><!-- content -->
                </div>
