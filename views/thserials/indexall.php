<?php

use yii\helpers\Html;
use kartik\grid\GridView;
use yii\widgets\Pjax;
use kartik\export\ExportMenu;

/* @var $this yii\web\View */
/* @var $searchModel app\models\Thserials */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Thserials';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="thserials-index">

    <?php Pjax::begin(); ?>
    <?php // echo $this->render('_search', ['model' => $searchModel]); 

$gridColumn = [
            ['class' => 'yii\grid\ActionColumn', 'template' => '{info}',
            'buttons' => [
                    'info' => function ($url, $model) {
                        return Html::a('<span class="glyphicon glyphicon-info-sign"></span>', $url, [
                                    'title' => Yii::t('app', 'Info'),
                        ]);
                    }
            ],
            'urlCreator' => function ($action, $model, $key, $index) {
                    if ($action === 'info') {
                        $url = Yii::$app->urlManager->createUrl(['movement/index',"MovementSearch[move_id]"=>$model['move_id']]);
                                                // $url = Yii::$app->urlManager->createUrl(['movement/index?MovementSearch%5Bmove_id%5D='.$model['move_id']]);
                        return $url;
                    }
                }
        ],
        'thserial',
        'mat_id',
        'mat.model',
        'mov.d1',
        'mov.adr.firma',
        'mov.m_type_id',
        'mov.mType.name',
        'mov.text1',
        'mov.text2',
        'move_id'
        ];
    ?>

    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => $gridColumn,
        'pjax' => true,
        'pjaxSettings' => ['options' => ['id' => 'kv-pjax-container-tempmove']],
        'panel' => [
            'type' => GridView::TYPE_PRIMARY,
            'heading' => '<span class="glyphicon glyphicon-book"></span>  ' . Html::encode($this->title),
        ],
        // your toolbar can include the additional full export menu
        'toolbar' => [
            ExportMenu::widget([
                'dataProvider' => $dataProvider,
                'columns' => $gridColumn,
                'pjax' => false,
                'clearBuffers' => true,
                'target' => ExportMenu::TARGET_BLANK,
                'fontAwesome' => true,
                'dropdownOptions' => [
                    'label' => 'Full',
                    'class' => 'btn btn-default',
                    'itemsBefore' => [
                        '<li class="dropdown-header">Export All Data</li>',
                    ],
                ],
            ]) ,
        ],
    ]); ?>
    <?php Pjax::end(); ?>
</div>
