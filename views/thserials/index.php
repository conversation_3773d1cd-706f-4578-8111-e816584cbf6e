<?php

use yii\helpers\Html;
use yii\helpers\Url;
// use yii\grid\GridView;
use yii\widgets\ActiveForm;
use kartik\grid\GridView;
use yii\widgets\Pjax;

/* @var $this yii\web\View */
/* @var $dataProvider yii\data\ActiveDataProvider */
/* @var $form ActiveForm */


// $this->title = 'Serial numbers';
// $this->params['breadcrumbs'][] = $this->title;

// if( isset($saved)) {
//                 $this->render('view', ['model' => $model, 'id'=>$model->id]);
//                 echo "<p>";
//                 echo Html::a('EDIT', ['view','id'=>$model->id ], ['class' => 'btn btn-success', 'id' =>'action1']);
//                 echo "</p>";
// }

// var_dump($model);
// exit;

$this->registerJsFile('https://unpkg.com/@zxing/library@latest/umd/index.min.js', ['position' => \yii\web\View::POS_HEAD]);

?>
<div class="Thserials">

    <h1>Add NEW TH, LOX, MON, ... serial(s)</h1>
    <h4>Serials for move_id=<?= $model->move_id; ?>,&nbsp; <?= $minfo ?></h4>
    <div class="thserials-form">

        <?php $form = ActiveForm::begin(); ?>

        <div class="row">
            <div class="col-sm-5">

                <?= $form->field($model, 'mat_id')->textInput() ?>

                <?= $form->field($model, 'move_id')->hiddenInput()->label(false) ?>

                <?= $form->field($model, 'thserial')->textInput(['maxlength' => true]) ?>

            </div>
            <div class="col-sm-5">
                <?= $form->field($model, 'multiadd')->textarea(['rows' => '6', 'placeholder' => 'model,serial'])->label('Import from CSV text'); ?>
            </div>
            <div class="col-sm-2">
                <?= Html::label('QR with move_id for mobile APP') ?>
                <img src="/print/qrcode?text=TH_<?= $model->move_id ?>" />
            </div>
        </div>
        <!--          <?= Html::checkbox('onlylocal', true, ['label' => 'Show ONLY local possible TH serials']) ?>
-->
        <div class="form-group">
            <?= Html::submitButton('Enter serial ', ['class' => 'btn btn-danger']) ?>
        </div>

        <?php ActiveForm::end(); ?>

        <?= GridView::widget([
            'dataProvider' => $missing,
            'export' => false,
            'panel' => [
                'type' => GridView::TYPE_PRIMARY,
                'heading' => '<span class="glyphicon glyphicon-book"></span>  Missing serials for this move_id',
            ],
        ]); ?>


        <?= Html::beginForm(['/thserials/index', 'move_id' => $model->move_id], 'GET'); ?>
        <div class="row">
            <div class="col-sm-4">
                <?= Html::label("Filter for parent move_id:", 'xmove_id') ?>
                <?= Html::textInput('xmove_id', $_GET['xmove_id'] ?? "") ?>

            </div>
            <div class="col-sm-8">
                &nbsp;
            </div>
        </div>
        <div class="form-group">
            <?= Html::submitButton('Show only filtered', ['class' => 'btn btn-primary']); ?>
        </div>
        <?= Html::endForm(); ?>


        <?php Pjax::begin(); ?>
        <?= GridView::widget([
            'dataProvider' => $possible,
            'columns' => [
                [
                    'class' => 'yii\grid\ActionColumn',
                    'template' => '{info}',
                    'buttons' => [
                        'info' => function ($url, $m) {
                            return Html::a('<span class="glyphicon glyphicon-thumbs-up"></span>', $url, [
                                'title' => 'Add this!!!',
                            ]);
                        }
                    ],
                    'urlCreator' => function ($action, $m, $key, $index) {
                        if ($action === 'info') {
                            $url = Yii::$app->urlManager->createUrl(['thserials/index', "mat_id" => $m['mat_id'], "move_id" => $m['xmove'], 'thserial' => $m['thserial'], 'xmove_id' => $_GET['xmove_id'] ?? ""]);
                            return $url;
                        }
                    }
                ],
                'thserial',
                'mat_id',
                'model',
                'move_id'
            ],
            'export' => false,
            'panel' => [
                'type' => GridView::TYPE_PRIMARY,
                'heading' => '<span class="glyphicon glyphicon-book"></span>  Possible serials which we have on the stocks',
            ],
        ]); ?>
        <?php Pjax::end(); ?>

        <div>
            <?php

            echo $this->render('//movement/_expand', ['model' => app\models\Movement::find()->where(["move_id" => $model->move_id])->one()]);
            ?>
        </div>

    </div>


    <p>
        <?= Html::a('ERASE all serials for this move_id', ['erasemoveid', 'move_id' => $model->move_id], [
            'class' => 'btn btn-danger',
            'data' => [
                'confirm' => 'Are you sure you want to delete all serials from move_id=' . $model->move_id . '?',
                'method' => 'post',
            ],
        ]) ?>

    </p>


    <!-- <script>
        var maxid = -111;
        setInterval(function worker() {
            $.getJSON('/thserials/getmaxid?move_id=<?= $model->move_id; ?>&xmove_id=<?= $_GET["xmove_id"] ?? ""; ?>')
                .done(function(json) {
                    console.log(json.maxid);
                    if (maxid == -111) {
                        maxid = json.maxid;
                    }
                    if (json.maxid != maxid) {
                        window.location.href = "/thserials/index?move_id=<?= $model->move_id ?>&xmove_id=<?= $_GET["xmove_id"] ?? ""; ?>";
                    }
                })
                .fail(function(jqxhr, textStatus, error) {
                    var err = textStatus + ", " + error;
                    console.log("Request Failed: " + err);
                })
        }, 2000);
    </script> -->

    <div class="row">
        <div class="col-sm-5">
            <div class="form-group">
                <?= Html::label('Scan TH Serial (QR/Barcode)') ?>
                <?= Html::textInput('scanner_input', '', [
                    'class' => 'form-control',
                    'id' => 'scanner_input',
                    'placeholder' => 'Scan or type serial number...',
                    'autofocus' => true
                ]) ?>
            </div>
        </div>
        <div class="col-sm-2">
            <?= Html::button('Open Camera Scanner', [
                'class' => 'btn btn-primary',
                'id' => 'openScannerBtn'
            ]) ?>
        </div>
    </div>

    <!-- Add the modal dialog -->
    <div class="modal fade" id="scannerModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Scan Code</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body position-relative">
                    <div id="scannerStatus" class="alert" style="display: none; position: absolute; top: 10px; left: 50%; transform: translateX(-50%); z-index: 1000;"></div>
                    <div class="form-group">
                        <label for="formatSelect">Select Format:</label>
                        <select class="form-control" id="formatSelect">
                            <option value="ALL">All Formats</option>
                            <option value="QR_CODE">QR Code</option>
                            <option value="CODE_128">Code 128</option>
                            <option value="CODE_39">Code 39</option>
                            <option value="EAN_13">EAN-13</option>
                            <option value="EAN_8">EAN-8</option>
                            <option value="UPC_A">UPC-A</option>
                            <option value="UPC_E">UPC-E</option>
                        </select>
                    </div>
                    <video id="preview" style="width: 100%;"></video>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" id="closeScanner">Close and Save</button>
                </div>
            </div>
        </div>
    </div>

    <?php
    $scannerUrl = Url::to(['thserials/addthonemobile']);
    $currentMoveId = $model->move_id;

    $js = <<<JS
    $('#scanner_input').on('keypress', function(e) {
        if(e.which == 13) { // Enter key
            e.preventDefault();
            var serial = $(this).val();
            if(serial) {
                $.ajax({
                    url: '{$scannerUrl}',
                    data: {
                        scanner: 1,
                        move_id: '{$currentMoveId}',
                        thserial: serial
                    },
                    method: 'GET',
                    dataType: 'json',
                    success: function(response) {
                        // Show message
                        if(response.message) {
                            alert(response.message);
                        }
                        // Clear input
                        $('#scanner_input').val('');
                        // Refresh page
                        location.reload();
                    },
                    error: function() {
                        alert('Error processing serial');
                        $('#scanner_input').val('');
                    }
                });
            }
        }
    });

    // Auto focus scanner input when page loads
    $(document).ready(function() {
        $('#scanner_input').focus();
    });

    // Keep focus on scanner input unless user explicitly clicks elsewhere
    $(document).click(function(e) {
        if (!$(e.target).is('input, textarea')) {
            $('#scanner_input').focus();
        }
    });
JS;

    $scannerJs = <<<JS
    let selectedDeviceId;
    let codeReader;

    $('#openScannerBtn').on('click', function() {
        $('#scannerModal').modal('show');
        initializeScanner();
    });

    async function initializeScanner() {
        try {
            codeReader = new ZXing.BrowserMultiFormatReader();
            const videoInputDevices = await navigator.mediaDevices.enumerateDevices();
            const cameras = videoInputDevices.filter(device => device.kind === 'videoinput');
            
            if (cameras.length === 0) {
                throw new Error('No cameras found on this device.');
            }
            
            // Try to select back camera
            selectedDeviceId = cameras[0].deviceId;
            for (const camera of cameras) {
                if (camera.label.toLowerCase().includes('back')) {
                    selectedDeviceId = camera.deviceId;
                    break;
                }
            }

            startScanning();
        } catch (err) {
            console.error(err);
            alert('Error accessing camera: ' + err);
        }
    }

    async function startScanning() {
        try {
            const videoElement = document.getElementById('preview');
            
            // Set formats based on selection
            const formatSelect = document.getElementById('formatSelect');
            const selectedFormat = formatSelect.value;
            
            let hints = new Map();
            if (selectedFormat !== 'ALL') {
                hints.set(ZXing.DecodeHintType.POSSIBLE_FORMATS, [ZXing[selectedFormat]]);
            }

            await codeReader.decodeFromVideoDevice(selectedDeviceId, videoElement, (result, err) => {
                if (result) {
                    // Don't close scanner immediately, just process the result
                    handleScan(result.text);
                }
                if (err && !(err instanceof ZXing.NotFoundException)) {
                    console.error(err);
                }
            }, hints);

        } catch (err) {
            console.error(err);
            alert('Error starting scanner: ' + err);
        }
    }

    function showStatus(message, isError = false) {
        const statusDiv = $('#scannerStatus');
        statusDiv.removeClass('alert-success alert-danger')
            .addClass(isError ? 'alert-danger' : 'alert-success')
            .html(message)
            .show()
            .fadeIn();
        
        // Auto hide after 3 seconds
        setTimeout(() => {
            statusDiv.fadeOut();
        }, 3000);
    }

    function handleScan(content) {
        // Fill the input with scanned content
        $('#scanner_input').val(content);
        
        // Trigger the AJAX call
        $.ajax({
            url: '{$scannerUrl}',
            data: {
                scanner: 1,
                move_id: '{$currentMoveId}',
                thserial: content
            },
            method: 'GET',
            dataType: 'json',
            success: function(response) {
                if(response.message) {
                    showStatus(response.message);
                }
                $('#scanner_input').val('');
                
                // Don't reload the page or close modal
                // Instead, just update any necessary UI elements
                // For example, you might want to update a count or list of scanned items
            },
            error: function(xhr) {
                showStatus('Error processing serial: ' + (xhr.responseJSON?.message || 'Unknown error'), true);
                $('#scanner_input').val('');
            }
        });
    }

    // Handle format selection change
    $('#formatSelect').on('change', function() {
        if (codeReader) {
            codeReader.reset();
            startScanning();
        }
    });

    // Clean up when modal closes
    $('#scannerModal').on('hidden.bs.modal', function () {
        if (codeReader) {
            codeReader.reset();
        }
    });

    // Add manual close button handler if needed
    $('#closeScanner').on('click', function() {
        if (codeReader) {
            codeReader.reset();
        }
        $('#scannerModal').modal('hide');
        location.reload(); // Only reload when manually closing
    });
JS;

    $this->registerJs($js);
    $this->registerJs($scannerJs);
    // Update the modal HTML status area
    $this->registerCss('#scannerStatus {
        position: absolute;
        top: 10px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 1000;
        min-width: 200px;
        text-align: center;
    }');
    ?>