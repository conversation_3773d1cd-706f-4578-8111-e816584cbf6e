<?php $this->pageTitle=Yii::app()->name . ' - Add watches serial #'; ?>

<h1>Add NEW TH, LOX, MON serial(s)</h1>

<?php if(Yii::app()->user->hasFlash('thserial')): ?>
<div class="confirmation">
<?php echo Yii::app()->user->getFlash('thserial'); ?>
</div>
<?php else: ?>


<h2>Importing serials from file</h2>

<div class="yiiForm">

<?php echo CHtml::form('','post',array('enctype'=>'multipart/form-data'));?>

<?php echo CHtml::errorSummary($thserial); ?>

<?php echo CHtml::activeHiddenField($thserial,'mat_id'); ?>
<?php echo CHtml::activeHiddenField($thserial,'thserial'); ?>

<div class="simple">
<?php echo CHtml::active<PERSON>abel($thserial,'move_id'); ?>
<?php echo CHtml::activeTextField($thserial,'move_id'); ?>
</div>
<!--
<div class="simple">
<?php echo CHtml::activeLabel($thserial,'thserial'); ?>
<?php echo CHtml::activeTextField($thserial,'thserial'); ?>
</div>
<div class="note">2 or 3 letters and 4 numbers or 8 numbers</div>
-->

<div class="simple">
<?php echo CHtml::activeLabel($thserial,'thfile'); ?>
<?php echo CHtml::activeFileField($thserial,'thfile'); ?>
</div>

<div class="note">use excel file</div>


<div class="action">
<?php    echo CHtml::submitButton('Upload XLSX', array('name' => 'submit', 'id'=>'AddserialFormSubmit')); ?>
</div>

<?php echo CHtml::endForm(); ?>



</div><!-- yiiForm -->
<br><br>
<?php echo CHtml::button('Report - all move_id with thserials',array(
            'submit'=>array('Thserial/ShowImported' )
                ));
?>

<?php endif; ?>
