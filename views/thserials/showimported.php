<?php

use yii\helpers\Html;
use yii\grid\GridView;


/* @var $this yii\web\View */
/* @var $searchModel app\models\ThserialsSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'All thserials imported movements';
$this->params['breadcrumbs'][] = $this->title;

echo "<h2>Under construction</h2>";
echo "<p>Every sold watch with serial number will be recorded here.</p>";

echo GridView::widget([
    'dataProvider'=>$data1,
    'columns'=>[
        'move_id',
        'firma',
 	   'd1',  // display the 'name' attribute of the 'category' relation
    	'thserials',   // display the 'content' attribute as purified HTML
    	'pcsindetail',
     // [         // display a column with "view", "update" and "delete" buttons
     //                    'header' => 'Detail/Edit',
     //             'type' => 'raw',
     //                    'name'=>'$data1->move_id',
     //                    'value' => 'Html::link($data->move_id,Yii::$app->createUrl("Thserial/AddThOneserial",array("move_id"=>$data->move_id)))',
     //    ]
    ]
	]);
