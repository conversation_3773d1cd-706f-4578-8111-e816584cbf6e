<?php $this->pageTitle=Yii::app()->name . ' - Add Tag Heuer Serial'; 
Yii::import('application.vendors.qr.*');
?>

<h1>Add NEW TH,LOX,MON serial(s)</h1>

<?php if(Yii::app()->user->hasFlash('thserial')): ?>
<div class="confirmation">
<?php echo Yii::app()->user->getFlash('thserial'); ?>
</div>
<?php else: ?>

<h4>Serials for move_id=<?php echo $thserial->move_id; ?></h4>

<div class="yiiForm">

<?php echo CHtml::form('','post', array('id'=>'mainForm'));?>

<?php echo CHtml::errorSummary($thserial); ?>

<div class="simple">
<?php echo CHtml::activeLabel($thserial,'move_id'); ?>
<?php echo CHtml::activeTextField($thserial,'move_id'); ?>
</div>

<div class="simple">
<?php echo CHtml::activeLabel($thserial,'mat_id'); ?>
<?php echo CHtml::activeTextField($thserial,'mat_id'); ?>
</div>

<div class="simple">
<?php echo CHtml::activeLabel($thserial,'thserial'); ?>
<?php echo CHtml::activeTextField($thserial,'thserial'); ?>
</div>
<div class="note">2 or 3 letters and 4 numbers or 8 numbers</div>

<div class="action">
<?php    echo CHtml::submitButton('Enter Serial', array('name' => 'btnSubmit', 'id'=>'AddserialFormSubmit')); ?>
</div>
<?php echo CHtml::activeCheckbox($thserial,'repliid', array("onClick"=>'document.getElementById("mainForm").submit()')); ?> Show ONLY local possible TH serials
<div class="action">
<?php echo CHtml::link(" RELOAD PAGE ",array('Thserial/AddThOneserial','detId'=>$thserial->move_id, 'repliid'=>$thserial->repliid)); ?>
</div>
<?php echo CHtml::endForm(); ?>


</div><!-- yiiForm -->
<div class="grid_6">
    <div class="content">
    <br><br>
    <?php 
        require_once('qrcode.php');
        $qr = QRCode::getMinimumQRCode("TH_".$thserial->move_id, QR_ERROR_CORRECT_LEVEL_L);
        $qr->printHTML("6px");
    ?>
    </div>
</div>
<p>Missing serials for move_id:<br>
<?php
            $dbm = new db_sqlany;
                $query = "
					Select d.mat_id||'-'||d.pcs missing, p.model from dba.m_detail d
					join dba.product p on p.mat_id=d.mat_id where (kod like 'WTH%'  or kod in ('WLOX','WMON','PMON','MIDO')) and d.move_id='".$thserial->move_id.
					"' and d.mat_id||'-'||d.pcs not in (select mat_id||'-'||count(*) from dba.thserials where move_id='".$thserial->move_id."' and mat_id=d.mat_id
                    group by move_id,mat_id) ";
                $dbm->query($query);
                $rawData = $dbm->getAll();
                foreach ($rawData as $key => $value) {
                	echo "<b>".str_replace("-","  TotalPcs:",$value->missing)."</b>  ".$value->model."<br>";
                }
?>
</p>

<br><br>

<?php echo $this->renderPartial('showPossibleThserials', array(
        'data2'=>$data2,
        'update'=>true,
)); ?>

<br><br>
<?php echo $this->renderPartial('showMoveId', array(
        'data1'=>$data1,
        'update'=>true,
)); ?>

<br><br>

<?php endif; ?>

<script>
var maxid = -111;
setInterval(function worker() {
    $.getJSON('/estock3/index.php?r=Thserial/GetMaxId&move_id=<?php echo $thserial->move_id; ?>')
  .done(function( json ) {
    if( maxid == -111 ){
        maxid = json.maxid;
    }
    if(json.maxid != maxid){
        window.location.href = "/estock3/index.php?r=Thserial/AddThOneserial&detId=<?php echo $thserial->move_id ?>&repliid=<?php echo $thserial->repliid ?>";
    }
    })
  .fail(function( jqxhr, textStatus, error ) {
    var err = textStatus + ", " + error;
    console.log( "Request Failed: " + err );
    })
}, 2000);

</script>