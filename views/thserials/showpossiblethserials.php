                <div class="grid_12">
   <div>

<h4>Possible TH, LOX, MON serials</h4>
<?php

if( isset($_REQUEST['detId']) && $_REQUEST['detId'] > 0) {
    $mm = $_REQUEST['detId'];
} else {
    $mm = $_REQUEST['move_id'];
}
$this->widget('zii.widgets.grid.CGridView', array(
    'dataProvider'=>$data2,
    'columns'=>array(
	'thserial',
    'mat_id',
    'xmodel',
    /*    array(
        'header' => 'CHOOSE',
        'type'=>'raw',
        'value' => '"<a style=\"cursor:pointer\" onclick=\"$(\'#AddThserialOneForm_mat_id\').val(\'$data->mat_id\') && $(\'#AddThserialOneForm_thserial\').val(\'$data->thserial\') && $(\'form\').submit();\">SET</a>"', 
    ),*/
    array(            // display a column with "view", "update" and "delete" buttons
        'header' => 'Set',
        'type' => 'raw',
        'name'=>'set_matid',
        'value' => 'CHtml::link("SET",Yii::app()->createUrl("Thserial/AddThOneserial",array("mat_id"=>$data->mat_id,"detId"=>'.$mm.',"thserial"=>$data->thserial)))',
    )
)));
?>
                </div>
</div>