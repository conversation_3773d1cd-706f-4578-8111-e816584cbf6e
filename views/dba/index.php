<?php

/* @var $this yii\web\View */
/* @var $dataProvider yii\data\ActiveDataProvider */

use yii\helpers\Html;
use kartik\grid\GridView;

$this->title = 'Update or create Wstock or B2B user';
$this->params['breadcrumbs'][] = $this->title;

echo "<p>";
echo Html::a('Create User', ['create'], ['class' => 'btn btn-success']);
echo "</p>";

?>
<div class="stock-detail-index">

<?php 
    $gridColumn = [
        [
            'class' => 'yii\grid\ActionColumn',
            'template' => '{update}'
        ],
        'username','repli','email','profile','all_flags',
        // ['class' => 'kartik\grid\EditableColumn', 'attribute' => 'password_hash' ],
        // ['class' => 'kartik\grid\EditableColumn', 'attribute' => 'repli' ],
        // ['class' => 'kartik\grid\EditableColumn', 'attribute' => 'all_flags' ],
        // ['class' => 'kartik\grid\EditableColumn', 'attribute' => 'email' ],
        // ['class' => 'kartik\grid\EditableColumn', 'attribute' => 'profile' ],
    ]; 
    ?>
    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'columns' => $gridColumn,
        'pjax' => true,
        'pjaxSettings' => ['options' => ['id' => 'kv-pjax-container-wuser']],
        'panel' => [
            'type' => GridView::TYPE_PRIMARY,
            'heading' => '<span class="glyphicon glyphicon-book"></span>  ' . Html::encode($this->title),
        ],
        'export' => false,
    ]); ?>

</div>
