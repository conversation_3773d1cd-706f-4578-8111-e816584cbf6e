<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use yii\widgets\MaskedInput;
use kartik\password\PasswordInput;
use kartik\date\DatePicker;


/* @var $this yii\web\View */
/* @var $model app\models\StockDetail */
/* @var $form yii\widgets\ActiveForm */

?>

<div class="stock-detail-form">

    <?php $form = ActiveForm::begin(); ?>

    <?= $form->errorSummary($model); ?>

    <?= $form->field($model, 'id')->textInput(['maxlength' => true, 'placeholder' => 'id', 'disabled' => 'true']) ?>

    <?= $form->field($model, 'repli')->textInput(['maxlength' => true, 'placeholder' => 'repli']) ?>

    <?= $form->field($model, 'username')->textInput(['maxlength' => true, 'placeholder' => 'username']) ?>

    <?= $form->field($model, 'all_flags')->textInput(['maxlength' => true, 'placeholder' => 'all_flags']) ?>

    <?= $form->field($model, 'email')->textInput(['maxlength' => true, 'placeholder' => 'email']) ?>

    <?= $form->field($model, 'profile')->textInput(['maxlength' => true, 'placeholder' => 'b2b profile in special format only!']) ?>

    <?= $form->field($model, 'password_hash')->textInput(['maxlength' => true, 'placeholder' => 'generated by systems', 'disabled' => 'true']) ?>

    <?= $form->field($model, 'password_reset_token')->widget(PasswordInput::class, [
            'togglePlacement' => 'left',
    ]);

     ?>
    <p>
        <?= Html::a(Yii::t('app', 'Generate Pwd'), null, ['class' => 'btn btn-success', 'id' => 'genpwdbtn']) ?>
    </p>

    <!-- <?= $form->field($model, 'banned_at')->textInput(['maxlength' => true, 'placeholder' => 'if date here yyyy-mm-dd, will be inactive since this date']) ?> -->
    <?= $form->field($model, 'banned_at')->widget(DatePicker::class,[
    'name' => 'banned_at',
    'value' => Date('Y-m-d'),
    'options' => ['placeholder' => 'if date here yyyy-mm-dd, will be inactive since this date'],
    'pluginOptions' => [
        'todayHighlight' => true,
        'todayBtn' => true,
        'format' => 'yyyy-mm-dd',
        'autoclose' => true,
    ]
]);?>
    <?php  $model->isNewRecord ? $model->status = 0 : $model->status = $model->status; ?>
    <?= $form->field($model, 'status')->radioList(['0'=>"send password to user's email",'1'=>'do not send email'],['separator'=>'&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'] ); ?>


    <div class="form-group">
        <?= Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>


    <?php 
    $script = <<< JS

    function setPwd(){
        $.get('/customers/getpwd',function(data){
            var pwd = $.parseJSON(data);
            $('#user-password_reset_token').val(pwd.pwd);
            })       
    }

    $("#genpwdbtn").on('click',function(){
        setPwd();
        });

    setPwd();



JS;
$this->registerJs($script);
?>
