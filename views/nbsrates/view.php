<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use kartik\grid\GridView;

/* @var $this yii\web\View */
/* @var $model app\models\Nbsrates */

$this->title = $model->nbs_date;
$this->params['breadcrumbs'][] = ['label' => 'Nbsrates', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="nbsrates-view">

    <div class="row">
        <div class="col-sm-9">
            <h2><?= 'Nbsrates'.' '. Html::encode($this->title) ?></h2>
        </div>
        <div class="col-sm-3" style="margin-top: 15px">
<?=             
             Html::a('<i class="fa glyphicon glyphicon-hand-up"></i> ' . 'PDF', 
                ['pdf', 'nbs_date' => $model->nbs_date, 'nbs_cur' => $model->nbs_cur],
                [
                    'class' => 'btn btn-danger',
                    'target' => '_blank',
                    'data-toggle' => 'tooltip',
                    'title' => 'Will open the generated PDF file in a new window'
                ]
            )?>
            
            <?= Html::a('Update', ['update', 'nbs_date' => $model->nbs_date, 'nbs_cur' => $model->nbs_cur], ['class' => 'btn btn-primary']) ?>
            <?= Html::a('Delete', ['delete', 'nbs_date' => $model->nbs_date, 'nbs_cur' => $model->nbs_cur], [
                'class' => 'btn btn-danger',
                'data' => [
                    'confirm' => 'Are you sure you want to delete this item?',
                    'method' => 'post',
                ],
            ])
            ?>
        </div>
    </div>

    <div class="row">
<?php 
    $gridColumn = [
        'nbs_date',
        'nbs_cur',
        'nbs_rate',
        'nbs_multi',
    ];
    echo DetailView::widget([
        'model' => $model,
        'attributes' => $gridColumn
    ]);
?>
    </div>
</div>
