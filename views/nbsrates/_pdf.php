<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use kartik\grid\GridView;

/* @var $this yii\web\View */
/* @var $model app\models\Nbsrates */

$this->title = $model->nbs_date;
$this->params['breadcrumbs'][] = ['label' => 'Nbsrates', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="nbsrates-view">

    <div class="row">
        <div class="col-sm-9">
            <h2><?= 'Nbsrates'.' '. Html::encode($this->title) ?></h2>
        </div>
    </div>

    <div class="row">
<?php 
    $gridColumn = [
        'nbs_date',
        'nbs_cur',
        'nbs_rate',
        'nbs_multi',
    ];
    echo DetailView::widget([
        'model' => $model,
        'attributes' => $gridColumn
    ]); 
?>
    </div>
</div>
