<?php

/* @var $this yii\web\View */
/* @var $searchModel app\models\NbsratesSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

use yii\helpers\Html;
use kartik\export\ExportMenu;
use kartik\grid\GridView;
use kartik\typeahead\Typeahead;
use yii\helpers\Url;

$this->title = 'Nbsrates';
$this->params['breadcrumbs'][] = $this->title;
$search = "$('.search-button').click(function(){
	$('.search-form').toggle(1000);
	return false;
});";
$this->registerJs($search);
?>
<div class="nbsrates-index">

    <h1><?= Html::encode($this->title) ?></h1>
    <?php // echo $this->render('_search', ['model' => $searchModel]);
/**
 * Prefetches some data then relies on remote requests (ajax) via Controller action
 * for suggestions when prefetched data is insufficient. 
 * The json encoded prefetch source is: 
 * [{value:"Andorra"}, {value:"United Arab Emirates"}, {value:"Afghanistan"}, 
 *  {value:"Antigua and Barbuda"}, {value:"Anguilla"}, {value:"Albania"}]
 */
// echo Typeahead::widget([
//     'name' => 'country',
//     'options' => ['placeholder' => 'Filter as you type ...'],
//     'pluginOptions' => ['highlight'=>true],
//     'pluginEvents' => [
//         "typeahead:selected" => "function(e,d) { console.log(\"typeahead:selected\",e,d,d.mat_id); }",
//         "typeahead:autocompleted" => "function() { console.log(\"typeahead:autocompleted\"); }",
//     ],
//     'scrollable' => true,
//     'dataset' => [
//         [
//             'datumTokenizer' => "Bloodhound.tokenizers.obj.whitespace('value')",
//             'display' => 'model',
//             'limit' => 10,
// //            'prefetch' => $baseUrl . '/samples/countries.json',
//             'remote' => [
//                 'url' => Url::to(['product/productlist']) . '?q=%QUERY',
//                 'wildcard' => '%QUERY'
//             ]
//         ]
//     ]
// ]);
?>
    <p>
        <?= Html::a('Create Nbsrates', ['create'], ['class' => 'btn btn-success']) ?>
        <?= Html::a('Advance Search', '#', ['class' => 'btn btn-info search-button']) ?>
    </p>
    <div class="search-form" style="display:none">
        <?=  $this->render('_search', ['model' => $searchModel]); ?>
    </div>
    <?php 
    $gridColumn = [
        ['class' => 'yii\grid\SerialColumn'],
        'nbs_date',
        'nbs_cur',
        'nbs_rate',
        'nbs_multi',
        [
            'class' => 'yii\grid\ActionColumn',
        ],
    ]; 
    ?>
    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => $gridColumn,
        'pjax' => true,
        'pjaxSettings' => ['options' => ['id' => 'kv-pjax-container-nbsrates']],
        'panel' => [
            'type' => GridView::TYPE_PRIMARY,
            'heading' => '<span class="glyphicon glyphicon-book"></span>  ' . Html::encode($this->title),
        ],
        // your toolbar can include the additional full export menu
        'toolbar' => [
            ExportMenu::widget([
                'dataProvider' => $dataProvider,
                'columns' => $gridColumn,
                'target' => ExportMenu::TARGET_BLANK,
                'fontAwesome' => true,
                'pjax' => false,
                'clearBuffers' => true,
                'dropdownOptions' => [
                    'label' => 'Full',
                    'class' => 'btn btn-default',
                    'itemsBefore' => [
                        '<li class="dropdown-header">Export All Data</li>',
                    ],
                ],
            ]) ,
        ],
    ]); ?>

</div>
