<?php 
use app\controllers\EstockTools;
use app\models\Selects;
$items = [];
$searchPlaceholder = "Search in documents...";

//Set Menu Items
if(!Yii::$app->user->isGuest ){
    if( EstockTools::isUserShop() ) {
        $searchPlaceholder = "Search klub member...";
        $items = [
                    ['label' => 'Menu KLUB WDL', 'options' => ['class' => 'header']],
                    ['label' => 'Klub members', 'icon' => 'file-code-o', 'url' => ['/customers']],

                    ['label' => 'Menu SHOP', 'options' => ['class' => 'header']],
                    ['label' => 'Clipboards', 'icon' => 'clipboard', 'url' => ['tempmove/shop']],
                    ['label' => 'Movements', 'icon' => 'book', 'url' => ['movement/mymoves']],
                    ['label' => 'Product', 'icon' => 'cubes', 'url' => ['product/indexinshop']],
                    ['label' => 'On Stock', 'icon' => 'info', 'url' => ['movement/onstock']],
                    ['label' => 'DOC numbers', 'icon' => 'info', 'url' => ['movement/docnumbers']],
                    ['label' => 'Sales Report', 'icon' => 'btc', 'url' => ['movement/salesreport']],
                    ['label' => 'UN Security DB', 'icon' => 'search', 'url' => ['site/unsearch']],
                ]; 
    }
    else if( EstockTools::isUserWstock() ) {
        $items = [
            ['label' => 'Menu WSTOCK', 'options' => ['class' => 'header']],
            ['label' => 'On Stock', 'icon' => 'info', 'url' => ['movement/onstock']],
            ['label' => 'Movements', 'icon' => 'book', 'url' => ['movement/index', 'sort' => '-move_id']],
            ['label' => 'Shoptet Eshop', 'icon' => 'book', 'url' => ['shoptetorders/index?sort=-order_id']],
            ['label' => 'Tools', 'icon' => 'gears', 'url' => "#",
                'items' => [
                    ['label' => 'Movement templates', 'icon' => 'clipboard', 'url' => "#",
                        'items' => EstockTools::getClipboardsMenu() 
                    ],
                    ['label' => 'Product', 'icon' => 'cubes', 'url' => ['product/index']],
                    ['label' => 'Additional tables', 'options' => ['class' => 'header']],
                    ['label' => 'Document numbers', 'icon' => 'list-ol', 'url' => ['document/index']],
                ]
           ],

            ['label' => 'Services', 'options' => ['class' => 'header']],
        ]; 
} else if( EstockTools::isUserWman() ) {
        $items = [
                    ['label' => 'Menu WSTOCK', 'options' => ['class' => 'header']],
                    ['label' => 'On Stock', 'icon' => 'info', 'url' => ['movement/onstock']],
                    ['label' => 'Movements', 'icon' => 'book', 'url' => ['movement/index', 'sort' => '-move_id']],
                    ['label' => 'Shoptet Eshop', 'icon' => 'book', 'url' => ['shoptetorders/index?sort=-order_id']],
                    ['label' => 'Tools', 'icon' => 'gears', 'url' => "#",
                    'items' => [
    
                        ['label' => 'Movement templates', 'icon' => 'clipboard', 'url' => "#",
                            'items' => EstockTools::getClipboardsMenu() 
                        ],
                        ['label' => 'Address', 'icon' => 'user', 'url' => ['address/index']],
                        ['label' => 'Product', 'icon' => 'cubes', 'url' => ['product/index']],
                        ['label' => 'Additional tables', 'options' => ['class' => 'header']],
                        ['label' => 'Document numbers', 'icon' => 'list-ol', 'url' => ['document/index']],
                        ['label' => 'KOD of product', 'icon' => 'list-ol', 'url' => ['kod/index']],
                        ['label' => 'STOCK detail, names', 'icon' => 'list-ol', 'url' => ['stockdetail/index']],
                        ['label' => 'MovementTYPEs', 'icon' => 'list-ol', 'url' => ['mtype/index']],
                        ['label' => 'VAT in EU countries', 'icon' => 'list-ol', 'url' => ['vat/index']],
                        ['label' => 'Services', 'options' => ['class' => 'header']],
                        [
                                'label' => 'Statistics',
                                'icon' => 'euro',
                                'url' => '#',
                                'items' => [
                                    // ['label' => 'Sales', 'icon' => 'euro', 'url' => ['statistics/viewdetail']],
                                    ['label' => 'Manager stats', 'icon' => 'euro', 'url' => ['statistics/viewmanagerstats']],
                                    ['label' => 'Retail stats by model', 'icon' => 'euro', 'url' => ['statistics/viewmanager2stats']],
                                        ]
                                    ],
                                ],
                            ],
                    ['label' => 'MISC', 'options' => ['class' => 'header']],

                ]; 
                //Special SQL selects for stock managers
                $userselects = [
                                ['label' => 'Custom select', 'icon' => 'file-code-o', 'url' => ['sql/customselect'],],
                            ];
                            foreach (Yii::$app->params['query'] as $key => $value) {
                                $xitems = [];
                                foreach ($value as $key2 => $value2) {
                                    $xitems[] = ['label' => $key2, 'icon' => 'file-code-o', 'url' => ['sql/sqlconfigcall','name'=>$key,'q'=>$key2],];
                                }
                                $userselects[] = ['label' => $key, 'icon' => 'cube', 'url' => '#', 'items' => $xitems];
                            }


                 array_push($items,
                        [
                            'label' => 'SQL Selects',
                            'icon' => 'flash',
                            'url' => '#',
                            'items' => $userselects
                        ]);
    } else if( EstockTools::isUserStock() ) {
                            $items = [
                                        ['label' => 'Menu WSTOCK', 'options' => ['class' => 'header']],
                                        ['label' => 'Clipboards', 'icon' => 'clipboard', 'url' => "#",
                                            'items' => EstockTools::getClipboardsMenu() 
                                        ],
                                        ['label' => 'Movements', 'icon' => 'book', 'url' => ['movement/index', 'sort' => '-move_id']],
                                        ['label' => 'Address', 'icon' => 'user', 'url' => ['address/index']],
                                        ['label' => 'Product', 'icon' => 'cubes', 'url' => ['product/index']],
                                        [
                                                'label' => 'Other tables',
                                                'icon' => 'book',
                                                'url' => '#',
                                                'items' => [
                                                            ['label' => 'Document numbers', 'icon' => 'list-ol', 'url' => ['document/index']],
                                                            ['label' => 'KOD of product', 'icon' => 'list-ol', 'url' => ['kod/index']],
                                                            ['label' => 'DISCOUNT groups of product', 'icon' => 'list-ol', 'url' => ['productdisc/index']],
                                                            ['label' => 'STOCK detail, names', 'icon' => 'list-ol', 'url' => ['stockdetail/index']],
                                                            ['label' => 'VAT in EU countries', 'icon' => 'list-ol', 'url' => ['vat/index']],
                                                        ]
                                                    ],
                                        ['label' => 'On Stock', 'icon' => 'info', 'url' => ['movement/onstock']],
                                        [
                                                'label' => 'Statistics',
                                                'icon' => 'euro',
                                                'url' => '#',
                                                'items' => [
                                                    // ['label' => 'Sales', 'icon' => 'euro', 'url' => ['statistics/viewdetail']],
                                                    ['label' => 'Manager stats', 'icon' => 'euro', 'url' => ['statistics/viewmanagerstats']],
                                                    ['label' => 'Retail stats by model', 'icon' => 'euro', 'url' => ['statistics/viewmanager2stats']],
                                                        ]
                                                    ],
                                        ['label' => 'Serial # track', 'icon' => 'barcode', 'url' => ['thserials/indexall']],
                    
                                        ['label' => 'Menu KLUB WDL', 'options' => ['class' => 'header']],
                                        ['label' => 'Klub members', 'icon' => 'file-code-o', 'url' => ['/customers']],
                    
                                        ['label' => 'MISC', 'options' => ['class' => 'header']],
                    
                                    ]; 
                                    //Special SQL selects for stock managers
                                    $userselects = [
                                                    ['label' => 'Custom select', 'icon' => 'file-code-o', 'url' => ['sql/customselect'],],
                                                    ['label' => 'Users', 'icon' => 'file-code-o', 'url' => ['sql/users'],],
                                                    ['label' => 'M Type', 'icon' => 'file-code-o', 'url' => ['sql/mtype'],],
                                                    ['label' => 'Document', 'icon' => 'file-code-o', 'url' => ['sql/document'],],
                                                    ['label' => 'B2B REPORT since 2023', 'icon' => 'file-code-o', 'url' => ['sql/b2breport'],],
                                                    ['label' => 'Movement all detail', 'icon' => 'file-code-o', 'url' => ['sql/createdocreport'],],
                                                ];

                                    //list of special selects for user
                                    if( isset(Selects::$selectsbyname[Yii::$app->user->identity->username]) ){
                                        foreach (Selects::$selectsbyname[Yii::$app->user->identity->username] as $key2 => $value2) {
                                            $xitems[] = ['label' => $key2, 'icon' => 'file-code-o', 'url' => ['sql/sqlcalluser','q'=>$key2],];
                                        }
                                        $userselects[] = ['label' => Yii::$app->user->identity->username, 'icon' => 'cube', 'url' => '#', 'items' => $xitems];
                                    }
                                    
                                    //list of special selects for user visible for all
                                    foreach (Selects::$query as $key => $value) {
                                        $xitems = [];
                                        foreach ($value as $key2 => $value2) {
                                            $xitems[] = ['label' => $key2, 'icon' => 'file-code-o', 'url' => ['sql/sqlcall','name'=>$key,'q'=>$key2],];
                                        }
                                        $userselects[] = ['label' => $key, 'icon' => 'cube', 'url' => '#', 'items' => $xitems];
                                    }
                    
                                     array_push($items,
                                            ['label' => 'Eshop', 'icon' => 'shopping-cart', 'url' => ['/eshop/index']],
                                            [
                                                'label' => 'SQL Selects',
                                                'icon' => 'flash',
                                                'url' => '#',
                                                'items' => $userselects
                                            ]);
                    
                    

    } elseif ( EstockTools::isUserB2b() ){
                $items = [

                    ['label' => 'Menu B2B', 'options' => ['class' => 'header']],
                    ['label' => 'Create order', 'icon' => 'clipboard', 'url' => ['tempmove/b2b']],
                    ['label' => 'Movements', 'icon' => 'book', 'url' => ['movement/b2bmoves']],
                ]; 

    }

    if( EstockTools::isUserUcto() ){
                 array_push($items,
                        ['label' => 'UCTO actions', 'icon' => 'book', 'url' => ['/sql/uctoselect']]
                    );        
    }

    if( EstockTools::isUserAdminForUsers() ){
        array_push($items,
        ['label' => 'B2B USER actions', 'icon' => 'book', 'url' => ['dba/b2bindex']],
        ['label' => 'WSTOCK USER actions', 'icon' => 'book', 'url' => ['dba/']]
    );        
}

    //INTREX ONLY THIS ONE
    if( Yii::$app->user->identity->username === 'intrex' ){
                $items = [
                    ['label' => 'What is on stock', 'icon' => 'cube', 'url' => ['movement/onstockforintrex']],
                ]; 
    }


    //Special SQL selects for stock managers
    if( EstockTools::isUserStock()){
        array_push($items,
                        [
                            'label' => 'Reports',
                            'icon' => 'flash',
                            'url' => '#',
                            'items' => [
                                ['label' => 'B2B REPORT since 2023', 'icon' => 'file-code-o', 'url' => ['sql/b2breport'],],
                                ['label' => 'ZAL REPORT', 'icon' => 'file-code-o', 'url' => ['sql/zalreport'],],
                                ['label' => 'ZAL RA REPORT', 'icon' => 'file-code-o', 'url' => ['sql/zalreport?text1=ZAL%20RA%25'],],
                                ['label' => 'ZAL SW REPORT', 'icon' => 'file-code-o', 'url' => ['sql/zalreport?text1=ZAL%20SW%25"'],],
                            ],
                        ]
        );        
    }

    //Special SQL selects for stock managers
    if( EstockTools::isUserB2b()){
        array_push($items,
                        [
                            'label' => 'Reports',
                            'icon' => 'flash',
                            'url' => '#',
                            'items' => [
                                ['label' => 'B2B Backorder report', 'icon' => 'file-code-o', 'url' => ['b2b/report'],],
                            ],
                        ]
        );        
    }



    //DBA tools
    if ( EstockTools::isUserDba() ){
        array_push($items,
            ['label' => 'DBA links', 'options' => ['class' => 'header']],
            ['label' => 'PRICE detail, names', 'icon' => 'list-ol', 'url' => ['pricedetail/index']],
            ['label' => 'MovementTYPEs', 'icon' => 'list-ol', 'url' => ['mtype/index']],
            ['label' => 'Product in shop', 'icon' => 'cubes', 'url' => ['product/indexinshop']],
            ['label' => 'UN Security DB', 'icon' => 'search', 'url' => ['site/unsearch']],
            ['label' => 'Gii', 'icon' => 'file-code-o', 'url' => ['/gii']],
            ['label' => 'Debug', 'icon' => 'dashboard', 'url' => ['/debug']]
                    );
    }
}

    //Add Login for everybody
    array_push($items,['label' => 'Menu Session', 'options' => ['class' => 'header']],
    ['label' => 'Login', 'icon' => 'sign-in', 'url' => ['site/login'], 'visible' => Yii::$app->user->isGuest],
    // ['label' => 'Login with Authentik', 'icon' => 'sign-in', 'url' => ['auth/auth', 'authclient' => 'authentik'], 'visible' => Yii::$app->user->isGuest],
    ['label' => 'Logout', 'icon' => 'sign-out', 'url' => ['site/logout'], 'visible' => !Yii::$app->user->isGuest]
    );

$searchbar = <<<SRB
        <!-- search form -->
        <form action="/" method="get" class="sidebar-form">
            <div class="input-group">
                <input type="text" name="q" class="form-control" placeholder="$searchPlaceholder"/>
              <span class="input-group-btn">
                <button type='submit' name='search' id='search-btn' class="btn btn-flat"><i class="fa fa-search"></i>
                </button>
              </span>
            </div>
        </form>
        <!-- /.search form -->
SRB

?>
<aside class="main-sidebar">

    <section class="sidebar">

        <!-- Sidebar user panel -->

        <?= Yii::$app->user->isGuest?'':(EstockTools::isUserB2b()?'':$searchbar); ?>

        <?= dmstr\widgets\Menu::widget(
            [
                'options' => ['class' => 'sidebar-menu tree', 'data-widget'=> 'tree'],
                'items' => $items
            ]
        ) ?>

    </section>

</aside>
