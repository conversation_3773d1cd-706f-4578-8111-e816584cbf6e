<?php
use yii\helpers\Html;
use app\controllers\EstockTools;
use yii\web\AssetBundle;

/* @var $this \yii\web\View */
/* @var $content string */
if ( !Yii::$app->user->isGuest ){
    $show = Yii::$app->user->identity->username;
    $img = "/images/female.png";
    $adinfo = "login successful...";
} else {
    $show = "Guest";
    $img = "/images/guest.png";
    $adinfo = "nobody - please login first";
}
?>

<header class="main-header">
            <?= Html::a('<span class="logo-mini"><small>'. Yii::$app->name .'</small></span><span class="logo-lg"> <b>'. Yii::$app->name .'</b></span>', Yii::$app->homeUrl, ['class' => 'logo']) ?>

    <nav class="navbar navbar-static-top" role="navigation">
                <a href="#" class="sidebar-toggle" data-toggle="push-menu" role="button">
            <span class="sr-only">Toggle navigation</span>
        </a>

        <div class="navbar-custom-menu">

            <ul class="nav navbar-nav">

                <!-- User Account: style can be found in dropdown.less -->

                <li class="dropdown user user-menu">
                    <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                        <img src="<?= $img ?>" class="user-image" alt="User Image"/>
                        <span class="hidden-xs"><?= $show ?></span>
                    </a>
                    <ul class="dropdown-menu">
                        <!-- User image -->
                        <li class="user-header">
                            <img src="<?= $img ?>" class="img-circle"
                                 alt="User Image"/>

                            <p>
                                <?= $show ?>
                                <small><?= $adinfo ?></small>
                            </p>
                        </li>
                        <!-- Menu Body -->
<!--                         <li class="user-body">
                            <div class="col-xs-4 text-center">
                                <a href="#">Sales</a>
                            </div>
                            <div class="col-xs-4 text-center">
                                <a href="#">Orders</a>
                            </div>
                            <div class="col-xs-4 text-center">
                                <a href="#">Awards</a>
                            </div>
                        </li> -->
                        <!-- Menu Footer-->
                        <li class="user-footer">
                            <div class="pull-left">
                                <a href="/muserconfig/profile" class="btn btn-default btn-flat">Profile</a>
                            </div>
                            <div class="pull-right">
                                <?= Html::a(
                                    'Sign out',
                                    ['/site/logout'],
                                    ['data-method' => 'post', 'class' => 'btn btn-default btn-flat']
                                ) ?>
                            </div>
                        </li>
                    </ul>
                </li>

            </ul>
        </div>



    </nav>
</header>
