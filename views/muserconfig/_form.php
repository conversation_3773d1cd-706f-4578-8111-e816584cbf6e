<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use kartik\date\DatePicker;
/* @var $this yii\web\View */
/* @var $model app\models\MUserConfig */
/* @var $form yii\widgets\ActiveForm */

//echo "<pre>";
$mymtypes = \app\controllers\EstockTools::getUserMtypes() ;
?>

<div class="muser-config-form">

    <?php $form = ActiveForm::begin(); ?>

<!--     <?= $form->field($model, 'userid')->textInput() ?>

    <?= $form->field($model, 'db')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'clip_id')->textInput() ?>

    <?= $form->field($model, 'pictures')->textInput(['maxlength' => true]) ?> -->
<div class="form-group row mb-0">

    <div class="col-sm-2">

    <?= $form->field($model, 'price')->widget(\kartik\select2\Select2::class, [
        'data' => \yii\helpers\ArrayHelper::map(\app\models\PriceDetail::find()->where("price_id not like 'p__' or price_id='p0e'")->
        asArray()->all(), 'price_id', 'desc'),
        'options' => ['placeholder' => 'Price'],
    ]); ?>

    </div>
    <div class="col-sm-6">
    <?= $form->field($model, 'adr')->widget(\kartik\select2\Select2::class, [
        'data' => \yii\helpers\ArrayHelper::map(\app\models\Address::find()->select(['adr_id',"adr_id||' '||\"firma\"||', '||\"city\"||', '||\"iso\" as firma"])->asArray()->all(), 'adr_id','firma'),
        'options' => ['placeholder' => 'Firma filter'],
        'pluginOptions' => [
            'allowClear' => true
        ],
        'pluginEvents' => [
          "select2:select" => "function() { 
                var label=$('#muserconfig-adr :selected').parent().attr('label');
                document.getElementById('muserconfig-adr_id').value=this.value;
           }",
        ]
    ]); ?>
            </div>
            <div class="col-sm-4">    
    <?= $form->field($model, 'adr_id')->textInput() ?>
            </div>
    </div>

    <div class="form-group row mb-0">
        <div class="col-sm-6">
        <?= $form->field($model, 'm_type_id')->widget(\kartik\select2\Select2::class, [
            'data' => \yii\helpers\ArrayHelper::map(\app\models\MType::find()->select(['m_type_id',"m_type_id||' '||\"name\" as name",
            "moving"])->where(['in','m_type_id',$mymtypes])->asArray()->all(), 'm_type_id', 'name', 'moving'),
            'options' => ['placeholder' => 'Movement Type'],
            'pluginEvents' => [
                    "select2:select" => "function() { 
                            var label=$('#muserconfig-m_type_id :selected').parent().attr('label');
                            if ( label == 'N'){
                                $('#muserconfig-stock_id1').attr(\"disabled\",\"disabled\");
                                $('#muserconfig-stock_id2').attr(\"disabled\",\"disabled\");
                                $('#muserconfig-stock_id1').val('null').trigger('change');
                                $('#muserconfig-stock_id2').val('null').trigger('change');
                            } else if ( label == 'R'){
                                $('#muserconfig-stock_id1').attr(\"disabled\",\"disabled\");
                                $('#muserconfig-stock_id2').attr(\"disabled\",false);
                                $('#muserconfig-stock_id1').val('null').trigger('change');
                                $('#muserconfig-stock_id2').val('null').trigger('change');
                            } else if ( label == 'S'){
                                $('#muserconfig-stock_id2').attr(\"disabled\",\"disabled\");
                                $('#muserconfig-stock_id1').attr(\"disabled\",false);
                                $('#muserconfig-stock_id1').val('null').trigger('change');
                                $('#muserconfig-stock_id2').val('null').trigger('change');
                            } else if ( label == 'M'){
                                $('#muserconfig-stock_id2').attr(\"disabled\",false);
                                $('#muserconfig-stock_id1').attr(\"disabled\",false);
                                $('#muserconfig-stock_id1').val('null').trigger('change');
                                $('#muserconfig-stock_id2').val('null').trigger('change');
                            }

                     }",
                ]
        ]); ?>
        </div>
        <div class="col-sm-3">

        <?= $form->field($model, 'stock_id1')->widget(\kartik\select2\Select2::class, [
            'data' => \yii\helpers\ArrayHelper::map(\app\models\StockDetail::find()->asArray()->all(), 'stock_id', 'sdescr'),
            'options' => ['placeholder' => 'From stock'],
        ]); ?>
        </div>
        <div class="col-sm-3">
            <?= $form->field($model, 'stock_id2')->widget(\kartik\select2\Select2::class, [
                'data' => \yii\helpers\ArrayHelper::map(\app\models\StockDetail::find()->asArray()->all(), 'stock_id', 'sdescr'),
                'options' => ['placeholder' => 'To stock'],
            ]); ?>
        </div>
    </div>
    <div class="form-group row mb-0">
        <div class="col-sm-6">
        <?= $form->field($model, 'text1')->textInput() ?>
        </div>
    <div class="col-sm-6">

    <?= $form->field($model, 'text2')->textInput() ?>
            </div>
    </div>

    <div class="form-group row mb-0">

    <div class="col-sm-4">

    <?= $form->field($model, 'd1')->widget(DatePicker::class,[
    'name' => 'd1',
    'value' => Date('Y-m-d'),
    'options' => ['placeholder' => 'Enter date ...'],
    'pluginOptions' => [
        'todayHighlight' => true,
        'todayBtn' => true,
        'format' => 'yyyy-mm-dd',
        'autoclose' => true,
    ]
]);?>
            </div>
            <div class="col-sm-4">  
    <?= $form->field($model, 'd2')->widget(DatePicker::class,[
    'name' => 'd1',
    'value' => Date('Y-m-d'),
    'options' => ['placeholder' => 'Enter date ...'],
    'pluginOptions' => [
        'todayHighlight' => true,
        'todayBtn' => true,
        'format' => 'yyyy-mm-dd',
        'autoclose' => true,
    ]
]);?>
            </div>
            <div class="col-sm-4">  
    <?= $form->field($model, 'd3')->widget(DatePicker::class,[
    'name' => 'd1',
    'value' => Date('Y-m-d'),
    'options' => ['placeholder' => 'Enter date ...'],
    'pluginOptions' => [
        'todayHighlight' => true,
        'todayBtn' => true,
        'format' => 'yyyy-mm-dd',
        'autoclose' => true,
    ]
]);?>
            </div>
    </div>

<!--     <?= $form->field($model, 'active')->textInput() ?>
 -->

    <div class="form-group">
        <?= Html::submitButton($model->isNewRecord ? 'Create new settings' : 'Update settings', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
    </div>


    <?php ActiveForm::end(); ?>

</div>
