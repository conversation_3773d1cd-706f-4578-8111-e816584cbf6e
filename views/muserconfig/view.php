<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model app\models\MUserConfig */

$this->title = $model->id;
$this->params['breadcrumbs'][] = ['label' => 'M User Configs', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
\yii\web\YiiAsset::register($this);
?>
<div class="muser-config-view">

    <h1><?= Html::encode($this->title) ?></h1>

    <p>
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary']) ?>
        <?= Html::a('Delete', ['delete', 'id' => $model->id], [
            'class' => 'btn btn-danger',
            'data' => [
                'confirm' => 'Are you sure you want to delete this item?',
                'method' => 'post',
            ],
        ]) ?>
    </p>

    <?= DetailView::widget([
        'model' => $model,
        'attributes' => [
            'id',
            'userid',
            'db',
            'clip_id',
            'pictures',
            'price',
            'adr_id',
            'adr_repli_id',
            'stock_id1',
            'stock_id2',
            'm_type_id',
            'text1',
            'text2',
            'd1',
            'd2',
            'd3',
            'active',
        ],
    ]) ?>

</div>
