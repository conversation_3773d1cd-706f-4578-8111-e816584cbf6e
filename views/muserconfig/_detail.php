<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use kartik\grid\GridView;

/* @var $this yii\web\View */
/* @var $model app\models\MUserConfig */

?>
<div class="muser-config-view">

    <div class="row">
        <div class="col-sm-9">
            <h2><?= Html::encode($model->id) ?></h2>
        </div>
    </div>

    <div class="row">
<?php 
    $gridColumn = [
        ['attribute' => 'id', 'visible' => false],
        [
            'attribute' => 'user.username',
            'label' => 'Userid',
        ],
        'db',
        'clip_id',
        'pictures',
        'price',
        [
            'attribute' => 'adr.adr_id',
            'label' => 'Adr',
        ],
        'adr_repli_id',
        'stock_id1',
        'stock_id2',
        'm_type_id',
        'text1',
        'text2',
        'd1',
        'd2',
        'd3',
        'active',
    ];
    echo DetailView::widget([
        'model' => $model,
        'attributes' => $gridColumn
    ]); 
?>
    </div>
</div>