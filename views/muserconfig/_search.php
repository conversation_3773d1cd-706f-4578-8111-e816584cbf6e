<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model app\models\MUserConfigSearch */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="muser-config-search">

    <?php $form = ActiveForm::begin([
        'action' => ['index'],
        'method' => 'get',
        'options' => [
            'data-pjax' => 1
        ],
    ]); ?>

    <?= $form->field($model, 'id') ?>

    <?= $form->field($model, 'userid') ?>

    <?= $form->field($model, 'db') ?>

    <?= $form->field($model, 'clip_id') ?>

    <?= $form->field($model, 'pictures') ?>

    <?php // echo $form->field($model, 'price') ?>

    <?php // echo $form->field($model, 'adr_id') ?>

    <?php // echo $form->field($model, 'adr_repli_id') ?>

    <?php // echo $form->field($model, 'stock_id1') ?>

    <?php // echo $form->field($model, 'stock_id2') ?>

    <?php // echo $form->field($model, 'm_type_id') ?>

    <?php // echo $form->field($model, 'text1') ?>

    <?php // echo $form->field($model, 'text2') ?>

    <?php // echo $form->field($model, 'd1') ?>

    <?php // echo $form->field($model, 'd2') ?>

    <?php // echo $form->field($model, 'd3') ?>

    <?php // echo $form->field($model, 'active') ?>

    <div class="form-group">
        <?= Html::submitButton('Search', ['class' => 'btn btn-primary']) ?>
        <?= Html::resetButton('Reset', ['class' => 'btn btn-default']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
