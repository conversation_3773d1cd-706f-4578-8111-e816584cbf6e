<?php

use yii\helpers\Html;
use kartik\grid\GridView;
use yii\widgets\Pjax;
/* @var $this yii\web\View */
/* @var $searchModel app\models\MUserConfigSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Managing clipboard settings';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="muser-config-index">

    <?php Pjax::begin(); ?>
    <?php // echo $this->render('_search', ['model' => $searchModel]); ?>

    <p>
        <?= Html::a('New clipboard with new settings', ['create'], ['class' => 'btn btn-success']) ?>
    </p>



    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'columns' => [
            ['class' => 'yii\grid\ActionColumn', 'template' => '{info}',
            'buttons' => [
                    'info' => function ($url, $model) {
                        return Html::a('<span class="glyphicon glyphicon-thumbs-up"></span>', $url, [
                                    'title' => 'Go!!!',
                        ]);
                    }
            ],
            'urlCreator' => function ($action, $model, $key, $index) {
                    if ($action === 'info') {
                        $url = Yii::$app->urlManager->createUrl(['tempmove/createfromconfig',"id"=>$model->id]);
                        return $url;
                    }
                }
        ],
            ['class' => 'yii\grid\ActionColumn', 'template' => '{update}'],

            'clip_id',
            'price',
            [
                'attribute' => 'adr_id',
                'label' => 'Firma',
                'value' => function($model){                   
                    return $model->adr->firma;                   
                },
                'filterType' => GridView::FILTER_SELECT2,
                'filter' => \yii\helpers\ArrayHelper::map(\app\models\Address::find()->asArray()->all(), 'adr_id', 'firma'),
                'filterWidgetOptions' => [
                    'pluginOptions' => ['allowClear' => true],
                ],
                'filterInputOptions' => ['placeholder' => 'Address', 'id' => 'grid-movement-search-adr_repli_id']
            ],
            //'adr_id',
            //'adr_repli_id',
            'stock_id1',
            'stock_id2',
            'm_type_id',
            //'text1',
            //'text2',
            'd1',
            //'d2',
            'd3',

            ['class' => 'yii\grid\ActionColumn', 'template' => '  {delete}'],
        ],
    ]); ?>
    <?php Pjax::end(); ?>
</div>
