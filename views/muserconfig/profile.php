<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model app\models\MUserConfig */

$this->title = $model->username;
$this->params['breadcrumbs'][] = ['label' => 'PROFILE', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
\yii\web\YiiAsset::register($this);
?>
<div class="muser-config-view">

    <?= DetailView::widget([
        'model' => $model,
        'attributes' => [
            'id',
            'username',
            'email',
            'repli',
            'all_flags',
        ],
    ]) ?>

</div>
