<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model app\models\Kod */
/* @var $form yii\widgets\ActiveForm */

// \mootensai\components\JsBlock::widget(['viewFile' => '_script', 'pos'=> \yii\web\View::POS_END, 
//     'viewParams' => [
//         'class' => 'Product', 
//         'relID' => 'product', 
//         'value' => \yii\helpers\Json::encode($model->products),
//         'isNewRecord' => ($model->isNewRecord) ? 1 : 0
//     ]
// ]);
?>

<div class="kod-form">

    <?php $form = ActiveForm::begin(); ?>

    <?= $form->errorSummary($model); ?>

    <?= $form->field($model, 'kod')->textInput(['maxlength' => true, 'placeholder' => 'Kod']) ?>

    <?= $form->field($model, 'name0')->textInput(['maxlength' => true, 'placeholder' => 'Name0']) ?>

    <?= $form->field($model, 'name1')->textInput(['maxlength' => true, 'placeholder' => 'Name1']) ?>

    <?= $form->field($model, 'name2')->textInput(['maxlength' => true, 'placeholder' => 'Name2']) ?>

    <?= $form->field($model, 'name3')->textInput(['maxlength' => true, 'placeholder' => 'Name3']) ?>

    <?= $form->field($model, 'label1')->textInput(['maxlength' => true, 'placeholder' => 'Label1']) ?>

    <?= $form->field($model, 'name4')->textInput(['maxlength' => true, 'placeholder' => 'Name4']) ?>

    <?= $form->field($model, 'cur')->textInput(['maxlength' => true, 'placeholder' => 'Cur']) ?>

    <?= $form->field($model, 'cgroup')->textInput() ?>

    <div class="form-group">
        <?= Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
