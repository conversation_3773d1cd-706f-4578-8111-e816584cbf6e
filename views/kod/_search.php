<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model app\models\KodSearch */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="form-kod-search">

    <?php $form = ActiveForm::begin([
        'action' => ['index'],
        'method' => 'get',
    ]); ?>

    <?= $form->field($model, 'kod')->textInput(['maxlength' => true, 'placeholder' => 'Kod']) ?>

    <?= $form->field($model, 'name0')->textInput(['maxlength' => true, 'placeholder' => 'Name0']) ?>

    <?= $form->field($model, 'name1')->textInput(['maxlength' => true, 'placeholder' => 'Name1']) ?>

    <?= $form->field($model, 'name2')->textInput(['maxlength' => true, 'placeholder' => 'Name2']) ?>

    <?= $form->field($model, 'name3')->textInput(['maxlength' => true, 'placeholder' => 'Name3']) ?>

    <?php /* echo $form->field($model, 'label1')->textInput(['maxlength' => true, 'placeholder' => 'Label1']) */ ?>

    <?php /* echo $form->field($model, 'name4')->textInput(['maxlength' => true, 'placeholder' => 'Name4']) */ ?>

    <?php /* echo $form->field($model, 'cur')->textInput(['maxlength' => true, 'placeholder' => 'Cur']) */ ?>

    <div class="form-group">
        <?= Html::submitButton('Search', ['class' => 'btn btn-primary']) ?>
        <?= Html::resetButton('Reset', ['class' => 'btn btn-default']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
