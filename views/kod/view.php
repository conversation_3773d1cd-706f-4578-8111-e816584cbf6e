<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use kartik\grid\GridView;

/* @var $this yii\web\View */
/* @var $model app\models\Kod */

$this->title = $model->kod;
$this->params['breadcrumbs'][] = ['label' => 'Kod', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="kod-view">

    <div class="row">
        <div class="col-sm-9">
            <h2><?= 'Kod'.' '. Html::encode($this->title) ?></h2>
        </div>
        <div class="col-sm-3" style="margin-top: 15px">
            
            <?= Html::a('Update', ['update', 'id' => $model->kod], ['class' => 'btn btn-primary']) ?>
            <?= Html::a('Delete', ['delete', 'id' => $model->kod], [
                'class' => 'btn btn-danger',
                'data' => [
                    'confirm' => 'Are you sure you want to delete this item?',
                    'method' => 'post',
                ],
            ])
            ?>
        </div>
    </div>

    <div class="row">
<?php 
    $gridColumn = [
        'kod',
        'name0',
        'name1',
        'name2',
        'name3',
        'label1',
        'name4',
        'cur',
        'cgroup',
    ];
    echo DetailView::widget([
        'model' => $model,
        'attributes' => $gridColumn
    ]);
?>
    </div>
    
    <div class="row">
<?php
if($providerProduct->totalCount){
    $gridColumnProduct = [
        ['class' => 'yii\grid\SerialColumn'],
            'mat_id',
            'ean13',
                        'model',
            'price',
            'p0',
            'p1',
            'p2',
            'p3',
            'p4',
            'p5',
            'p6',
            'p8',
            'p9',
            'pdescr',
            'tax',
            'unit',
            'min',
            'max',
            'p7',
            'p0a',
            'p0b',
            'p0c',
            'p0d',
            'p0e',
            'p0f',
            'p0g',
            'p0h',
            'p0i',
            'p0j',
            'p1b',
            'p1c',
            'p1d',
            'p1e',
            'p1f',
            'p1g',
            'p1h',
            'p1i',
            'p1j',
            'p2b',
            'p2c',
            'p2d',
            'p2e',
            'p2f',
            'p2g',
            'p2h',
            'p2i',
            'p2j',
            'p1a',
            'p2a',
    ];
    echo Gridview::widget([
        'dataProvider' => $providerProduct,
        'pjax' => true,
        'pjaxSettings' => ['options' => ['id' => 'kv-pjax-container-product']],
        'panel' => [
            'type' => GridView::TYPE_PRIMARY,
            'heading' => '<span class="glyphicon glyphicon-book"></span> ' . Html::encode('Product'),
        ],
        'export' => false,
        'columns' => $gridColumnProduct
    ]);
}
?>

    </div>
</div>
