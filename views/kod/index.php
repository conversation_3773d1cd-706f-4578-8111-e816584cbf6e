<?php

/* @var $this yii\web\View */
/* @var $searchModel app\models\KodSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

use yii\helpers\Html;
use kartik\export\ExportMenu;
use kartik\grid\GridView;

$this->title = 'Kod table - groups of products';
$this->params['breadcrumbs'][] = $this->title;
$search = "$('.search-button').click(function(){
	$('.search-form').toggle(1000);
	return false;
});";
$this->registerJs($search);
?>
<div class="kod-index">

    <?php // echo $this->render('_search', ['model' => $searchModel]); ?>

    <p>
        <?= Html::a('Create Kod/group', ['create'], ['class' => 'btn btn-success']) ?>
<!--         <?= Html::a('Advance Search', '#', ['class' => 'btn btn-info search-button']) ?>
 -->    </p>
    <div class="search-form" style="display:none">
        <?=  $this->render('_search', ['model' => $searchModel]); ?>
    </div>
    <?php 
    $gridColumn = [
        [
            'class' => 'yii\grid\ActionColumn', 'template' => '{update}'
        ],
        'kod',
        ['class' => 'kartik\grid\EditableColumn', 'attribute' => 'name0' ],
        ['class' => 'kartik\grid\EditableColumn', 'attribute' => 'name1' ],
        ['class' => 'kartik\grid\EditableColumn', 'attribute' => 'name2' ],
        ['class' => 'kartik\grid\EditableColumn', 'attribute' => 'label1' ],
        ['class' => 'kartik\grid\EditableColumn', 'attribute' => 'cur' ],
        // 'name0',
        // 'name1',
        // 'name2',
        // //'name3',
        // 'label1',
        // //'name4',
        // 'cur',
         'cgroup',
        [
            'class' => 'yii\grid\ActionColumn', 'template' => '{delete}'
        ],
    ]; 
    ?>
    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => $gridColumn,
        'pjax' => true,
        'pjaxSettings' => ['options' => ['id' => 'kv-pjax-container-kod']],
        'panel' => [
            'type' => GridView::TYPE_PRIMARY,
            'heading' => '<span class="glyphicon glyphicon-book"></span>  ' . Html::encode($this->title),
        ],
        'export' => false,
        // your toolbar can include the additional full export menu
        // 'toolbar' => [
        //     '{export}',
        //     ExportMenu::widget([
        //         'dataProvider' => $dataProvider,
        //         'columns' => $gridColumn,
        //         'target' => ExportMenu::TARGET_BLANK,
        //         'fontAwesome' => true,
        //         'dropdownOptions' => [
        //             'label' => 'Full',
        //             'class' => 'btn btn-default',
        //             'itemsBefore' => [
        //                 '<li class="dropdown-header">Export All Data</li>',
        //             ],
        //         ],
        //         'exportConfig' => [
        //             ExportMenu::FORMAT_PDF => false
        //         ]
        //     ]) ,
        // ],
    ]); ?>

</div>
