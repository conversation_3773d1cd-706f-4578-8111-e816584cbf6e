<div class="form-group" id="add-product">
<?php
use kartik\grid\GridView;
use kartik\builder\TabularForm;
use yii\data\ArrayDataProvider;
use yii\helpers\Html;
use yii\widgets\Pjax;

$dataProvider = new ArrayDataProvider([
    'allModels' => $row,
    'pagination' => [
        'pageSize' => -1
    ]
]);
echo TabularForm::widget([
    'dataProvider' => $dataProvider,
    'formName' => 'Product',
    'checkboxColumn' => false,
    'actionColumn' => false,
    'attributeDefaults' => [
        'type' => TabularForm::INPUT_TEXT,
    ],
    'attributes' => [
        'mat_id' => ['type' => TabularForm::INPUT_TEXT],
        'ean13' => ['type' => TabularForm::INPUT_TEXT],
        'model' => ['type' => TabularForm::INPUT_TEXT],
        'price' => ['type' => TabularForm::INPUT_TEXT],
        'p0' => ['type' => TabularForm::INPUT_TEXT],
        'p1' => ['type' => TabularForm::INPUT_TEXT],
        'p2' => ['type' => TabularForm::INPUT_TEXT],
        'p3' => ['type' => TabularForm::INPUT_TEXT],
        'p4' => ['type' => TabularForm::INPUT_TEXT],
        'p5' => ['type' => TabularForm::INPUT_TEXT],
        'p6' => ['type' => TabularForm::INPUT_TEXT],
        'p8' => ['type' => TabularForm::INPUT_TEXT],
        'p9' => ['type' => TabularForm::INPUT_TEXT],
        'pdescr' => ['type' => TabularForm::INPUT_TEXT],
        'tax' => ['type' => TabularForm::INPUT_TEXT],
        'unit' => ['type' => TabularForm::INPUT_TEXT],
        'min' => ['type' => TabularForm::INPUT_TEXT],
        'max' => ['type' => TabularForm::INPUT_TEXT],
        'p7' => ['type' => TabularForm::INPUT_TEXT],
        'p0a' => ['type' => TabularForm::INPUT_TEXT],
        'p0b' => ['type' => TabularForm::INPUT_TEXT],
        'p0c' => ['type' => TabularForm::INPUT_TEXT],
        'p0d' => ['type' => TabularForm::INPUT_TEXT],
        'p0e' => ['type' => TabularForm::INPUT_TEXT],
        'p0f' => ['type' => TabularForm::INPUT_TEXT],
        'p0g' => ['type' => TabularForm::INPUT_TEXT],
        'p0h' => ['type' => TabularForm::INPUT_TEXT],
        'p0i' => ['type' => TabularForm::INPUT_TEXT],
        'p0j' => ['type' => TabularForm::INPUT_TEXT],
        'p1b' => ['type' => TabularForm::INPUT_TEXT],
        'p1c' => ['type' => TabularForm::INPUT_TEXT],
        'p1d' => ['type' => TabularForm::INPUT_TEXT],
        'p1e' => ['type' => TabularForm::INPUT_TEXT],
        'p1f' => ['type' => TabularForm::INPUT_TEXT],
        'p1g' => ['type' => TabularForm::INPUT_TEXT],
        'p1h' => ['type' => TabularForm::INPUT_TEXT],
        'p1i' => ['type' => TabularForm::INPUT_TEXT],
        'p1j' => ['type' => TabularForm::INPUT_TEXT],
        'p2b' => ['type' => TabularForm::INPUT_TEXT],
        'p2c' => ['type' => TabularForm::INPUT_TEXT],
        'p2d' => ['type' => TabularForm::INPUT_TEXT],
        'p2e' => ['type' => TabularForm::INPUT_TEXT],
        'p2f' => ['type' => TabularForm::INPUT_TEXT],
        'p2g' => ['type' => TabularForm::INPUT_TEXT],
        'p2h' => ['type' => TabularForm::INPUT_TEXT],
        'p2i' => ['type' => TabularForm::INPUT_TEXT],
        'p2j' => ['type' => TabularForm::INPUT_TEXT],
        'p1a' => ['type' => TabularForm::INPUT_TEXT],
        'p2a' => ['type' => TabularForm::INPUT_TEXT],
        'del' => [
            'type' => 'raw',
            'label' => '',
            'value' => function($model, $key) {
                return
                    Html::hiddenInput('Children[' . $key . '][id]', (!empty($model['id'])) ? $model['id'] : "") .
                    Html::a('<i class="glyphicon glyphicon-trash"></i>', '#', ['title' =>  'Delete', 'onClick' => 'delRowProduct(' . $key . '); return false;', 'id' => 'product-del-btn']);
            },
        ],
    ],
    'gridSettings' => [
        'panel' => [
            'heading' => false,
            'type' => GridView::TYPE_DEFAULT,
            'before' => false,
            'footer' => false,
            'after' => Html::button('<i class="glyphicon glyphicon-plus"></i>' . 'Add Product', ['type' => 'button', 'class' => 'btn btn-success kv-batch-create', 'onClick' => 'addRowProduct()']),
        ]
    ]
]);
echo  "    </div>\n\n";
?>

