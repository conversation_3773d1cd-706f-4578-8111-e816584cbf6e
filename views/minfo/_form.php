<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use yii\jui\AutoComplete;
use yii\web\JsExpression;

/* @var $this yii\web\View */
/* @var $model app\models\Minfo */
/* @var $form yii\widgets\ActiveForm */

$searchUrl = \yii\helpers\Url::to(['minfo/search']);
?>

<div class="minfo-form">

    <?php $form = ActiveForm::begin(); ?>

    <div class="row">
        <div class="col-md-6">
            <label for="search">Search and replace</label>
            <?= AutoComplete::widget([
                'name' => 'search',
                'options' => ['placeholder' => 'Search by surname, company or VAT ID...', 'class' => 'form-control'],
                'clientOptions' => [
                    'source' => new JsExpression("function(request, response) {
                        $.get('{$searchUrl}', {
                            term: request.term
                        }, function(data) {
                            response(data);
                        });
                    }"),
                    'minLength' => '2',
                    'select' => new JsExpression("function(event, ui) {
                        $('#minfo-isurname').val(ui.item.isurname);
                        $('#minfo-icompany').val(ui.item.icompany);
                        $('#minfo-iicdph').val(ui.item.iicdph);
                        $('#minfo-iname').val(ui.item.iname);
                        $('#minfo-istreet').val(ui.item.istreet);
                        $('#minfo-icity').val(ui.item.icity);
                        $('#minfo-izip').val(ui.item.izip);
                        $('#minfo-iphone').val(ui.item.iphone);
                        $('#minfo-icountry').val(ui.item.icountry);
                        $('#minfo-iico').val(ui.item.iico);
                        $('#minfo-idic').val(ui.item.idic);
                        $('#minfo-dcompany').val(ui.item.dcompany);
                        $('#minfo-dname').val(ui.item.dname);
                        $('#minfo-dsurname').val(ui.item.dsurname);
                        $('#minfo-dstreet').val(ui.item.dstreet);
                        $('#minfo-dcity').val(ui.item.dcity);
                        $('#minfo-dzip').val(ui.item.dzip);
                        $('#minfo-dphone').val(ui.item.dphone);
                        $('#minfo-dcountry').val(ui.item.dcountry);
                        
                        return false;
                    }")
                ]
            ]); ?>
        </div>
    </div>

    <br />
    <hr />
    <br />

    <?= $form->field($model, 'status')->radioList(['N' => "Not categorized", 'R' => 'Regular', 'E' => 'Eshop', 'S' => 'Shop']); ?>

    <?= $form->field($model, 'orderid')->textInput() ?>

    <?= $form->field($model, 'email')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'iname')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'isurname')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'istreet')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'icity')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'izip')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'iphone')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'icountry')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'icompany')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'iico')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'idic')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'iicdph')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'dcompany')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'dname')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'dsurname')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'dstreet')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'dcity')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'dzip')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'dphone')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'dcountry')->textInput(['maxlength' => true]) ?>

    <p><a onclick="document.getElementById('minfo-mnote').value = 'Osobou povinnou platiť DPH je osoba, ktorej je služba dodaná (podľa čl. 193 až 196 smernice Rady 2006/112/ES z 28. novembra 2006 o spoločnom systéme dane z pridanej hodnoty v znení smernice Rady 2006/ 138/ES z 19. decembra 2006).';">Set DPH text</a>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <a onclick="document.getElementById('minfo-mnote').value = 'The person liable for payment of VAT is the person to whom the service is supplied (according to Articles 193 to 196 of Council Directive 2006/112/EC of 28 November 2006 on the common system of value added tax as amended by Council Directive 2006/138/EC of 19 December 2006).';">Set VAT text</a>

    </p>
    <?= $form->field($model, 'mnote')->textarea(['rows' => 6]) ?>

    <?= $form->field($model, 'shipment')->radioList([
        'Osobný odber' => "Osobný odber",
        'kuriér DHL' => 'kuriér DHL',
        'kuriér UPS' => 'kuriér UPS',
        'kuriér Slovenská pošta' => 'kuriér Slovenská pošta',
        'Zásielkovňa' => 'Zásielkovňa',
        'Packeta' => 'Packeta',
        'DHL' => 'DHL',
        'UPS' => 'UPS',
        'Collection' => 'Collection',
        'Post' => 'Post'
    ]); ?>

    <?= $form->field($model, 'branch')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'voucher')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'payment')->radioList([
        'V hotovosti' => "V hotovosti",
        'Prevodom' => 'Prevodom',
        'PayPal' => 'PayPal',
        'Platobnou kartou' => 'Platobnou kartou',
        'Dobierka' => 'Dobierka',
        'Cash' => 'Cash',
        'Bank transfer' => 'Bank transfer',
        'Payment card' => 'Payment card',
        'Cash on delivery' => 'Cash on delivery',
        'Stripe' => 'Stripe'
    ]); ?>

    <?= $form->field($model, 'incoterms')->radioList(
        ['EXW' => 'EXW Ex works', 'FCA' => 'FCA Free carrier', 'FAS' => 'FAS Free alongside ship', 'FOB' => 'FOB Free on board', 'CFR' => 'CFR Cost and freight', 'CIF' => 'CIF Cost, insurance and freight', 'CPT' => 'CPT Cost paid to...', 'CIP' => 'CIP Carrier and insurance paid to...', 'DAT' => 'DAT Delivery at terminal', 'DAP' => 'DAP Delivery at place', 'DDP' => 'DDP Delivery duty paid']
    )
    ?>



    <div class="form-group">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success']) ?>
        <?= Html::resetButton('Cancel', ['class' => 'btn btn-secondary']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>