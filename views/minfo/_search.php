<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model app\models\MinfoSearch */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="minfo-search">

    <?php $form = ActiveForm::begin([
        'action' => ['index'],
        'method' => 'get',
    ]); ?>

    <?= $form->field($model, 'id') ?>

    <?= $form->field($model, 'move_id') ?>

    <?= $form->field($model, 'status') ?>

    <?= $form->field($model, 'orderid') ?>

    <?= $form->field($model, 'email') ?>

    <?php // echo $form->field($model, 'iname') ?>

    <?php // echo $form->field($model, 'isurname') ?>

    <?php // echo $form->field($model, 'istreet') ?>

    <?php // echo $form->field($model, 'icity') ?>

    <?php // echo $form->field($model, 'izip') ?>

    <?php // echo $form->field($model, 'iphone') ?>

    <?php // echo $form->field($model, 'icountry') ?>

    <?php // echo $form->field($model, 'icompany') ?>

    <?php // echo $form->field($model, 'iico') ?>

    <?php // echo $form->field($model, 'idic') ?>

    <?php // echo $form->field($model, 'iicdph') ?>

    <?php // echo $form->field($model, 'dcompany') ?>

    <?php // echo $form->field($model, 'dname') ?>

    <?php // echo $form->field($model, 'dsurname') ?>

    <?php // echo $form->field($model, 'dstreet') ?>

    <?php // echo $form->field($model, 'dcity') ?>

    <?php // echo $form->field($model, 'dzip') ?>

    <?php // echo $form->field($model, 'dphone') ?>

    <?php // echo $form->field($model, 'dcountry') ?>

    <?php // echo $form->field($model, 'mnote') ?>

    <?php // echo $form->field($model, 'shipment') ?>

    <?php // echo $form->field($model, 'branch') ?>

    <?php // echo $form->field($model, 'voucher') ?>

    <?php // echo $form->field($model, 'payment') ?>

    <div class="form-group">
        <?= Html::submitButton('Search', ['class' => 'btn btn-primary']) ?>
        <?= Html::resetButton('Reset', ['class' => 'btn btn-default']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
