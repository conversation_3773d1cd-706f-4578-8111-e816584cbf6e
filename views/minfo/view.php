<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use app\controllers\EstockTools;


/* @var $this yii\web\View */
/* @var $model app\models\Minfo */

$this->title = "Info for move_id: " .  $model->move_id;

$movementinfo = Yii::$app->db->createCommand("SELECT * FROM movement WHERE move_id=:move_id")->bindValue(':move_id', $model->move_id)->queryOne();

//title is link to movement, show movement main info
echo Html::tag('h1', Html::a(
    'Total: ' . $movementinfo['total'] . ', ' . $movementinfo['d1'],
    ['//mdetailprod/documentdetaillist', 'id' => $model->move_id],

));


$this->params['breadcrumbs'][] = ['label' => 'Minfos', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
\yii\web\YiiAsset::register($this);

if ($model->move_id > 0) {
    $actionButtons =  EstockTools::getprintlinks($model->move_id);
}

?>
<div class="minfo-view">

    <p> <?= $actionButtons ?> </p>

    <p>
        <?= Html::a('Set/ Update', ['//minfo/update', 'id' => $model->id], ['class' => 'btn btn-primary']) ?>

    </p>

    <?= DetailView::widget([
        'model' => $model,
        'attributes' => [
            // 'id',
            // 'move_id',
            'status',
            'orderid',
            'email:email',
            'iname',
            'isurname',
            'istreet',
            'icity',
            'izip',
            'iphone',
            'icountry',
            'icompany',
            'iico',
            'idic',
            'iicdph',
            'dcompany',
            'dname',
            'dsurname',
            'dstreet',
            'dcity',
            'dzip',
            'dphone',
            'dcountry',
            'mnote:ntext',
            'shipment',
            'branch',
            'voucher',
            'payment',
            'incoterms',
        ],
    ]) ?>

</div>