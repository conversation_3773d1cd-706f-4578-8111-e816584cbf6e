<?php

use yii\helpers\Html;
use yii\grid\GridView;

/* @var $this yii\web\View */
/* @var $searchModel app\models\MinfoSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Minfos';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="minfo-index">

    <h1><?= Html::encode($this->title) ?></h1>
    <?php // echo $this->render('_search', ['model' => $searchModel]); ?>

    <p>
        <?= Html::a('Create Minfo', ['create'], ['class' => 'btn btn-success']) ?>
    </p>

    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => [
            ['class' => 'yii\grid\SerialColumn'],

            'id',
            'move_id',
            'status',
            'orderid',
            'email:email',
            //'iname',
            //'isurname',
            //'istreet',
            //'icity',
            //'izip',
            //'iphone',
            //'icountry',
            //'icompany',
            //'iico',
            //'idic',
            //'iicdph',
            //'dcompany',
            //'dname',
            //'dsurname',
            //'dstreet',
            //'dcity',
            //'dzip',
            //'dphone',
            //'dcountry',
            //'mnote:ntext',
            //'shipment',
            //'branch',
            //'voucher',
            //'payment',

            ['class' => 'yii\grid\ActionColumn'],
        ],
    ]); ?>
</div>
