<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model app\models\AdrContact */

$this->title = $model->adr_id;
$this->params['breadcrumbs'][] = ['label' => 'Adr Contacts', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
\yii\web\YiiAsset::register($this);
?>
<div class="adr-contact-view">

    <h1><?= Html::encode($this->title) ?></h1>

    <p>
        <?= Html::a('Update', ['update', 'adr_id' => $model->adr_id, 'number' => $model->number, 'repli_id' => $model->repli_id], ['class' => 'btn btn-primary']) ?>
        <?= Html::a('Delete', ['delete', 'adr_id' => $model->adr_id, 'number' => $model->number, 'repli_id' => $model->repli_id], [
            'class' => 'btn btn-danger',
            'data' => [
                'confirm' => 'Are you sure you want to delete this item?',
                'method' => 'post',
            ],
        ]) ?>
    </p>

    <?= DetailView::widget([
        'model' => $model,
        'attributes' => [
            'adr_id',
            'number',
            'describe',
            'person',
            'repli_id',
        ],
    ]) ?>

</div>
