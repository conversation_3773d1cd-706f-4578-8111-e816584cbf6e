<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/** @var yii\web\View $this */
/** @var app\models\MTypeSearch $model */
/** @var yii\widgets\ActiveForm $form */
?>

<div class="mtype-search">

    <?php $form = ActiveForm::begin([
        'action' => ['index'],
        'method' => 'get',
        'options' => [
            'data-pjax' => 1
        ],
    ]); ?>

    <?= $form->field($model, 'm_type_id') ?>

    <?= $form->field($model, 'name') ?>

    <?= $form->field($model, 'mdescr') ?>

    <?= $form->field($model, 'doc_id') ?>

    <?= $form->field($model, 'flag') ?>

    <?php // echo $form->field($model, 'usergroup') ?>

    <?php // echo $form->field($model, 'moving') ?>

    <?php // echo $form->field($model, 'stock_id1') ?>

    <?php // echo $form->field($model, 'stock_id2') ?>

    <?php // echo $form->field($model, 'fifogroup') ?>

    <?php // echo $form->field($model, 'mdescr2') ?>

    <?php // echo $form->field($model, 'changeprice') ?>

    <?php // echo $form->field($model, 'taxable') ?>

    <div class="form-group">
        <?= Html::submitButton(Yii::t('app', 'Search'), ['class' => 'btn btn-primary']) ?>
        <?= Html::resetButton(Yii::t('app', 'Reset'), ['class' => 'btn btn-outline-secondary']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
