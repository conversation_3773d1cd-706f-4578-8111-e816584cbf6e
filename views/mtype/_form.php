<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/** @var yii\web\View $this */
/** @var app\models\MType $model */
/** @var yii\widgets\ActiveForm $form */
?>

<div class="mtype-form">

    <?php $form = ActiveForm::begin(); ?>

    <?= $form->field($model, 'name')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'mdescr')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'doc_id')->widget(\kartik\select2\Select2::class, [
            'data' => \yii\helpers\ArrayHelper::map(\app\models\Document::find()->asArray()->all(), 'doc_id', 'name'),
            'options' => ['placeholder' => 'Document group numbering'],
            'pluginOptions' => [
                'allowClear' => false
            ],
        ]); ?>


    <?= $form->field($model, 'flag')->textInput() ?>

    <?= $form->field($model, 'usergroup')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'moving')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'stock_id1')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'stock_id2')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'fifogroup')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'mdescr2')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'changeprice')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'taxable')->textInput(['maxlength' => true]) ?>

    <div class="form-group">
        <?= Html::submitButton(Yii::t('app', 'Save'), ['class' => 'btn btn-success']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
