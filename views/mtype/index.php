<?php

use app\models\MType;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\grid\ActionColumn;
use yii\grid\GridView;
use yii\widgets\Pjax;
/** @var yii\web\View $this */
/** @var app\models\MTypeSearch $searchModel */
/** @var yii\data\ActiveDataProvider $dataProvider */

$this->title = Yii::t('app', 'Definition of documents');
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="mtype-index">

    <p>
        <?= Html::a(Yii::t('app', 'Create M_Type'), ['create'], ['class' => 'btn btn-success']) ?>
    </p>

    <?php Pjax::begin(); ?>
    <?php // echo $this->render('_search', ['model' => $searchModel]); ?>

    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => [
            ['class' => 'yii\grid\SerialColumn'],

            'm_type_id',
            'name',
            'mdescr',
            'doc_id',
            'flag',
            //'usergroup',
            //'moving',
            //'stock_id1',
            //'stock_id2',
            //'fifogroup',
            //'mdescr2',
            //'changeprice',
            //'taxable',
            [
                'class' => ActionColumn::className(),
                'urlCreator' => function ($action, MType $model, $key, $index, $column) {
                    return Url::toRoute([$action, 'm_type_id' => $model->m_type_id]);
                 }
            ],
        ],
    ]); ?>

    <?php Pjax::end(); ?>

</div>
