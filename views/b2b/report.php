<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use kartik\grid\GridView;
use kartik\export\ExportMenu;
use yii\widgets\ActiveForm;


$this->title = 'B2B Report';
$this->params['breadcrumbs'][] = ['label' => 'Report', 'url' => ['report']];

    if( isset($p1)){
		echo GridView::widget([
	'dataProvider' => $p1,
	// 'options' => ['style' => 'font-size:12px;font-family: monospace;'],
	'panel' => [
		'type' => GridView::TYPE_PRIMARY,
		'heading' => isset($title)?$title:'Report...',
	],
	'toolbar' => [
		'{export}',
	],
]); 
	}//isset p1



    if( isset($p2)){
		echo GridView::widget([
	'dataProvider' => $p2,
	// 'options' => ['style' => 'font-size:12px;font-family: monospace;'],
	'panel' => [
		'type' => GridView::TYPE_PRIMARY,
		'heading' => isset($title2)?$title2:'Report...',
	],
	'toolbar' => [
		'{export}',
	],
]); 
	}//isset p2


?>

