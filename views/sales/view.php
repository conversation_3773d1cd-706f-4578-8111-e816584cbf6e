<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model app\models\WowSales */

$this->title = 'Sale to: '.$model->cust->nick.', '.$model->cust->firstname.' '.$model->cust->lastname.', '.$model->sale_date;
$this->params['breadcrumbs'][] = ['label' => Yii::t('app', 'Wow Sales'), 'url' => ['index']];
?>
<div class="wow-sales-view">


<p>
<?= Html::a('View '.$model->cust->nick, ['customers/view', 'id' => $model->cust->id], ['class' => 'btn btn-primary']) ?>
 Sale:
        <?= Html::a(Yii::t('app', 'Update'), ['update', 'orders_id' => $model->orders_id, 'card_nr' => $model->card_nr, 'mat_id' => $model->mat_id], ['class' => 'btn btn-primary']) ?>
        <?= Html::a(Yii::t('app', 'Delete'), ['delete', 'orders_id' => $model->orders_id, 'card_nr' => $model->card_nr, 'mat_id' => $model->mat_id], [
            'class' => 'btn btn-danger',
            'data' => [
                'confirm' => Yii::t('app', 'Are you sure you want to delete this item?'),
                'method' => 'post',
            ],
        ]) ?>
    </p>

    <?= DetailView::widget([
        'model' => $model,
        'attributes' => [
            'orders_id',
            'mat_id',
            'pcs',
            'sale_price',
            'card_nr',
            'sale_date',
            'sale_spare',
            'sale_type',
            'sale_info',
            'cust_id',
        ],
    ]) ?>

</div>
