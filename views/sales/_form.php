<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use kartik\widgets\Typeahead;
use yii\helpers\Url;
use app\controllers\EstockTools;

$shopPrice = EstockTools::getShopParams()['shopmtypes'][0][4];

/* @var $this yii\web\View */
/* @var $model app\models\WowSales */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="wow-sales-form">

    <?php $form = ActiveForm::begin(); ?>
<?php
    echo '<label class="control-label">Model</label>';

echo Typeahead::widget([
    'name' => 'modelsearch',
    'options' => ['placeholder' => 'Search model as you type ...'],
    'pluginOptions' => ['highlight'=>true],
    'pluginEvents' => [
        "typeahead:selected" => "function(e,d) { $(\"#wowsales-mat_id\").val(d.mat_id); $('#maintitle').text(d.kod+ ' ' + d.model ); setprice(); }",
        "typeahead:autocompleted" => "function() { console.log(\"typeahead:autocompleted\"); }",
    ],
    'dataset' => [
        [
            'datumTokenizer' => "Bloodhound.tokenizers.obj.whitespace('value')",
            'display' => 'model',
//            'prefetch' => $baseUrl . '/samples/countries.json',
            'remote' => [
                'url' => Url::to(['product/productlist']) . '?q=%QUERY',
                'wildcard' => '%QUERY'
            ]
        ]
    ]
]);
?>

    <?= $form->field($model, 'mat_id')->textInput() ?>



    <?= $form->field($model, 'pcs')->textInput(['value'=> "1"]) ?>

    <?= $form->field($model, 'sale_price')->textInput(['maxlength' => true])->label('Price - '.Yii::$app->request->get('card_discount')."%") ?>

    <?= $form->field($model, 'card_nr')->hiddenInput(['maxlength' => true,'value' => Yii::$app->request->get('card_nr')])->label(false) ?>
<!--
    <?= $form->field($model, 'sale_date')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'sale_spare')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'sale_type')->textInput(['maxlength' => true]) ?>
-->
    <?php $model->sale_info = ''; ?>
    <?= $form->field($model, 'sale_info')->radioList(['' => "  NO ACTION   ", 'eshop' => 'E-SHOP  ', 'sale87' => 'Sale 87  ' ,  'order17' => 'ORDER 17'],['separator' => "  |  "])->label("Sale type") ?>

   <?php $model->orders_id = 0; ?>
   <?= $form->field($model, 'orders_id')->textInput(['style' => ['display' => 'none']])->label(null,['style' => ['display' => 'none']]) ?>

 
<!--
    <?= $form->field($model, 'sale_info')->textInput(['maxlength' => true]) ?>
-->
    <?= $form->field($model, 'cust_id')->hiddenInput(['value' => Yii::$app->request->get('cust_id')])->label(false) ?>

    <div class="form-group">
        <?= Html::submitButton(Yii::t('app', 'Save'), ['class' => 'btn btn-success']) ?>
    </div>

    <?php ActiveForm::end(); ?>

    <?php

    $script = "
    function setprice() {
        var m = $(\"#wowsales-mat_id\").val();
        var cd = ".Yii::$app->request->get('card_discount')."0;
        var p = \"".$shopPrice."\";
        $.get('". Url::to(['/sales/getmodel']) ."',{mat_id:m,price:p},function(data){
            var model = $.parseJSON(data);
            $('#maintitle').text(model.kod+ ' ' + model.model + ' ".$shopPrice.": ' + model.".$shopPrice."  );
            $('#wowsales-sale_price').val(model.".$shopPrice.");
            });
        };

    $(\"#wowsales-mat_id\").on('keyup change', setprice );

    ";

    $this->registerJs($script);
?>
</div>
