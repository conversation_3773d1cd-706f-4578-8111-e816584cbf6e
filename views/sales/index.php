<?php

use yii\helpers\Html;
use yii\grid\GridView;
use yii\widgets\Pjax;
/* @var $this yii\web\View */
/* @var $searchModel app\models\SearchWowSales */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = Yii::t('app', 'WDL Sales');
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="wow-sales-index">

    <h1><?= Html::encode($this->title) ?></h1>
    <?php Pjax::begin(); ?>
    <?php echo $this->render('_search', ['model' => $searchModel]); ?>

    <p>
        <?= Html::a(Yii::t('app', 'Create WDL Sales'), ['create', 'cust_id'=>2], ['class' => 'btn btn-success']) ?>
    </p>

    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => [
            ['class' => 'yii\grid\SerialColumn'],

            //'orders_id',
            'mat_id',
            'pcs',
            'sale_price',
            'card_nr',
            'sale_date',
            //'sale_spare',
            //'sale_type',
            //'sale_info',
            'cust_id',

            ['class' => 'yii\grid\ActionColumn'],
        ],
    ]); ?>
    <?php Pjax::end(); ?>
</div>
