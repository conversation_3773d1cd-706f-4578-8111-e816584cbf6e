<div class="form-group" id="add-acc-register">
<?php
use kartik\grid\GridView;
use kartik\builder\TabularForm;
use yii\data\ArrayDataProvider;
use yii\helpers\Html;
use yii\widgets\Pjax;

$dataProvider = new ArrayDataProvider([
    'allModels' => $row,
    'pagination' => [
        'pageSize' => -1
    ]
]);
echo TabularForm::widget([
    'dataProvider' => $dataProvider,
    'formName' => 'AccRegister',
    'checkboxColumn' => false,
    'actionColumn' => false,
    'attributeDefaults' => [
        'type' => TabularForm::INPUT_TEXT,
    ],
    'attributes' => [
        'acc_reg_id' => ['type' => TabularForm::INPUT_HIDDEN],
        'acc_repli_id' => ['type' => TabularForm::INPUT_TEXT],
        'acc_id' => ['type' => TabularForm::INPUT_TEXT],
        'repli_id' => ['type' => TabularForm::INPUT_TEXT],
        'adr_id' => [
            'label' => 'Address',
            'type' => TabularForm::INPUT_WIDGET,
            'widgetClass' => \kartik\select2\Select2::class,
            'options' => [
                'data' => \yii\helpers\ArrayHelper::map(\app\models\Address::find()->orderBy('adr_id')->asArray()->all(), 'repli_id', 'adr_id'),
                'options' => ['placeholder' => 'Choose Address'],
            ],
            'columnOptions' => ['width' => '200px']
        ],
        'num' => ['type' => TabularForm::INPUT_TEXT],
        'trans_date' => ['type' => TabularForm::INPUT_TEXT],
        'amount' => ['type' => TabularForm::INPUT_TEXT],
        'first_date' => ['type' => TabularForm::INPUT_TEXT],
        'last_date' => ['type' => TabularForm::INPUT_TEXT],
        'cat_id' => ['type' => TabularForm::INPUT_TEXT],
        'ext_bank_acc_nr' => ['type' => TabularForm::INPUT_TEXT],
        'ext_bank_code' => ['type' => TabularForm::INPUT_TEXT],
        'v_symbol' => ['type' => TabularForm::INPUT_TEXT],
        'k_symbol' => ['type' => TabularForm::INPUT_TEXT],
        's-symbol' => ['type' => TabularForm::INPUT_TEXT],
        'text' => ['type' => TabularForm::INPUT_TEXT],
        'cat_repli_id' => ['type' => TabularForm::INPUT_TEXT],
        'c' => ['type' => TabularForm::INPUT_TEXT],
        'valid' => ['type' => TabularForm::INPUT_TEXT],
        'amount2' => ['type' => TabularForm::INPUT_TEXT],
        'trans_acc_id' => ['type' => TabularForm::INPUT_TEXT],
        'trans_repli_id' => ['type' => TabularForm::INPUT_TEXT],
        'user_id' => ['type' => TabularForm::INPUT_TEXT],
        'payee' => ['type' => TabularForm::INPUT_TEXT],
        'tax_group' => ['type' => TabularForm::INPUT_CHECKBOX,
            'options' => [
                'style' => 'position : relative; margin-top : -9px'
            ]
        ],
        'del' => [
            'type' => 'raw',
            'label' => '',
            'value' => function($model, $key) {
                return
                    Html::hiddenInput('Children[' . $key . '][id]', (!empty($model['id'])) ? $model['id'] : "") .
                    Html::a('<i class="glyphicon glyphicon-trash"></i>', '#', ['title' =>  'Delete', 'onClick' => 'delRowAccRegister(' . $key . '); return false;', 'id' => 'acc-register-del-btn']);
            },
        ],
    ],
    'gridSettings' => [
        'panel' => [
            'heading' => false,
            'type' => GridView::TYPE_DEFAULT,
            'before' => false,
            'footer' => false,
            'after' => Html::button('<i class="glyphicon glyphicon-plus"></i>' . 'Add Acc Register', ['type' => 'button', 'class' => 'btn btn-success kv-batch-create', 'onClick' => 'addRowAccRegister()']),
        ]
    ]
]);
echo  "    </div>\n\n";
?>

