<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model app\models\AddressSearch */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="address-search">

    <?php $form = ActiveForm::begin([
        'action' => ['index'],
        'method' => 'get',
        'options' => [
            'data-pjax' => 1
        ],
    ]); ?>

    <?= $form->field($model, 'firma') ?>

    <?= $form->field($model, 'street') ?>

    <?= $form->field($model, 'city') ?>

    <?= $form->field($model, 'zip') ?>

    <?= $form->field($model, 'ico') ?>

    <?php // echo $form->field($model, 'drc1') ?>

    <?php // echo $form->field($model, 'drc2') ?>

    <?php // echo $form->field($model, 'account') ?>

    <?php // echo $form->field($model, 'bankcode') ?>

    <?php // echo $form->field($model, 'memo') ?>

    <?php // echo $form->field($model, 'adr_id') ?>

    <?php // echo $form->field($model, 'firma_id') ?>

    <?php // echo $form->field($model, 'info') ?>

    <?php // echo $form->field($model, 'credit_complet') ?>

    <?php // echo $form->field($model, 'discount') ?>

    <?php // echo $form->field($model, 'expire') ?>

    <?php // echo $form->field($model, 'akc_disc') ?>

    <?php // echo $form->field($model, 'currency') ?>

    <?php // echo $form->field($model, 'owner_name') ?>

    <?php // echo $form->field($model, 'area') ?>

    <?php // echo $form->field($model, 'repli_id') ?>

    <?php // echo $form->field($model, 'repli_id2') ?>

    <?php // echo $form->field($model, 'iso') ?>

    <div class="form-group">
        <?= Html::submitButton('Search', ['class' => 'btn btn-primary']) ?>
        <?= Html::resetButton('Reset', ['class' => 'btn btn-default']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
