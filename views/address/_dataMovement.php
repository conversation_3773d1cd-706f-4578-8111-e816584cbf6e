<?php
use kartik\grid\GridView;
use yii\data\ArrayDataProvider;

    $dataProvider = new ArrayDataProvider([
        'allModels' => $model->movements,
        'key' => function($model){
            return ['move_id' => $model->move_id, 'repli_id' => $model->repli_id];
        }
    ]);
    $gridColumns = [
        ['class' => 'yii\grid\SerialColumn'],
        'move_id',
                'd1',
        'd2',
        'd3',
        'doc_id',
        'number',
        'emp_id',
        'total',
        'payment',
        'X',
        'c_number',
        'total0',
        'total1',
        'total2',
        'rounding',
        'tax1',
        'tax2',
        'discount',
        'text1',
        'text2',
        'm_type_id',
        'repli_id',
        'stock_id1',
        'stock_id2',
        'acc_reg_id',
        'acc_repli_id',
        [
            'class' => 'yii\grid\ActionColumn',
            'controller' => 'movement'
        ],
    ];
    
    echo GridView::widget([
        'dataProvider' => $dataProvider,
        'columns' => $gridColumns,
        'containerOptions' => ['style' => 'overflow: auto'],
        'pjax' => true,
        'beforeHeader' => [
            [
                'options' => ['class' => 'skip-export']
            ]
        ],
        'export' => [
            'fontAwesome' => true
        ],
        'bordered' => true,
        'striped' => true,
        'condensed' => true,
        'responsive' => true,
        'hover' => true,
        'showPageSummary' => false,
        'persistResize' => false,
    ]);
