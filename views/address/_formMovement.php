<div class="form-group" id="add-movement">
<?php
use kartik\grid\GridView;
use kartik\builder\TabularForm;
use yii\data\ArrayDataProvider;
use yii\helpers\Html;
use yii\widgets\Pjax;

$dataProvider = new ArrayDataProvider([
    'allModels' => $row,
    'pagination' => [
        'pageSize' => -1
    ]
]);
echo TabularForm::widget([
    'dataProvider' => $dataProvider,
    'formName' => 'Movement',
    'checkboxColumn' => false,
    'actionColumn' => false,
    'attributeDefaults' => [
        'type' => TabularForm::INPUT_TEXT,
    ],
    'attributes' => [
        'move_id' => ['type' => TabularForm::INPUT_HIDDEN],
        'adr_id' => [
            'label' => 'Address',
            'type' => TabularForm::INPUT_WIDGET,
            'widgetClass' => \kartik\select2\Select2::class,
            'options' => [
                'data' => \yii\helpers\ArrayHelper::map(\app\models\Address::find()->orderBy('adr_id')->asArray()->all(), 'repli_id', 'adr_id'),
                'options' => ['placeholder' => 'Choose Address'],
            ],
            'columnOptions' => ['width' => '200px']
        ],
        'd1' => ['type' => TabularForm::INPUT_TEXT],
        'd2' => ['type' => TabularForm::INPUT_TEXT],
        'd3' => ['type' => TabularForm::INPUT_TEXT],
        'doc_id' => ['type' => TabularForm::INPUT_TEXT],
        'number' => ['type' => TabularForm::INPUT_TEXT],
        'emp_id' => ['type' => TabularForm::INPUT_TEXT],
        'total' => ['type' => TabularForm::INPUT_TEXT],
        'payment' => ['type' => TabularForm::INPUT_TEXT],
        'X' => ['type' => TabularForm::INPUT_TEXT],
        'c_number' => ['type' => TabularForm::INPUT_TEXT],
        'total0' => ['type' => TabularForm::INPUT_TEXT],
        'total1' => ['type' => TabularForm::INPUT_TEXT],
        'total2' => ['type' => TabularForm::INPUT_TEXT],
        'rounding' => ['type' => TabularForm::INPUT_TEXT],
        'tax1' => ['type' => TabularForm::INPUT_TEXT],
        'tax2' => ['type' => TabularForm::INPUT_TEXT],
        'discount' => ['type' => TabularForm::INPUT_TEXT],
        'text1' => ['type' => TabularForm::INPUT_TEXT],
        'text2' => ['type' => TabularForm::INPUT_TEXT],
        'm_type_id' => ['type' => TabularForm::INPUT_TEXT],
        'repli_id' => ['type' => TabularForm::INPUT_TEXT],
        'stock_id1' => ['type' => TabularForm::INPUT_TEXT],
        'stock_id2' => ['type' => TabularForm::INPUT_TEXT],
        'acc_reg_id' => ['type' => TabularForm::INPUT_TEXT],
        'acc_repli_id' => ['type' => TabularForm::INPUT_TEXT],
        'del' => [
            'type' => 'raw',
            'label' => '',
            'value' => function($model, $key) {
                return
                    Html::hiddenInput('Children[' . $key . '][id]', (!empty($model['id'])) ? $model['id'] : "") .
                    Html::a('<i class="glyphicon glyphicon-trash"></i>', '#', ['title' =>  'Delete', 'onClick' => 'delRowMovement(' . $key . '); return false;', 'id' => 'movement-del-btn']);
            },
        ],
    ],
    'gridSettings' => [
        'panel' => [
            'heading' => false,
            'type' => GridView::TYPE_DEFAULT,
            'before' => false,
            'footer' => false,
            'after' => Html::button('<i class="glyphicon glyphicon-plus"></i>' . 'Add Movement', ['type' => 'button', 'class' => 'btn btn-success kv-batch-create', 'onClick' => 'addRowMovement()']),
        ]
    ]
]);
echo  "    </div>\n\n";
?>

