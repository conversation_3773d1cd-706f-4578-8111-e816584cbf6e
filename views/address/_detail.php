<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use kartik\grid\GridView;

/* @var $this yii\web\View */
/* @var $model app\models\Address */

?>
<div class="address-view">

    <div class="row">
        <div class="col-sm-9">
            <h2><?= Html::encode($model->firma) ?></h2>
        </div>
    </div>

    <p>
        <?= Html::a('Update', ['//address/update', 'id' => $model->adr_id], ['class' => 'btn btn-primary']) ?>
    </p>

    <div class="row">
<?php 
    $gridColumn = [
        'firma',
        'street',
        'city',
        'zip',
        'ico',
        'drc1',
        'drc2',
        'account',
        'bankcode',
        'memo',
        'adr_id',
        'firma_id',
        'info',
        'credit_complet',
        'discount',
        'expire',
        'akc_disc',
        'currency',
        'owner_name',
        'area',
        'iso',
    ];
    echo DetailView::widget([
        'model' => $model,
        'attributes' => $gridColumn
    ]); 
?>
    </div>
</div>