<?php
use kartik\grid\GridView;
use yii\data\ArrayDataProvider;

    $dataProvider = new ArrayDataProvider([
        'allModels' => $model->accRegisters,
        'key' => function($model){
            return ['acc_reg_id' => $model->acc_reg_id, 'acc_repli_id' => $model->acc_repli_id];
        }
    ]);
    $gridColumns = [
        ['class' => 'yii\grid\SerialColumn'],
        'acc_reg_id',
        'acc_repli_id',
        'acc_id',
        'repli_id',
                'num',
        'trans_date',
        'amount',
        'first_date',
        'last_date',
        'cat_id',
        'ext_bank_acc_nr',
        'ext_bank_code',
        'v_symbol',
        'k_symbol',
        's-symbol',
        'text',
        'cat_repli_id',
        'c',
        'valid',
        'amount2',
        'trans_acc_id',
        'trans_repli_id',
        'user_id',
        'payee',
        'tax_group:boolean',
        [
            'class' => 'yii\grid\ActionColumn',
            'controller' => 'acc-register'
        ],
    ];
    
    echo GridView::widget([
        'dataProvider' => $dataProvider,
        'columns' => $gridColumns,
        'containerOptions' => ['style' => 'overflow: auto'],
        'pjax' => true,
        'beforeHeader' => [
            [
                'options' => ['class' => 'skip-export']
            ]
        ],
        'export' => [
            'fontAwesome' => true
        ],
        'bordered' => true,
        'striped' => true,
        'condensed' => true,
        'responsive' => true,
        'hover' => true,
        'showPageSummary' => false,
        'persistResize' => false,
    ]);
