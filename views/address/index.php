<?php

use yii\helpers\Html;
use kartik\export\ExportMenu;
use kartik\grid\GridView;
use yii\widgets\Pjax;
/* @var $this yii\web\View */
/* @var $searchModel app\models\AddressSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Addresses';
$this->params['breadcrumbs'][] = $this->title;

ini_set("memory_limit","512M"); // For export
ini_set('max_execution_time', 300); //300 seconds = 5 minutes


?>
<div class="address-index">

    <?php Pjax::begin(); ?>
    <?php // echo $this->render('_search', ['model' => $searchModel]); ?>

    <p>
        <?= Html::a('Create Address', ['create'], ['class' => 'btn btn-success']) ?>
    </p>

    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => [
            ['class' => 'yii\grid\ActionColumn', 'template'=>'{view}'],
            'adr_id',
            'firma',
            'street',
            'city',
            'zip',
            'ico',
            'drc1',
            'drc2',
            'account',
            'bankcode',
            'memo',
            'info',
            //'credit_complet',
            //'discount',
            //'expire',
            //'akc_disc',
            //'currency',
            'owner_name',
            'firma_id',
            'area',
            //'repli_id',
            //'repli_id2',
            'iso',

            ['class' => 'yii\grid\ActionColumn', 'template'=>'{update} {delete}'],
        ],
        'panel' => [
            'type' => GridView::TYPE_PRIMARY,
            'heading' => '<span class="glyphicon glyphicon-book"></span>  ' . Html::encode($this->title),
        ],
        'toolbar' => [
            ExportMenu::widget([
                'dataProvider' => $dataProvider,
                'pjax' => false,
                'clearBuffers' => true,
                'target' => ExportMenu::TARGET_BLANK,
                'fontAwesome' => true,
                'dropdownOptions' => [
                    'label' => 'Full',
                    'class' => 'btn btn-default',
                    'itemsBefore' => [
                        '<li class="dropdown-header">Export All Data</li>',
                    ],
                ],
            ]) ,
        ]
    ]); ?>
    <?php Pjax::end(); ?>
</div>
