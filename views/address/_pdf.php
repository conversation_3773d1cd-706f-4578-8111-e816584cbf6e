<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use kartik\grid\GridView;

/* @var $this yii\web\View */
/* @var $model app\models\Address */

$this->title = $model->adr_id;
$this->params['breadcrumbs'][] = ['label' => 'Address', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="address-view">

    <div class="row">
        <div class="col-sm-9">
            <h2><?= 'Address'.' '. Html::encode($this->title) ?></h2>
        </div>
    </div>

    <div class="row">
<?php 
    $gridColumn = [
        'firma',
        'street',
        'city',
        'zip',
        'ico',
        'drc1',
        'drc2',
        'account',
        'bankcode',
        'memo',
        'adr_id',
        'firma_id',
        'info',
        'credit_complet',
        'discount',
        'expire',
        'akc_disc',
        'currency',
        'owner_name',
        'area',
        'repli_id',
        'repli_id2',
        'iso',
    ];
    echo DetailView::widget([
        'model' => $model,
        'attributes' => $gridColumn
    ]); 
?>
    </div>
    
    <div class="row">
<?php
if($providerAccRegister->totalCount){
    $gridColumnAccRegister = [
        ['class' => 'yii\grid\SerialColumn'],
        'acc_reg_id',
        'acc_repli_id',
        'acc_id',
        'repli_id',
                        'num',
        'trans_date',
        'amount',
        'first_date',
        'last_date',
        'cat_id',
        'ext_bank_acc_nr',
        'ext_bank_code',
        'v_symbol',
        'k_symbol',
        's-symbol',
        'text',
        'cat_repli_id',
        'c',
        'valid',
        'amount2',
        'trans_acc_id',
        'trans_repli_id',
        'user_id',
        'payee',
        'tax_group:boolean',
    ];
    echo Gridview::widget([
        'dataProvider' => $providerAccRegister,
        'panel' => [
            'type' => GridView::TYPE_PRIMARY,
            'heading' => Html::encode('Acc Register'),
        ],
        'panelHeadingTemplate' => '<h4>{heading}</h4>{summary}',
        'toggleData' => false,
        'columns' => $gridColumnAccRegister
    ]);
}
?>
    </div>
    
    <div class="row">
<?php
if($providerMovement->totalCount){
    $gridColumnMovement = [
        ['class' => 'yii\grid\SerialColumn'],
        'move_id',
                'd1',
        'd2',
        'd3',
        'doc_id',
        'number',
        'emp_id',
        'total',
        'payment',
        'X',
        'c_number',
        'total0',
        'total1',
        'total2',
        'rounding',
        'tax1',
        'tax2',
        'discount',
        'text1',
        'text2',
        'm_type_id',
        'repli_id',
        'stock_id1',
        'stock_id2',
        'acc_reg_id',
        'acc_repli_id',
    ];
    echo Gridview::widget([
        'dataProvider' => $providerMovement,
        'panel' => [
            'type' => GridView::TYPE_PRIMARY,
            'heading' => Html::encode('Movement'),
        ],
        'panelHeadingTemplate' => '<h4>{heading}</h4>{summary}',
        'toggleData' => false,
        'columns' => $gridColumnMovement
    ]);
}
?>
    </div>
</div>
