<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use kartik\grid\GridView;
use kartik\export\ExportMenu;
use yii\widgets\ActiveForm;



$this->title = isset($sql)?substr($sql,0,41)."...":"";
$this->params['breadcrumbs'][] = ['label' => 'Sql', 'url' => ['stat1']];

echo "<h4>Červené tlačidlá aj prerátavajú, zelené len zobrazujú výsledok </h4>";
echo 
  Html::a('5117 Recount', ['/sql/uctoselect','tab'=>'yuctogw5117model', 'proc' => 'gw_karty5117model' , 'recount' => 'yes' ], ['class' => 'btn btn-danger', 'id' =>'action1', 'data-pjax' => 0]);
echo "&nbsp;&nbsp;";
echo 
  Html::a('5117', ['/sql/uctoselect','tab'=>'yuctogw5117model', 'proc' => 'gw_karty5117model' ], ['class' => 'btn btn-success', 'id' =>'action6', 'data-pjax' => 0]);
echo "&nbsp;&nbsp;";
echo 
  Html::a('5117 !', ['/sql/uctoselect','tab'=>'yuctogw5117model', 'proc' => 'gw_karty5117model', 'missingonly' => 'yes' ], ['class' => 'btn btn-warning', 'id' =>'action3', 'data-pjax' => 0]);
echo "&nbsp;&nbsp;";



echo 
  Html::a('5039 Recount', ['/sql/uctoselect','tab'=>'yuctogw5039model', 'proc' => 'gw_karty5039model' , 'recount' => 'yes' ], ['class' => 'btn btn-danger', 'id' =>'action2', 'data-pjax' => 0]);
echo "&nbsp;&nbsp;";
echo 
  Html::a('5039', ['/sql/uctoselect','tab'=>'yuctogw5039model', 'proc' => 'gw_karty5039model' ], ['class' => 'btn btn-success', 'id' =>'action5', 'data-pjax' => 0]);
echo "&nbsp;&nbsp;";
echo 
  Html::a('5039 !', ['/sql/uctoselect','tab'=>'yuctogw5039model', 'proc' => 'gw_karty5039model', 'missingonly' => 'yes' ], ['class' => 'btn btn-warning', 'id' =>'action3', 'data-pjax' => 0]);
echo "&nbsp;&nbsp;";


echo 
  Html::a('5040 Recount', ['/sql/uctoselect','tab'=>'yuctogw5040model', 'proc' => 'gw_karty5040model' , 'recount' => 'yes' ], ['class' => 'btn btn-danger', 'id' =>'action4', 'data-pjax' => 0]);
echo "&nbsp;&nbsp;";
echo 
  Html::a('5040', ['/sql/uctoselect','tab'=>'yuctogw5040model', 'proc' => 'gw_karty5040model' ], ['class' => 'btn btn-success', 'id' =>'action7', 'data-pjax' => 0]);
echo "&nbsp;&nbsp;";
echo 
  Html::a('5040 !', ['/sql/uctoselect','tab'=>'yuctogw5040model', 'proc' => 'gw_karty5040model', 'missingonly' => 'yes' ], ['class' => 'btn btn-warning', 'id' =>'action8', 'data-pjax' => 0]);
echo "&nbsp;&nbsp;";

echo 
  Html::a('GW Recount', ['/sql/uctoselect','tab'=>'yuctoGWmodel', 'proc' => 'gw_karty_prve_prijemky_model' , 'recount' => 'yes' ], ['class' => 'btn btn-danger', 'id' =>'action9', 'data-pjax' => 0]);
echo "&nbsp;&nbsp;";
echo 
  Html::a('GW', ['/sql/uctoselect','tab'=>'yuctoGWmodel', 'proc' => 'gw_karty_prve_prijemky_model' ], ['class' => 'btn btn-success', 'id' =>'action10', 'data-pjax' => 0]);
echo "&nbsp;&nbsp;";
echo 
  Html::a('GW !', ['/sql/uctoselect','tab'=>'yuctoGWmodel', 'proc' => 'gw_karty_prve_prijemky_model', 'missingonly' => 'yes' ], ['class' => 'btn btn-warning', 'id' =>'action8', 'data-pjax' => 0]);
echo "&nbsp;&nbsp;";

echo 
  Html::a('3731 Recount', ['/sql/uctoselect','tab'=>'yuctogw3731model', 'proc' => 'gw_karty3731model' , 'recount' => 'yes' ], ['class' => 'btn btn-danger', 'id' =>'action11', 'data-pjax' => 0]);
echo "&nbsp;&nbsp;";
echo 
  Html::a('3731', ['/sql/uctoselect','tab'=>'yuctogw3731model', 'proc' => 'gw_karty3731model' ], ['class' => 'btn btn-success', 'id' =>'action12', 'data-pjax' => 0]);
echo "&nbsp;&nbsp;";
echo 
  Html::a('3731 !', ['/sql/uctoselect','tab'=>'yuctogw3731model', 'proc' => 'gw_karty3731model', 'missingonly' => 'yes' ], ['class' => 'btn btn-warning', 'id' =>'action8', 'data-pjax' => 0]);
echo "&nbsp;&nbsp;";

echo 
  Html::a('RACIO Recount', ['/sql/uctoselect','tab'=>'yuctomodel', 'proc' => 'ucto_kartymodel' , 'recount' => 'yes' ], ['class' => 'btn btn-danger', 'id' =>'action13', 'data-pjax' => 0]);
echo "&nbsp;&nbsp;";
echo 
  Html::a('RACIO', ['/sql/uctoselect','tab'=>'yuctomodel', 'proc' => 'ucto_kartymodel' ], ['class' => 'btn btn-success', 'id' =>'action14', 'data-pjax' => 0]);
echo "&nbsp;&nbsp;";
echo 
  Html::a('RACIO !', ['/sql/uctoselect','tab'=>'yuctomodel', 'proc' => 'ucto_kartymodel', 'missingonly' => 'yes' ], ['class' => 'btn btn-warning', 'id' =>'action8', 'data-pjax' => 0]);
echo "&nbsp;&nbsp;";

echo "<br><p>&nbsp;</p>";

echo Yii::$app->controller->renderPartial('_formuctoupdate', [
            'uctoupdateform' => isset( $uctoupdateform ) ? $uctoupdateform : new app\models\UpdateUctoMovementForm(),
			'uctoupdatepricesform' => isset( $uctoupdatepricesform ) ? $uctoupdatepricesform : new app\models\UpdateUctoPricesMovementForm(),
        ]);

if( isset($pupdate)){
	echo GridView::widget([
		'dataProvider' => $pupdate,
		'panel' => [
			'type' => GridView::TYPE_PRIMARY,
			'heading' => isset($title)?$title:'Tieto riadky su urcene na aktualizaciu',
		],
	]); 
	echo "&nbsp;&nbsp;";
	echo 
	  Html::a('VYKONAJ !!!', ['/sql/uctoselect','updateprikazy'=>$pupdatecmd], ['class' => 'btn btn-danger', 'id' =>'actionupdatecmd', 'data-pjax' => 0]);
	}//isset pupdate

echo "<br><p>&nbsp;</p>";

    if( isset($form)){
$f = ActiveForm::begin(); ?>

<div class="row">
	<div class="col-md-4">
<?php
if( $form->label1 != "N/A"){
	echo $f->field($form, 'param1');
}
echo '</div><div class="col-md-4">';

if( $form->label2 != "N/A"){
	echo $f->field($form, 'param2');
}

echo '</div><div class="col-md-4">';
if( $form->label3 != "N/A"){
	echo $f->field($form, 'param3');
}
echo '</div></div>';

?>

<div class="row">
	<div class="col-md-12">
		<?php
if( $form->xxlabxx != "N/A"){
	echo $f->field($form, 'xxparamxx');
}
echo '</div></div>';

?>
<div class="row">
	<div class="col-md-12">
<?php if( $form->sql != "N/A"){
	echo $f->field($form, 'sql')->textarea(['rows' => '6']);
}
?>

	</div>
</div>

        <div class="form-group">
            <?= Html::submitButton(isset($pall)?'Next report ':'Show report ', ['class' => 'btn btn-danger']) ?>
        </div>
    <?php ActiveForm::end(); 
}

    if( isset($pall)){
		echo GridView::widget([
	'dataProvider' => $pall,
	// 'options' => ['style' => 'font-size:12px;font-family: monospace;'],
	'panel' => [
		'type' => GridView::TYPE_PRIMARY,
		'heading' => isset($title)?$title:'Report...',
	],
	'toolbar' => [
		'{export}',
		// ExportMenu::widget([
		// 	'dataProvider' => $pall,
		// 	'target' => ExportMenu::TARGET_BLANK,
		// 	'fontAwesome' => true,
  //           'pjax' => false,
  //           'clearBuffers' => true,
		// 	'dropdownOptions' => [
		// 		'label' => 'Full',
		// 		'class' => 'btn btn-default',
		// 		'itemsBefore' => [
		// 			'<li class="dropdown-header">Export All Data</li>',
		// 		],
		// 	],
		// ]) ,
	],
]); 
	}//isset pall
?>
<p><?= isset($sql)?$sql:"" ?></p>
