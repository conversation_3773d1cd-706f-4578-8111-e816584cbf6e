<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use kartik\grid\GridView;
use kartik\export\ExportMenu;
use yii\widgets\ActiveForm;


$this->title = isset($sql)?substr($sql,0,41)."...":"";
$this->params['breadcrumbs'][] = ['label' => 'Sql', 'url' => ['stat1']];

    if( isset($form)){
$f = ActiveForm::begin(); ?>

<div class="row">
	<div class="col-md-4">
<?php
if( $form->label1 != "N/A"){
	echo $f->field($form, 'param1');
}
echo '</div><div class="col-md-4">';

if( $form->label2 != "N/A"){
	echo $f->field($form, 'param2');
}

echo '</div><div class="col-md-4">';
if( $form->label3 != "N/A"){
	echo $f->field($form, 'param3');
}
echo '</div></div>';

?>

<div class="row">
	<div class="col-md-12">
		<?php
if( $form->xxlabxx != "N/A"){
	echo $f->field($form, 'xxparamxx');
}
echo '</div></div>';

?>
<div class="row">
	<div class="col-md-12">
<?php if( $form->sql != "N/A"){
	echo $f->field($form, 'sql')->textarea(['rows' => '6']);
}
?>

	</div>
</div>

        <div class="form-group">
            <?= Html::submitButton(isset($pall)?'Next report ':'Show report ', ['class' => 'btn btn-danger']) ?>
        </div>
    <?php ActiveForm::end(); 
}

    if( isset($pall)){
		echo GridView::widget([
	'dataProvider' => $pall,
	// 'options' => ['style' => 'font-size:12px;font-family: monospace;'],
	'panel' => [
		'type' => GridView::TYPE_PRIMARY,
		'heading' => isset($title)?$title:'Report...',
	],
	'toolbar' => [
		'{export}',
		// ExportMenu::widget([
		// 	'dataProvider' => $pall,
		// 	'target' => ExportMenu::TARGET_BLANK,
		// 	'fontAwesome' => true,
  //           'pjax' => false,
  //           'clearBuffers' => true,
		// 	'dropdownOptions' => [
		// 		'label' => 'Full',
		// 		'class' => 'btn btn-default',
		// 		'itemsBefore' => [
		// 			'<li class="dropdown-header">Export All Data</li>',
		// 		],
		// 	],
		// ]) ,
	],
]); 
	}//isset pall
?>
<p><?= isset($sql)?$sql:"" ?></p>
