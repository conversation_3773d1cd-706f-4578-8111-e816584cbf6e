<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

if (Yii::$app->name == 'WstockUcto') { //EDIT ALL...

    if( isset($uctoupdateform)){
		$f = ActiveForm::begin();
    	echo $f->errorSummary($uctoupdateform);
        echo "<p>Funkcia: viacero riadkov, každý riadok spôsobí zmenu v jednom pohybe, zmen<PERSON> mat_id</p>";
        echo "<p>Pozorne skontrolujte vysledok riadkov urcenych na aktualizaciu, az potom vykonajte update!!!</p>";
		echo $f->field($uctoupdateform, 'multiadd')->textarea(
            ['rows' => '6','placeholder'=>$uctoupdateform->multiaddPlaceholder
            ])->label('Zadate riadky na aktualizaciu, kazdy riadok spôsobí zmenu v jednom pohybe, zmen<PERSON> mat_id');
?>


        <div class="form-group">
            <?= Html::submitButton($uctoupdateform->multiaddLabel, ['class' => 'btn btn-danger']) ?>
        </div>
    <?php ActiveForm::end(); 
	} else {
		echo "No form defined";
	}

    echo "<br><p>&nbsp;</p>";

    if( isset($uctoupdatepricesform)){
		$f2 = ActiveForm::begin();
    	echo $f2->errorSummary($uctoupdatepricesform);
        echo "<p>Funkcia: viacero riadkov, každý riadok spôsobí zmenu v jednom pohybe, zmení PRICE</p>";
        echo "<p>Pozorne skontrolujte vysledok riadkov urcenych na aktualizaciu, az potom vykonajte update!!!</p>";
		echo $f2->field($uctoupdatepricesform, 'multiaddprices')->textarea(
            ['rows' => '6','placeholder'=>$uctoupdatepricesform->multiaddpricesPlaceholder
            ])->label('Zadate riadky na aktualizaciu, kazdy riadok spôsobí zmenu v jednom pohybe, zmení PRICE');
?>


        <div class="form-group">
            <?= Html::submitButton($uctoupdatepricesform->multiaddpricesLabel, ['class' => 'btn btn-danger']) ?>
        </div>
    <?php ActiveForm::end(); 
	} else {
		echo "No update ucto prices form defined";
	}



    }