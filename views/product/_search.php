<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model app\models\ProductSearch */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="form-product-search">

    <?php $form = ActiveForm::begin([
        'action' => ['index'],
        'method' => 'get',
    ]); ?>

    <?= $form->field($model, 'mat_id')->textInput(['placeholder' => 'Mat']) ?>

    <?= $form->field($model, 'ean13')->textInput(['maxlength' => true, 'placeholder' => 'Ean13']) ?>

    <?= $form->field($model, 'kod')->textInput(['maxlength' => true, 'placeholder' => 'Kod']) ?>

    <?= $form->field($model, 'model')->textInput(['maxlength' => true, 'placeholder' => 'Model']) ?>

    <?= $form->field($model, 'price')->textInput(['maxlength' => true, 'placeholder' => 'Price']) ?>

    <?php /* echo $form->field($model, 'p0')->textInput(['maxlength' => true, 'placeholder' => 'P0']) */ ?>

    <?php /* echo $form->field($model, 'p1')->textInput(['maxlength' => true, 'placeholder' => 'P1']) */ ?>

    <?php /* echo $form->field($model, 'p2')->textInput(['maxlength' => true, 'placeholder' => 'P2']) */ ?>

    <?php /* echo $form->field($model, 'p3')->textInput(['maxlength' => true, 'placeholder' => 'P3']) */ ?>

    <?php /* echo $form->field($model, 'p4')->textInput(['maxlength' => true, 'placeholder' => 'P4']) */ ?>

    <?php /* echo $form->field($model, 'p5')->textInput(['maxlength' => true, 'placeholder' => 'P5']) */ ?>

    <?php /* echo $form->field($model, 'p6')->textInput(['maxlength' => true, 'placeholder' => 'P6']) */ ?>

    <?php /* echo $form->field($model, 'p8')->textInput(['maxlength' => true, 'placeholder' => 'P8']) */ ?>

    <?php /* echo $form->field($model, 'p9')->textInput(['maxlength' => true, 'placeholder' => 'P9']) */ ?>

    <?php /* echo $form->field($model, 'pdescr')->textInput(['maxlength' => true, 'placeholder' => 'Pdescr']) */ ?>

    <?php /* echo $form->field($model, 'tax')->textInput(['maxlength' => true, 'placeholder' => 'Tax']) */ ?>

    <?php /* echo $form->field($model, 'unit')->textInput(['maxlength' => true, 'placeholder' => 'Unit']) */ ?>

    <?php /* echo $form->field($model, 'min')->textInput(['maxlength' => true, 'placeholder' => 'Min']) */ ?>

    <?php /* echo $form->field($model, 'max')->textInput(['maxlength' => true, 'placeholder' => 'Max']) */ ?>

    <?php /* echo $form->field($model, 'p7')->textInput(['maxlength' => true, 'placeholder' => 'P7']) */ ?>

    <?php /* echo $form->field($model, 'p0a')->textInput(['maxlength' => true, 'placeholder' => 'P0a']) */ ?>

    <?php /* echo $form->field($model, 'p0b')->textInput(['maxlength' => true, 'placeholder' => 'P0b']) */ ?>

    <?php /* echo $form->field($model, 'p0c')->textInput(['maxlength' => true, 'placeholder' => 'P0c']) */ ?>

    <?php /* echo $form->field($model, 'p0d')->textInput(['maxlength' => true, 'placeholder' => 'P0d']) */ ?>

    <?php /* echo $form->field($model, 'p0e')->textInput(['maxlength' => true, 'placeholder' => 'P0e']) */ ?>

    <?php /* echo $form->field($model, 'p0f')->textInput(['maxlength' => true, 'placeholder' => 'P0f']) */ ?>

    <?php /* echo $form->field($model, 'p0g')->textInput(['maxlength' => true, 'placeholder' => 'P0g']) */ ?>

    <?php /* echo $form->field($model, 'p0h')->textInput(['maxlength' => true, 'placeholder' => 'P0h']) */ ?>

    <?php /* echo $form->field($model, 'p0i')->textInput(['maxlength' => true, 'placeholder' => 'P0i']) */ ?>

    <?php /* echo $form->field($model, 'p0j')->textInput(['maxlength' => true, 'placeholder' => 'P0j']) */ ?>

    <?php /* echo $form->field($model, 'p1b')->textInput(['maxlength' => true, 'placeholder' => 'P1b']) */ ?>

    <?php /* echo $form->field($model, 'p1c')->textInput(['maxlength' => true, 'placeholder' => 'P1c']) */ ?>

    <?php /* echo $form->field($model, 'p1d')->textInput(['maxlength' => true, 'placeholder' => 'P1d']) */ ?>

    <?php /* echo $form->field($model, 'p1e')->textInput(['maxlength' => true, 'placeholder' => 'P1e']) */ ?>

    <?php /* echo $form->field($model, 'p1f')->textInput(['maxlength' => true, 'placeholder' => 'P1f']) */ ?>

    <?php /* echo $form->field($model, 'p1g')->textInput(['maxlength' => true, 'placeholder' => 'P1g']) */ ?>

    <?php /* echo $form->field($model, 'p1h')->textInput(['maxlength' => true, 'placeholder' => 'P1h']) */ ?>

    <?php /* echo $form->field($model, 'p1i')->textInput(['maxlength' => true, 'placeholder' => 'P1i']) */ ?>

    <?php /* echo $form->field($model, 'p1j')->textInput(['maxlength' => true, 'placeholder' => 'P1j']) */ ?>

    <?php /* echo $form->field($model, 'p2b')->textInput(['maxlength' => true, 'placeholder' => 'P2b']) */ ?>

    <?php /* echo $form->field($model, 'p2c')->textInput(['maxlength' => true, 'placeholder' => 'P2c']) */ ?>

    <?php /* echo $form->field($model, 'p2d')->textInput(['maxlength' => true, 'placeholder' => 'P2d']) */ ?>

    <?php /* echo $form->field($model, 'p2e')->textInput(['maxlength' => true, 'placeholder' => 'P2e']) */ ?>

    <?php /* echo $form->field($model, 'p2f')->textInput(['maxlength' => true, 'placeholder' => 'P2f']) */ ?>

    <?php /* echo $form->field($model, 'p2g')->textInput(['maxlength' => true, 'placeholder' => 'P2g']) */ ?>

    <?php /* echo $form->field($model, 'p2h')->textInput(['maxlength' => true, 'placeholder' => 'P2h']) */ ?>

    <?php /* echo $form->field($model, 'p2i')->textInput(['maxlength' => true, 'placeholder' => 'P2i']) */ ?>

    <?php /* echo $form->field($model, 'p2j')->textInput(['maxlength' => true, 'placeholder' => 'P2j']) */ ?>

    <?php /* echo $form->field($model, 'p1a')->textInput(['maxlength' => true, 'placeholder' => 'P1a']) */ ?>

    <?php /* echo $form->field($model, 'p2a')->textInput(['maxlength' => true, 'placeholder' => 'P2a']) */ ?>

    <div class="form-group">
        <?= Html::submitButton('Search', ['class' => 'btn btn-primary']) ?>
        <?= Html::resetButton('Reset', ['class' => 'btn btn-default']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
