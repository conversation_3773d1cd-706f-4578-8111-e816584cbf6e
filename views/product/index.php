<?php

/* @var $this yii\web\View */
/* @var $searchModel app\models\ProductSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

use yii\helpers\Html;
use kartik\export\ExportMenu;
use kartik\grid\GridView;
use yii\helpers\Url;
use app\controllers\EstockTools;

ini_set("memory_limit", "512M"); // For export
ini_set('max_execution_time', 300); //300 seconds = 5 minutes


$this->title = 'Product';
$this->params['breadcrumbs'][] = $this->title;
$search = "$('.search-button').click(function(){
	$('.search-form').toggle(1000);
	return false;
});";
$this->registerJs($search);
?>
<div class="product-index">

    <h1><?= Html::encode($this->title) ?></h1>
    <?php // echo $this->render('_search', ['model' => $searchModel]); 
    ?>

    <p>
        <?= Html::a('Create Product', ['create'], ['class' => 'btn btn-success']) ?>
        <?= Html::a('Advance Search', '#', ['class' => 'btn btn-info search-button']) ?>
        <?= Html::a('Update prices', ['batchupdate'], ['class' => 'btn btn-danger']) ?>
    </p>
    <div class="search-form" style="display:none">
        <?= $this->render('_search', ['model' => $searchModel]); ?>
    </div>
    <?php
    $gridColumn = [
        ['class' => 'yii\grid\SerialColumn'],

        [
            'class' => 'kartik\grid\ExpandRowColumn',
            'width' => '50px',
            'value' => function ($model, $key, $index, $column) {
                return GridView::ROW_COLLAPSED;
            },
            // 'detailUrl' => Url::to([EstockTools::isUserShop()?'/product/viewdetailshop':'/product/stockcard']),
            'detailUrl' => Url::to(['/product/stockcard']),
            'headerOptions' => ['class' => 'kartik-sheet-style'],
            'expandOneOnly' => false
        ],
        [
            'class' => 'yii\grid\ActionColumn',
            'template' => '{update} '
        ],
        'mat_id',
        'ean13',
        'kod',
        'model',
        'model2',
        'hs_code',
        'coo',
        [
            'header' => 'Buying <br> price',
            'attribute' => 'price',
            'format' => ['decimal', 2],
        ],
        [
            'attribute' => 'p0',
            'label' => 'Wholesale SK <br /> p0',
            'encodeLabel' => false,
        ],
        'p1',
        'p2',
        'p3',
        'p4',
        'p5',
        'p6',
        'p7',
        'p8',
        'p9',
        'pdescr',
        // 'tax',
        // 'unit',
        // 'min',
        // 'max',
        'p0a',
        'p0b',
        'p0c',
        // 'p0d',
        'p0e',
        'p0f',
        // 'p0g',
        // 'p0h',
        // 'p0i',
        // 'p0j',
        // 'p1b',
        // 'p1c',
        // 'p1d',
        // 'p1e',
        // 'p1f',
        // 'p1g',
        // 'p1h',
        // 'p1i',
        // 'p1j',
        // 'p2b',
        // 'p2c',
        // 'p2d',
        // 'p2e',
        // 'p2f',
        // 'p2g',
        // 'p2h',
        // 'p2i',
        // 'p2j',
        // 'p1a',
        // 'p2a',
        [
            'class' => 'yii\grid\ActionColumn',
            'template' => '{delete}'
        ],
    ];
    ?>
    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => $gridColumn,
        'formatter' => ['class' => 'yii\i18n\Formatter', 'nullDisplay' => ''],
        'export' => false,
        'pjax' => true,
        'pjaxSettings' => ['options' => ['id' => 'kv-pjax-container-product']],
        'panel' => [
            'type' => GridView::TYPE_PRIMARY,
            'heading' => '<span class="glyphicon glyphicon-book"></span>  ' . Html::encode($this->title),
        ],
        // your toolbar can include the additional full export menu
        'toolbar' => [
            ExportMenu::widget([
                'dataProvider' => $dataProvider,
                'columns' => $gridColumn,
                'pjax' => false,
                'clearBuffers' => true,
                'target' => ExportMenu::TARGET_BLANK,
                'fontAwesome' => true,
                'dropdownOptions' => [
                    'label' => 'Full',
                    'class' => 'btn btn-default',
                    'itemsBefore' => [
                        '<li class="dropdown-header">Export All Data</li>',
                    ],
                ],
            ]),
        ],
    ]); ?>

</div>