<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use app\controllers\EstockTools;


/* @var $this yii\web\View */
/* @var $model app\models\Product */
/* @var $form yii\widgets\ActiveForm */

?>

<div class="product-form">

    <?php $form = ActiveForm::begin(); ?>

    <?= $form->errorSummary($model); ?>

    <?= $form->field($model, 'mat_id')->textInput(['placeholder' => 'Mat', 'readonly' => $model->isNewRecord || EstockTools::isUserDba() ? false : true]) ?>

    <?= $form->field($model, 'ean13')->textInput(['maxlength' => true, 'placeholder' => 'Ean13']) ?>

    <!--     <?= $form->field($model, 'kod')->textInput(['maxlength' => true, 'placeholder' => 'Kod', 'readonly' => $model->isNewRecord || EstockTools::isUserDba() ? false : true]) ?>
 -->

    <?= $form->field($model, 'kod')->widget(kartik\select2\Select2::classname(), [
        'data' => \yii\helpers\ArrayHelper::map(\app\models\Kod::find()->asArray()->all(), 'kod', 'kod'),
        'options' => ['class' => 'readonlyclass', 'placeholder' => 'Kod'],
        'pluginEvents' => [
            "select2:opening" => $model->isNewRecord || EstockTools::isUserDba() ? "" : "function() { $('.readonlyclass').attr('disabled', true); }",
        ],
    ]); ?>

    <?= $form->field($model, 'model')->textInput(['maxlength' => true, 'placeholder' => 'Model', 'readonly' => $model->isNewRecord || EstockTools::isUserDba() ? false : true]) ?>

    <?= $form->field($model, 'pdescr')->textInput(['maxlength' => true, 'placeholder' => 'Pdescr']) ?>

    <?= $form->field($model, 'price')->textInput(['maxlength' => true, 'placeholder' => 'Price']) ?>

    <?= $form->field($model, 'p0')->textInput(['maxlength' => true, 'placeholder' => 'P0']) ?>

    <?= $form->field($model, 'p1')->textInput(['maxlength' => true, 'placeholder' => 'P1']) ?>

    <?= $form->field($model, 'p2')->textInput(['maxlength' => true, 'placeholder' => 'P2']) ?>

    <?= $form->field($model, 'p3')->textInput(['maxlength' => true, 'placeholder' => 'P3']) ?>

    <?= $form->field($model, 'p4')->textInput(['maxlength' => true, 'placeholder' => 'P4']) ?>

    <?= $form->field($model, 'p5')->textInput(['maxlength' => true, 'placeholder' => 'P5']) ?>

    <?= $form->field($model, 'p6')->textInput(['maxlength' => true, 'placeholder' => 'P6']) ?>

    <?= $form->field($model, 'p7')->textInput(['maxlength' => true, 'placeholder' => 'P7']) ?>

    <?= $form->field($model, 'p8')->textInput(['maxlength' => true, 'placeholder' => 'P8']) ?>

    <?= $form->field($model, 'p9')->textInput(['maxlength' => true, 'placeholder' => 'P9']) ?>

    <!--     <?= $form->field($model, 'tax')->textInput(['maxlength' => true, 'placeholder' => 'Tax']) ?>

    <?= $form->field($model, 'unit')->textInput(['maxlength' => true, 'placeholder' => 'Unit']) ?>

    <?= $form->field($model, 'min')->textInput(['maxlength' => true, 'placeholder' => 'Min']) ?>

    <?= $form->field($model, 'max')->textInput(['maxlength' => true, 'placeholder' => 'Max']) ?>

-->

    <?= $form->field($model, 'p0a')->textInput(['maxlength' => true, 'placeholder' => 'P0a']) ?>

    <?= $form->field($model, 'p0b')->textInput(['maxlength' => true, 'placeholder' => 'P0b']) ?>
    <?= $form->field($model, 'p0c')->textInput(['maxlength' => true, 'placeholder' => 'P0c']) ?>
    <!--

    <?= $form->field($model, 'p0d')->textInput(['maxlength' => true, 'placeholder' => 'P0d']) ?>
-->

    <?= $form->field($model, 'p0e')->textInput(['maxlength' => true, 'placeholder' => 'P0e']) ?>
    <?= $form->field($model, 'hs_code')->textInput(['maxlength' => true, 'placeholder' => 'HS code']) ?>

    <?= $form->field($model, 'coo')->widget(kartik\select2\Select2::classname(), [
        'data' => \yii\helpers\ArrayHelper::map(\app\models\Countries::find()->asArray()->all(), 'iso', 'printable_name'),
        'options' => ['class' => 'readonlyclass', 'placeholder' => 'Country of origin'],
        // 'pluginEvents' => [
        //     "select2:opening" => $model->isNewRecord || EstockTools::isUserDba() ? "" : "function() { $('.readonlyclass').attr('disabled', true); }",
        // ],
    ]); ?>

    <!--

    <?= $form->field($model, 'p0f')->textInput(['maxlength' => true, 'placeholder' => 'P0f']) ?>

    <?= $form->field($model, 'p0g')->textInput(['maxlength' => true, 'placeholder' => 'P0g']) ?>

    <?= $form->field($model, 'p0h')->textInput(['maxlength' => true, 'placeholder' => 'P0h']) ?>

    <?= $form->field($model, 'p0i')->textInput(['maxlength' => true, 'placeholder' => 'P0i']) ?>

    <?= $form->field($model, 'p0j')->textInput(['maxlength' => true, 'placeholder' => 'P0j']) ?>

    <?= $form->field($model, 'p1b')->textInput(['maxlength' => true, 'placeholder' => 'P1b']) ?>

    <?= $form->field($model, 'p1c')->textInput(['maxlength' => true, 'placeholder' => 'P1c']) ?>

    <?= $form->field($model, 'p1d')->textInput(['maxlength' => true, 'placeholder' => 'P1d']) ?>

    <?= $form->field($model, 'p1e')->textInput(['maxlength' => true, 'placeholder' => 'P1e']) ?>

    <?= $form->field($model, 'p1f')->textInput(['maxlength' => true, 'placeholder' => 'P1f']) ?>

    <?= $form->field($model, 'p1g')->textInput(['maxlength' => true, 'placeholder' => 'P1g']) ?>

    <?= $form->field($model, 'p1h')->textInput(['maxlength' => true, 'placeholder' => 'P1h']) ?>

    <?= $form->field($model, 'p1i')->textInput(['maxlength' => true, 'placeholder' => 'P1i']) ?>

    <?= $form->field($model, 'p1j')->textInput(['maxlength' => true, 'placeholder' => 'P1j']) ?>

    <?= $form->field($model, 'p2b')->textInput(['maxlength' => true, 'placeholder' => 'P2b']) ?>

    <?= $form->field($model, 'p2c')->textInput(['maxlength' => true, 'placeholder' => 'P2c']) ?>

    <?= $form->field($model, 'p2d')->textInput(['maxlength' => true, 'placeholder' => 'P2d']) ?>

    <?= $form->field($model, 'p2e')->textInput(['maxlength' => true, 'placeholder' => 'P2e']) ?>

    <?= $form->field($model, 'p2f')->textInput(['maxlength' => true, 'placeholder' => 'P2f']) ?>

    <?= $form->field($model, 'p2g')->textInput(['maxlength' => true, 'placeholder' => 'P2g']) ?>

    <?= $form->field($model, 'p2h')->textInput(['maxlength' => true, 'placeholder' => 'P2h']) ?>

    <?= $form->field($model, 'p2i')->textInput(['maxlength' => true, 'placeholder' => 'P2i']) ?>

    <?= $form->field($model, 'p2j')->textInput(['maxlength' => true, 'placeholder' => 'P2j']) ?>

    <?= $form->field($model, 'p1a')->textInput(['maxlength' => true, 'placeholder' => 'P1a']) ?>

    <?= $form->field($model, 'p2a')->textInput(['maxlength' => true, 'placeholder' => 'P2a']) ?>
     -->
    <div class="form-group">
        <?= Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>