<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

$this->title = "Batch product update";
    if( isset($form)){
		$f = ActiveForm::begin();
    	echo $f->errorSummary($form);
		echo $f->field($form, 'updatebox')->textarea(['rows' => '6','placeholder'=>$form->updatePlaceholder])->label($form->updateLabel);
?>

<p>
	You can use comma delimited string - CSV or copy paste string from your Excel file. Do not forget columns with comma: <b>,</b>
	 <br>
	 and count of columns should be every line the same as the heading line.<br>
	 <b>Heading</b> has the first word KEY. You can use mat_id or model. Please do not use anything else.<br>
	 Other heading words can be price,p0 ... p9, etc. in any order.<br>
	 If you get error, last command is not successfully executed and all the rest lines were not executed, too.<br>
	 If not found, nothing is updated. No new product will be created. You get no info about commands without impact.<br>
	 Database has unique key for mat_id and also for model. If you use other key, <b>YOU CAN COMPLETELY DESTROY PRODUCT TABLE...</b>
</p>


        <div class="form-group">
            <?= Html::submitButton($form->updateLabel, ['class' => 'btn btn-danger']) ?>
        </div>
    <?php ActiveForm::end(); 
	} else {
		echo "No form defined";
	}
