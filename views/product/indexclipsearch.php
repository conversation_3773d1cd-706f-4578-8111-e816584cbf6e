<?php

/* @var $this yii\web\View */
/* @var $searchModel app\models\ProductSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

use yii\helpers\Html;
use yii\helpers\Url;
use kartik\grid\GridView;
use app\controllers\EstockTools;

?>
<div class="product-index">

    <?php 
    $gridColumn = [
                [
            'class' => 'kartik\grid\ExpandRowColumn',
            'width' => '50px',
            'value' => function ($model, $key, $index, $column) {
                return GridView::ROW_COLLAPSED;
            },
            'detailUrl' => Url::to('/product/stockcard'), // Url::to([EstockTools::isUserShop()?'/product/viewdetailshop':'/product/stockcard']),
            'headerOptions' => ['class' => 'kartik-sheet-style'],
            'expandOneOnly' => false
        ],
        'mat_id',
        'ean13',
        'kod',
        'model',
        'pdescr',
    ]; 

    // Nech vidno ceny ako nastavene
    //array_merge($gridColumn, EstockTools::getB2bParams()['shopmtypes'][0][4]); 

    ?>
    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'formatter' => ['class' => 'yii\i18n\Formatter','nullDisplay' => ''],
        'columns' => $gridColumn, // array_merge($gridColumn, [EstockTools::getShopParams()['shopmtypes'][0][4]]),
        'pjax' => true,
        'pjaxSettings' => ['options' => ['id' => 'kv-pjax-container-product']],
        'panel' => [
            'type' => GridView::TYPE_PRIMARY,
            'heading' => '<span class="glyphicon glyphicon-book"></span>  Search in products',
        ],
    ]); ?>

</div>
