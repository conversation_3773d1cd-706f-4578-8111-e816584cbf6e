<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use kartik\grid\GridView;
?>
<div id="data<?= $idcko  ?>" >
<div class="product-view">
    <div class="row">
         <div class="col-sm-12">
<?php 
    $gridColumn = [
    	['class' => 'yii\grid\ActionColumn', 'template' => '{info}',
			'buttons' => [
			        'info' => function ($url, $model) {
			            return Html::a('<span class="glyphicon glyphicon-info-sign"></span>', $url, [
			                        'title' => Yii::t('app', 'Info'),
			            ]);
			        }
			],
			'urlCreator' => function ($action, $model, $key, $index) {
        			if ($action === 'info') {
            			$url = Yii::$app->urlManager->createUrl(['movement/index',"MovementSearch[move_id]"=>$model['move_id']]);
            			return $url;
        			}
    			}
        ],
		'move_id',
        'm_type_id',
		'xname',
		'd1',
		'pcs',
        'from_stock',
        'to_stock',
        'text1',
        'text2',
        'firma',
        'serials'
    ];
    echo GridView::widget([
        'dataProvider' => $data1,
        'columns' => $gridColumn,
        'layout' => '{items}'
    ]); 
?>
        </div>
    </div>
</div>
