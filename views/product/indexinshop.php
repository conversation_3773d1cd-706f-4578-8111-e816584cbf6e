<?php

/* @var $this yii\web\View */
/* @var $searchModel app\models\ProductSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

use yii\helpers\Html;
use yii\helpers\Url;
use kartik\grid\GridView;
use app\controllers\EstockTools;
$this->title = 'Product...';
?>
<div class="product-index">
<p>
        <?= Html::a('New product', ['createinshop'], ['class' => 'btn btn-success']) ?>
</p>
    <?php 
    $gridColumn = [
        [
            'class' => 'yii\grid\ActionColumn', 'template' => '{update} ',
            'buttons' => [
                'update' => function ($url, $model, $key) {
                    return Html::a(Html::tag('span', '', ['class' => 'glyphicon glyphicon-pencil']), Url::to(['product/updateinshop', 'id' => $key]), [
                        'title' => Yii::t('yii', 'Update'),
                    ]);
                },    
            ],
        ],
        'mat_id',
        'kod',
        'model',
        'pdescr',
    ]; 

    ?>
    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'formatter' => ['class' => 'yii\i18n\Formatter','nullDisplay' => ''],
        'columns' => array_merge($gridColumn, EstockTools::getShopParams()['productEditPrices']),
        'pjax' => true,
        'pjaxSettings' => ['options' => ['id' => 'kv-pjax-container-product']],
        'panel' => [
            'type' => GridView::TYPE_PRIMARY,
            'heading' => '<span class="glyphicon glyphicon-book"></span>  Search in products',
        ],
    ]); ?>

</div>
