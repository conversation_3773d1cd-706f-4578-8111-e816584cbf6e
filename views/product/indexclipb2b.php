<?php

/* @var $this yii\web\View */
/* @var $searchModel app\models\ProductSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

use yii\helpers\Html;
use yii\helpers\Url;
use kartik\grid\GridView;
use app\controllers\EstockTools;

?>
<div class="product-index">

    <?php 
    $gridColumn = [
                    ['class' => 'yii\grid\ActionColumn', 'template' => '{info}',
            'buttons' => [
                    'info' => function ($url, $m) {
                        return Html::a('<span class="glyphicon glyphicon-thumbs-up"></span>', $url, [
                                    'title' => 'Add this!!!',
                                    'onclick' => "$('#tempmove-mat_id').val('".$m['mat_id']."');$('#tempmove-price').val('".$m[EstockTools::getB2bParams()['shopmtypes'][0][4]]."'); $('#w1').submit(); return false;"
                        ]);
                    }
            ],
            'urlCreator' => function ($action, $m, $key, $index) {
                    if ($action === 'info') {
                        $url = Yii::$app->urlManager->createUrl(['#']);
                        return $url;
                    }
                }
        ],

        'mat_id',
        'kod',
        'model',
        'pdescr',
        'stockstatus'
    ]; 

    ?>
    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'formatter' => ['class' => 'yii\i18n\Formatter','nullDisplay' => ''],
        'columns' => array_merge($gridColumn, [EstockTools::getB2bParams()['shopmtypes'][0][4]]),
        'pjax' => true,
        'pjaxSettings' => ['options' => ['id' => 'kv-pjax-container-product']],
        'panel' => [
            'type' => GridView::TYPE_PRIMARY,
            'heading' => '<span class="glyphicon glyphicon-book"></span>  Search in products',
        ],
    ]); ?>

</div>
