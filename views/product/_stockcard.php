<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use kartik\grid\GridView;

//print_r($model,false);

/* @var $this yii\web\View */
/* @var $model app\models\Product */

?>
<div class="product-view">

    <div class="row">
        <div class="col-sm-9">
        </div>
    </div>

    <div class="row">
         <div class="col-sm-9">
<?php 
    echo "<h4>Sum of pcs here: ".$sum1."</h4>";
    $gridColumn = [
        'kod',
        'model',
	'pcs',
	'd1',
	'name',
        'price',
        'detail_info',
    ];
    echo GridView::widget([
        'dataProvider' => $model,
        'columns' => $gridColumn
    ]); 
    echo "<p><i>Note: <b>only this year</b> movements shown</i></p>";
?>
        </div>
    </div>
    <div class="row">
         <div class="col-sm-9">
<?php 
    echo "<h4>Remaining on other shops</h4>";
    $gridColumn2 = [
        'kod',
        'model',
    'firma',
    'city',
    'remains',
    ];
    echo GridView::widget([
        'dataProvider' => $model2,
        'columns' => $gridColumn2
    ]); 
?>
        </div>
    </div>
</div>