<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use kartik\grid\GridView;

/* @var $this yii\web\View */
/* @var $model app\models\Product */

$this->title = $model->mat_id;
$this->params['breadcrumbs'][] = ['label' => 'Product', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="product-view">

    <div class="row">
        <div class="col-sm-9">
            <h2><?= 'Product'.' '. Html::encode($this->title) ?></h2>
        </div>
    </div>

    <div class="row">
<?php 
    $gridColumn = [
        'mat_id',
        'ean13',
        'kod',
        'model',
        'price',
        'p0',
        'p1',
        'p2',
        'p3',
        'p4',
        'p5',
        'p6',
        'p8',
        'p9',
        'pdescr',
        'tax',
        'unit',
        'min',
        'max',
        'p7',
        'p0a',
        'p0b',
        'p0c',
        'p0d',
        'p0e',
        'p0f',
        'p0g',
        'p0h',
        'p0i',
        'p0j',
        'p1b',
        'p1c',
        'p1d',
        'p1e',
        'p1f',
        'p1g',
        'p1h',
        'p1i',
        'p1j',
        'p2b',
        'p2c',
        'p2d',
        'p2e',
        'p2f',
        'p2g',
        'p2h',
        'p2i',
        'p2j',
        'p1a',
        'p2a',
    ];
    echo DetailView::widget([
        'model' => $model,
        'attributes' => $gridColumn
    ]); 
?>
    </div>
</div>
