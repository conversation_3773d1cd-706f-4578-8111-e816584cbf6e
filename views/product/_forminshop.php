<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use app\controllers\EstockTools;


/* @var $this yii\web\View */
/* @var $model app\models\Product */
/* @var $form yii\widgets\ActiveForm */

?>

<div class="product-form">

    <?php $form = ActiveForm::begin(); ?>

    <?= $form->errorSummary($model); ?>

    <?= $form->field($model, 'kod')->widget(kartik\select2\Select2::class, [
        'data' => \yii\helpers\ArrayHelper::map(\app\models\Kod::find()->asArray()->all(), 'kod', 'kod'),
        'options' => ['class' =>'readonlyclass', 'placeholder' => 'Kod'],
        'pluginEvents' => [
        "select2:opening" => $model->isNewRecord||EstockTools::isUserDba()? "" : "function() { $('.readonlyclass').attr('disabled', true); }",
        ], 
    ]); ?>

    <?= $form->field($model, 'model')->textInput(['maxlength' => true, 'placeholder' => 'Model', 'readonly' => $model->isNewRecord||EstockTools::isUserDba()?false:true]) ?>

    <?= $form->field($model, 'pdescr')->textInput(['maxlength' => true, 'placeholder' => 'Pdescr']) ?>

    <?php
        foreach( EstockTools::getShopParams()['productEditPrices'] as $priceName => $priceLabel ) {
            echo $form->field($model, $priceLabel)->textInput(['maxlength' => true, 'placeholder' => $priceLabel]);
        }

    ?>
    <div class="form-group">
        <?= Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
