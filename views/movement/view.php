<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use kartik\grid\GridView;

/* @var $this yii\web\View */
/* @var $model app\models\Movement */

$this->title = $model->move_id;
$this->params['breadcrumbs'][] = ['label' => 'Movement', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="movement-view">

    <div class="row">
        <div class="col-sm-2" style="margin-top: 15px">
            <?= Html::a('List', ['index'], ['class' => 'btn btn-success']) ?>
        </div>
        <div class="col-sm-7">
            <h2><?= 'Movement'.' '. Html::encode($this->title) ?></h2>
        </div>
        <div class="col-sm-3" style="margin-top: 15px">
<?=             
             Html::a('<i class="fa glyphicon glyphicon-hand-up"></i> ' . 'PDF', 
                ['pdf', 'move_id' => $model->move_id],
                [
                    'class' => 'btn btn-danger',
                    'target' => '_blank',
                    'data-toggle' => 'tooltip',
                    'title' => 'Will open the generated PDF file in a new window'
                ]
            )?>
            
            <?= Html::a('Update', ['update', 'id' => $model->move_id], ['class' => 'btn btn-primary']) ?>
            <?= Html::a('Delete', ['delete', 'id' => $model->move_id], [
                'class' => 'btn btn-danger',
                'data' => [
                    'confirm' => 'Are you sure you want to delete this item?',
                    'method' => 'post',
                ],
            ])
            ?>
        </div>
    </div>

    <div class="row">
<?php 
    $gridColumn = [
        'move_id',
        [
            'attribute' => 'adr.adr_id',
            'label' => 'Adr',
        ],
        'd1',
        'd2',
        'd3',
        'doc_id',
        'number',
        'emp_id',
        'total',
        'payment',
        ['attribute' => 'X', 'visible' => false],
        'c_number',
        'total0',
        'total1',
        'total2',
        'rounding',
        'tax1',
        'tax2',
        'discount',
        'text1',
        'text2',
        'm_type_id',
        [
            'attribute' => 'adr.firma',
            'label' => 'Firma',
        ],
        'stock_id1',
        'stock_id2',
        'parent_move_id'
    ];
    echo DetailView::widget([
        'model' => $model,
        'attributes' => $gridColumn
    ]);
?>
    </div>
    <div class="row">
        <h4>Address<?= ' '. Html::encode($this->title) ?></h4>
    </div>
    <?php 
    $gridColumnAddress = [
        'firma',
        'street',
        'city',
        'zip',
        'ico',
        'drc1',
        'drc2',
        'account',
        'bankcode',
        'memo',
        'firma_id',
        'info',
        'credit_complet',
        'discount',
        'expire',
        'akc_disc',
        'currency',
        'owner_name',
        'area',
        'iso',
    ];
    echo DetailView::widget([
        'model' => $model->adr,
        'attributes' => $gridColumnAddress    ]);
    ?>
    <div class="row">
        <h4>Address<?= ' '. Html::encode($this->title) ?></h4>
    </div>
    <?php 
    $gridColumnAddress = [
        'firma',
        'street',
        'city',
        'zip',
        'ico',
        'drc1',
        'drc2',
        'account',
        'bankcode',
        'memo',
        'adr_id',
        'firma_id',
        'info',
        'credit_complet',
        'discount',
        'expire',
        'akc_disc',
        'currency',
        'owner_name',
        'area',
        'iso',
    ];
    ?>
</div>
