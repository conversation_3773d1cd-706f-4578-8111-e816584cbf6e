<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model app\models\Movement */
/* @var $form yii\widgets\ActiveForm */

?>

<div class="movement-form">

    <?php $form = ActiveForm::begin(); ?>

    <?= $form->errorSummary($model); ?>
<!-- 
    <?= $form->field($model, 'move_id')->textInput(['placeholder' => 'Move']) ?>

    <?= $form->field($model, 'adr_id')->widget(kartik\select2\Select2::classname(), [
        'data' => \yii\helpers\ArrayHelper::map(\app\models\Address::find()->asArray()->all(), 'adr_id','adr_id'),
        'options' => ['placeholder' => 'Choose Address'],
        'pluginOptions' => [
            'allowClear' => true
        ],
    ]); ?>
 -->
    <?= $form->field($model, 'd1')->textInput(['maxlength' => true, 'placeholder' => 'D1']) ?>

    <?= $form->field($model, 'd2')->textInput(['maxlength' => true, 'placeholder' => 'D2']) ?>

    <?= $form->field($model, 'd3')->textInput(['maxlength' => true, 'placeholder' => 'D3']) ?>

 <!--    <?= $form->field($model, 'doc_id')->textInput(['maxlength' => true, 'placeholder' => 'Doc']) ?>
 -->
    <?= $form->field($model, 'number')->textInput(['placeholder' => 'Number']) ?>

 <!--    <?= $form->field($model, 'emp_id')->textInput(['maxlength' => true, 'placeholder' => 'Emp']) ?>

    <?= $form->field($model, 'total')->textInput(['maxlength' => true, 'placeholder' => 'Total']) ?>

    <?= $form->field($model, 'payment')->textInput(['maxlength' => true, 'placeholder' => 'Payment']) ?>

    <?= $form->field($model, 'X', ['template' => '{input}'])->textInput(['style' => 'display:none']); ?>

    <?= $form->field($model, 'c_number')->textInput(['placeholder' => 'C Number']) ?>

    <?= $form->field($model, 'total0')->textInput(['maxlength' => true, 'placeholder' => 'Total0']) ?>

    <?= $form->field($model, 'total1')->textInput(['maxlength' => true, 'placeholder' => 'Total1']) ?>

    <?= $form->field($model, 'total2')->textInput(['maxlength' => true, 'placeholder' => 'Total2']) ?>

    <?= $form->field($model, 'rounding')->textInput(['maxlength' => true, 'placeholder' => 'Rounding']) ?>

    <?= $form->field($model, 'tax1')->textInput(['maxlength' => true, 'placeholder' => 'Tax1']) ?>

    <?= $form->field($model, 'tax2')->textInput(['maxlength' => true, 'placeholder' => 'Tax2']) ?>

    <?= $form->field($model, 'discount')->textInput(['maxlength' => true, 'placeholder' => 'Discount']) ?>
 -->
    <?= $form->field($model, 'text1')->textInput(['maxlength' => true, 'placeholder' => 'Text1']) ?>

    <?= $form->field($model, 'text2')->textInput(['maxlength' => true, 'placeholder' => 'Text2']) ?>
<!-- 
    <?= $form->field($model, 'm_type_id')->textInput(['placeholder' => 'M Type']) ?>
-->
    <?= $form->field($model, 'adr_id')->widget(kartik\select2\Select2::classname(), [
        'data' => \yii\helpers\ArrayHelper::map(\app\models\Address::find()->select(['adr_id',"adr_id||' '||\"firma\" as firma" ])->asArray()->all(), 'adr_id','firma'),
        'options' => ['placeholder' => 'Choose Address'],
        'pluginOptions' => [
            'allowClear' => true
        ],
    ]); ?>

<!--
    <?= $form->field($model, 'stock_id1')->textInput(['maxlength' => true, 'placeholder' => 'Stock Id1']) ?>

    <?= $form->field($model, 'stock_id2')->textInput(['maxlength' => true, 'placeholder' => 'Stock Id2']) ?>
 -->
    <div class="form-group">
        <?= Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
