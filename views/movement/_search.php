<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use kartik\daterange\DateRangePicker;
/* @var $this yii\web\View */
/* @var $model app\models\MovementSearch */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="form-movement-search">

    <?php $form = ActiveForm::begin([
        'action' => ['index'],
        'method' => 'get',
    ]); ?>

<div class="row"><div class="col-sm-3">
    <?= $form->field($model, 'move_id')->textInput(['placeholder' => 'Exact move_id']) ?>
</div><div class="col-sm-3">
    <?= $form->field($model, 'adr_id')->widget(\kartik\select2\Select2::classname(), [
        'data' => \yii\helpers\ArrayHelper::map(\app\models\Address::find()->orderBy('firma')->asArray()->all(), 'adr_id', 'firma'),
        'options' => ['placeholder' => 'Firma filter'],
        'pluginOptions' => [
            'allowClear' => true
        ],
    ]); ?>
</div><div class="col-sm-3">
    <?php  echo $form->field($model, 'number')->textInput(['placeholder' => 'Document number'])  ?>
</div><div class="col-sm-3">
    <?php  echo $form->field($model, 'c_number')->textInput(['placeholder' => 'C Number (eshop #)'])  ?>
</div></div>
<div class="row"><div class="col-sm-4">

    <?= $form->field($model, 'd1',  ['options' => ['class' => 'drp-container form-group']] )->widget(DateRangePicker::classname(), [
        'initRangeExpr' => true,
        'hideInput' => true,
        'pluginOptions' => [
                'opens' => 'left',
                'ranges' => [
                "Today" => ["moment().startOf('day')", "moment()"],
                "Yesterday" => [
                    "moment().startOf('day').subtract(1,'days')",
                    "moment().endOf('day').subtract(1,'days')"
                ],
                "Last 7 days" => ["moment().startOf('day').subtract(6, 'days')","moment()"],
                "Last 30 days" => ["moment().startOf('day').subtract(29, 'days')","moment()"],
                "This month" => ["moment().startOf('month')","moment().endOf('month')"],
                "Last month" => ["moment().subtract(1, 'month').startOf('month')","moment().subtract(1, 'month').endOf('month')"],
                "Last 3 months" => ["moment().subtract(3, 'month').startOf('month')","moment().endOf('month')"],
                "This year" => ["moment().startOf('year')","moment().endOf('year')"],
            ],
          'hideInput'=>false,
            'useWithAddon'=>true,
            'format' => 'YYYY-MM-DD',
             'locale' => [
                            'format' => 'YYYY-MM-DD'
                        ],
        ]
    ]);?>
</div><div class="col-sm-4">

    <?= $form->field($model, 'd2',  ['options' => ['class' => 'drp-container form-group']] )->widget(DateRangePicker::classname(), [
        'initRangeExpr' => true,
        'hideInput' => true,
        'pluginOptions' => [
                'opens' => 'left',
                'ranges' => [
                "Today" => ["moment().startOf('day')", "moment()"],
                "Yesterday" => [
                    "moment().startOf('day').subtract(1,'days')",
                    "moment().endOf('day').subtract(1,'days')"
                ],
                "Last 7 days" => ["moment().startOf('day').subtract(6, 'days')","moment()"],
                "Last 30 days" => ["moment().startOf('day').subtract(29, 'days')","moment()"],
                "This month" => ["moment().startOf('month')","moment().endOf('month')"],
                "Last month" => ["moment().subtract(1, 'month').startOf('month')","moment().subtract(1, 'month').endOf('month')"],
                "Last 3 months" => ["moment().subtract(3, 'month').startOf('month')","moment().endOf('month')"],
                "This year" => ["moment().startOf('year')","moment().endOf('year')"],
            ],
          'hideInput'=>false,
            'useWithAddon'=>true,
            'format' => 'YYYY-MM-DD',
             'locale' => [
                            'format' => 'YYYY-MM-DD'
                        ],
        ]
    ]);?>

</div><div class="col-sm-4">

    <?= $form->field($model, 'd3',  ['options' => ['class' => 'drp-container form-group']] )->widget(DateRangePicker::classname(), [
        'initRangeExpr' => true,
        'hideInput' => true,
        'pluginOptions' => [
                'opens' => 'left',
                'ranges' => [
                "Today" => ["moment().startOf('day')", "moment()"],
                "Yesterday" => [
                    "moment().startOf('day').subtract(1,'days')",
                    "moment().endOf('day').subtract(1,'days')"
                ],
                "Last 7 days" => ["moment().startOf('day').subtract(6, 'days')","moment()"],
                "Last 30 days" => ["moment().startOf('day').subtract(29, 'days')","moment()"],
                "This month" => ["moment().startOf('month')","moment().endOf('month')"],
                "Last month" => ["moment().subtract(1, 'month').startOf('month')","moment().subtract(1, 'month').endOf('month')"],
                "Last 3 months" => ["moment().subtract(3, 'month').startOf('month')","moment().endOf('month')"],
                "This year" => ["moment().startOf('year')","moment().endOf('year')"],
            ],
          'hideInput'=>false,
            'useWithAddon'=>true,
            'format' => 'YYYY-MM-DD',
             'locale' => [
                            'format' => 'YYYY-MM-DD'
                        ],
        ]
    ]);?>

</div></div>

    <?php /* echo $form->field($model, 'doc_id')->textInput(['maxlength' => true, 'placeholder' => 'Doc']) */ ?>

<div class="row"><div class="col-sm-3">

    <?php
        // Usage with ActiveForm and model
        echo $form->field($model, 'emp_id')->widget(\kartik\select2\Select2::classname(), [
            'data' => \yii\helpers\ArrayHelper::map(\app\models\Movement::find()->select(['emp_id'])->groupBy(['emp_id'])->asArray()->all(), 'emp_id', 'emp_id'),
            'options' => ['placeholder' => 'Filter username ...'],
            'pluginOptions' => [
                'allowClear' => true
            ],
        ]);
?>

</div><div class="col-sm-3">
    <?php /* echo $form->field($model, 'emp_id')->textInput(['maxlength' => true, 'placeholder' => 'Username'])  */ ?>

    <?php  echo $form->field($model, 'total')->textInput(['maxlength' => true, 'placeholder' => 'Total'])  ?>

    <?php /* echo $form->field($model, 'payment')->textInput(['maxlength' => true, 'placeholder' => 'Payment']) */ ?>

    <?php /* echo $form->field($model, 'X', ['template' => '{input}'])->textInput(['style' => 'display:none']); */ ?>


    <?php /* echo $form->field($model, 'total0')->textInput(['maxlength' => true, 'placeholder' => 'Total0']) */ ?>

    <?php /* echo $form->field($model, 'total1')->textInput(['maxlength' => true, 'placeholder' => 'Total1']) */ ?>

    <?php /* echo $form->field($model, 'total2')->textInput(['maxlength' => true, 'placeholder' => 'Total2']) */ ?>

    <?php /* echo $form->field($model, 'rounding')->textInput(['maxlength' => true, 'placeholder' => 'Rounding']) */ ?>

    <?php /* echo $form->field($model, 'tax1')->textInput(['maxlength' => true, 'placeholder' => 'Tax1']) */ ?>

    <?php /* echo $form->field($model, 'tax2')->textInput(['maxlength' => true, 'placeholder' => 'Tax2']) */ ?>

    <?php /* echo $form->field($model, 'discount')->textInput(['maxlength' => true, 'placeholder' => 'Discount']) */ ?>
</div><div class="col-sm-3">
    <?php  echo $form->field($model, 'text1')->textInput(['maxlength' => true, 'placeholder' => 'Text1'])  ?>
</div><div class="col-sm-3">
    <?php  echo $form->field($model, 'text2')->textInput(['maxlength' => true, 'placeholder' => 'Text2'])  ?>
</div></div>
<div class="row"><div class="col-sm-4">
        <?= $form->field($model, 'm_type_id')->widget(\kartik\select2\Select2::classname(), [
        'data' => \yii\helpers\ArrayHelper::map(\app\models\MType::find()->select(['m_type_id',"m_type_id||' '||\"name\" as name",
            "moving"])->asArray()->all(), 'm_type_id', 'name','moving'),
        'options' => ['placeholder' => 'Document type m_type_id'],
        'pluginOptions' => [
            'allowClear' => true
        ],
    ]); ?>

    <?php /* echo $form->field($model, 'repli_id')->textInput(['placeholder' => 'Repli']) */ ?>

    <?php /* echo $form->field($model, 'adr_repli_id')->widget(\kartik\select2\Select2::classname(), [
        'data' => \yii\helpers\ArrayHelper::map(\app\models\Address::find()->orderBy('repli_id')->asArray()->all(), 'repli_id', 'adr_id'),
        'options' => ['placeholder' => 'Choose Address'],
        'pluginOptions' => [
            'allowClear' => true
        ],
    ]); */ ?>
</div><div class="col-sm-4">
        <?= $form->field($model, 'stock_id1')->widget(\kartik\select2\Select2::classname(), [
        'data' => \yii\helpers\ArrayHelper::map(\app\models\StockDetail::find()->asArray()->all(), 'stock_id', 'sdescr'),
        'options' => ['placeholder' => 'From Stock'],
        'pluginOptions' => [
            'allowClear' => true
        ],
    ]); ?>
</div><div class="col-sm-4">
        <?= $form->field($model, 'stock_id2')->widget(\kartik\select2\Select2::classname(), [
        'data' => \yii\helpers\ArrayHelper::map(\app\models\StockDetail::find()->asArray()->all(), 'stock_id', 'sdescr'),
        'options' => ['placeholder' => 'To Stock'],
        'pluginOptions' => [
            'allowClear' => true
        ],
    ]); ?>
</div>
</div>
    <?php /* echo $form->field($model, 'acc_reg_id')->textInput(['placeholder' => 'Acc Reg']) */ ?>

    <?php /* echo $form->field($model, 'acc_repli_id')->textInput(['placeholder' => 'Acc Repli']) */ ?>

    <div class="form-group">
        <?= Html::submitButton('Search', ['class' => 'btn btn-primary']) ?>
        <?= Html::resetButton('Reset', ['class' => 'btn btn-default']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
