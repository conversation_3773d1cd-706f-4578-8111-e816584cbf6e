<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use kartik\grid\GridView;
use kartik\export\ExportMenu;
use yii\widgets\ActiveForm;
use yii\helpers\Url;
use app\controllers\EstockTools;

$this->title = isset($sql) ? substr($sql, 0, 41) . "..." : "";
$this->params['breadcrumbs'][] = ['label' => 'Onstock', 'url' => ['onstock']];

echo "<p>";
echo Html::a('Recount all stocks', ['recount'], null, ['class' => 'btn btn-primary']);
echo "</p>";


if (isset($form)) {
    $f = ActiveForm::begin(); ?>

    <div class="row">
        <div class="col-md-3">
            <?php
            echo $f->field($form, 'model');
            echo '</div><div class="col-md-2">';
            echo $f->field($form, 'mat_id');
            echo '</div><div class="col-md-2">';
            echo $f->field($form, 'kod');

            echo '</div><div class="col-md-3">';
            echo  $f->field($form, 'stock_id')->widget(\kartik\select2\Select2::classname(), [
                'data' => \yii\helpers\ArrayHelper::map(\app\models\StockDetail::find()->select(['stock_id', "stock_id||' '||\"sdescr\" as sdescr"])->asArray()->all(), 'stock_id', 'sdescr'),
                'options' => ['placeholder' => 'Only stock'],
                'pluginOptions' => [
                    'allowClear' => true
                ],
            ]);

            echo '</div><div class="col-md-2">';
            echo  $f->field($form, 'repli')->widget(\kartik\select2\Select2::classname(), [
                'data' => [-1 => '-1 ALL', 0 => '0 SK and CZ', 2 => '2 HU'],
                'options' => ['placeholder' => 'Country'],
                'pluginOptions' => [
                    'allowClear' => false
                ],
            ]);

            //    echo $f->field($form, 'repli');

            ?>
        </div>
    </div>

    <div class="row">
        <div class="col-md-2">
            <?php

            echo $f->field($form, 'price1')->widget(\kartik\select2\Select2::classname(), [
                'data' => \yii\helpers\ArrayHelper::map(\app\models\PriceDetail::find()->asArray()->all(), 'price_id', 'desc'),
                'options' => ['placeholder' => 'Choose price'],
                'pluginOptions' => [
                    'allowClear' => true
                ],
            ]);

            // echo $f->field($form, 'price1'); 
            echo '</div><div class="col-md-2">';
            echo $f->field($form, 'price2')->widget(\kartik\select2\Select2::classname(), [
                'data' => \yii\helpers\ArrayHelper::map(\app\models\PriceDetail::find()->asArray()->all(), 'price_id', 'desc'),
                'options' => ['placeholder' => 'Choose price'],
                'pluginOptions' => [
                    'allowClear' => true
                ],
            ]);

            echo '</div><div class="col-md-2">';
            echo $f->field($form, 'price3')->widget(\kartik\select2\Select2::classname(), [
                'data' => \yii\helpers\ArrayHelper::map(\app\models\PriceDetail::find()->asArray()->all(), 'price_id', 'desc'),
                'options' => ['placeholder' => 'Choose price'],
                'pluginOptions' => [
                    'allowClear' => true
                ],
            ]);

            echo '</div><div class="col-md-2">';
            $d1array = \yii\helpers\ArrayHelper::map(\app\models\StockDetail::find()->asArray()->all(), 'stock_id', 'sdescr');
            $d1array = array_merge(['all' => 'ALL'], $d1array);
            echo $f->field($form, 'd1')->widget(\kartik\select2\Select2::classname(), [
                'data' => $d1array,
                'options' => ['placeholder' => 'Choose stock'],
                'pluginOptions' => [
                    'allowClear' => true
                ],
            ]);

            ?>
        </div>
    </div>

    <div class="form-group">
        <?= Html::submitButton('Show stock values', ['class' => 'btn btn-danger']) ?>
    </div>
<?php ActiveForm::end();
}

if (isset($pall) && count($pall->getModels())) {
    echo GridView::widget([
        'dataProvider' => $pall,
        'formatter' => ['class' => 'yii\i18n\Formatter', 'nullDisplay' => ''],
        'columns' =>  array_merge(
            [
                [
                    'class' => 'kartik\grid\ExpandRowColumn',
                    'width' => '50px',
                    'value' => function ($model, $key, $index, $column) {
                        return GridView::ROW_COLLAPSED;
                    },
                    // 'detailUrl' => Url::to([EstockTools::isUserShop()?'/product/viewdetailshop':'/product/stockcard']),
                    'detailUrl' => Url::to(['/product/stockcard']),
                    'headerOptions' => ['class' => 'kartik-sheet-style'],
                    'expandOneOnly' => false
                ]
            ],
            array_keys($pall->getModels()[0])
        ),
        'panel' => [
            'type' => GridView::TYPE_PRIMARY,
            'heading' => isset($title) ? $title : 'Results...',
        ],
        'toolbar' => [
            '{export}',
            //     ExportMenu::widget([
            //         'dataProvider' => $pall,
            //         'target' => ExportMenu::TARGET_BLANK,
            //         'fontAwesome' => true,
            //         'pjax' => false,
            //          'clearBuffers' => true,
            //         'dropdownOptions' => [
            //             'label' => 'Full',
            //             'class' => 'btn btn-default',
            //             'itemsBefore' => [
            //                 '<li class="dropdown-header">Export All Data</li>',
            //             ],
            //         ],
            //     ]) ,
        ],
    ]);
} //isset pall 
else {
    echo "<h4>Not found</h4>";
}
?>