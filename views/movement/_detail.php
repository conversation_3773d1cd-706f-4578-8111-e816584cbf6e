<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use kartik\grid\GridView;

/* @var $this yii\web\View */
/* @var $model app\models\Movement */
// echo "<pre>";
// print_r($model);
// exit;
?>
<div class="movement-view">

    <div class="row">
        <div class="col-sm-9">
            <h2><?= Html::encode($model->move_id) ?></h2>
        </div>
    </div>

    <div class="row">
<?php 
    $gridColumn = [
        'move_id',
        [
            'attribute' => 'adr.adr_id',
            'label' => 'Adr',
        ],
        'd1',
        'd2',
        'd3',
        'doc_id',
        'number',
        'emp_id',
        'total',
        'payment',
        ['attribute' => 'X', 'visible' => false],
        'c_number',
        'total0',
        'total1',
        'total2',
        'rounding',
        'tax1',
        'tax2',
        'discount',
        'text1',
        'text2',
        'm_type_id',
        [
            'attribute' => 'adr.firma',
            'label' => 'Firma',
        ],
        'stock_id1',
        'stock_id2',
        'parent_move_id'
    ];
    echo DetailView::widget([
        'model' => $model,
        'attributes' => $gridColumn
    ]); 
?>
    </div>
</div>