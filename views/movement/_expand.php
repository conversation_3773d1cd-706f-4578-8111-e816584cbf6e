<?php
use yii\helpers\Html;
use kartik\tabs\TabsX;
use yii\helpers\Url;
use yii\data\ActiveDataProvider;
use app\models\Mdetailprod;
use app\models\Address;

//$query = $model->mdetails;
// $adr = new ActiveDataProvider([
//     'query' => Address::find()->where(['adr_id' => $model->adr_id ]),
// ]);

// print_r($adr);
// exit;
if (Yii::$app->name == 'WstockUcto') { //EDIT ALL...

    $dataProvider = new ActiveDataProvider([
        'query' => Mdetailprod::find()->where([ 'move_id' => $model->move_id]),
    ]);
    // $dataProvider->pagination = false;
    $dataProvider->pagination->pageSize=100;
    $items = [
        [
            'label' => '<i class="glyphicon glyphicon-eur"></i> '. Html::encode('Detail'),
            'content' => $this->render( '//mdetailprod/_detailucto' , [
                'dataProvider' => $dataProvider,
            ]),
        ],
    ];


} else {
    $dataProvider = new ActiveDataProvider([
        'query' => Mdetailprod::find()->where([ 'move_id' => $model->move_id]),
    ]);

    $edataProvider = new ActiveDataProvider([
        'query' => Mdetailprod::find()->where([ 'move_id' => $model->move_id]),
    ]);
    $edataProvider->pagination = false;
    $dataProvider->pagination->pageSize=10;
    $items = [
        [
            'label' => '<i class="glyphicon glyphicon-eur"></i> '. Html::encode('Detail'),
            'content' => $this->render( '//mdetailprod/_detail' , [
                'dataProvider' => $dataProvider,
                'edataProvider' => $edataProvider,
            ]),
        ],
    ];

}


$items[] =
    // [
    //     'label' => '<i class="glyphicon glyphicon-user"></i> '. Html::encode('Address'),
    //     'content' => $this->render('//address/_detail', [
    //         'model' => $adr,
    //     ]),
    // ],

    [
        'label' => '<i class="glyphicon glyphicon-list-alt"></i> '. Html::encode('Movement'),
        'content' => $this->render('_detail', [
            'model' => $model,
        ]),
    ];


if( isset($model->adr)){
    $items[] =     [
        'label' => '<i class="glyphicon glyphicon-user"></i> '. Html::encode('Address'),
        'content' => $this->render('//address/_detail', [
            'model' => $model->adr,
        ]),
    ];
}
if( isset($model->firstMinfo)){
    $items[] =     [
        'label' => '<i class="glyphicon glyphicon-list"></i> '. Html::encode('Invoice info'),
        'content' => $this->render('//minfo/view', [
            'model' => $model->firstMinfo,
        ]),
    ];
}
if( isset($model->mlog)){
    $ds = new \app\models\MlogSearch();
    $ds->move_id = $model->move_id;
    $dataProvider = $ds->search([]);
    $dataProvider->sort = ['defaultOrder' => ['id' => SORT_DESC]];
    $items[] =     [
        'label' => '<i class="glyphicon glyphicon-book"></i> '. Html::encode('Log'),
        'content' => $this->render('//mlog/viewmove', [
            'dataProvider' => $dataProvider,
            'searchModel' => $ds,
            'move_id' => $model->move_id,
            'isOnTheWay' => $model->isOnTheWay(),
            'isReceived' => $model->isReceived(),
        ]),
    ];
}

echo TabsX::widget([
    'items' => $items,
    'position' => TabsX::POS_ABOVE,
    'encodeLabels' => false,
    'class' => 'tes',
    'pluginOptions' => [
        'bordered' => true,
        'sideways' => true,
        'enableCache' => false
    ],
]);
?>
