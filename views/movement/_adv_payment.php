<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use yii\widgets\DetailView;

$this->title = 'Movement'.' '. $model2->move_id. ' - ' . $model2->adr->firma . ' - update parent document';

?>

    <div class="movement-form">
        <div class="row">
        <?php $form = ActiveForm::begin(); ?>
        <?= $form->errorSummary($model2); ?>
        <?= $form->field($model2, 'move_id')->hiddenInput()->label(false) ?>


        <?= $form->field($model2, 'parent_move_id')->widget(\kartik\select2\Select2::classname(), [
  	    'data' => \yii\helpers\ArrayHelper::map(\app\models\Movement::find()->select(['move_id','concat(move_id,\' \',(select firma from address where adr_id=movement.adr_id),\' \',d1,\' \',total,\' \',text1,\' \',text2) as document'])
  	    	->where('m_type_id in (25,26,158) and move_id not in (select parent_move_id from movement where parent_move_id is not null group by parent_move_id)')->asArray()->all(), 'move_id', 'document' ),
      	'options' => ['placeholder' => 'Choose Payment document'],
      	        'pluginOptions' => [
            'allowClear' => true
        ],
    ]); ?>

        </div>
        <div class="form-group">
            <?= Html::submitButton('SET Payment document' , ['class' =>  'btn btn-primary']) ?>
        </div>

        <?php ActiveForm::end(); ?>
    </div>



    <div class="row">
<?php 
    $gridColumn = [
        'move_id',
        [
            'attribute' => 'adr.adr_id',
            'label' => 'Adr',
        ],
        'd1',
        'd2',
        'd3',
        'doc_id',
        'number',
        'emp_id',
        'total',
        'payment',
        ['attribute' => 'X', 'visible' => false],
        'c_number',
        'total0',
        'total1',
        'total2',
        'rounding',
        'tax1',
        'tax2',
        'discount',
        'text1',
        'text2',
        'm_type_id',
        [
            'attribute' => 'adr.firma',
            'label' => 'Firma',
        ],
        'stock_id1',
        'stock_id2',
        'parent_move_id'
    ];
    echo DetailView::widget([
        'model' => $model2,
        'attributes' => $gridColumn
    ]); 
?>
    </div>

