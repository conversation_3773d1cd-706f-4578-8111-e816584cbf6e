<?php

/* @var $this yii\web\View */
/* @var $searchModel app\models\MovementSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

use yii\helpers\Html;
use kartik\export\ExportMenu;
use kartik\grid\GridView;
use yii\helpers\Url;
use app\controllers\EstockTools;
use kartik\daterange\DateRangePicker;


$search = "$('.search-button').click(function(){
	$('.search-form').toggle(1000);
	return false;
});";
$this->registerJs($search);
?>
<h1> <?= Yii::$app->user->identity->username ?> Movements
<?php echo Html::a('This month only ', Url::current(['datefilter'=>'thismonth' ]), ['class' => 'btn btn-success', 'id' =>'datefilter1']);
    echo "&nbsp;&nbsp;&nbsp;";
    echo Html::a('Last month', Url::current(['datefilter'=>'lastmonth' ]), ['class' => 'btn btn-success', 'id' =>'datefilter2']);
    echo "&nbsp;&nbsp;&nbsp;";
    echo Html::a('This year', Url::current(['datefilter'=>'thisyear' ]), ['class' => 'btn btn-success', 'id' =>'datefilter3']);
    echo "&nbsp;&nbsp;&nbsp;";
    echo Html::a('Last year', Url::current(['datefilter'=>'lastyear' ]), ['class' => 'btn btn-success', 'id' =>'datefilter4']);
?>
</h1>
<h4 id="filtertitle"><?= $filtertitle ?></h4>
<div class="movement-index">

    <?php 
    $gridColumn = [
        [
            'class' => 'kartik\grid\ExpandRowColumn',
            'width' => '50px',
            'value' => function ($model, $key, $index, $column) {
                return GridView::ROW_COLLAPSED;
            },
            'detailUrl' => Url::to(['mdetailprod/viewb2bdetail']),
            'detail' => function ($model, $key, $index, $column) {
                return Yii::$app->controller->renderPartial('//mdetailprod/_detailb2b', ['model' => $model]);
            },
            'headerOptions' => ['class' => 'kartik-sheet-style'],
            'expandOneOnly' => false
        ],
        [

            'label' => 'M TYPE',
            'attribute' => 'm_type_id',
            'content'=>function($data){
//                $mtypes = Yii::$app->params['shopmtypes'];
            if( \app\controllers\EstockTools::isUserShop() ){
                $mtypes = \app\controllers\EstockTools::getShopParams()['shopmtypes'];                
            } else {
                $mtypes = [];
            }
 
                return ''.(isset($mtypes["$data->m_type_id"]) ? $mtypes["$data->m_type_id"] : $data->mType->name );
            }
            ],
        'number',
        'd1',
        [
        'class' => '\kartik\grid\DataColumn',
        'attribute' => 'sumpcs',
        'pageSummary' => true
        ],
        [
        'class' => '\kartik\grid\DataColumn',
        'attribute' => 'total',
        'pageSummary' => true
        ],
        'text1',
        'text2',

    ]; 
    ?>
    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => $gridColumn,
        'pjax' => true,
        'showPageSummary' => true,
        'pjaxSettings' => ['options' => ['id' => 'kv-pjax-container-movement']],
        'panel' => [
            'type' => GridView::TYPE_PRIMARY,
            'heading' => '<span class="glyphicon glyphicon-book"></span>  ' . Html::encode('MOVEMENTS'),
        ],
        // // your toolbar can include the additional full export menu
        // 'toolbar' => [
        //     '{export}',
        //     ExportMenu::widget([
        //         'dataProvider' => $dataProvider,
        //         'columns' => $gridColumn,
        //         'target' => ExportMenu::TARGET_BLANK,
        //         'fontAwesome' => true,
        //         'dropdownOptions' => [
        //             'label' => 'Full',
        //             'class' => 'btn btn-default',
        //             'itemsBefore' => [
        //                 '<li class="dropdown-header">Export All Data</li>',
        //             ],
        //         ],
        //     ]) ,
        // ],
    ]); 

//VYNULUJ HORE TITULOK A BREAD
$this->title = '';

$this->params['breadcrumbs'] = [];

    ?>

</div>
