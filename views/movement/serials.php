<?php

/* @var $this yii\web\View */
/* @var $searchModel app\models\MovementSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

use yii\helpers\Html;
use kartik\export\ExportMenu;
use kartik\grid\GridView;
use yii\helpers\Url;


$search = "$('.search-button').click(function(){
	$('.search-form').toggle(1000);
	return false;
});";
$this->registerJs($search);
?>
<div class="movement-index">

    <?php 
    $gridColumn = [
        [
            'class' => 'kartik\grid\ExpandRowColumn',
            'width' => '50px',
            'value' => function ($model, $key, $index, $column) {
                return GridView::ROW_COLLAPSED;
            },
            'detailUrl' => Url::to(['mdetailprod/viewdetail']),
            // 'detail' => function ($model, $key, $index, $column) {
            //     return Yii::$app->controller->renderPartial('_expand', ['model' => $model]);
            // },
            'headerOptions' => ['class' => 'kartik-sheet-style'],
            'expandOneOnly' => false
        ],
        [

            'label' => 'M TYPE',
            'attribute' => 'm_type_id',
            'content'=>function($data){
		$mtypes = \app\controllers\EstockTools::getShopParams()['shopmtypes'];
                return ''.(isset($mtypes["$data->m_type_id"]) ? $mtypes["$data->m_type_id"] : $data->m_type_id );
            }
            ],
        'move_id',
        'd1',
        'total',
        'text1',
        'text2',
        'emp_id',
        'stock_id1',
        'stock_id2',
        [
            'class' => 'yii\grid\ActionColumn',
        ],
    ]; 
    ?>
    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => $gridColumn,
        'pjax' => true,
        'pjaxSettings' => ['options' => ['id' => 'kv-pjax-container-movement']],
        'panel' => [
            'type' => GridView::TYPE_PRIMARY,
            'heading' => '<span class="glyphicon glyphicon-book"></span>  ' . Html::encode('MOVEMENTS'),
        ],
        // // your toolbar can include the additional full export menu
        // 'toolbar' => [
        //     '{export}',
        //     ExportMenu::widget([
        //         'dataProvider' => $dataProvider,
        //         'columns' => $gridColumn,
        //         'target' => ExportMenu::TARGET_BLANK,
        //         'fontAwesome' => true,
        //         'dropdownOptions' => [
        //             'label' => 'Full',
        //             'class' => 'btn btn-default',
        //             'itemsBefore' => [
        //                 '<li class="dropdown-header">Export All Data</li>',
        //             ],
        //         ],
        //     ]) ,
        // ],
    ]); 

//VYNULUJ HORE TITULOK A BREAD
$this->title = '';

$this->params['breadcrumbs'] = [];

    ?>

</div>
