<?php

/* @var $this yii\web\View */
/* @var $searchModel app\models\MovementSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

use yii\helpers\Html;
use kartik\export\ExportMenu;
use kartik\grid\GridView;
use yii\helpers\Url;
use app\controllers\EstockTools;
use kartik\daterange\DateRangePicker;


$search = "$('.search-button').click(function(){
	$('.search-form').toggle(1000);
	return false;
});";
$this->registerJs($search);
?>
<h1> <?= Yii::$app->user->identity->username ?> Movements
<?php echo Html::a('This month only ', Url::current(['datefilter'=>'thismonth' ]), ['class' => 'btn btn-success', 'id' =>'datefilter1']);
    echo "&nbsp;&nbsp;&nbsp;";
    echo Html::a('Last month', Url::current(['datefilter'=>'lastmonth' ]), ['class' => 'btn btn-success', 'id' =>'datefilter2']);
    echo "&nbsp;&nbsp;&nbsp;";
    echo Html::a('This year', Url::current(['datefilter'=>'thisyear' ]), ['class' => 'btn btn-success', 'id' =>'datefilter3']);
    echo "&nbsp;&nbsp;&nbsp;";
    echo Html::a('Last year', Url::current(['datefilter'=>'lastyear' ]), ['class' => 'btn btn-success', 'id' =>'datefilter4']);
    echo "&nbsp;&nbsp;&nbsp;";
    echo Html::a('To receive', Url::current(['datefilter'=>'toconfirm' ]), ['class' => 'btn btn-warning', 'id' =>'datefilter5']);
?>
</h1>
<h4 id="filtertitle"><?= $filtertitle ?></h4>
<div class="movement-index">

    <?php 
    $gridColumn = [
        EstockTools::isUserShop()?
        [
            'class' => 'kartik\grid\ExpandRowColumn',
            'width' => '50px',
            'value' => function ($model, $key, $index, $column) {
                return GridView::ROW_COLLAPSED;
            },
            'detailUrl' => Url::to(['mdetailprod/viewdetail']),
            'detail' => function ($model, $key, $index, $column) {
                return Yii::$app->controller->renderPartial('_expand', ['model' => $model]);
            },
            'headerOptions' => ['class' => 'kartik-sheet-style'],
            'expandOneOnly' => false
        ]:
        [
            'class' => 'kartik\grid\ExpandRowColumn',
            'value' => function ($model, $key, $index, $column) {
                return GridView::ROW_COLLAPSED;
            },
            'expandOneOnly' => false,
            'enableCache' => false,
            'detailUrl' => Url::to(['movementajaxdetail'])
        ],
        [
            'attribute'=>'move_id',  
                             'value' => function ($model) {
                  
                                $isOntheway = $model->isOntheway() && !$model->isReceived() ? Html::a(
                                    ' <i class="fa fa-truck" aria-hidden="true"  style="color:#990033"></i> '.$model->isOnTheWayWhere()
                                    , ['//mlog/createreceived', 'move_id' => $model->move_id ], ['class' => 'btn btn-warning', 'data' => [
                                        'confirm' => 'Are you sure goods are received to final stock?',
                                        'method' => 'post',
                                    ]])
                                    : '';
                                $isReceived = $model->isReceived() ? ' <i class="fa fa-check" aria-hidden="true" style="color:#00cc11"></i>' : '';
                                $nrOfMessages = $model->getNrOfMessages() > 0 ? ' <span class="badge btn-success">' . $model->getNrOfMessages() . '</span>' : '';
                                $tracking = $model->hasTracking() ? Html::a('<i class="fa fa-map-marker" aria-hidden="true"></i>', 
                                $model->hasTracking(), ['target' => '_blank']) : '';
                                                     return Html::a(Html::encode( $model->move_id), 
                                     Url::to(['/mdetailprod/documentdetaillist', 'id' => $model->move_id])) 
                                     . "&nbsp;" . $isOntheway . "&nbsp;" . $isReceived . "&nbsp;" . $nrOfMessages . "&nbsp;" . $tracking;
                                    },
            'format' => 'raw',     
           ],
        [

            'label' => 'M TYPE',
            'attribute' => 'm_type_id',
            'content'=>function($data){
//                $mtypes = Yii::$app->params['shopmtypes'];
            if( \app\controllers\EstockTools::isUserShop() ){
                $mtypes = \app\controllers\EstockTools::getShopParams()['shopmtypes'];                
            } else {
                $mtypes = [];
            }
                return ''.(isset($mtypes["$data->m_type_id"]) ? $mtypes["$data->m_type_id"][1] : $data->mType->name );
            },
            'filterType' => GridView::FILTER_SELECT2,
            'filter' => \yii\helpers\ArrayHelper::map(\app\models\MType::getMyMTypes($adresa)->select(['m_type_id',"m_type_id||' '||\"name\" as name"
        ])->asArray()->all(), 'm_type_id', 'name'),
            'filterWidgetOptions' => [
                'pluginOptions' => ['allowClear' => true],
            ],
            'filterInputOptions' => ['placeholder' => 'M type id', 'id' => 'grid-movement-search-m_type_id']
        ],
        [
                'attribute' => 'adr_id',
                'label' => 'Firma',
                'value' => function($model){                   
                    return $model->adr->firma;                   
                },
                // 'filterType' => GridView::FILTER_SELECT2,
                // 'filter' => \yii\helpers\ArrayHelper::map(\app\models\Address::find()->asArray()->all(), 'adr_id', 'firma'),
                // 'filterWidgetOptions' => [
                //     'pluginOptions' => ['allowClear' => true],
                // ],
                // 'filterInputOptions' => ['placeholder' => 'Address', 'id' => 'grid-movement-search-adr_id']
            ],	    
        'number',
        'd1',
        [
        'class' => '\kartik\grid\DataColumn',
        'attribute' => 'sumpcs',
        'pageSummary' => true
        ],
        [
        'class' => '\kartik\grid\DataColumn',
        'attribute' => 'total',
        'pageSummary' => true
        ],
        'text1',
        'text2',
        'stock_id1',
        'stock_id2',
        [
            'class' => 'yii\grid\ActionColumn',
            'header' => EstockTools::isUserShop()?'Edit':'Edit/ Storno',
            'template' => EstockTools::isUserShop()?'{update}':'{parent} {update} {delete}',
            'buttons'  => [

                'parent' => function($url, $model) {
                    return Html::a(EstockTools::isMtypeWithAdvancedPayment($model->m_type_id) ? 'Prepaid '.$model->parent_move_id : '', ['updateparent', 'id' => $model->move_id], []);

            }

            ]

        ],

    ]; 
    ?>
    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => $gridColumn,
        'pjax' => true,
        'showPageSummary' => true,
        'pjaxSettings' => ['options' => ['id' => 'kv-pjax-container-movement']],
        'panel' => [
            'type' => GridView::TYPE_PRIMARY,
            'heading' => '<span class="glyphicon glyphicon-book"></span>  ' . Html::encode('MOVEMENTS'),
        ],
        'rowOptions'=>function($model){
            if($model->X == 9){
                return ['class' => 'danger'];
            }
        },
        // // your toolbar can include the additional full export menu
        // 'toolbar' => [
        //     '{export}',
        //     ExportMenu::widget([
        //         'dataProvider' => $dataProvider,
        //         'columns' => $gridColumn,
        //         'target' => ExportMenu::TARGET_BLANK,
        //         'fontAwesome' => true,
        //         'dropdownOptions' => [
        //             'label' => 'Full',
        //             'class' => 'btn btn-default',
        //             'itemsBefore' => [
        //                 '<li class="dropdown-header">Export All Data</li>',
        //             ],
        //         ],
        //     ]) ,
        // ],
    ]); 

//VYNULUJ HORE TITULOK A BREAD
$this->title = '';

$this->params['breadcrumbs'] = [];

    ?>

</div>
