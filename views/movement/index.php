<?php

/* @var $this yii\web\View */
/* @var $searchModel app\models\MovementSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

use yii\helpers\Html;
use kartik\export\ExportMenu;
use kartik\grid\GridView;
use kartik\daterange\DateRangePicker;
use app\controllers\EstockTools;
use yii\helpers\Url;

$this->title = 'Movement';
$this->params['breadcrumbs'][] = $this->title;
$search = "$('.search-button').click(function(){
	$('.search-form').toggle(500);
	return false;
});";
$this->registerJs($search);
?>
<div class="movement-index">

    <h1><?= Html::encode($this->title) ?>        <?= Html::a('Advance Search', '#', ['class' => 'btn btn-info search-button']) ?>
         <?= Html::a('Show Only MY movements', 'mymanagermoves', ['class' => 'btn btn-primary']) ?>
</h1>
    <?php // echo $this->render('_search', ['model' => $searchModel]); ?>

<!--     <p>
         <?= Html::a('Create Movement', ['create'], ['class' => 'btn btn-success']) ?> 
    </p>
 -->    <div class="search-form" style="display:none">
        <?=  $this->render('_search', ['model' => $searchModel]); ?>
    </div>
    <?php 
    $gridColumn = [
        [
            'class' => 'kartik\grid\ExpandRowColumn',
            'value' => function ($model, $key, $index, $column) {
                return GridView::ROW_COLLAPSED;
            },
            'expandOneOnly' => false,
            'enableCache' => false,
            'detailUrl' => Url::to(['movementajaxdetail'])
        ],
        [
            'attribute'=>'move_id',  
                             'value' => function ($model) {
                  
                                $isOntheway = $model->isOntheway() && !$model->isReceived() ? Html::a(
                                    ' <i class="fa fa-truck" aria-hidden="true"  style="color:#990033"></i> '.$model->isOnTheWayWhere()
                                    , ['//mlog/createreceived', 'move_id' => $model->move_id ], ['class' => 'btn btn-warning', 'data' => [
                                        'confirm' => 'Are you sure goods are received to final stock?',
                                        'method' => 'post',
                                    ]])
                                    : '';
                                $isReceived = $model->isReceived() ? ' <i class="fa fa-check" aria-hidden="true" style="color:#00cc11"></i>' : '';
                                $nrOfMessages = $model->getNrOfMessages() > 0 ? ' <span class="badge btn-success">' . $model->getNrOfMessages() . '</span>' : '';
                                $tracking = $model->hasTracking() ? Html::a('<i class="fa fa-map-marker" aria-hidden="true"></i>', 
                                $model->hasTracking(), ['target' => '_blank']) : '';
                                                     return Html::a(Html::encode( $model->move_id), 
                                     Url::to(['/mdetailprod/documentdetaillist', 'id' => $model->move_id])) 
                                     . "&nbsp;" . $isOntheway . "&nbsp;" . $isReceived . "&nbsp;" . $nrOfMessages . "&nbsp;" . $tracking;
                                    },
            'format' => 'raw',     
           ],
        // [
        //         'attribute' => 'adr_id',
        //         'label' => 'Adr',
        //         'value' => function($model){
        //             if ($model->adr)
        //             {return $model->adr->adr_id;}
        //             else
        //             {return NULL;}
        //         },
        //         'filterType' => GridView::FILTER_SELECT2,
        //         'filter' => \yii\helpers\ArrayHelper::map(\app\models\Address::find()->asArray()->all(), 'repli_id', 'adr_id'),
        //         'filterWidgetOptions' => [
        //             'pluginOptions' => ['allowClear' => true],
        //         ],
        //         'filterInputOptions' => ['placeholder' => 'Address', 'id' => 'grid-movement-search-adr_id']
        //     ],
        [
                'attribute' => 'adr_id',
                'label' => 'Firma',
                'value' => function($model){                   
                    return $model->adr->firma;                   
                },
                'filterType' => GridView::FILTER_SELECT2,
                'filter' => \yii\helpers\ArrayHelper::map(\app\models\Address::find()->asArray()->all(), 'adr_id', 'firma'),
                'filterWidgetOptions' => [
                    'pluginOptions' => ['allowClear' => true],
                ],
                'filterInputOptions' => ['placeholder' => 'Address', 'id' => 'grid-movement-search-adr_repli_id']
            ],
        [
                'attribute' => 'd1',
                'label' => 'D1',
                'value' => 'd1',
                // 'filterType' => \kartik\grid\GridView::FILTER_DATE_RANGE,
                // 'filterWidgetOptions' => [
                //     'model'=>$searchModel,
                //     'attribute'=>'d1',
                //     //'presetDropdown' => true,
                //     'pjaxContainerId'=>'kv-pjax-container-movement',
                //     'convertFormat' => false,
                //     'pluginOptions' => [
                //       'separator' => ' - ',
                //       'format' => 'YYYY-MM-DD',
                //       'locale' => [
                //             'format' => 'YYYY-MM-DD'
                //         ],
                //     ],

                //     'pluginEvents' => [
                //         "apply.daterangepicker" => "function() { apply_filter('d1') }",
                //     ]    


                // ],
                                            // here we render the widget
            'filter' => DateRangePicker::widget([
                'model' => $searchModel,
                'attribute' => 'd1',
                'useWithAddon' => true,
                'pjaxContainerId'=>'kv-pjax-container-movement',
                'pluginOptions' => [
                    'format' => 'YYYY-MM-DD',
                ]
            ]),




//                'filterInputOptions' => ['placeholder' => 'Address', 'id' => 'grid-movement-search-adr_repli_id']
            ],
      //  'd1',
        // 'doc_id',
        'number',

                [
                'attribute' => 'emp_id',
                'label' => 'Username',
                'value' => function($model){                   
                    return $model->emp_id;                   
                },
                'filterType' => GridView::FILTER_SELECT2,
                'filter' => \yii\helpers\ArrayHelper::map(\app\models\Movement::find()->select(['emp_id'])->groupBy(['emp_id'])->asArray()->all(), 'emp_id', 'emp_id'),
                'filterWidgetOptions' => [
                    'pluginOptions' => ['allowClear' => true],
                ],
                'filterInputOptions' => ['placeholder' => 'Username', 'id' => 'grid-movement-search-emp_id']
            ],

        [
        'class' => '\kartik\grid\DataColumn',
        'attribute' => 'sumpcs',
        'pageSummary' => true
        ],
        [
        'class' => '\kartik\grid\DataColumn',
        'attribute' => 'total',
        'pageSummary' => true
        ],
        // 'payment',
        // ['attribute' => 'X', 'visible' => false],
        // 'c_number',
        // 'total0',
        // 'total1',
        // 'total2',
        // 'rounding',
        // 'tax1',
        'tax2',
        // 'discount',
        'text1',
        'text2',

        [
                'attribute' => 'm_type_id',
                'label' => 'M Type ID',
                'value' => function($model){                   
                    return $model->m_type_id;                   
                },
                'filterType' => GridView::FILTER_SELECT2,
                'filter' => \yii\helpers\ArrayHelper::map(\app\models\MType::find()->select(['m_type_id',"m_type_id||' '||\"name\" as name",
            "moving"])->asArray()->all(), 'm_type_id', 'name','moving'),
                'filterWidgetOptions' => [
                    'pluginOptions' => ['allowClear' => true],
                ],
                'filterInputOptions' => ['placeholder' => 'M type id', 'id' => 'grid-movement-search-m_type_id']
            ],

        'doc_id',
        // 'd3',
        // 'repli_id',
            [
                'attribute' => 'stock_id1',
                'label' => 'From',
                'value' => function($model){                   
                    return $model->stock_id1;                   
                },
                'filterType' => GridView::FILTER_SELECT2,
                'filter' => \yii\helpers\ArrayHelper::map(\app\models\StockDetail::find()->select(['stock_id',"sdescr"])->asArray()->all(), 'stock_id', 'sdescr'),
                'filterWidgetOptions' => [
                    'pluginOptions' => ['allowClear' => true],
                ],
                'filterInputOptions' => ['placeholder' => 'From stock', 'id' => 'grid-movement-search-stock_id1']
            ],

            [
                'attribute' => 'stock_id2',
                'label' => 'To',
                'value' => function($model){                   
                    return $model->stock_id2;                   
                },
                'filterType' => GridView::FILTER_SELECT2,
                'filter' => \yii\helpers\ArrayHelper::map(\app\models\StockDetail::find()->select(['stock_id',"sdescr"])->asArray()->all(), 'stock_id', 'sdescr'),
                'filterWidgetOptions' => [
                    'pluginOptions' => ['allowClear' => true],
                ],
                'filterInputOptions' => ['placeholder' => 'To stock', 'id' => 'grid-movement-search-stock_id2']
            ],

        'firstMDetail.currency',
        // 'parent_move_id',
        // 'acc_repli_id',
        [
            'class' => 'yii\grid\ActionColumn',
            'header' => '',
            'template' =>  '{parent} {update} {delete}',
            'buttons'  => [

                'parent' => function($url, $model) {

                return Html::a(EstockTools::isMtypeWithAdvancedPayment($model->m_type_id) ? 'Prepaid '.$model->parent_move_id : '', ['updateparent', 'id' => $model->move_id], []);

            }

            ]
        ],
    ]; 
    ?>
    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => $gridColumn,
        'showPageSummary' => true,
        'pjax' => true,
        'rowOptions'=>function($model){
            if($model->X == 9){
                return ['class' => 'danger'];
            } elseif( !empty($model->isOntheway() && empty($model->isReceived()))) {
                return ['class' => 'warning'];
            }
        },
        'pjaxSettings' => ['options' => ['id' => 'kv-pjax-container-movement']],
        'panel' => [
            'type' => GridView::TYPE_PRIMARY,
            'heading' => '<span class="glyphicon glyphicon-book"></span>  ' . Html::encode($this->title),
        ],
        // your toolbar can include the additional full export menu
        'toolbar' => [
            ExportMenu::widget([
                'dataProvider' => $dataProvider,
                'columns' => $gridColumn,
                'target' => ExportMenu::TARGET_BLANK,
                'pjax' => false,
                'clearBuffers' => true,
                'showConfirmAlert' => false,
                'fontAwesome' => true,
                'dropdownOptions' => [
                    'label' => 'Full',
                    'class' => 'btn btn-default',
                    'itemsBefore' => [
                        '<li class="dropdown-header">Export All Data</li>',
                    ],
                ],
            ]) ,
        ],
    ]); ?>

</div>

<script type="text/javascript">

function apply_filter() {
    $('.grid-view').yiiGridView('applyFilter');
}

</script>

