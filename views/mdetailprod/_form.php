<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model app\models\Mdetailprod */
/* @var $form yii\widgets\ActiveForm */

?>

<div class="mdetailprod-form">

    <?php $form = ActiveForm::begin(); ?>

    <?= $form->errorSummary($model); ?>

    <?= $form->field($model, 'move_id')->textInput(['placeholder' => 'Move']) ?>

    <?= $form->field($model, 'repli_id')->textInput(['placeholder' => 'Repli']) ?>

    <?= $form->field($model, 'mat_id')->textInput(['placeholder' => 'Mat']) ?>

    <?= $form->field($model, 'kod')->textInput(['maxlength' => true, 'placeholder' => 'Kod']) ?>

    <?= $form->field($model, 'model')->textInput(['maxlength' => true, 'placeholder' => 'Model']) ?>

    <?= $form->field($model, 'price')->textInput(['maxlength' => true, 'placeholder' => 'Price']) ?>

    <?= $form->field($model, 'pcs')->textInput(['placeholder' => 'Pcs']) ?>

    <?= $form->field($model, 'discount')->textInput(['maxlength' => true, 'placeholder' => 'Discount']) ?>

    <?= $form->field($model, 'tax')->textInput(['maxlength' => true, 'placeholder' => 'Tax']) ?>

    <?= $form->field($model, 'currency')->textInput(['maxlength' => true, 'placeholder' => 'Currency']) ?>

    <?= $form->field($model, 'detail_info')->textInput(['maxlength' => true, 'placeholder' => 'Detail Info']) ?>

    <div class="form-group">
        <?= Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
