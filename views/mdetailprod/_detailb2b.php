<?php

/* @var $this yii\web\View */
/* @var $searchModel app\models\MDetailSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

use yii\helpers\Html;
use kartik\export\ExportMenu;
use kartik\grid\GridView;
use yii\data\ActiveDataProvider;
use app\models\Mdetailprod;
use app\models\Thserials;
use app\controllers\EstockTools;

$this->title = '';
$this->params['breadcrumbs'] = null;

$dataProvider = new ActiveDataProvider([
    'query' => Mdetailprod::find()->where([ 'move_id' => $model->move_id]),
]);

?>
<div class="mdetail-index">

    <!-- <h1><?= Html::encode($this->title) ?></h1> -->
    <?php // echo $this->render('_search', ['model' => $searchModel]); ?>

    <?php 
    $gridColumn = [
        'mat_id',
        'kod',
        'model',
        'price',
        [
        'class' => '\kartik\grid\DataColumn',
        'attribute' => 'pcs',
        'pageSummary' => true
        ],
        'discount',
        'tax',
        [
        'class' => '\kartik\grid\FormulaColumn',
        'label' => 'Total',
        'value' => function ($model, $key, $index, $widget) {
            $p = compact('model', 'key', 'index');

            return $widget->col(4, $p)*($widget->col(3, $p)*(1-$widget->col(5, $p)/100));
        },
                'pageSummary' => true
        ],
        [
        'class' => '\kartik\grid\FormulaColumn',
        'label' => 'Total+VAT',
        'value' => function ($model, $key, $index, $widget) {
            $p = compact('model', 'key', 'index');

            return (1+$widget->col(6, $p)/100)*$widget->col(4, $p)*($widget->col(3, $p)*(1-$widget->col(5, $p)/100));
        },
                'pageSummary' => true
        ],
        'currency',

        'detail_info',
    ]; 
    ?>
    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'columns' => $gridColumn,
        'showPageSummary' => true,
        'pjax' => true,
        'pjaxSettings' => ['options' => ['id' => 'kv-pjax-container-mdetail'.$dataProvider->models[0]->move_id ]],
        'panel' => [
            'type' => GridView::TYPE_PRIMARY,
            'heading' => '<span class="glyphicon glyphicon-book"></span>  ' . Html::encode("Detail"),
        ],

    ]); ?>

</div>