<?php
use yii\helpers\Html;
use kartik\tabs\TabsX;
use yii\helpers\Url;
use yii\data\ActiveDataProvider;
use app\models\Mdetailprod;
use app\models\Movement;
use app\models\Address;



//$query = $model->mdetails;

$dataProvider = new ActiveDataProvider([
    'query' => Mdetailprod::find()->where([ 'move_id' => $model->move_id]),
]);

$items = [
    [
        'label' => '<i class="glyphicon glyphicon-eur"></i> '. Html::encode('Detail'),
        'content' => $this->render('//mdetailprod/_detail', [
            'dataProvider' => $dataProvider, 'move_id' => $model->move_id
        ]),
    ],
    // [
    //     'label' => '<i class="glyphicon glyphicon-eur"></i> '. Html::encode('Actions'),
    //     'content' => $this->render('//mdetailprod/_actions', [
    //         'dataProvider' => $dataProvider,
    //     ]),
    // ],

    [
        'label' => '<i class="glyphicon glyphicon-user"></i> '. Html::encode('Address'),
        'content' => $this->render('//address/_detail', [
            'model' => Address::findOne(['adr_id'=>$model->adr_id]),
        ]),
    ],

    [
        'label' => '<i class="glyphicon glyphicon-book"></i> '. Html::encode('Movement'),
        'content' => $this->render('//movement/_detail', [
            'model' => $model,
        ]),
    ],

        ];
echo TabsX::widget([
    'items' => $items,
    'position' => TabsX::POS_ABOVE,
    'encodeLabels' => false,
    'class' => 'tes',
    'pluginOptions' => [
        'bordered' => true,
        'sideways' => true,
        'enableCache' => false
    ],
]);
?>
