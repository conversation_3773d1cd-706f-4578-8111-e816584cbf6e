<?php
use yii\helpers\Html;
use kartik\tabs\TabsX;
use yii\helpers\Url;
use yii\data\ActiveDataProvider;
use app\models\Mdetailprod;
use app\models\MdetailprodSearch;
use app\models\Movement;
use app\models\Address;


$dataProvider = new ActiveDataProvider([
    'query' => Mdetailprod::find()->where([ 'move_id' => $model->move_id]),
]);
$dataProvider->pagination = false;
$searchModel = new MdetailprodSearch();
if( isset($_GET['MdetailprodSearch']) ){
    $searchModel->attributes = $_GET['MdetailprodSearch'];
    $searchModel->move_id = $model->move_id;
    $dataProvider = $searchModel->search([]);
}

$items = [
    [
        'label' => '<i class="glyphicon glyphicon-eur"></i> '. Html::encode('Detail'),
        'content' => $this->render(Yii::$app->name == 'WstockUcto' ? '//mdetailprod/_detailucto' : '//mdetailprod/_detail', [
            'dataProvider' => $dataProvider, 'move_id' => $model->move_id, 'searchModel' => $searchModel,
        ]),
    ],
    [
        'label' => '<i class="glyphicon glyphicon-user"></i> '. Html::encode('Address'),
        'content' => $this->render('//address/_detail', [
            'model' => Address::findOne(['adr_id'=>$model->adr_id]),
        ]),
    ],

    [
        'label' => '<i class="glyphicon glyphicon-list-alt"></i> '. Html::encode('Movement'),
        'content' => $this->render('//movement/_detail', [
            'model' => $model,
        ]),
    ],

        ];

        if( isset($model->firstMinfo)){
            $items[] =     [
                'label' => '<i class="glyphicon glyphicon-list"></i> '. Html::encode('Invoice info'),
                'content' => $this->render('//minfo/view', [
                    'model' => $model->firstMinfo,
                ]),
            ];
        }        

        if( isset($model->mlog)){
            $ds = new \app\models\MlogSearch();
            $ds->move_id = $model->move_id;
            $dataProvider = $ds->search([]);
            $dataProvider->sort = ['defaultOrder' => ['id' => SORT_DESC]];
            $items[] =     [
                'label' => '<i class="glyphicon glyphicon-book"></i> '. Html::encode('Log'),
                'content' => $this->render('//mlog/viewmove', [
                    'dataProvider' => $dataProvider,
                    'searchModel' => $ds,
                    'move_id' => $model->move_id,
                    'isOnTheWay' => $model->isOnTheWay(),
                    'isReceived' => $model->isReceived(),
                ]),
            ];
        }
        
?>
<h4><?= 'move_id='.$model->move_id.', d1='.$model->d1.', total='.$model->total.'  '.$model->text1.' '.$model->text2 ?></h4>
<?php

echo TabsX::widget([
    'items' => $items,
    'position' => TabsX::POS_ABOVE,
    'encodeLabels' => false,
    'class' => 'tes',
    'pluginOptions' => [
        'bordered' => true,
        'sideways' => true,
        'enableCache' => false
    ],
]);
?>
