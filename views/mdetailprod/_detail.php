<?php

/* @var $this yii\web\View */
/* @var $searchModel app\models\MDetailSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

use yii\helpers\Html;
use kartik\export\ExportMenu;
use kartik\grid\GridView;
use yii\data\ActiveDataProvider;
use app\models\Mdetailprod;
use app\models\Thserials;
use app\controllers\EstockTools;

$this->title = '';
$this->params['breadcrumbs'] = null;

?>
<div class="mdetail-index">

    <!-- <h1><?= Html::encode($this->title) ?></h1> -->
    <?php // echo $this->render('_search', ['model' => $searchModel]); ?>

    <p>
        <?php 
        $actionButtons = "";
        if( $dataProvider->models[0]->move_id > 0 ){
            $actionButtons =  EstockTools::getprintlinks($dataProvider->models[0]->move_id);
            $actionButtons .= "&nbsp;";
            $actionButtons .= Html::a('TH Serials', ['/thserials/index','move_id'=>$dataProvider->models[0]->move_id ], ['class' => 'btn btn-success', 'id' =>'action1', 'data-pjax' => 0]);
            $actionButtons .= "&nbsp;";
            $actionButtons .= Html::a('Show Multiadd', ['/movement/showmultiadd','id'=>$dataProvider->models[0]->move_id ], ['class' => 'btn btn-warning', 'id' =>'action2']);
            if( Yii::$app->user->identity->repli == 2 ){
                $actionButtons .= Html::a('-> CHRONO', ['/movement/exportforchrono','id'=>$dataProvider->models[0]->move_id ], ['class' => 'btn btn-warning', 'id' =>'action3', 'data-pjax' => 0]);
            }
            $actionButtons .= 
             Html::a('<i class="fa glyphicon glyphicon-hand-up"></i> ' . 'PDF', 
                ['/mdetailprod/documentlist', 'id' => $dataProvider->models[0]->move_id],
                [
                    'class' => 'btn btn-danger',
                    'target' => '_blank',
                    'data-pjax' => 0,
                    'data-toggle' => 'tooltip',
                    'title' => 'General print document'
                ]
            );
        }

        // $showAllBtn = ' ';
        // if(  $dataProvider->getTotalCount() > 20 ){
        //     $showAllBtn = '{toggleData}';
        // }

    ?>
    </p>
    <?php 
    $gridColumn = [
        'mat_id',
        'kod',
        'model',
        'price',
        [
        'class' => '\kartik\grid\DataColumn',
        'attribute' => 'pcs',
        'pageSummary' => true
        ],
        'discount',
        'tax',
        [
        'class' => '\kartik\grid\FormulaColumn',
        'label' => 'Total',
        'value' => function ($model, $key, $index, $widget) {
            $p = compact('model', 'key', 'index');

            return $widget->col(4, $p)*($widget->col(3, $p)*(1-$widget->col(5, $p)/100));
        },
                'pageSummary' => true
        ],
        [
        'class' => '\kartik\grid\FormulaColumn',
        'label' => 'T+VAT',
        'value' => function ($model, $key, $index, $widget) {
            $p = compact('model', 'key', 'index');

            return (1+$widget->col(6, $p)/100)*$widget->col(4, $p)*($widget->col(3, $p)*(1-$widget->col(5, $p)/100));
        },
        'pageSummary' => true
        ],
        [
        'class' => '\kartik\grid\DataColumn',
        'attribute' => 'currency',
        'label' => "Curr"
        ],
        [
            'class' => 'kartik\grid\ExpandRowColumn',
            'width' => '50px',
            'value' => function ($model, $key, $index, $column) {
                return GridView::ROW_COLLAPSED;
            },
            'detail' => function ($model, $key, $index, $column) {
                $m = new Thserials();
                $m->move_id=$model->move_id;
                $m->mat_id=$model->mat_id;
                return Yii::$app->controller->renderPartial('../thserials/create', ['model' => $m ]);
            },
            'headerOptions' => ['class' => 'kartik-sheet-style'],
            'expandOneOnly' => true
        ],
        [
                // 'class' => 'kartik\grid\EditableColumn',
                'attribute' => 'firstthserial',
                'label' => '1st serial',
                // 'editableOptions' => [
                //     'asPopover' => false
                // ],
                // 'value' => function($model,$key,$index){                   
                //     return $model->firstthserial;
                // },
        ],
        'detail_info',
    ];
    ?>
    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'columns' => $gridColumn,
        'showPageSummary' => true,
        'filterModel' => isset($searchModel)?$searchModel:null,
        'pjax' => true,
        'pjaxSettings' => ['options' => ['id' => 'kv-pjax-container-mdetail'.$dataProvider->models[0]->move_id ]],
        'panel' => [
            'type' => GridView::TYPE_PRIMARY,
            'heading' => '<span class="glyphicon glyphicon-book"></span>  ' . Html::encode("Detail"),
        ],
        'toolbar' => [
            [
                'content'=> $actionButtons,
            ],
            Yii::$app->controller->action->id == 'documentdetaillist' ? //Export menu only on single site
            ExportMenu::widget([
                'dataProvider' => isset($edataProvider) ? $edataProvider : $dataProvider,
                'columns' => $gridColumn,
                'pjax' => false,
                'clearBuffers' => true,
                'target' => ExportMenu::TARGET_BLANK,
                'fontAwesome' => true,
                'showConfirmAlert' => false,
                'dropdownOptions' => [
                    'label' => 'Full',
                    'class' => 'btn btn-default',
                    'itemsBefore' => [
                        '<li class="dropdown-header">Export All Data</li>',
                    ],
                ],
            ]) : "" ,
        ],
    ]); ?>

</div>