<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model app\models\MdetailprodSearch */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="form-mdetailprod-search">

    <?php $form = ActiveForm::begin([
        'action' => ['index'],
        'method' => 'get',
    ]); ?>

    <?= $form->field($model, 'move_id')->textInput(['placeholder' => 'Move']) ?>

    <?= $form->field($model, 'repli_id')->textInput(['placeholder' => 'Repli']) ?>

    <?= $form->field($model, 'mat_id')->textInput(['placeholder' => 'Mat']) ?>

    <?= $form->field($model, 'kod')->textInput(['maxlength' => true, 'placeholder' => 'Kod']) ?>

    <?= $form->field($model, 'model')->textInput(['maxlength' => true, 'placeholder' => 'Model']) ?>

    <?php /* echo $form->field($model, 'price')->textInput(['maxlength' => true, 'placeholder' => 'Price']) */ ?>

    <?php /* echo $form->field($model, 'pcs')->textInput(['placeholder' => 'Pcs']) */ ?>

    <?php /* echo $form->field($model, 'discount')->textInput(['maxlength' => true, 'placeholder' => 'Discount']) */ ?>

    <?php /* echo $form->field($model, 'tax')->textInput(['maxlength' => true, 'placeholder' => 'Tax']) */ ?>

    <?php /* echo $form->field($model, 'currency')->textInput(['maxlength' => true, 'placeholder' => 'Currency']) */ ?>

    <?php /* echo $form->field($model, 'detail_info')->textInput(['maxlength' => true, 'placeholder' => 'Detail Info']) */ ?>

    <div class="form-group">
        <?= Html::submitButton('Search', ['class' => 'btn btn-primary']) ?>
        <?= Html::resetButton('Reset', ['class' => 'btn btn-default']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
