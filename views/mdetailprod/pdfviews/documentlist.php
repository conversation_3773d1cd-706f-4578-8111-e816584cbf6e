<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use kartik\grid\GridView;

/* @var $this yii\web\View */
/* @var $model app\models\Mdetailprod */

$this->title = $model->movement->mType->name.'  '.$model->movement->number;
$this->params['breadcrumbs'][] = ['label' => 'Mdetailprod', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;

if (class_exists('yii\debug\Module')) {
    $this->off(\yii\web\View::EVENT_END_BODY, [\yii\debug\Module::getInstance(), 'renderToolbar']);
}

?>
<div class="mdetailprod-view">

    <div class="row">
        <div class="col-sm-9">
            <h2><?= Html::encode($this->title) ?></h2>
        </div>
    </div>

    <div class="row">
<?php 

    $headerColumn = [
        [
        'label' => 'Supplier/customer',        'format' => 'raw', 'value' => function ($data) {
                         return $data->movement->adr->firma.', '.$data->movement->adr->street.", ".$data->movement->adr->zip." ".$data->movement->adr->city;
                     },
        ],
        [
        'label' => 'Date', 'format' => 'raw', 'value' => function ($data) {
                         return date('d. M Y', strtotime($data->movement->d1));
                     },
        ],
        'move_id',
    ];
    if( !empty($model->movement->stock_id1) ){
        $headerColumn[] = ['label' => 'From Stock', 'format' => 'raw', 'value' => function ($data) { return $data->movement->stockdetail1->sdescr;},];
    }
    if( !empty($model->movement->stock_id2) ){
        $headerColumn[] = ['label' => 'To Stock', 'format' => 'raw', 'value' => function ($data) { return $data->movement->stock_id2;},];
    }
    // echo "<pre>";
    // var_dump($headerColumn);
    // exit;
    echo DetailView::widget([
        'model' => $model,
        'attributes' => $headerColumn
    ]); 

    $gridColumn = [
        'mat_id',
        'kod',
        'model',
        'price',
        'price',
        'pcs',
        'discount',
        // [
        // 'label' => '%Disc', 'format' => 'raw', 'value' => function ($data) {
        //                 if( $data->discount <> 0 ){
        //                     return $data->discount;
        //                 } else {
        //                     return '';
        //                 }
        //              },
        // ],
        // [       'label' => 'Tax','value' => function ($data) {
        //                 return $data->tax.'%';
        //              },
        // ],
        'tax',
        'currency',
        'detail_info',
    ];

    echo "<div>&nbsp;</div>";
    echo GridView::widget([
        'dataProvider' => $dataProvider,
        'columns' => $gridColumn,
        'pjax' => false,
        'tableOptions' =>  ['class' => 'table table-condensed'],
        'layout' => '{items}'
        // 'panel' => [
        //     'type' => GridView::TYPE_PRIMARY,
        //     'heading' => '<span class="glyphicon glyphicon-book"></span>  ' . Html::encode('MOVEMENTS'),
        // ],
    ]); 
    

?>
<p><b>Sum of pcs: </b><?= $sumpcs; ?> <b>Total: </b><?= $model->movement->total0 + $model->movement->total1 + $model->movement->total2; ?>
<b> VAT: </b><?= $model->movement->tax1 + $model->movement->tax2; ?> 
<b> Total with VAT: </b><?= $model->movement->total; ?> 
</p>
    <p><?= $model->movement->text1; ?></p>
    <p><?= $model->movement->text2; ?></p>

    </div>
</div>
