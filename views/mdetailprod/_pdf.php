<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use kartik\grid\GridView;

/* @var $this yii\web\View */
/* @var $model app\models\Mdetailprod */

$this->title = $model->move_id;
$this->params['breadcrumbs'][] = ['label' => 'Mdetailprod', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="mdetailprod-view">

    <div class="row">
        <div class="col-sm-9">
            <h2><?= 'Mdetailprod'.' '. Html::encode($this->title) ?></h2>
        </div>
    </div>

    <div class="row">
<?php 
    $gridColumn = [
        [
        'label' => 'Firma',
        'format' => 'raw',
        'value' => function ($data) {
                         return Html::a($data->movement->adr->firma, ['/admin/region/view', 'id' => $data->move_id]);
                     },
        ],
        'move_id',
        'repli_id',
        'mat_id',
        'kod',
        'model',
        'price',
        'pcs',
        'discount',
        'tax',
        'currency',
        'detail_info',
    ];
    echo DetailView::widget([
        'model' => $model,
        'attributes' => $gridColumn
    ]); 
?>
    </div>
</div>
