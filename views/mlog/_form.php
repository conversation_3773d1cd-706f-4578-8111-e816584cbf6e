<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use app\models\Mlog;

/** @var yii\web\View $this */
/** @var app\models\Mlog $model */
/** @var yii\widgets\ActiveForm $form */
?>

<div class="mlog-form">

    <?php $form = ActiveForm::begin(); ?>

    <?= $form->field($model, 'move_id')->hiddenInput(['value' =>   Yii::$app->request->get()['move_id'] ?? ''])->label(false) ?>

    <?= $form->field($model, 'flags')->radioList(Mlog::FLAGS, ['separator' => '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;']) ?>

    <?= $form->field($model, 'note')->textInput(['maxlength' => true]) ?>

    <?=  $form->field($model, 'user_name')->hiddenInput(['value' =>  Yii::$app->user->identity->username])->label(false) ?>

    <div class="form-group">
        <?= Html::submitButton('Create', ['class' => 'btn btn-success']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
