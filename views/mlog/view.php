<?php

use yii\helpers\Html;
use yii\widgets\DetailView;
use app\controllers\EstockTools;

/** @var yii\web\View $this */
/** @var app\models\Mlog $model */

$this->title = 'Information created at ' . $model->created_at . ' by ' . $model->user_name;
\yii\web\YiiAsset::register($this);
?>

<div class="mlog-view">

    <?= DetailView::widget([
        'model' => $model,
        'attributes' => [
            'created_at',
            'move_id',
            'flags',
            'note',
            'user_name',
        ],
    ]) ?>

</div>

<?= Html::a('OK', [EstockTools::isUserShop() ? '//movement/mymoves' : '//movement/index' , "MovementSearch[move_id]" => $model->move_id], ['class' => 'btn btn-default']) ?>
