<?php

use app\controllers\EstockTools;
use app\models\Mlog;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\grid\ActionColumn;
use yii\grid\GridView;
use yii\widgets\Pjax;
/** @var yii\web\View $this */
/** @var app\models\MlogSearch $searchModel */
/** @var yii\data\ActiveDataProvider $dataProvider */

$this->title = '';
?>
<div class="mlog-index">

    <h1><?= Html::encode($this->title) ?></h1>

    <p>
        <?= Html::a('New log information', ['//mlog/create', 'move_id' => $move_id ], ['class' => 'btn btn-success']) ?>
        <?= empty($isOnTheWay) ? Html::a('Set "ON THE WAY"', ['//mlog/createontheway', 'move_id' => $move_id ], ['class' => 'btn btn-danger']) : "<b>Is On The Way</b> since ".$isOnTheWay ?>
        <?= empty($isReceived) ? ( !empty($isOnTheWay) ?
              Html::a('Confirm "RECEIVED"', ['//mlog/createreceived', 'move_id' => $move_id ], ['class' => 'btn btn-warning', 'data' => [
                'confirm' => 'Are you sure goods are received to final stock?',
                'method' => 'post',
            ]]) : "" )
               : " <b>Received</b> ".$isReceived ?>
    </p>

    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'columns' => [
            [
                'attribute' => 'created_at',
                'value' => function ($model) {
                    return Html::tag('div', EstockTools::getRelativeTime($model->created_at), ['data-toggle'=>'tooltip','data-placement'=>'left','title'=>$model->created_at,'style'=>'cursor:default;']);
                },
                'format'=>'raw',
            ],
            'flags',
            'note',
            'user_name',
        ],
    ]); ?>

<p><i><b>Log</b> is for everyone and for every additional movement information! Feel free to write whatever happens concerning this movement. You can use it even as a group chat... Thank you.</i></p>
</div>
