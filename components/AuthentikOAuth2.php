<?php
// In components/AuthentikOAuth2.php
namespace app\components;

use yii\authclient\OAuth2;
use yii\authclient\OAuthToken;
use Yii;


/**
 * Authentik allows authentication via Authentik OAuth2.
 *
 * In order to use Authentik OAuth2 you must register your application at
 * https://authentik.com/developer/apps
 *
 * Example application configuration:
 *
 * ~~~
 * 'components' => [
 *     'authClientCollection' => [
 *         'class' => 'yii\authclient\Collection',
 *         'clients' => [
 *             'authentik' => [
 *                 'class' => 'app\components\AuthentikOAuth2',
 *                 'clientId' => 'authentik_client_id',
 *                 'clientSecret' => 'authentik_client_secret',
 *             ],
 *         ],
 *     ]
 *     ...
 * ]
 * ~~~
 *
 * @see https://authentik.com/developer/apps
 * @see https://authentik.com/developer/docs/api
 */
class AuthentikOAuth2 extends OAuth2
{
    // public $apiBaseUrl = 'http://casas.grapph.com:9000/application/o/wstock';

    /**
     * {@inheritdoc}
     */
    public $accessTokenLocation = self::ACCESS_TOKEN_LOCATION_HEADER;

    /**
     * @inheritdoc
     */
    protected function initUserAttributes()
    {
        $userInfo = $this->api('application/o/userinfo/', 'GET');
        Yii::debug("Authentik user info: " . print_r($userInfo, true), __METHOD__);
        return $userInfo;
    }

    /**
     * @inheritdoc
     */
    protected function defaultName()
    {
        return 'authentik';
    }

    /**
     * @inheritdoc
     */
    protected function defaultTitle()
    {
        return 'Authentik';
    }

    /**
     * @inheritdoc
     */
    protected function createToken(array $tokenConfig = [])
    {
        return new OAuthToken($tokenConfig);
    }
}
