<?php

namespace app\commands;

use Yii;
use yii\console\Controller;
use yii\console\ExitCode;

/**
 * Get garmin.sk stock and update our garmin stock in the table	garminstock, then rewrite movement 5895 and recount all
 *
 * used in crontab
 *
 * ```
 * $ ./yii garmin
 * ```
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2.0
 */
class GarminController extends Controller
{

	public function actionIndex()
	{

		$username = "17gwatcwe";
		$password = "djhNdhsl43452";
		$remote_url = 'http://garmin.sk/i6ws/Default.asmx/GetResult?resultType=StoItemQtyFree';

		// Create a stream
		$opts = array(
			'http'=>array(
				'method'=>"GET",
				'header' => "Authorization: Basic " . base64_encode("$username:$password")                 
			)
		);

		$context = stream_context_create($opts);

		// Open the file using the HTTP headers set above
		$a = file_get_contents($remote_url, false, $context);

		print_r($a);

		$xml = simplexml_load_string($a);
		if( is_null($xml)){
			return ExitCode::DATAERR;
		}	

		try {
			$cmd = Yii::$app->db->createCommand("drop table if exists garminstock");
			$cmd->execute();
			$cmd = Yii::$app->db->createCommand("create table garminstock (code varchar(255) primary key, ean varchar(255), free integer)");
			$cmd->execute();
		} catch (\yii\db\Exception $e) {
			return ExitCode::DATAERR;
		}

		foreach($xml->StoItem as $key=>$val) {

			$sql =  "insert into garminstock (code,ean,free) values ( '".$val['Code']."', ";
			$sql .= $val['PartNo'] ?  "'".$val['PartNo']."'" : 'null';
			$sql .=  ",".$val['QtyFree'].") ON CONFLICT DO NOTHING";
                // echo $sql."\n";
			$cmd = Yii::$app->db->createCommand($sql);


			try {
				$cmd->execute();
			} catch (\yii\db\Exception $e) {
				return ExitCode::DATAERR;
			}

		}
			//
			// HACK: REWRITES MOVEMENT 5895 !!!!!!!
			//
			//
		$moveid = "5895";

		$sql2 = "delete from m_detail where move_id=".$moveid;
		$cmd2 = Yii::$app->db->createCommand($sql2);

		$sql3 = "insert into m_detail(move_id,mat_id,pcs,price, detail_info, fifo_repli_id) select ".$moveid." as move_id, p.mat_id, g.free, p.price, '',0 from product p join  garminstock g on p.model=g.code and p.kod in ('GARS','GARM') ";
		$cmd3 = Yii::$app->db->createCommand($sql3);

		try {
			$cmd2->execute();
			$cmd3->execute();

			Yii::$app->db->createCommand("call totalcountasuser(".$moveid.",'dba')")->query();
			Yii::$app->db->createCommand("call dyninfo_recount_all()")->query();

		} catch (\yii\db\Exception $e) {
			return ExitCode::DATAERR;
		}


		return ExitCode::OK;
	}
}
