<?php

namespace app\commands;

use Yii;
use yii\console\Controller;
use yii\console\ExitCode;

/**
 * Set prices for p0e and p0f by calling stored procedure setpricesp0ep0f()
 *
 * used in crontab
 *
 * ```
 * $ ./yii setpricesp0ep0f
 * ```
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2.0
 */

class Setpricesp0ep0fController extends Controller
{

	public function actionIndex()
	{

		$sql =  "call setpricesp0ep0f()";
		$cmd = Yii::$app->db->createCommand($sql);

		try {
			$cmd->execute();
		} catch (\yii\db\Exception $e) {
			return ExitCode::DATAERR;
		}

		return ExitCode::OK;
	}

}
