<?php

namespace app\commands;

use Yii;
use yii\console\Controller;
use yii\console\ExitCode;
use SimpleXMLElement;
use app\models\ShoptetOrderItems;
use app\models\ShoptetOrders;

/**
 * Get shoptet synchro orders, create and update orders in our db
 *
 * used in crontab
 *
 * ```
 * $ ./yii shoptetordersimport
 * ```
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2.0
 */
class ShoptetordersimportController extends Controller
{

	public function actionIndex()
	{

//		$remote_url = 'https://www.wikarska.com/export/orders.xml?patternId=6&ip=**************&partnerId=3&hash=848ffcd664a33a87267ec389d38fcdc779b2de2286241ad1ea21fe853ffd9143';
		$remote_url = 'https://www.wikarska.com/export/ordersFeed.xml?patternId=6&ip=**************&partnerId=3&hash=848ffcd664a33a87267ec389d38fcdc779b2de2286241ad1ea21fe853ffd9143';
//		$remote_url = "/root/w.xml";

		// Create a stream
		$opts = array(
			'http'=>array(
				'method'=>"GET"
			)
		);

		$context = stream_context_create($opts);

		// Open the file using the HTTP headers set above
		$xmlContent = file_get_contents($remote_url, false, $context);

		// print_r($a);

        $xml = new SimpleXMLElement($xmlContent);
		if( is_null($xml)){
			return ExitCode::DATAERR;
		}
	

		try {

            foreach ($xml->ORDER as $order) {
				echo "Order: ".$order->ORDER_ID."\n";
                $shoptetOrder = ShoptetOrders::findOne(['order_id' => (string)$order->ORDER_ID]) ?? new ShoptetOrders();
                $shoptetOrder->order_id = (string)$order->ORDER_ID;
                $shoptetOrder->code = (string)$order->CODE ?? null;
                $shoptetOrder->cdate = (string)$order->DATE ?? null;
                $shoptetOrder->status = (string)$order->STATUS ?? null;
                $shoptetOrder->currency_code = (string)$order->CURRENCY->CODE ?? null;
                $shoptetOrder->currency_exchange_rate = (float)$order->CURRENCY->EXCHANGE_RATE ?? null;
                // Add additional fields as needed
				$shoptetOrder->customer_email = (string)$order->CUSTOMER->EMAIL ?? null;
				$shoptetOrder->customer_phone = (string)$order->CUSTOMER->PHONE ?? null;
				$shoptetOrder->bill_name = (string)$order->CUSTOMER->BILLING_ADDRESS->NAME ?? null;
				$shoptetOrder->bill_company = (string)$order->CUSTOMER->BILLING_ADDRESS->COMPANY ?? null;
				$shoptetOrder->bill_street = (string)$order->CUSTOMER->BILLING_ADDRESS->STREET ?? null;
				$shoptetOrder->bill_house_number = (string)$order->CUSTOMER->BILLING_ADDRESS->HOUSE_NUMBER ?? null;
				$shoptetOrder->bill_city = (string)$order->CUSTOMER->BILLING_ADDRESS->CITY ?? null;
				$shoptetOrder->bill_zip = (string)$order->CUSTOMER->BILLING_ADDRESS->ZIP ?? null;
				$shoptetOrder->bill_country = (string)$order->CUSTOMER->BILLING_ADDRESS->COUNTRY ?? null;
				$shoptetOrder->bill_company_id = (string)$order->CUSTOMER->BILLING_ADDRESS->COMPANY_ID ?? null;
				$shoptetOrder->bill_vat_id = (string)$order->CUSTOMER->BILLING_ADDRESS->VAT_ID ?? null;
				$shoptetOrder->customer_identification_number = (string)$order->CUSTOMER->BILLING_ADDRESS->CUSTOMER_IDENTIFICATION_NUMBER ?? null;
				$shoptetOrder->delivery_name = (string)$order->CUSTOMER->SHIPPING_ADDRESS->NAME ?? null;
				$shoptetOrder->delivery_company = (string)$order->CUSTOMER->SHIPPING_ADDRESS->COMPANY ?? null;
				$shoptetOrder->delivery_street = (string)$order->CUSTOMER->SHIPPING_ADDRESS->STREET ?? null;
				$shoptetOrder->delivery_house_number = (string)$order->CUSTOMER->SHIPPING_ADDRESS->HOUSE_NUMBER ?? null;
				$shoptetOrder->delivery_city = (string)$order->CUSTOMER->SHIPPING_ADDRESS->CITY ?? null;
				$shoptetOrder->delivery_zip = (string)$order->CUSTOMER->SHIPPING_ADDRESS->ZIP ?? null;
				$shoptetOrder->delivery_country = (string)$order->CUSTOMER->SHIPPING_ADDRESS->COUNTRY ?? null;
				$shoptetOrder->customer_ip_address = (string)$order->CUSTOMER->IP_ADDRESS ?? null;
				$shoptetOrder->remark = (string)$order->REMARK ?? null;
				$shoptetOrder->shop_remark = (string)$order->SHOP_REMARK ?? null;
				$shoptetOrder->referer = (string)$order->REFERER ?? null;
				$shoptetOrder->package_number = (string)$order->PACKAGE_NUMBER ?? null;
				$shoptetOrder->varchar1 = (string)$order->VARCHAR1 ?? null;
				$shoptetOrder->varchar2 = (string)$order->VARCHAR2 ?? null;
				$shoptetOrder->varchar3 = (string)$order->VARCHAR3 ?? null;
				$shoptetOrder->text1 = (string)$order->TEXT1 ?? null;
				$shoptetOrder->text2 = (string)$order->TEXT2 ?? null;
				$shoptetOrder->text3 = (string)$order->TEXT3 ?? null;
				$shoptetOrder->weight = (float)$order->WEIGHT ?? null;
				$shoptetOrder->total_price_with_vat = (float)$order->TOTAL_PRICE->WITH_VAT ?? null;
				$shoptetOrder->total_price_without_vat = (float)$order->TOTAL_PRICE->WITHOUT_VAT ?? null;
				$shoptetOrder->total_price_vat = (float)$order->TOTAL_PRICE->VAT ?? null;
				$shoptetOrder->total_price_rounding = (float)$order->TOTAL_PRICE->ROUNDING ?? null;
				$shoptetOrder->total_price_to_pay = (float)$order->TOTAL_PRICE->TO_PAY ?? null;
				$shoptetOrder->paid = (bool)$order->PAID ?? null;
				// print_r($shoptetOrder);
				if ($shoptetOrder->save()) {
					// try to create movement also
					if( $shoptetOrder->status == 'Vyřízena' && $shoptetOrder->movement_id == null
						&& $shoptetOrder->cdate > date('Y-m-d H:i:s', strtotime('-1 month'))){
						$shoptetOrder->createMovement();
					}
				} else {
					print_r($shoptetOrder->getAttributes());
					print_r($shoptetOrder->getErrors());
					return ExitCode::DATAERR;
				}
					
			
                foreach ($order->ORDER_ITEMS->ITEM as $item) {
					echo "   Item: ".$item->NAME."\n";
                    $orderItem = ShoptetOrderItems::findOne([
                        'order_id' => $shoptetOrder->order_id,
                        'name' => (string)$item->NAME,
                    ]) ?? new ShoptetOrderItems();
                    $orderItem->order_id = $shoptetOrder->order_id;
                    $orderItem->otype = (string)$item->TYPE ?? null;
                    $orderItem->name = (string)$item->NAME ?? null;
                    $orderItem->amount = (float)$item->AMOUNT ?? null;
                    $orderItem->ocode = (string)$item->CODE ?? null;
                    // Add additional fields as needed
					$orderItem->variant_name = (string)$item->VARIANT_NAME ?? null;
					$orderItem->ean = (string)$item->EAN ?? null;
					$orderItem->plu = (string)$item->PLU ?? null;
					$orderItem->manufacturer = (string)$item->MANUFACTURER ?? null;
					$orderItem->supplier = (string)$item->SUPPLIER ?? null;
					$orderItem->unit = (string)$item->UNIT ?? null;
					$orderItem->weight = (float)$item->WEIGHT ?? null;
					$orderItem->status = (string)$item->STATUS ?? null;
					$orderItem->discount = (float)$item->DISCOUNT ?? null;
					$orderItem->unit_price_with_vat = (float)$item->UNIT_PRICE->WITH_VAT ?? null;
					$orderItem->unit_price_without_vat = (float)$item->UNIT_PRICE->WITHOUT_VAT ?? null;
					$orderItem->unit_price_vat = (float)$item->UNIT_PRICE->VAT ?? null;
					$orderItem->unit_price_vat_rate = (float)$item->UNIT_PRICE->VAT_RATE ?? null;
					$orderItem->unit_discount_price_with_vat = (float)$item->UNIT_PRICE->DISCOUNT->WITH_VAT ?? null;
					$orderItem->unit_discount_price_without_vat = (float)$item->UNIT_PRICE->DISCOUNT->WITHOUT_VAT ?? null;
					$orderItem->total_price_with_vat = (float)$item->TOTAL_PRICE->WITH_VAT ?? null;
					$orderItem->total_price_without_vat = (float)$item->TOTAL_PRICE->WITHOUT_VAT ?? null;
					$orderItem->total_price_vat = (float)$item->TOTAL_PRICE->VAT ?? null;
					$orderItem->total_price_vat_rate = (float)$item->TOTAL_PRICE->VAT_RATE ?? null;
					$orderItem->surcharges = (string)$item->SURCHARGES ?? null;

					if ($orderItem->save()) {
					} else {
						print_r($orderItem->getAttributes());
						print_r($orderItem->getErrors());
						return ExitCode::DATAERR;
					}
                }
            }

		} catch (\yii\db\Exception $e) {
			return ExitCode::DATAERR;
		}

		$nroforders = Yii::$app->db->createCommand( "select count(*) from shoptet_orders" )->queryScalar();
		$nrofitems = Yii::$app->db->createCommand( "select count(*) from shoptet_order_items" )->queryScalar();

		echo "Together orders: ".$nroforders." $nrofitems\n";
		
		return ExitCode::OK;
	}
}
