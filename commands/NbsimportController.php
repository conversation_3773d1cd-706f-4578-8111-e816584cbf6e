<?php

namespace app\commands;

use Yii;
use yii\console\Controller;
use yii\console\ExitCode;

/**
 * Get daily NBS exchange rates and update our nbs_rates table
 *
 * used in crontab
 *
 * ```
 * $ ./yii nbsimport
 * ```
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2.0
 */

class NbsimportController extends Controller
{

    public function actionIndex()
    {

        $a = file_get_contents('http://www.ecb.europa.eu/stats/eurofxref/eurofxref-daily.xml');

        $xml = simplexml_load_string($a);
        foreach($xml->Cube->Cube->Cube as $key=>$val) {
                $sql =  "insert into nbs_rates (nbs_multi,nbs_cur,nbs_rate,nbs_date) values (   1 ,'".$val['currency']."', ".$val['rate'].",'".$xml->Cube->Cube['time']."') ON CONFLICT DO NOTHING";
                $cmd = Yii::$app->db->createCommand($sql);

               try {
                    $cmd->execute();
                } catch (\yii\db\Exception $e) {
		    return ExitCode::DATAERR;
                }

        }

        return ExitCode::OK;
    }
}

