<?php

namespace app\commands\ucto;

use Yii;
use yii\console\Controller;
use yii\console\ExitCode;

class PrijemkyController extends Controller
{


  public function actionIndex()
  {
    // Initialize a ROS PDF class object using DIN-A4, with background color gray
$pdf = new pdfphp\src\Cezpdf('a4','portrait','color',[0.8,0.8,0.8]);
// Set pdf Bleedbox
$pdf->ezSetMargins(20,20,20,20);
// Use one of the pdf core fonts
$mainFont = 'Times-Roman';
// Select the font
$pdf->selectFont($mainFont);
// Define the font size
$size=12;
// Modified to use the local file if it can
$pdf->openHere('Fit');

// Output some colored text by using text directives and justify it to the right of the document
$pdf->ezText("PDF with some <c:color:1,0,0>blue</c:color> <c:color:0,1,0>red</c:color> and <c:color:0,0,1>green</c:color> colours", $size, ['justification'=>'right']);
// Output the pdf as stream, but uncompress
$pdf->ezStream(['compress'=>0]);

/*
    Yii::$app->db->createCommand("alter table yuctomodel add banska integer default 0")->execute();
    Yii::$app->db->createCommand("alter table yuctomodel add blava integer default 0")->execute();
    Yii::$app->db->createCommand("alter table yuctomodel add m_type_id integer default 0")->execute();


  Yii::$app->db->createCommand("update yuctomodel set m_type_id=(select m_type_id from movement where move_id=yuctomodel.move_id)")->execute();


  Yii::$app->db->createCommand("select sum(pcs),sum(pcs*n_price),d1,m_type_id from yuctomodel where xx<>'!' group by d1,m_type_id")->execute();
  Yii::$app->db->createCommand("select sum(pcs),sum(pcs*n_price),month(d1),m_type_id from yuctomodel where xx<>'!' group by month(d1),m_type_id order by m_type_id")->execute();



  Yii::$app->db->createCommand("delete from yuctomodel where move_id<0")->execute();
  Yii::$app->db->createCommand("update yuctomodel set blava=0, banska=0")->execute();

  Yii::$app->db->createCommand("update yuctomodel set blava=pcs where xx<>'!' and m_type_id not in (87,97)")->execute();
  Yii::$app->db->createCommand("update yuctomodel set banska=pcs where xx<>'!' and m_type_id in (87,97)")->execute();

  Yii::$app->db->createCommand("update yuctomodel set blava=pcs,banska=0 where move_id  in (438502, 438503 )")->execute();
  Yii::$app->db->createCommand("update yuctomodel set banska=pcs,blava=0 where move_id=426954")->execute();

*/
    return ExitCode::OK;
  }
}




