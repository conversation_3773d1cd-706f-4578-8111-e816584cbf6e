

ucto2022=# update m_detail set fifo_price=price, price=price/1.2 where move_id in (select move_id from movement where year(d1)=2022 and m_type_id=97);
UPDATE 95
ucto2022=# update m_detail set fifo_price=price, price=price/1.2 where move_id in (select move_id from movement where year(d1)=2022 and m_type_id=148);
UPDATE 1
ucto2022=# \q


PRVE PRIJEMKY
***********************************
v db2021:
select sum(pcs) xpcs,sum(pcs*n_price) xsum from yuctogwmodel where xx<>'!'

1143 "543688.84"

select sum(pcs) xpcs,sum(pcs*n_price) xsum from yuctogw5039model where xx<>'!'

807 "654249.96"

select sum(pcs) xpcs,sum(pcs*n_price) xsum from yuctogw5117model where xx<>'!'

181 "83968.68"

select sum(pcs) xpcs,sum(pcs*n_price) xsum from yuctogw5040model where xx<>'!'
82 "77814.52"

select sum(pcs) xpcs,sum(pcs*n_price) xsum from yuctogw3731model where xx<>'!'
675 "252110.35"

select sum(banska) bb,sum(banska*n_price) bbs, sum(blava) ba, sum(blava*n_price) bas from yuctomodel where xx<>'!'
"2804" "407856.49" "17216" "2922058.95"

postgres@vmi307697:~$ psql ucto2021 <naprveprijemky.sql 
COPY 759
COPY 630
COPY 496
COPY 73
COPY 85
COPY 3372
COPY 1334
postgres@vmi307697:~$ cat naprveprijemky.sql 
COPY (
select mat_id mid,sum(pcs) as x3,n_price,'// ' as lomi ,n_price from yuctogwmodel y where xx<> '!' group by mat_id,n_price having sum(pcs)<>0 order by mat_id, n_price
)
to '/tmp/yuctogwmodel2021' with delimiter ','; 
COPY (
select mat_id mid,sum(pcs) as x3,n_price,'// ' as lomi ,n_price from yuctogw3731model y where xx<> '!' group by mat_id,n_price having sum(pcs)<>0 order by mat_id, n_price
)
to '/tmp/yuctogw3731model2021' with delimiter ',';
COPY (
select mat_id mid,sum(pcs) as x3,n_price,'// ' as lomi ,n_price from yuctogw5039model y where xx<> '!' group by mat_id,n_price having sum(pcs)<>0 order by mat_id, n_price
)
to '/tmp/yuctogw5039model2021' with delimiter ',';
COPY (
select mat_id mid,sum(pcs) as x3,n_price,'// ' as lomi ,n_price from yuctogw5040model y where xx<> '!' group by mat_id,n_price having sum(pcs)<>0 order by mat_id, n_price
)
to '/tmp/yuctogw5040model2021' with delimiter ',';
COPY (
select mat_id mid,sum(pcs) as x3,n_price,'// ' as lomi ,n_price from yuctogw5117model y where xx<> '!' group by mat_id,n_price having sum(pcs)<>0 order by mat_id, n_price
)
to '/tmp/yuctogw5117model2021' with delimiter ',';
COPY (
select mat_id mid,sum(blava) as x3,n_price,'// ' as lomi ,n_price from yuctomodel y where xx<> '!' group by mat_id,n_price having sum(blava)<>0 order by mat_id, n_price
)
to '/tmp/yuctobamodel2021' with delimiter ',';
COPY (
select mat_id mid,sum(banska) as x3,n_price,'// ' as lomi ,n_price from yuctomodel y where xx<> '!' group by mat_id,n_price having sum(banska)<>0 order by mat_id, n_price
)
to '/tmp/yuctobbmodel2021' with delimiter ',';



VYSTUPY:
*****************
- zaedituj ucto/UctoParams.php
- skontroluj  skripty a parametre z  getall.sh
