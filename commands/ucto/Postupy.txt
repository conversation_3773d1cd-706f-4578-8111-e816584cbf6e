Kontrola RACIO:

select sum(pcs) xpcs,sum(pcs*n_price) xsum ,sum(banska*n_price) xbb,sum(blava*n_price) xba,sum(blava) xxba,sum(banska) xxbb from yuctomodel where xx<>'!'

Prva prijemka RACIO:


select mat_id mid,sum(banska)           
x3,n_price,'// ' pozn,mat_id||'_' ||n_price np2 from yuctomodel y where xx<> '!' group by mat_id,n_price having sum(banska)<>0 order by mat_id, n_price; 


select mat_id mid,sum(blava)           
x3,n_price,'// ' pozn,mat_id||'_' ||n_price np2 from yuctomodel y where xx<> '!' group by mat_id,n_price having sum(blava)<>0 order by mat_id, n_price; 

