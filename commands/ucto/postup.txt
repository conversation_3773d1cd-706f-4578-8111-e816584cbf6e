#  ROZDELENIE POSTUP

# Rucne alebo <PERSON>rozdelenieController:

alter table yuctomodel add banska integer default 0;
alter table yuctomodel add blava integer default 0;
alter table yuctomodel add m_type_id integer default 0;
update yuctomodel set m_type_id=(select m_type_id from movement where move_id=yuctomodel.move_id);


delete from yuctomodel where move_id<0;
update yuctomodel set blava=0, banska=0;
update yuctomodel set blava=pcs where xx<>'!' and m_type_id not in (87,97);
update yuctomodel set banska=pcs where xx<>'!' and m_type_id in (87,97);

Najdi prve prijemky
select * from movement where move_id in (select move_id from yuctomodel where d1='2021-01-01');

BA: 39581, 27230
BB: 27229

update yuctomodel set blava=pcs,banska=0 where move_id in (39581, 27230);
update yuctomodel set banska=pcs,blava=0 where move_id=27229;



# ALEBO STACI TOTO:
# raciosplitController moze urobit aj horne veci pomocou  --del=yes.
# len si ho pozri a edituj prve prijemky a rok

 2157  ./yii ucto/raciosplit --appconfig=config/consoleucto.php --del=yes
 2166  ./yii ucto/raciosplit --appconfig=config/consoleucto.php  --firstba=yes --firstbb=yes
 2166  ./yii ucto/raciosplit --appconfig=config/consoleucto.php  --firstba=yes --firstbb=yes
 2166  ./yii ucto/raciosplit --appconfig=config/consoleucto.php  --firstba=yes --firstbb=yes
 2166  ./yii ucto/raciosplit --appconfig=config/consoleucto.php  --firstba=yes --firstbb=yes




# Musel som presuny datum zmenit na 2.1  z 1.1...


update yuctomodel set d1='2021-01-02' where d1='2021-01-01' and move_id=0;



# potom dodatocne presuny vyratane:
 select '$this->gwucto_move( "blava","banska","'|| mat_id||'",'||n_price||',1,"'||(select max(d1) from yuctomodel where  mat_id=y.mat_id and n_price=y.n_price and xx='R')||'"); //' , sum(pcs*n_price),sum(banska*n_price) bb,sum(blava*n_price) ba, (select kod||' '||model from product where mat_id=y.mat_id) xsort from yuctomodel y group by mat_id,n_price having sum(blava*n_price)>0 and sum(blava*n_price)>3*n_price and sum(banska*n_price) =0 order by xsort;


#  a potom si vytvor presuny a pridaj do kodu a potom 
 2171  ./yii ucto/raciosplit --appconfig=config/consoleucto.php  --addmore=y

# a na konci (ale neviem ci dobre robi... mal  som nuly v prevodoch medzi prev.)

./yii ucto/nrrecountracio --appconfig=config/consoleucto.php


# a potom a medzitym parkrat uz smelo
./yii ucto/prijvydmodel --appconfig=config/consoleucto.php --firma=racioba --recount=yes
./yii ucto/prijvydmodel --appconfig=config/consoleucto.php --firma=raciobb --recount=yes

root@vmi307697:/var/www/vhosts/pgestock# mv commands/ucto/out/prijvyd_yuctomodelblavaModel.xlsx commands/ucto/out/2021/
root@vmi307697:/var/www/vhosts/pgestock# mv commands/ucto/out/prijvyd_yuctomodelbanskaModel.xlsx commands/ucto/out/2021/












