<?php
/* 
// All needed params for commands
*/


namespace app\commands\ucto;

use Yii;

class Uctoparams
{


  public $par_rok = 2021;
  public $tabulkaModel;
  public $par_sklad = 'yucto.pcs';
  public $par_firma;
  public $par_file;
  public $par_id;
  public $par_street;
  public $par_city;
  public $par_ico;
  public $par_dic;
  public $par_head;

    /**
     * @param string $id the ID of this controller.
     * @param Module $module the module that this controller belongs to.
     * @param array $config name-value pairs that will be used to initialize the object properties.
     */
    public function __construct($id)
    {
        $this->id = $id;
        switch ($id) {
        	case '3731':
        		$this->par_firma = "W-Watch, spol. s r.o., prevádzka Optima Košice";
				$this->par_file = "WW3731";
				$this->par_id="3731";
				$this->par_street = 'Cukrová 14';
				$this->par_city = '811 08 Bratislava';
				$this->par_ico = '45445320';
				$this->par_dic = 'SK2023008328';
				$this->tabulkaModel = "yuctogw3731model";

				break;

        	case '5039':
        		$this->par_firma = "Swiss Watches, s.r.o.";
				$this->par_file = "Sw5039";
				$this->par_id = "5039";

				$this->par_street = 'Pribinova 8';
				$this->par_city = '811 09 Bratislava';
				$this->par_ico = '44 975 813';
				$this->par_dic = '2022898141';
				$this->tabulkaModel = "yuctogw5039model";

				break;

        	case '5040':
				$this->par_firma = "Tag Heuer Galleria Eurovea";
				$this->par_file = "TH5040";
				$this->par_id = "5040";
				$this->par_street = 'Pribinova 8';
				$this->par_city = '811 09 Bratislava';
				$this->par_ico = '44 833 334';
				$this->par_dic = '2022849147';
				$this->tabulkaModel = "yuctogw5040model";

        		break;

        	case '5117':
        		$this->par_firma = "Swiss Watch Distribution, s.r.o.";
				$this->par_file = "SwissW5117";
				$this->par_id="5117";

				$this->par_street = 'Krajinská 1';
				$this->par_city = '82106 Bratislava';
				$this->par_ico = '45841802';
				$this->par_dic = 'SK2023109066';
				$this->tabulkaModel = "yuctogw5117model";

				break;
				
			case 'gw':
				$this->par_firma = "G-WATCH, spol. s r.o.";
				$this->par_file = "gwatch";
				$this->par_id = "gw";

				$this->par_street = 'Cukrová 14';
				$this->par_city = '811 08 Bratislava';
				$this->par_ico = '35 746 700';
				$this->par_dic = 'SK2020220257';
				$this->tabulkaModel = "yuctogwmodel";

				break;

			case 'racioba':
				$this->par_firma = "RACIO, Export-Import-Consulting, s.r.o., prev. Bratislava";
				$this->par_file = "ba";
				$this->par_id = "blava";

				$this->par_street = 'Krajinská 1';
				$this->par_city = '821 06 Bratislava';
				$this->par_ico = '31400345';
				$this->par_dic = 'SK2020821880';
				$this->tabulkaModel = "yuctomodel";
				$this->par_sklad = 'yucto.blava';

				break;

			case 'raciobb':
				$this->par_firma = "RACIO, Export-Import-Consulting, s.r.o., prev. B. Bystrica";
				$this->par_file = "bb";
				$this->par_id = "banska";

				$this->par_street = 'Krajinská 1';
				$this->par_city = '821 06 Bratislava';
				$this->par_ico = '31400345';
				$this->par_dic = 'SK2020821880';
				$this->tabulkaModel = "yuctomodel";
				$this->par_sklad = 'yucto.banska';

				break;


        	default:
        		# code...
        		break;
        }

        $this->par_head = $this->par_firma.', '.$this->par_street.', '.$this->par_city.', IČO: '.$this->par_ico.' DIČ:'.$this->par_dic;

    }
}
