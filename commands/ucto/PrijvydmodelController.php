<?php
/* 
// XLS ten najdolezitejsi
*/


namespace app\commands\ucto;

use Yii;
use yii\console\Controller;
use yii\console\ExitCode;
use app\commands\ucto\Uctoparams;

class PrijvydmodelController extends Controller
{

  //Params, use php yii prijvydmodel --firma=5040
  public $firma, $recount;
  public $ucto;
  private $TBS; //doc container


  public $prvaprijemka = 0, $prij = [], $vyd = [];
  public $prijSum = [];
  public $dobroSum = [],
  $dodaSum =  [],
  $presSum = [],
  $v113Sum = [],
  $v137Sum = [],
  $p138Sum = [], //only 5039
  $vydSum = [],
  $vdobroSum = [],
  $vdodaSum =  [],
  $vpresSum = [];

  public function options($actionID)
  {
    return array_merge(parent::options($actionID), [
      'firma', 'recount'
    ]);
  }

  private function nrRecount(){
    /// MAME len 2 prevadzku, ziadne prevody
    /// teda rovno dame nr do doc_nr
  
      //DOC_NR recount prijemky
    echo "recounting...";

      $yy = "SELECT row_number() OVER () as nn,yucto.move_id as xmove_id,
          yucto.d1 as xd1 from
          ".$this->ucto->tabulkaModel." yucto where
          yucto.move_id in (select move_id from movement) and 
          pcs <>0 and
          yucto.n_price <> 0 and
          yucto.xx = 'R'
          group by move_id,d1 order by
          yucto.d1 asc,
          yucto.move_id asc
      ";
      foreach(Yii::$app->db->createCommand( $yy  )->queryAll() as $re ){
        $p1 = $re['xmove_id'];
        $p2 = $re['nn'];
        $p4 = $re['xd1'];
        Yii::$app->db->createCommand( "update ".$this->ucto->tabulkaModel." set doc_nr= ".$p2." where move_id=".$p1." and d1='".$p4."'")->execute();
      }

      //DOC_NR recount vydajky
      $yy = "SELECT row_number() OVER () as  nn,yucto.move_id as xmove_id,
          yucto.d1 as xd1 from
          ".$this->ucto->tabulkaModel." yucto where
          yucto.move_id in (select move_id from movement) and 
          pcs <>0 and
          yucto.n_price <> 0 and
          yucto.xx = 'F'
          group by move_id,d1 order by
          yucto.d1 asc,
          yucto.move_id asc";
      foreach(Yii::$app->db->createCommand( $yy  )->queryAll() as $re ){
        $p1 = $re['xmove_id'];
        $p2 = $re['nn'];
        $p4 = $re['xd1'];
        Yii::$app->db->createCommand("update ".$this->ucto->tabulkaModel." set doc_nr= ".$p2." where move_id=".$p1." and d1='".$p4."'")->execute();
      }
    //PRECISLOVANIE PREVODOV netreba
  }

  private function getSql($xx, $mesiac){
    if( $this->ucto->par_sklad == 'yucto.blava'){
      $presuntxt = 'Presun - Banska Bystrica';
    } elseif( $this->ucto->par_sklad == 'yucto.banska') {
      $presuntxt = 'Presun - Bratislava';
    } else {
      $presuntxt = 'Presun medzi pobockami';
    }
    $select =   "
    SELECT 
    sum(yucto.n_price*".$this->ucto->par_sklad.") as spolu,
    yucto.d1,
    yucto.fnumber,
    case when movement.move_id is null then '".$presuntxt."' 
    when movement.m_type_id=117 or movement.m_type_id=131 or movement.m_type_id=146 then 'Dobropis dodavatelovi' 
    when movement.m_type_id=130 or movement.m_type_id=145 then 'Faktura'     
    when movement.m_type_id=89 then 'Prijem' 
    else  
    substring(m_type.name,0, case when position( '//' in m_type.name) > 0 then position( '//' in m_type.name)  else 255 end)
    end  \"name\",   
    case when movement.m_type_id in (24,102,110) then address.firma||', c. '||yucto.fnumber else
    address.firma end firma,
    case when movement.text1 is null then '' else movement.text1 end as text1,   
    sum( ".$this->ucto->par_sklad.") as pocetks,
    coalesce(movement.move_id,-1,movement.move_id) as xmove,
    movement.m_type_id,
    case when movement.move_id is null then doc_nr||'p' else doc_nr||'' end as nr
    FROM 
    ".$this->ucto->tabulkaModel." yucto left outer join movement on ( movement.move_id = yucto.move_id )   
    left outer join address on ( movement.adr_id = address.adr_id )  
    left outer join m_type on ( m_type.m_type_id = movement.m_type_id )
    WHERE 
    ( yucto.xx = '".$xx."' )   AND ".$this->ucto->par_sklad." <> 0
    and month(yucto.d1) = '".$mesiac."'

    group by
    yucto.d1,   
    yucto.fnumber,   
    m_type.name,   
    address.firma,   
    movement.text1,   
    movement.move_id,
    movement.m_type_id,
    yucto.doc_nr
    ORDER BY yucto.d1 ASC,   
    yucto.doc_nr ASC
    ";
      //echo $select;
    return $select;
  }



  public function actionIndex()
  {
    $this->ucto = new Uctoparams($this->firma);

    if( $this->recount ) $this->nrRecount();

    $this->TBS = new \hscstudio\export\OpenTBS; // new instance of TBS
    if( $this->ucto->par_id == "5039" ){
      $template = 'prijvyd5039eshop.xlsx';
    } else {
      $template = 'prijvyd.xlsx';
    }

    $this->TBS->LoadTemplate(__DIR__ . '/'.$template, OPENTBS_ALREADY_UTF8); 



    for( $mesiac = 1; $mesiac < 13; $mesiac++){


      $l2=1;
      foreach(Yii::$app->db->createCommand( $this->getSql('F', $mesiac)  )
        ->queryAll() as $ref ){

        if($ref['m_type_id']==113||$ref['m_type_id']==116||$ref['m_type_id']==117){
          $textik = iconv("WINDOWS-1250//IGNORE","UTF-8//IGNORE",$ref['text1']);
        } elseif ($ref['m_type_id']==130||$ref['m_type_id']==145||$ref['m_type_id']==137||$ref['m_type_id']==139||$ref['m_type_id']==147||$ref['m_type_id']==150){
          $textik = ", ".$ref['number'];
        } else {
          $textik = "";
        }
        $this->vyd[$mesiac][] = array_merge($ref,array("obchod"=>$textik));
        $l2++;
      }


//Po Mesiaci

      $sumamesiac = Yii::$app->db->createCommand(
       " SELECT        sum(yucto.n_price*".$this->ucto->par_sklad.") as sumarizacia       FROM        ".$this->ucto->tabulkaModel." yucto WHERE
       ( yucto.xx = 'F' )   AND ".$this->ucto->par_sklad."  <> 0 and month(yucto.d1) = '".$mesiac."'"
     )->queryScalar();

      $this->vydSum[$mesiac][] = $sumamesiac?array( 'sumarizacia'=>$sumamesiac):array( 'sumarizacia'=>0);
    //117 al. 131
      $sumamesiac = Yii::$app->db->createCommand(
       " SELECT        sum(yucto.n_price*".$this->ucto->par_sklad.") as sumarizacia       FROM        ".$this->ucto->tabulkaModel." yucto
       join movement m on m.move_id=yucto.move_id WHERE
       m.m_type_id in (116,117,131,146) and
       ( yucto.xx = 'F' )   AND ".$this->ucto->par_sklad."  <> 0 and month(yucto.d1) = '".$mesiac."'"
     )->queryScalar();
      $this->vdobroSum[$mesiac][] = $sumamesiac?$sumamesiac:0;


     //138 eshop dobropis a pridany export 140 - len 5039
      $sumamesiac = Yii::$app->db->createCommand(
       " SELECT        sum(yucto.n_price*".$this->ucto->par_sklad.") as sumarizacia       FROM        ".$this->ucto->tabulkaModel." yucto
       join movement m on m.move_id=yucto.move_id WHERE
       m.m_type_id in (138,140) and
       ( yucto.xx = 'R' )   AND ".$this->ucto->par_sklad."  <> 0 and month(yucto.d1) = '".$mesiac."'"
     )->queryScalar();
      $this->p138Sum[$mesiac][] = $sumamesiac?$sumamesiac:0;

    //87
      $sumamesiac = Yii::$app->db->createCommand(
       " SELECT        sum(yucto.n_price*".$this->ucto->par_sklad.") as sumarizacia       FROM        ".$this->ucto->tabulkaModel." yucto
       join movement m on m.move_id=yucto.move_id WHERE
       m.m_type_id in (87,24,102,114,110,167,169,149) and
       ( yucto.xx = 'F' )   AND ".$this->ucto->par_sklad."  <> 0 and month(yucto.d1) = '".$mesiac."'"
     )->queryScalar();
      $this->vdodaSum[$mesiac][] = $sumamesiac?$sumamesiac:0;


    //14 - ToDo asi presuny su pri move_id=-1
      $sumamesiac = Yii::$app->db->createCommand(
       " SELECT        sum(yucto.n_price*".$this->ucto->par_sklad.") as sumarizacia       FROM        ".$this->ucto->tabulkaModel." yucto
       WHERE  move_id<=0 and
       ( yucto.xx = 'F' )   AND ".$this->ucto->par_sklad."  <> 0 and month(yucto.d1) = '".$mesiac."'"
     )->queryScalar();
      $this->vpresSum[$mesiac][] = $sumamesiac?$sumamesiac:0;

    //113 al. 130 - faktury od dod pri racio vsetko, pridane 156,147 pre 5040, 150 pre 5039, 
      $sumamesiac = Yii::$app->db->createCommand(
       " SELECT        sum(yucto.n_price*".$this->ucto->par_sklad.") as sumarizacia       FROM        ".$this->ucto->tabulkaModel." yucto
       join movement m on m.move_id=yucto.move_id WHERE
       m.m_type_id in (113,130, 145, 156, 147, 150, 154, 152) and
       ( yucto.xx = 'F' )   AND ".$this->ucto->par_sklad."  <> 0 and month(yucto.d1) = '".$mesiac."'"
     )->queryScalar();
      $this->v113Sum[$mesiac][] = $sumamesiac?$sumamesiac:0;

    //137 eshop fakt a pridany aj export eshop fakt 139 len 5039
      $sumamesiac = Yii::$app->db->createCommand(
       " SELECT        sum(yucto.n_price*".$this->ucto->par_sklad.") as sumarizacia       FROM        ".$this->ucto->tabulkaModel." yucto
       join movement m on m.move_id=yucto.move_id WHERE
       m.m_type_id in (137,139) and
       ( yucto.xx = 'F' )   AND ".$this->ucto->par_sklad."  <> 0 and month(yucto.d1) = '".$mesiac."'"
     )->queryScalar();
      $this->v137Sum[$mesiac][] = $sumamesiac?$sumamesiac:0;


}//dalsi mesiac






for( $mesiac = 1; $mesiac < 13; $mesiac++){

  $l2=1;
  foreach(Yii::$app->db->createCommand( $this->getSql('R', $mesiac)  )
    ->queryAll() as $ref ){
    $this->prij[$mesiac][] = array_merge($ref,array("obchod"=>$ref['text1']));
  $l2++;
}


//Po Mesiaci

$sumamesiac = Yii::$app->db->createCommand(
 " SELECT        sum(yucto.n_price*".$this->ucto->par_sklad.") as sumarizacia       FROM        ".$this->ucto->tabulkaModel." yucto WHERE
 ( yucto.xx = 'R' )   AND ".$this->ucto->par_sklad."  <> 0 and month(yucto.d1) = '".$mesiac."'"
)->queryScalar();
$this->prijSum[$mesiac][] = $sumamesiac?array( 'sumarizacia'=>$sumamesiac):array( 'sumarizacia'=>0);
    //97 131, dobro pridane 157,148 pre 5040, 155 pre 5039, 153 pre 3731
$sumamesiac = Yii::$app->db->createCommand(
 " SELECT        sum(yucto.n_price*".$this->ucto->par_sklad.") as sumarizacia       FROM        ".$this->ucto->tabulkaModel." yucto
 join movement m on m.move_id=yucto.move_id WHERE
 m.m_type_id in (69,97,109,131,146,157,148, 155, 153, 151, 168, 170) and
 ( yucto.xx = 'R' )   AND ".$this->ucto->par_sklad."  <> 0 and month(yucto.d1) = '".$mesiac."'"
)->queryScalar();
$this->dobroSum[$mesiac][] = $sumamesiac?$sumamesiac:0;
    //89 134
$sumamesiac = Yii::$app->db->createCommand(
 " SELECT        sum(yucto.n_price*".$this->ucto->par_sklad.") as sumarizacia       FROM        ".$this->ucto->tabulkaModel." yucto
 join movement m on m.move_id=yucto.move_id WHERE
 m.m_type_id in (89,134,86) and
 ( yucto.xx = 'R' )   AND ".$this->ucto->par_sklad."  <> 0 and month(yucto.d1) = '".$mesiac."'"
)->queryScalar();
$this->dodaSum[$mesiac][] = $sumamesiac?$sumamesiac:0;


    //14 - ToDo asi presuny su pri move_id=-1
$sumamesiac = Yii::$app->db->createCommand(
 " SELECT        sum(yucto.n_price*".$this->ucto->par_sklad.") as sumarizacia       FROM        ".$this->ucto->tabulkaModel." yucto
 WHERE  move_id<=0 and 
 ( yucto.xx = 'R' )   AND ".$this->ucto->par_sklad."  <> 0 and month(yucto.d1) = '".$mesiac."'"
)->queryScalar();
$this->presSum[$mesiac][] = $sumamesiac?$sumamesiac:0;


}//dalsi mesiac



//prvaprijemka - POZOR, rata ze prva prijemka teda zostatky je 1.1. spravena a nic ine tam nie je ako prijem
$this->prvaprijemka = Yii::$app->db->createCommand(
 " SELECT        sum(yucto.n_price*".$this->ucto->par_sklad.") as sumarizacia       FROM        ".$this->ucto->tabulkaModel." yucto
 WHERE
 yucto.xx = 'R'   AND yucto.d1 = '".$this->ucto->par_rok."-01-01'"
)->queryScalar();



// ----------------- 
// nastav premenne
// ----------------- 
$this->TBS->VarRef['par_rok'] = $this->ucto->par_rok;
$this->TBS->VarRef['par_firma'] = $this->ucto->par_firma;
$this->TBS->VarRef['par_street'] = $this->ucto->par_street;
$this->TBS->VarRef['par_city'] = $this->ucto->par_city;
$this->TBS->VarRef['par_ico'] = $this->ucto->par_ico;
$this->TBS->VarRef['par_dic'] = $this->ucto->par_dic;

$this->TBS->VarRef['sp1'] = $this->prijSum[1][0]['sumarizacia'];
$this->TBS->VarRef['sp2'] = $this->prijSum[2][0]['sumarizacia'];
$this->TBS->VarRef['sp3'] = $this->prijSum[3][0]['sumarizacia'];
$this->TBS->VarRef['sp4'] = $this->prijSum[4][0]['sumarizacia'];
$this->TBS->VarRef['sp5'] = $this->prijSum[5][0]['sumarizacia'];
$this->TBS->VarRef['sp6'] = $this->prijSum[6][0]['sumarizacia'];
$this->TBS->VarRef['sp7'] = $this->prijSum[7][0]['sumarizacia'];
$this->TBS->VarRef['sp8'] = $this->prijSum[8][0]['sumarizacia'];
$this->TBS->VarRef['sp9'] = $this->prijSum[9][0]['sumarizacia'];
$this->TBS->VarRef['sp10'] = $this->prijSum[10][0]['sumarizacia'];
$this->TBS->VarRef['sp11'] = $this->prijSum[11][0]['sumarizacia'];
$this->TBS->VarRef['sp12'] = $this->prijSum[12][0]['sumarizacia'];

$this->TBS->VarRef['zostatky'] = $this->prvaprijemka;
$this->TBS->VarRef['sv1'] = $this->vydSum[1][0]['sumarizacia'];
$this->TBS->VarRef['sv2'] = $this->vydSum[2][0]['sumarizacia'];
$this->TBS->VarRef['sv3'] = $this->vydSum[3][0]['sumarizacia'];
$this->TBS->VarRef['sv4'] = $this->vydSum[4][0]['sumarizacia'];
$this->TBS->VarRef['sv5'] = $this->vydSum[5][0]['sumarizacia'];
$this->TBS->VarRef['sv6'] = $this->vydSum[6][0]['sumarizacia'];
$this->TBS->VarRef['sv7'] = $this->vydSum[7][0]['sumarizacia'];
$this->TBS->VarRef['sv8'] = $this->vydSum[8][0]['sumarizacia'];
$this->TBS->VarRef['sv9'] = $this->vydSum[9][0]['sumarizacia'];
$this->TBS->VarRef['sv10'] = $this->vydSum[10][0]['sumarizacia'];
$this->TBS->VarRef['sv11'] = $this->vydSum[11][0]['sumarizacia'];
$this->TBS->VarRef['sv12'] = $this->vydSum[12][0]['sumarizacia'];

$this->TBS->VarRef['pdobro1'] = $this->dobroSum[1][0];
$this->TBS->VarRef['pdobro2'] = $this->dobroSum[2][0];
$this->TBS->VarRef['pdobro3'] = $this->dobroSum[3][0];
$this->TBS->VarRef['pdobro4'] = $this->dobroSum[4][0];
$this->TBS->VarRef['pdobro5'] = $this->dobroSum[5][0];
$this->TBS->VarRef['pdobro6'] = $this->dobroSum[6][0];
$this->TBS->VarRef['pdobro7'] = $this->dobroSum[7][0];
$this->TBS->VarRef['pdobro8'] = $this->dobroSum[8][0];
$this->TBS->VarRef['pdobro9'] = $this->dobroSum[9][0];
$this->TBS->VarRef['pdobro10'] = $this->dobroSum[10][0];
$this->TBS->VarRef['pdobro11'] = $this->dobroSum[11][0];
$this->TBS->VarRef['pdobro12'] = $this->dobroSum[12][0];

$this->TBS->VarRef['pdoda1'] = $this->dodaSum[1][0] - $this->prvaprijemka;
$this->TBS->VarRef['pdoda2'] = $this->dodaSum[2][0];
$this->TBS->VarRef['pdoda3'] = $this->dodaSum[3][0];
$this->TBS->VarRef['pdoda4'] = $this->dodaSum[4][0];
$this->TBS->VarRef['pdoda5'] = $this->dodaSum[5][0];
$this->TBS->VarRef['pdoda6'] = $this->dodaSum[6][0];
$this->TBS->VarRef['pdoda7'] = $this->dodaSum[7][0];
$this->TBS->VarRef['pdoda8'] = $this->dodaSum[8][0];
$this->TBS->VarRef['pdoda9'] = $this->dodaSum[9][0];
$this->TBS->VarRef['pdoda10'] = $this->dodaSum[10][0];
$this->TBS->VarRef['pdoda11'] = $this->dodaSum[11][0];
$this->TBS->VarRef['pdoda12'] = $this->dodaSum[12][0];

$this->TBS->VarRef['ppres1'] = $this->presSum[1][0];
$this->TBS->VarRef['ppres2'] = $this->presSum[2][0];
$this->TBS->VarRef['ppres3'] = $this->presSum[3][0];
$this->TBS->VarRef['ppres4'] = $this->presSum[4][0];
$this->TBS->VarRef['ppres5'] = $this->presSum[5][0];
$this->TBS->VarRef['ppres6'] = $this->presSum[6][0];
$this->TBS->VarRef['ppres7'] = $this->presSum[7][0];
$this->TBS->VarRef['ppres8'] = $this->presSum[8][0];
$this->TBS->VarRef['ppres9'] = $this->presSum[9][0];
$this->TBS->VarRef['ppres10'] = $this->presSum[10][0];
$this->TBS->VarRef['ppres11'] = $this->presSum[11][0];
$this->TBS->VarRef['ppres12'] = $this->presSum[12][0];

$this->TBS->VarRef['vdobro1'] = $this->vdobroSum[1][0];
$this->TBS->VarRef['vdobro2'] = $this->vdobroSum[2][0];
$this->TBS->VarRef['vdobro3'] = $this->vdobroSum[3][0];
$this->TBS->VarRef['vdobro4'] = $this->vdobroSum[4][0];
$this->TBS->VarRef['vdobro5'] = $this->vdobroSum[5][0];
$this->TBS->VarRef['vdobro6'] = $this->vdobroSum[6][0];
$this->TBS->VarRef['vdobro7'] = $this->vdobroSum[7][0];
$this->TBS->VarRef['vdobro8'] = $this->vdobroSum[8][0];
$this->TBS->VarRef['vdobro9'] = $this->vdobroSum[9][0];
$this->TBS->VarRef['vdobro10'] = $this->vdobroSum[10][0];
$this->TBS->VarRef['vdobro11'] = $this->vdobroSum[11][0];
$this->TBS->VarRef['vdobro12'] = $this->vdobroSum[12][0];

$this->TBS->VarRef['vmop1'] = $this->vdodaSum[1][0];
$this->TBS->VarRef['vmop2'] = $this->vdodaSum[2][0];
$this->TBS->VarRef['vmop3'] = $this->vdodaSum[3][0];
$this->TBS->VarRef['vmop4'] = $this->vdodaSum[4][0];
$this->TBS->VarRef['vmop5'] = $this->vdodaSum[5][0];
$this->TBS->VarRef['vmop6'] = $this->vdodaSum[6][0];
$this->TBS->VarRef['vmop7'] = $this->vdodaSum[7][0];
$this->TBS->VarRef['vmop8'] = $this->vdodaSum[8][0];
$this->TBS->VarRef['vmop9'] = $this->vdodaSum[9][0];
$this->TBS->VarRef['vmop10'] = $this->vdodaSum[10][0];
$this->TBS->VarRef['vmop11'] = $this->vdodaSum[11][0];
$this->TBS->VarRef['vmop12'] = $this->vdodaSum[12][0];

$this->TBS->VarRef['vpresu1'] = $this->vpresSum[1][0];
$this->TBS->VarRef['vpresu2'] = $this->vpresSum[2][0];
$this->TBS->VarRef['vpresu3'] = $this->vpresSum[3][0];
$this->TBS->VarRef['vpresu4'] = $this->vpresSum[4][0];
$this->TBS->VarRef['vpresu5'] = $this->vpresSum[5][0];
$this->TBS->VarRef['vpresu6'] = $this->vpresSum[6][0];
$this->TBS->VarRef['vpresu7'] = $this->vpresSum[7][0];
$this->TBS->VarRef['vpresu8'] = $this->vpresSum[8][0];
$this->TBS->VarRef['vpresu9'] = $this->vpresSum[9][0];
$this->TBS->VarRef['vpresu10'] = $this->vpresSum[10][0];
$this->TBS->VarRef['vpresu11'] = $this->vpresSum[11][0];
$this->TBS->VarRef['vpresu12'] = $this->vpresSum[12][0];

$this->TBS->VarRef['vdoda1'] = $this->v113Sum[1][0];
$this->TBS->VarRef['vdoda2'] = $this->v113Sum[2][0];
$this->TBS->VarRef['vdoda3'] = $this->v113Sum[3][0];
$this->TBS->VarRef['vdoda4'] = $this->v113Sum[4][0];
$this->TBS->VarRef['vdoda5'] = $this->v113Sum[5][0];
$this->TBS->VarRef['vdoda6'] = $this->v113Sum[6][0];
$this->TBS->VarRef['vdoda7'] = $this->v113Sum[7][0];
$this->TBS->VarRef['vdoda8'] = $this->v113Sum[8][0];
$this->TBS->VarRef['vdoda9'] = $this->v113Sum[9][0];
$this->TBS->VarRef['vdoda10'] = $this->v113Sum[10][0];
$this->TBS->VarRef['vdoda11'] = $this->v113Sum[11][0];
$this->TBS->VarRef['vdoda12'] = $this->v113Sum[12][0];

//5039 Len tu je eshop: 
    if( $this->ucto->par_id == "5039" ){

  $this->TBS->VarRef['eshopf1'] = $this->v137Sum[1][0];
  $this->TBS->VarRef['eshopf2'] = $this->v137Sum[2][0];
  $this->TBS->VarRef['eshopf3'] = $this->v137Sum[3][0];
  $this->TBS->VarRef['eshopf4'] = $this->v137Sum[4][0];
  $this->TBS->VarRef['eshopf5'] = $this->v137Sum[5][0];
  $this->TBS->VarRef['eshopf6'] = $this->v137Sum[6][0];
  $this->TBS->VarRef['eshopf7'] = $this->v137Sum[7][0];
  $this->TBS->VarRef['eshopf8'] = $this->v137Sum[8][0];
  $this->TBS->VarRef['eshopf9'] = $this->v137Sum[9][0];
  $this->TBS->VarRef['eshopf10'] = $this->v137Sum[10][0];
  $this->TBS->VarRef['eshopf11'] = $this->v137Sum[11][0];
  $this->TBS->VarRef['eshopf12'] = $this->v137Sum[12][0];

  $this->TBS->VarRef['eshopr1'] = $this->p138Sum[1][0];
  $this->TBS->VarRef['eshopr2'] = $this->p138Sum[2][0];
  $this->TBS->VarRef['eshopr3'] = $this->p138Sum[3][0];
  $this->TBS->VarRef['eshopr4'] = $this->p138Sum[4][0];
  $this->TBS->VarRef['eshopr5'] = $this->p138Sum[5][0];
  $this->TBS->VarRef['eshopr6'] = $this->p138Sum[6][0];
  $this->TBS->VarRef['eshopr7'] = $this->p138Sum[7][0];
  $this->TBS->VarRef['eshopr8'] = $this->p138Sum[8][0];
  $this->TBS->VarRef['eshopr9'] = $this->p138Sum[9][0];
  $this->TBS->VarRef['eshopr10'] = $this->p138Sum[10][0];
  $this->TBS->VarRef['eshopr11'] = $this->p138Sum[11][0];
  $this->TBS->VarRef['eshopr12'] = $this->p138Sum[12][0];

 }

//$this->TBS->VarRef['zostatky'] || $this->TBS->VarRef['zostatky'] = -999999;



for( $mesiac = 1; $mesiac < 13; $mesiac++){
    // -------------------------------------------- 
    // Merging and other operations on the template 
    // -------------------------------------------- 
  $this->TBS->PlugIn(OPENTBS_SELECT_SHEET, sprintf("P%u",$mesiac)); 
  $this->TBS->MergeBlock('p', is_null($this->prij[$mesiac])?[]:$this->prij[$mesiac]); 
  $this->TBS->MergeBlock('ps', $this->prijSum[$mesiac]); 
}
for( $mesiac = 1; $mesiac < 13; $mesiac++){
    // -------------------------------------------- 
    // Merging and other operations on the template 
    // -------------------------------------------- 
  $this->TBS->PlugIn(OPENTBS_SELECT_SHEET, sprintf("V%u",$mesiac)); 
  $this->TBS->MergeBlock('p', is_null($this->vyd[$mesiac])?[]:$this->vyd[$mesiac]); 
  $this->TBS->MergeBlock('ps', $this->vydSum[$mesiac]); 
}



// ----------------- 
// Output the result 
// ----------------- 
// print all the available keys for the arrays of variables
// print_r($this->prij[1]);
// print_r(compact(array_keys(get_defined_vars())));
// print_r($this->v113Sum);
// print_r($this->vpresSum);
// print_r($this->vdodaSum);
// print_r($this->vdobroSum);
// print_r($this->presSum);
// print_r($this->dodaSum);
// print_r($this->presSum);
// print_r($this->dobroSum);
//print_r($this->vydSum);

//print_r(array_keys(get_defined_vars()));
$output_file_name = str_replace('.', '_'.$this->ucto->tabulkaModel.$this->ucto->par_id.'Model.', $template); 
  // Output the result as a file on the server. 
  $this->TBS->Show(OPENTBS_FILE, __DIR__ .'/out/'. $output_file_name); // Also merges all [onshow] automatic fields. 

echo __DIR__ .'/out/'.$output_file_name;
print_r($this->ucto);
echo $this->ucto->par_id;
echo $template;
  return ExitCode::OK;
}
}

