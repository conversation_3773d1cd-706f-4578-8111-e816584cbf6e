<?php

namespace app\commands\ucto;

use Yii;
use yii\console\Controller;
use yii\console\ExitCode;
use app\commands\ucto\Uctoparams;
use TCPDF;


class TestpdfController extends Controller
{

  public $firma;
  private $pdf, $ucto;
  private $kodpcs, $nprice,$xcurrency, $mytab;
  private $kodkod, $kodpata, $kodname, $riadok;

  public function options($actionID)
  {
    return array_merge(parent::options($actionID), [
      'firma'
    ]);
  }

  private function echo_group1( ){

     $tbl = '<br><br><br><br><table width="100%" border="0" cellpadding="1" cellspacing="1"><thead><tr style="background-color:#676767;color:#ffffff">';
	$stl = 0;
     foreach($this->mytab[0] as $header=>$value){
	    if( in_array($stl,[1,4,5,8]) ) {
		$tbl .= '<td  width="33"  align="center">'.$header.'</td>';
	    } elseif( $stl == 2 ){
		$tbl .= '<td  width="150"  align="center">'.$header.'</td>';
	    } else {
		$tbl .= '<td align="center">'.$header.'</td>';
	    }
	    $stl++;
     }
    $tbl .= '</tr></thead>';
     $linenr = 1;
     foreach($this->mytab as $xline){
	$tbl .= '<tr style="background-color:';
	$tbl .= $linenr % 2 ? '#ededed">':'#fafafa">';
	$stl = 0;
	$linenr++;
	foreach($xline as $value) {
	    if( in_array($stl,[1,4,5,8]) ) {
		$tbl .= '<td width="33" align="center">'.$value.'</td>';
	    } elseif( $stl == 2 ){
		$tbl .= '<td width="150" align="right">'.$value.'</td>';
	    } else {
		$tbl .= '<td align="right">'.$value.'</td>';
	    }
	    $stl++;
	}
	$tbl .= "</tr>";
     }
	$tbl .= "</table>";

/*
      $this->pdf->ezTable($this->mytab,'','',
        array( 'width'=>500, 'fontSize' => 5, 'showHeadings'=>1,'shaded'=> 2,'showLines'=> 0,
        'shadeCol' => array(0.98,0.98,0.98),'shadeCol2' => array(0.95,0.95,0.95),
         'cols'=>array(
        'Skl. cena'=>array('justification'=>'right'),
        'Prijat? EUR'=>array('justification'=>'right'),
        'Vydan? EUR'=>array('justification'=>'right'),
        'Zostatok EUR'=>array('justification'=>'right')
            )
          ));
*/
//    echo $tbl;
//exit;
    $this->pdf->SetFont('dejavusans', '', 5);
    $this->pdf->writeHTML($tbl, true, false, false, false, '');
    $this->pdf->SetFont('dejavusans', '', 8);
    $this->pdf->Write(0, sprintf(" Zostatok pre nákupnú cenu %11.2f %s: %d ks %11.2f %s \n",$this->nprice,$this->xcurrency, $this->kodpcs, $this->nprice*$this->kodpcs, $this->xcurrency), '', 0, 'R', true, 0, false, false, 0);
    //$this->pdf->writeHTML("<div align='right'>".sprintf(" Zostatok pre nákupnú cenu %11.2f %s: %d ks %11.2f %s \n",$this->nprice,$this->xcurrency, $this->kodpcs, $this->nprice*$this->kodpcs, $this->xcurrency)."</div><br><br>" , true, false, false, false, '');
           
//      $this->pdf->ezText( sprintf(" Zostatok pre n?kupn? cenu %11.2f %s: %d ks %11.2f %s \n",$this->nprice,$this->xcurrency, $this->kodpcs, $this->nprice*$this->kodpcs, $this->xcurrency), 8, array('justification' => 'right'));
      $this->kodpcs = 0;
        
  }


  public function actionIndex()
  {

    $this->ucto = new Uctoparams($this->firma);

    // Initialize a ROS PDF class object using DIN-A4, with background color gray
    //$this->pdf = new pdfphp\src\Cezpdf('a4','portrait','color',[0.8,0.8,0.8]);
    $this->pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

// set document information
$this->pdf->SetCreator(PDF_CREATOR);
$this->pdf->SetAuthor('Firma');
$this->pdf->SetTitle('Skladove karty');
$this->pdf->SetSubject('Skladove karty');
$this->pdf->SetKeywords('Internal, Confidential, PDF');
    $this->pdf->SetFont('dejavusans', '', 12);

// set docume

    $this->xcurrency = 'EUR';

    // Select the font
    //$this->pdf->selectFont('Times-Roman');
// set header and footer fonts
$this->pdf->setPrintHeader(false);
$this->pdf->setFooterFont(Array(PDF_FONT_NAME_DATA, '', PDF_FONT_SIZE_DATA));


// set margins
$this->pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
$this->pdf->SetFooterMargin(PDF_MARGIN_FOOTER);

// add a page
$this->pdf->AddPage();

// print a block of text using Write()
$this->pdf->Write(0, $this->ucto->par_head, '', 0, 'C', true, 0, false, false, 0);
    $this->pdf->SetFont('dejavusans', '', 18);
    $this->pdf->writeHTML("<br><br><h1>Skladové karty, rok ".$this->ucto->par_rok."</h1>", true, false, false, false, '');

    //Hlava a pata
    //$this->pdf -> ezSetMargins(50,90,50,50);
//    $all = $this->pdf->openObject();
//    $this->pdf->saveState();
//    $this->pdf->setStrokeColor(0,0,0,1);
//    $this->pdf->addText(60,802,9,$par_head); 
//    $this->pdf->line(20,40,578,40);
//    $this->pdf->line(20,800,578,800);
//    $this->pdf->addText(50,34,6,'');
//    $this->pdf->restoreState();
//    $this->pdf->closeObject();
      //Koniec
//    $this->pdf->addObject($all,'all');                
//    $this->pdf->ezStartPageNumbers(500,28,10,'',_("Strana").' {PAGENUM} / {TOTALPAGENUM}',1);
//    $this->pdf->addText(160,750,12,'Skladov? karty, rok <b>'.$this->ucto->par_rok.'</b>');

//    $this->pdf->ezSetDy(-80); 


    $this->nprice = -999;
    $this->riadok = 0;
    $this->mytab = [];
    $this->kodname = '';
    $this->kodkod = '';
    $this->kodpcs = 0;
    $this->kodpata = '';
          
    $yy = "SELECT
                yucto.n_price Skladova_cena,
                yucto.d1 Datum,
      case when movement.m_type_id=89 or  movement.m_type_id=117  then movement.text1
      when  movement.m_type_id=97 then 'Maloobch. dobropis'
      when movement.m_type_id=87 then 'Maloobch. predaj'
      else address.firma||' '||movement.text1
      end  as  Dodavatel_Odberatel,

                case when movement.move_id is null then  doc_nr||'p' else doc_nr||'' end as Cislo_dokladu,
                yucto.pcs Pocet,
                yucto.pcs*yucto.n_price Hodnota,
                ( SELECT sum(pcs) as Zostatok from ".$this->ucto->tabulkaModel." yugw where mat_id=yucto.mat_id and n_price=yucto.n_price and d1<=yucto.d1 and matid<=yucto.matid ),
                ( SELECT sum(pcs)*yucto.n_price as HodnotaZostatku from ".$this->ucto->tabulkaModel." yugw where mat_id=yucto.mat_id and n_price=yucto.n_price and d1<=yucto.d1 and matid<=yucto.matid),
                yucto.xx,
                yucto.mat_id xKod,
                p.model SkladovaKarta,
                yucto.remains,
                yucto.pcs,
                yucto.matid,
                m_type.name,
                coalesce(movement.move_id,-1) as xmove,
                yucto.mat_id
                FROM product p join
                ".$this->ucto->tabulkaModel." yucto on yucto.mat_id=p.mat_id left outer join movement on (movement.move_id = yucto.move_id )
                left outer join address on (movement.adr_id = address.adr_id )
                left outer join m_type  on (m_type.m_type_id = movement.m_type_id )
                 
                WHERE  ".$this->ucto->par_sklad." <> 0
                  and xx<>'!'
                ORDER BY p.model, n_price, yucto.d1, yucto.matid
         ";
    if( $this->ucto->par_id == 'banska' || $this->ucto->par_id == 'blava' ) {
        $yy = "SELECT
                yucto.n_price Skladova_cena,
                yucto.d1 Datum,
     case when yucto.move_id<0 and sum(blava)>sum(banska) and 'blava'='".$this->ucto->par_id."' then 'prevod z prevadzky Banska Bystrica'
     when yucto.move_id<0 and sum(banska)<sum(blava) and 'banska'='".$this->ucto->par_id."' then 'prevod na prevadzku Bratislava'
     when yucto.move_id<0 and sum(blava)<sum(banska) and 'blava'='".$this->ucto->par_id."' then 'prevod na prevadzku Banska Bystrica'
     when yucto.move_id<0 and sum(banska)>sum(blava) and 'banska'='".$this->ucto->par_id."' then  'prevod z prevadzky Bratislava'
     when movement.m_type_id=87 then address.firma
     when movement.m_type_id=24 or movement.m_type_id=110 or movement.m_type_id=102 or movement.m_type_id=112 or 
         movement.m_type_id=114 or movement.m_type_id=69 or movement.m_type_id=116 then
         address.firma||' '||cast(movement.number as varchar(255))
         else address.firma||' '||movement.text1
     end  as  Dodavatel_Odberatel,

                case when movement.move_id is null then  doc_nr||'p' else doc_nr||'' end as cislo_dokladu,
                sum(yucto.".$this->ucto->par_id.") Pocet,
                sum(yucto.".$this->ucto->par_id."*yucto.n_price) Hodnota,
                ( SELECT sum(".$this->ucto->par_id.") as Zostatok from ".$this->ucto->tabulkaModel." yugw where kod=yucto.kod and n_price=yucto.n_price and d1<=yucto.d1 and matid<=min(yucto.matid) ),
                ( SELECT sum(".$this->ucto->par_id.")*yucto.n_price as HodnotaZostatku from ".$this->ucto->tabulkaModel." yugw where kod=yucto.kod and n_price=yucto.n_price and d1<=yucto.d1 and 
		    matid<=min(yucto.matid)),
                yucto.xx,
                yucto.mat_id xKod,
                p.model SkladovaKarta,
                sum(yucto.remains),
                sum(yucto.".$this->ucto->par_id."),
                min(yucto.matid) xmatid,
                m_type.name,
                coalesce(movement.move_id,-1) as xmove,
                yucto.mat_id
                FROM product p join
                ".$this->ucto->tabulkaModel." yucto on yucto.mat_id=p.mat_id left outer join movement on (movement.move_id = yucto.move_id )
                left outer join address on (movement.adr_id = address.adr_id )
                left outer join m_type  on (m_type.m_type_id = movement.m_type_id )
                 
                WHERE 
                   xx<>'!'
		group by yucto.n_price,yucto.d1, movement.move_id,yucto.xx,m_type.name,movement.text1,yucto.kod,p.mat_id,
			address.firma,movement.number,movement.m_type_id,
		       yucto.move_id,yucto.doc_nr,p.model,yucto.mat_id
			having sum(yucto.".$this->ucto->par_id.")<>0
                ORDER BY p.model, n_price, yucto.d1, yucto.xx desc, xmatid;


         ";

    }

//echo $yy;
//exit;

         $iii = 0;     
        foreach(Yii::$app->db->createCommand( $yy  )
            ->queryAll() as $result ){
//print_r($result);
          $iii++;
          if( $iii > 2999000 ) break;
            //KOD grupa;
            if( $this->kodname <> '' ) {
		// add a page
		$this->pdf->AddPage();
	        $this->pdf->SetFont('dejavusans', '', 12);
		$this->pdf->Write(0, "Skladová karta: ".$this->kodname, '', 0, 'L', true, 0, false, false, 0);  
              //$this->pdf->ezText( "Skladov? karta: $kodname", 12, array('justification' => 'left'));
            }
            $this->kodname = $this->kodpata = '';

            if( $result['xkod'] <> $this->kodkod ){
              $this->kodname = $result['skladovakarta'];
              
            }

            if( $result['skladova_cena'] <> $this->nprice && $this->riadok > 0 ){
                //  echo $this->kodname.' => ';
              $this->echo_group1( );
              $this->mytab = [];     
            }

            $this->nprice = $result['skladova_cena'];                     
            $this->riadok++;
            $this->kodkod = $result['xkod'];
            $this->kodpcs += $result['pocet'];

            $this->mytab[] = array( "Skl. cena EUR"=> $result['skladova_cena'],
                      "Dátum"=>$result['datum'],
                      /* nechce tie kolonky v tejto firme */
        //              "Dodavatel/ Odberatel"=>$result['Dodavatel_Odberatel'],
                      "Dodávateľ/ Odberateľ"=>$result['dodavatel_odberatel'],
                      "Doklad"=>$result['Pocet']>0?$result['cislo_dokladu'].$_GET['s']:$result['cislo_dokladu'],
                     // "."=>'',
                      "Prijaté ks"=>$result['pocet']>0?$result['pocet']:'',
                      "Vydané ks"=>$result['pocet']>0?'':$result['pocet'],
                      "Prijaté EUR"=>$result['hodnota']>0?sprintf("%11.2f",$result['hodnota']):'',
                      "Vydané EUR"=>$result['hodnota']>0?'':sprintf("%11.2f",$result['hodnota']),
                      "Zostatok ks"=>$this->kodpcs,
                      "Zostatok EUR"=>sprintf("%11.2f",$this->kodpcs*$result['skladova_cena'])
                  );
              
          }


          //Este raz grupa
        //  echo "?????ideme dalej";
          $this->echo_group1( );
        //  echo "ideme dalej";
          
          //$this->pdf->ezText( $this->kodpata."\n", 12, array('justification' => 'right'));
	    $this->pdf->SetFont('dejavusans', '', 8);
	  $this->pdf->Write(0, $this->kodpata."\n", '', 0, 'L', true, 0, false, false, 0);
//          $pdfcode = $this->pdf->ezOutput();

          $this->pdf->Output("/tmp/".$this->ucto->par_file."_".$this->ucto->par_rok."_skl_karta_model.pdf",'I');
//          fwrite($fpx,$pdfcode);
//          fclose($fpx);                                                                              

// Output the pdf as stream, but uncompress
// $pdf->ezStream(['compress'=>0]);

    return ExitCode::OK;
  }
}

