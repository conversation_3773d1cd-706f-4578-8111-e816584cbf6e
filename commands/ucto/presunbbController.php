<?php
/* 
// presun do bb nech tam je stock
*/



namespace app\commands\ucto;

use Yii;
use yii\console\Controller;
use yii\console\ExitCode;

class PresunbbController extends Controller
{


  //PRESUN DO SUBORU LEBO NEMAM DOBRY PHP cmd
  public $novematid = 0;


  private function gwucto_move( $from, $to, $kod, $price, $pcs, $datum ){ //namiesto procedury, lebo ta brala len 97-tky

     $query = "select mat_id from yuctomodel g where kod='".$kod."' and n_price=".$price." /* and d1<='".$datum."' */ and ".$from." > 0 order by d1 limit 1";
     //echo $query;
    $mat_id =  Yii::$app->db->createCommand($query)->queryScalar();
     echo "insert into yuctomodel(xx,kod,n_price,s_price,d1,number,pcs,remains,move_id,repli_id,mat_id,".$from.") values  ('F','".$kod."',".$price.",0,'".$datum."',".$this->novematid.",-".$pcs.",0,-1,0,'".$mat_id."',-".$pcs.");\n";
     echo "insert into yuctomodel(xx,kod,n_price,s_price,d1,number,pcs,remains,move_id,repli_id,mat_id,".$to.") values  ('R','".$kod."',".$price.",0,'".$datum."',".$this->novematid.",".$pcs.",0,-1,0,'".$mat_id."',".$pcs.");\n";
     $this->novematid++;

  }



  public function actionIndex()
  {

    //ZACINAME
    ob_start();  

    $this->novematid =  Yii::$app->db->createCommand("SELECT max(matid)+1 from yuctomodel")->queryScalar();

    //TU ZADAJ POTREBNE PRESUNY
    $this->gwucto_move( "blava","banska","221865",633.11,1,"2020-10-25"); // 5697.99 0.00  5697.99 WTHS WBK1312.BA0652
    $this->gwucto_move( "blava","banska","221866",584.11,1,"2020-10-25"); // 2920.55 0.00  2920.55 WTHS WBK1312.FC8259


    fwrite(fopen('/tmp/presunBB.sql','w'), ob_get_contents());
    ob_end_flush();

    echo "DONE  pozri subor /tmp/*.sql" ;

    return ExitCode::OK;
  }
}




