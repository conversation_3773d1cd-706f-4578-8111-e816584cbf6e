<?php

namespace app\commands\ucto;

use Yii;
use yii\console\Controller;
use yii\console\ExitCode;
use app\commands\ucto\Uctoparams;
use app\commands\ucto\pdfphp\src\Cezpdf;
use app\commands\ucto\pdfphp\src\Cpdf;


class SkladovakartaController extends Controller
{

  public $firma;
  private $pdf, $ucto;
  private $kodpcs, $nprice,$xcurrency, $mytab;
  private $kodkod, $kodpata, $kodname, $riadok;

  public function options($actionID)
  {
    return array_merge(parent::options($actionID), [
      'firma'
    ]);
  }

  private function echo_group1( ){

      $this->pdf->ezTable($this->mytab,'','',
        array( 'width'=>500, 'fontSize' => 5, 'showHeadings'=>1,'shaded'=> 2,'showLines'=> 0,
        'shadeCol' => array(0.98,0.98,0.98),'shadeCol2' => array(0.95,0.95,0.95),
         'cols'=>array(
        'Skl. cena'=>array('justification'=>'right'),
        'Prijat? EUR'=>array('justification'=>'right'),
        'Vydan? EUR'=>array('justification'=>'right'),
        'Zostatok EUR'=>array('justification'=>'right')
            )
          ));           
      $this->pdf->ezText( sprintf(" Zostatok pre n?kupn? cenu %11.2f %s: %d ks %11.2f %s \n",$this->nprice,$this->xcurrency, $this->kodpcs, $this->nprice*$this->kodpcs, $this->xcurrency), 8, array('justification' => 'right'));
      $this->kodpcs = 0;
        
  }


  public function actionIndex()
  {

    $this->ucto = new Uctoparams($this->firma);

    // Initialize a ROS PDF class object using DIN-A4, with background color gray
    $this->pdf = new pdfphp\src\Cezpdf('a4','portrait','color',[0.8,0.8,0.8]);
    $this->xcurrency = 'EUR';

    // Select the font
    $this->pdf->selectFont('Times-Roman');


    //Hlava a pata
    $this->pdf -> ezSetMargins(50,90,50,50);
    $all = $this->pdf->openObject();
    $this->pdf->saveState();
    $this->pdf->setStrokeColor(0,0,0,1);
    $this->pdf->addText(60,802,9,$par_head); 
    $this->pdf->line(20,40,578,40);
    $this->pdf->line(20,800,578,800);
    $this->pdf->addText(50,34,6,'');
    $this->pdf->restoreState();
    $this->pdf->closeObject();
      //Koniec
    $this->pdf->addObject($all,'all');                
    $this->pdf->ezStartPageNumbers(500,28,10,'',_("Strana").' {PAGENUM} / {TOTALPAGENUM}',1);
    $this->pdf->addText(160,750,12,'Skladov? karty, rok <b>'.$this->ucto->par_rok.'</b>');

    $this->pdf->ezSetDy(-80); 
    $this->nprice = -999;
    $this->riadok = 0;
    $this->mytab = [];
    $this->kodname = '';
    $this->kodkod = '';
    $this->kodpcs = 0;
    $this->kodpata = '';
          
    $yy = "SELECT
                yucto.n_price Skladova_cena,
                yucto.d1 Datum,
      case when movement.m_type_id=89 or  movement.m_type_id=117  then movement.text1
      when  movement.m_type_id=97 then 'Maloobch. dobropis'
      when movement.m_type_id=87 then 'Maloobch. predaj'
      else address.firma||' '||movement.text1
      end  as  Dodavatel_Odberatel,

                case when movement.move_id is null then  doc_nr||'p' else doc_nr||'' end as Cislo_dokladu,
                yucto.pcs Pocet,
                yucto.pcs*yucto.n_price Hodnota,
                ( SELECT sum(pcs) as Zostatok from ".$this->ucto->tabulkaModel." yugw where mat_id=yucto.mat_id and n_price=yucto.n_price and d1<=yucto.d1 and matid<=yucto.matid ),
                ( SELECT sum(pcs)*yucto.n_price as HodnotaZostatku from ".$this->ucto->tabulkaModel." yugw where mat_id=yucto.mat_id and n_price=yucto.n_price and d1<=yucto.d1 and matid<=yucto.matid),
                yucto.xx,
                yucto.mat_id xKod,
                p.model SkladovaKarta,
                yucto.remains,
                yucto.pcs,
                yucto.matid,
                m_type.name,
                coalesce(movement.move_id,-1) as xmove,
                yucto.mat_id
                FROM product p join
                ".$this->ucto->tabulkaModel." yucto on yucto.mat_id=p.mat_id left outer join movement on (movement.move_id = yucto.move_id )
                left outer join address on (movement.adr_id = address.adr_id )
                left outer join m_type  on (m_type.m_type_id = movement.m_type_id )
                 
                WHERE  ".$this->ucto->par_sklad." <> 0
                  and xx<>'!'
                ORDER BY p.model, n_price, yucto.d1, yucto.matid
         ";

         $iii = 0;     
        foreach(Yii::$app->db->createCommand( $yy  )
            ->queryAll() as $result ){
          $iii++;
          if( $iii > 2999000 ) break;
            //KOD grupa;
            if( $this->kodname <> '' ) {  
              $this->pdf->ezText( "Skladov? karta: $kodname", 12, array('justification' => 'left'));
            }
            $this->kodname = $this->kodpata = '';

            if( $result['xKod'] <> $this->kodkod ){
              $this->kodname = $result['SkladovaKarta'];
              
            }

            if( $result['Skladova_cena'] <> $this->nprice && $this->riadok > 0 ){
                  echo $this->kodname.' => ';
              $this->echo_group1( );
              $this->mytab = [];     
            }

            $this->nprice = $result['Skladova_cena'];                     
            $this->riadok++;
            $this->kodkod = $result['xKod'];
            $this->kodpcs += $result['Pocet'];

            $this->mytab[] = array( "Skl. cena"=> $result['Skladova_cena'],
                      "D?tum"=>$result['Datum'],
                      /* nechce tie kolonky v tejto firme */
        //              "Dodavatel/ Odberatel"=>$result['Dodavatel_Odberatel'],
        //              "Doklad"=>$result['Pocet']>0?$result['Cislo_dokladu'].$_GET['s']:$result['Cislo_dokladu'],
                      "Dod?vate?/ Odberate?"=>$result['Dodavatel_Odberatel'],
                      "."=>'',
                      "Prijat? ks"=>$result['Pocet']>0?$result['Pocet']:'',
                      "Vydan? ks"=>$result['Pocet']>0?'':$result['Pocet'],
                      "Prijat? EUR"=>$result['Hodnota']>0?sprintf("%11.2f",$result['Hodnota']):'',
                      "Vydan? EUR"=>$result['Hodnota']>0?'':sprintf("%11.2f",$result['Hodnota']),
                      "Zostatok ks"=>$this->kodpcs,
                      "Zostatok EUR"=>sprintf("%11.2f",$this->kodpcs*$result['Skladova_cena'])
                  );
              
          }


          //Este raz grupa
        //  echo "?????ideme dalej";
          $this->echo_group1( );
        //  echo "ideme dalej";
          
          $this->pdf->ezText( $this->kodpata."\n", 12, array('justification' => 'right'));
          $pdfcode = $this->pdf->ezOutput();
          $fpx=fopen("/tmp/".$this->ucto->par_file."_".$this->ucto->par_rok."_skl_karta_model.pdf",'wb');
          fwrite($fpx,$pdfcode);
          fclose($fpx);                                                                              

// Output the pdf as stream, but uncompress
// $pdf->ezStream(['compress'=>0]);

    return ExitCode::OK;
  }
}

