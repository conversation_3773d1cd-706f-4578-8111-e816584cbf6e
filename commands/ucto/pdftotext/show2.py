import fitz  # PyMuPDF

def extract_and_print_text(pdf_path):
    # Open the PDF file
    pdf_document = fitz.open(pdf_path)

    # Iterate through each page
    for page_num in range(len(pdf_document)):
        page = pdf_document.load_page(page_num)
        text = page.get_text("text")
        
        # Split the text into lines
        lines = text.split('\n')
        
        # Iterate through the lines
        for i, line in enumerate(lines):
            if "Skladová karta:" in line and "0 ks        0.00 EUR" not in line:
                # Find the next line containing "bbbb"
                for j in range(i + 1, len(lines)):
                    if "Zostatok pre nákupnú cenu" in lines[j] and "0 ks        0.00 EUR" not in lines[j]:
                        print(line + " " + lines[j])
                        break

# Specify the path to your PDF file
pdf_path = 'ba.pdf'

# Call the function
extract_and_print_text(pdf_path)

