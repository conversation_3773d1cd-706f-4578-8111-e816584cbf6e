<?php

namespace app\commands\ucto;

use Yii;
use yii\console\Controller;
use yii\console\ExitCode;

class RaciosplitController extends Controller
{

  //Params, use php yii raciosplit --del=yes
  public $del,$firstba,$firstbb,$addmore;


  private $novematid;

  public function options($actionID)
  {
    return array_merge(parent::options($actionID), [
      'del','firstba','firstbb','addmore'
    ]);
  }

  private function gwucto_move( $from, $to, $kod, $price, $pcs, $datum ){ //namiesto procedury, lebo ta brala len 97-tky

  //echo $datum."xxx";
    $queryx = "select d1 from yuctomodel where kod='".$kod."' and n_price=".$price." and move_id<0 and ".$to.">0 order by d1 desc limit 1";
    // echo "\n".$queryx."\n";
     $uzje = Yii::$app->db->createCommand( $queryx )->queryScalar();
   if( !empty($uzje ) ){
     echo '--'.$uzje."\n";
     $query = "select mat_id, d1, ".$from." kdispo from yuctomodel g where kod='".$kod."' and n_price=".$price." and d1<='".$datum."' and ".$from." > 0 
    and d1>'".$uzje."' order by d1 limit 1";
  //   echo '--'.$query."\n";
    } else {
      $query = "select mat_id, d1, sum(".$from.") kdispo from yuctomodel g where kod='".$kod."' and n_price=".$price." and d1<='".$datum."' and ".$from." > 0 
    group by mat_id, d1 order by d1 limit 1";
    }
     //echo $query;
     $result = Yii::$app->db->createCommand( $query )->queryOne();
     if(empty($mat_id) ){
    $query = "select mat_id, d1,".$from." kdispo from yuctomodel g where kod='".$kod."' and n_price=".$price." and d1<='".$datum."' and ".$from." > 0 
     order by d1 limit 1";
    $result = Yii::$app->db->createCommand( $query )->queryOne();
     }
     //ist( $mat_id, $d1, $kdispo )
     if( $result['kdispo'] < $pcs ){
        $pcs = $result['kdispo'];
      } //len tolko dame kolko mame
     $cmd1 =  "insert into yuctomodel(xx,kod,n_price,s_price,d1,fnumber,pcs,remains,move_id,mat_id,".$from.") values  ('F','".$kod."',".$price.",0,'".$result['d1']."',".$this->novematid.",-".$pcs.",-1,0,'".$result['mat_id']."',-".$pcs.");\n";
     $cmd2 = "insert into yuctomodel(xx,kod,n_price,s_price,d1,fnumber,pcs,remains,move_id,mat_id,".$to.") values  ('R','".$kod."',".$price.",0,'".$result['d1']."',".$this->novematid.",".$pcs.",-1,0,'".$result['mat_id']."',".$pcs.");\n";

  //  nechcem jedinecny number, preto zakomentovane
  //   $novematid++;

          Yii::$app->db->createCommand($cmd1)->execute();
          Yii::$app->db->createCommand($cmd2)->execute();
          // echo $cmd1.$cmd2;

  }





  public function actionIndex()
  {


    if( $this->del ) {
      Yii::$app->db->createCommand("alter table yuctomodel add IF NOT EXISTS banska integer default 0")->execute();
      Yii::$app->db->createCommand("alter table yuctomodel add IF NOT EXISTS blava integer default 0")->execute();
      Yii::$app->db->createCommand("alter table yuctomodel add IF NOT EXISTS m_type_id integer default 0")->execute();


      Yii::$app->db->createCommand("update yuctomodel set m_type_id=(select m_type_id from movement where move_id=yuctomodel.move_id)")->execute();


      // Yii::$app->db->createCommand("select sum(pcs),sum(pcs*n_price),d1,m_type_id from yuctomodel where xx<>'!' group by d1,m_type_id")->execute();
      // Yii::$app->db->createCommand("select sum(pcs),sum(pcs*n_price),month(d1),m_type_id from yuctomodel where xx<>'!' group by month(d1),m_type_id order by m_type_id")->execute();



      Yii::$app->db->createCommand("delete from yuctomodel where move_id<0")->execute();
      Yii::$app->db->createCommand("update yuctomodel set blava=0, banska=0")->execute();

      // 87 a 97 len prevadzka banska:
      Yii::$app->db->createCommand("update yuctomodel set blava=pcs where xx<>'!' and m_type_id not in (87,97)")->execute();
      Yii::$app->db->createCommand("update yuctomodel set banska=pcs where xx<>'!' and m_type_id in (87,97)")->execute();

        // Najdi prijemky
        // select * from movement where move_id in (select move_id from yuctomodel where d1='2020-01-01');
      Yii::$app->db->createCommand("update yuctomodel set blava=pcs,banska=0 where move_id  in (39581, 27230)")->execute();
      Yii::$app->db->createCommand("update yuctomodel set banska=pcs,blava=0 where move_id=27229")->execute();
    }




    $this->novematid = Yii::$app->db->createCommand( "select max(matid)+1 from yuctomodel" )->queryScalar();

    echo "novematid ".$this->novematid;

    if( $this->firstba ) {
      $query = "select kod,n_price,sum(banska) bb from yuctomodel g where xx<>'!' group by kod, n_price having sum(banska)<0";
      $query .= " order by kod,n_price";

      //echo $query;

      foreach(Yii::$app->db->createCommand( $query  )->queryAll() as $rr ){
        $pomkod = $rr["kod"];
        $pomprice = $rr["n_price"];
        $pompcs = $rr["bb"];
        
        //  gwucto_move( 'blava','banska',$pomkod,$pomprice,-$pompcs,'2006-12-24');
        $this->gwucto_move( 'blava','banska',$pomkod,$pomprice,-$pompcs,'2021-12-31');
      }
   }

    if( $this->firstbb ) {
      $query = "select kod,n_price,sum(blava) bb from yuctomodel g where xx<>'!' group by kod, n_price having sum(blava)<0";
      $query .= " order by kod,n_price";

      //echo $query;

      foreach(Yii::$app->db->createCommand( $query  )->queryAll() as $rr ){
        $pomkod = $rr["kod"];
        $pomprice = $rr["n_price"];
        $pompcs = $rr["bb"];
        
        //  gwucto_move( 'blava','banska',$pomkod,$pomprice,-$pompcs,'2006-12-24');
        $this->gwucto_move( 'banska','blava',$pomkod,$pomprice,-$pompcs,'2021-12-31');
      }
   }


   if ( $this->addmore ){

    /* following select result add:

    select '$this->gwucto_move( "blava","banska","'|| mat_id||'",'||n_price||',1,"'||(select max(d1) from yuctomodel where  mat_id=y.mat_id and n_price=y.n_price and xx='R')||'"); //' , sum(pcs*n_price),sum(banska*n_price) bb,sum(blava*n_price) ba, (select kod||' '||model from product where mat_id=y.mat_id) xsort from yuctomodel y group by mat_id,n_price having sum(blava*n_price)>0 and sum(blava*n_price)>3*n_price and sum(banska*n_price) =0 order by xsort;
    */

/*
$this->gwucto_move( "blava","banska","230465",28.13,1,"2021-01-01"); //   |   112.52 | 0.00 |   112.52 | AMON 125910
 $this->gwucto_move( "blava","banska","231470",31.88,1,"2021-04-16"); //   |   159.40 | 0.00 |   159.40 | AMON 125912
 $this->gwucto_move( "blava","banska","230379",31.88,1,"2021-01-01"); //   |   159.40 | 0.00 |   159.40 | AMON 125916
 $this->gwucto_move( "blava","banska","215805",30.23,1,"2021-05-07"); //   |   151.15 | 0.00 |   151.15 | ATHC EB0091
 $this->gwucto_move( "blava","banska","196231",36.00,1,"2021-12-21"); //   |   288.00 | 0.00 |   288.00 | BNDM 155S
 $this->gwucto_move( "blava","banska","218108",2.00,1,"2021-11-03"); //    |   252.00 | 0.00 |   252.00 | BOXX 4900701 MB giftbox
 $this->gwucto_move( "blava","banska","233202",2.76,1,"2021-10-19"); //    |    33.12 | 0.00 |    33.12 | BOXX 4900911
 $this->gwucto_move( "blava","banska","233816",4.56,1,"2021-12-17"); //    |    45.60 | 0.00 |    45.60 | BOXX 4900921
 $this->gwucto_move( "blava","banska","229297",102.07,1,"2021-02-05"); //  |   816.56 | 0.00 |   816.56 | BTHC BT6234
 $this->gwucto_move( "blava","banska","222821",112.50,1,"2021-05-20"); //  |   787.50 | 0.00 |   787.50 | JMON 124057
 $this->gwucto_move( "blava","banska","230428",99.38,1,"2021-06-22"); //   |   397.52 | 0.00 |   397.52 | JMON 126101
 $this->gwucto_move( "blava","banska","231643",123.75,1,"2021-06-26"); //  |   618.75 | 0.00 |   618.75 | JMON 128386
 $this->gwucto_move( "blava","banska","200425",133.13,1,"2021-10-12"); //  |   798.78 | 0.00 |   798.78 | LMON 103384
 $this->gwucto_move( "blava","banska","203528",140.63,1,"2021-03-15"); //  |  1406.30 | 0.00 |  1406.30 | LMON 113220
 $this->gwucto_move( "blava","banska","218614",98.71,1,"2021-11-12"); //   |   394.84 | 0.00 |   394.84 | LMON 118653
 $this->gwucto_move( "blava","banska","225489",129.38,1,"2021-03-15"); //  |  1293.80 | 0.00 |  1293.80 | LMON 123719
 $this->gwucto_move( "blava","banska","225490",129.38,1,"2021-03-15"); //  |  1164.42 | 0.00 |  1164.42 | LMON 123720
 $this->gwucto_move( "blava","banska","225488",71.25,1,"2021-03-15"); //   |   712.50 | 0.00 |   712.50 | LMON 123728
 $this->gwucto_move( "blava","banska","222517",98.71,1,"2021-11-12"); //   |   690.97 | 0.00 |   690.97 | LMON 123946
 $this->gwucto_move( "blava","banska","229943",106.88,1,"2021-06-14"); //  |   641.28 | 0.00 |   641.28 | LMON 126034
 $this->gwucto_move( "blava","banska","231561",118.13,1,"2021-05-04"); //  |   590.65 | 0.00 |   590.65 | LMON 126209
 $this->gwucto_move( "blava","banska","230449",75.00,1,"2021-06-22"); //   |   375.00 | 0.00 |   375.00 | LMON 126210
 $this->gwucto_move( "blava","banska","231820",67.50,1,"2021-06-14"); //   |   405.00 | 0.00 |   405.00 | LMON 126221
 $this->gwucto_move( "blava","banska","231496",109.88,1,"2021-11-12"); //  |   659.28 | 0.00 |   659.28 | LMON 126250
 $this->gwucto_move( "blava","banska","230740",118.13,1,"2021-06-22"); //  |   590.65 | 0.00 |   590.65 | LMON 126276
 $this->gwucto_move( "blava","banska","231821",140.63,1,"2021-06-14"); //  |   562.52 | 0.00 |   562.52 | LMON 126662
 $this->gwucto_move( "blava","banska","229930",133.13,1,"2021-09-10"); //  |   532.52 | 0.00 |   532.52 | LMON 126663
 $this->gwucto_move( "blava","banska","229945",106.88,1,"2021-11-03"); //  |   534.40 | 0.00 |   534.40 | LMON 126737
 $this->gwucto_move( "blava","banska","232287",125.63,1,"2021-10-19"); //  |   502.52 | 0.00 |   502.52 | LMON 128575
 $this->gwucto_move( "blava","banska","231768",110.63,1,"2021-11-03"); //  |   553.15 | 0.00 |   553.15 | LMON 128586
 $this->gwucto_move( "blava","banska","231771",56.25,1,"2021-11-03"); //   |   225.00 | 0.00 |   225.00 | LMON 128597
 $this->gwucto_move( "blava","banska","233238",435.00,1,"2021-10-18"); //  |  1740.00 | 0.00 |  1740.00 | LMON 128617
 $this->gwucto_move( "blava","banska","200091",6.83,1,"2021-06-01"); //    |    27.32 | 0.00 |    27.32 | LMON 3021
 $this->gwucto_move( "blava","banska","200095",6.83,1,"2021-06-01"); //    |    47.81 | 0.00 |    47.81 | LMON 3026
 $this->gwucto_move( "blava","banska","200257",114.38,1,"2021-02-10"); //  |  1143.80 | 0.00 |  1143.80 | LMON 35790
 $this->gwucto_move( "blava","banska","200257",118.13,1,"2021-12-09"); //  |  1653.82 | 0.00 |  1653.82 | LMON 35790
 $this->gwucto_move( "blava","banska","112872",5.83,1,"2021-04-21"); //    |    58.30 | 0.00 |    58.30 | OMON 105162
 $this->gwucto_move( "blava","banska","124219",2.66,1,"2021-08-25"); //    |    15.96 | 0.00 |    15.96 | OMON 105195
 $this->gwucto_move( "blava","banska","148443",1.91,1,"2021-04-22"); //    |     7.64 | 0.00 |     7.64 | OMON 11096

 $this->gwucto_move( "blava","banska","201682",1.91,1,"2021-05-21"); //    |     9.55 | 0.00 |     9.55 | OMON 111537
 $this->gwucto_move( "blava","banska","201683",1.91,1,"2021-04-22"); //    |    11.46 | 0.00 |    11.46 | OMON 111538
 $this->gwucto_move( "blava","banska","212853",5.66,1,"2021-04-22"); //    |    33.96 | 0.00 |    33.96 | OMON 116191
 $this->gwucto_move( "blava","banska","222807",5.83,1,"2021-12-30"); //    |   116.60 | 0.00 |   116.60 | OMON 124504
 $this->gwucto_move( "blava","banska","232305",8.33,1,"2021-12-15"); //    |    66.64 | 0.00 |    66.64 | OMON 128186
 $this->gwucto_move( "blava","banska","231562",9.83,1,"2021-05-04"); //    |    49.15 | 0.00 |    49.15 | OMON 128195
 $this->gwucto_move( "blava","banska","232289",2.66,1,"2021-08-05"); //    |    53.20 | 0.00 |    53.20 | OMON 128199
 $this->gwucto_move( "blava","banska","233748",2.66,1,"2021-12-06"); //    |    26.60 | 0.00 |    26.60 | OMON 128204
 $this->gwucto_move( "blava","banska","231658",5.66,1,"2021-11-17"); //    |    56.60 | 0.00 |    56.60 | OMON 128211
 $this->gwucto_move( "blava","banska","231112",5.66,1,"2021-12-09"); //    |    56.60 | 0.00 |    56.60 | OMON 128213
 $this->gwucto_move( "blava","banska","231113",5.66,1,"2021-10-01"); //    |   101.88 | 0.00 |   101.88 | OMON 128214
 $this->gwucto_move( "blava","banska","231993",5.66,1,"2021-10-01"); //    |    62.26 | 0.00 |    62.26 | OMON 128215
 $this->gwucto_move( "blava","banska","231342",5.66,1,"2021-12-09"); //    |   164.14 | 0.00 |   164.14 | OMON 128227
 $this->gwucto_move( "blava","banska","231505",5.66,1,"2021-06-05"); //    |   260.36 | 0.00 |   260.36 | OMON 128228
 $this->gwucto_move( "blava","banska","231114",5.83,1,"2021-12-30"); //    |    58.30 | 0.00 |    58.30 | OMON 128232
 $this->gwucto_move( "blava","banska","231660",5.66,1,"2021-05-17"); //    |    96.22 | 0.00 |    96.22 | OMON 128233
 $this->gwucto_move( "blava","banska","232006",3.79,1,"2021-07-09"); //    |    37.90 | 0.00 |    37.90 | OMON 128243
 $this->gwucto_move( "blava","banska","231659",5.66,1,"2021-08-18"); //    |    56.60 | 0.00 |    56.60 | OMON 128248
 $this->gwucto_move( "blava","banska","200240",1.91,1,"2021-04-22"); //    |    15.28 | 0.00 |    15.28 | OMON 15673
 $this->gwucto_move( "blava","banska","148478",150.00,1,"2021-12-07"); //  |   600.00 | 0.00 |   600.00 | PMON 10456
 $this->gwucto_move( "blava","banska","203198",176.25,1,"2021-12-06"); //  |  1057.50 | 0.00 |  1057.50 | PMON 112672
 $this->gwucto_move( "blava","banska","203192",159.38,1,"2021-12-17"); //  |   796.90 | 0.00 |   796.90 | PMON 112678
 $this->gwucto_move( "blava","banska","208323",95.63,1,"2021-08-19"); //   |  2199.49 | 0.00 |  2199.49 | PMON 114796
 $this->gwucto_move( "blava","banska","208324",75.00,1,"2021-02-23"); //   |   675.00 | 0.00 |   675.00 | PMON 114797
 $this->gwucto_move( "blava","banska","208327",91.88,1,"2021-10-11"); //   |   826.92 | 0.00 |   826.92 | PMON 114809
 $this->gwucto_move( "blava","banska","222470",121.88,1,"2021-12-06"); //  |   487.52 | 0.00 |   487.52 | PMON 118848
 $this->gwucto_move( "blava","banska","231640",318.75,1,"2021-07-09"); //  |  1275.00 | 0.00 |  1275.00 | PMON 126198
 $this->gwucto_move( "blava","banska","231477",215.63,1,"2021-09-14"); //  |   862.52 | 0.00 |   862.52 | PMON 126346
 $this->gwucto_move( "blava","banska","232394",136.88,1,"2021-11-17"); //  |   684.40 | 0.00 |   684.40 | PMON 126362
 $this->gwucto_move( "blava","banska","231929",78.75,1,"2021-06-26"); //   |  1181.25 | 0.00 |  1181.25 | PMON 128089
 $this->gwucto_move( "blava","banska","148383",156.44,1,"2021-11-12"); //  |   625.76 | 0.00 |   625.76 | PMON 7569
 $this->gwucto_move( "blava","banska","232129",1.13,1,"2021-07-12"); //    |     6.78 | 0.00 |     6.78 | SPPC HB1061
 $this->gwucto_move( "blava","banska","119425",1.59,1,"2021-07-12"); //    |    12.72 | 0.00 |    12.72 | SPPC HC1139
 $this->gwucto_move( "blava","banska","231004",1.28,1,"2021-09-10"); //    |     5.12 | 0.00 |     5.12 | SPPC HC1148
 $this->gwucto_move( "blava","banska","229504",1.00,1,"2021-09-10"); //    |     6.00 | 0.00 |     6.00 | SPPC HF1090
 $this->gwucto_move( "blava","banska","106948",1.60,1,"2021-07-12"); //    |     8.00 | 0.00 |     8.00 | SPPC HG1089
 $this->gwucto_move( "blava","banska","190034",1.14,1,"2021-07-12"); //    |     4.56 | 0.00 |     4.56 | SPPC HG1117
 $this->gwucto_move( "blava","banska","229507",0.87,1,"2021-07-12"); //    |     4.35 | 0.00 |     4.35 | SPPC HG1118
 $this->gwucto_move( "blava","banska","229507",1.00,1,"2021-09-10"); //    |     6.00 | 0.00 |     6.00 | SPPC HG1118
 $this->gwucto_move( "blava","banska","231423",1.47,1,"2021-01-11"); //    |     5.88 | 0.00 |     5.88 | SPPC HG1220
 $this->gwucto_move( "blava","banska","120120",189.23,1,"2021-11-08"); //  |   756.92 | 0.00 |   756.92 | TISS T063.637.16.057.00
 $this->gwucto_move( "blava","banska","108695",303.18,1,"2021-01-01"); //  |  1212.72 | 0.00 |  1212.72 | TISS T099.407.36.038.00
 $this->gwucto_move( "blava","banska","106959",126.00,1,"2021-01-01"); //  | 59346.00 | 0.00 | 59346.00 | TISS T101.210.16.031.00
 $this->gwucto_move( "blava","banska","106932",203.43,1,"2021-10-04"); //  |   813.72 | 0.00 |   813.72 | TISS T101.910.11.036.00
 $this->gwucto_move( "blava","banska","228586",130.10,1,"2021-12-17"); //  |   520.40 | 0.00 |   520.40 | TISS T109.210.22.031.00
 $this->gwucto_move( "blava","banska","210911",134.83,1,"2021-11-30"); //  |   943.81 | 0.00 |   943.81 | TISS T109.210.33.031.00
 $this->gwucto_move( "blava","banska","211550",137.21,1,"2021-07-27"); //  |   823.26 | 0.00 |   823.26 | TISS T109.410.22.031.00
 $this->gwucto_move( "blava","banska","216183",179.78,1,"2021-10-19"); //  |   898.90 | 0.00 |   898.90 | TISS T112.210.11.036.00
 $this->gwucto_move( "blava","banska","231889",198.69,1,"2021-10-04"); //  |   794.76 | 0.00 |   794.76 | TISS T114.417.17.037.02
 $this->gwucto_move( "blava","banska","228588",288.58,1,"2021-10-04"); //  |  1154.32 | 0.00 |  1154.32 | TISS T115.417.37.051.00
 $this->gwucto_move( "blava","banska","216807",167.95,1,"2021-12-21"); //  |  1343.60 | 0.00 |  1343.60 | TISS T116.617.11.047.01


 $this->gwucto_move( "blava","banska","214148",164.80,1,"2021-01-01"); //  |   659.20 | 0.00 |   659.20 | TISS T116.617.36.047.00
 $this->gwucto_move( "blava","banska","230821",203.43,1,"2021-12-13"); //  |   813.72 | 0.00 |   813.72 | TISS T125.617.11.051.00
 $this->gwucto_move( "blava","banska","231338",167.95,1,"2021-12-13"); //  |   671.80 | 0.00 |   671.80 | TISS T137.410.11.031.00
 $this->gwucto_move( "blava","banska","204226",2180.14,1,"2021-11-04"); // | 10900.70 | 0.00 | 10900.70 | WTHS CAW211P_FC6356
 $this->gwucto_move( "blava","banska","207617",563.93,1,"2021-11-04"); //  |  3947.51 | 0.00 |  3947.51 | WTHS CAZ1014_BA0842
 $this->gwucto_move( "blava","banska","231076",694.59,1,"2021-10-07"); //  |  2778.36 | 0.00 |  2778.36 | WTHS CAZ101AH_BA0842
 $this->gwucto_move( "blava","banska","216216",571.44,1,"2021-12-09"); //  |  2857.20 | 0.00 |  2857.20 | WTHS CAZ101E_BA0842
 $this->gwucto_move( "blava","banska","216219",571.45,1,"2021-12-09"); //  |  9143.20 | 0.00 |  9143.20 | WTHS CAZ101K_BA0842
 $this->gwucto_move( "blava","banska","216605",643.15,1,"2021-12-09"); //  |  2572.60 | 0.00 |  2572.60 | WTHS CAZ101N_FC8243
 $this->gwucto_move( "blava","banska","216589",2226.93,1,"2021-12-09"); // |  8907.72 | 0.00 |  8907.72 | WTHS CBG2A1Z_FT6157
 $this->gwucto_move( "blava","banska","228501",1829.08,1,"2021-11-04"); // |  9145.40 | 0.00 |  9145.40 | WTHS CBN2010_BA0642
 $this->gwucto_move( "blava","banska","228512",1994.95,1,"2021-12-09"); // | 11969.70 | 0.00 | 11969.70 | WTHS CBN2A1A_BA0643
 $this->gwucto_move( "blava","banska","204258",916.79,1,"2021-02-11"); //  |  3667.16 | 0.00 |  3667.16 | WTHS WAR201E_FC6292
 $this->gwucto_move( "blava","banska","211976",649.53,1,"2021-05-20"); //  |  3247.65 | 0.00 |  3247.65 | WTHS WAY101C_BA0746
 $this->gwucto_move( "blava","banska","228522",1055.99,1,"2021-11-04"); // |  4223.96 | 0.00 |  4223.96 | WTHS WAY201T_BA0927
 $this->gwucto_move( "blava","banska","207619",475.93,1,"2021-11-04"); //  |  5711.16 | 0.00 |  5711.16 | WTHS WAZ1010_BA0842
 $this->gwucto_move( "blava","banska","201941",451.95,1,"2021-10-07"); //  |  2259.75 | 0.00 |  2259.75 | WTHS WAZ1110_BA0875
 $this->gwucto_move( "blava","banska","204273",411.36,1,"2021-12-09"); //  |  5347.68 | 0.00 |  5347.68 | WTHS WAZ1110_FT8023
 $this->gwucto_move( "blava","banska","204274",469.68,1,"2021-10-07"); //  |  1878.72 | 0.00 |  1878.72 | WTHS WAZ1112_BA0875
 $this->gwucto_move( "blava","banska","204274",475.94,1,"2021-11-04"); //  |  2379.70 | 0.00 |  2379.70 | WTHS WAZ1112_BA0875
 $this->gwucto_move( "blava","banska","216544",482.29,1,"2021-12-09"); //  |  1929.16 | 0.00 |  1929.16 | WTHS WAZ1118_BA0875
 $this->gwucto_move( "blava","banska","204275",517.76,1,"2021-12-09"); //  |  7766.40 | 0.00 |  7766.40 | WTHS WAZ111A_BA0875
 $this->gwucto_move( "blava","banska","204275",510.95,1,"2021-11-04"); //  |  2554.75 | 0.00 |  2554.75 | WTHS WAZ111A_BA0875
 $this->gwucto_move( "blava","banska","212004",497.63,1,"2021-09-02"); //  |  1990.52 | 0.00 |  1990.52 | WTHS WBD1312_BA0740
 $this->gwucto_move( "blava","banska","228524",843.22,1,"2021-12-09"); //  |  5902.54 | 0.00 |  5902.54 | WTHS WBD1325_BB0320
 $this->gwucto_move( "blava","banska","221863",661.92,1,"2021-02-05"); //  |  3309.60 | 0.00 |  3309.60 | WTHS WBK1311_BA0652
 $this->gwucto_move( "blava","banska","228504",1003.00,1,"2021-11-04"); // |  4012.00 | 0.00 |  4012.00 | WTHS WBN2010_BA0640
 $this->gwucto_move( "blava","banska","232964",1097.28,1,"2021-09-10"); // |  4389.12 | 0.00 |  4389.12 | WTHS WBN2412_BA0621
 $this->gwucto_move( "blava","banska","231686",1022.38,1,"2021-08-05"); // |  4089.52 | 0.00 |  4089.52 | WTHS WBP201A_BA0632
 $this->gwucto_move( "blava","banska","232419",909.46,1,"2021-09-10"); //  | 11822.98 | 0.00 | 11822.98 | WTHS WBP201A_FT6197
 $this->gwucto_move( "blava","banska","232420",908.88,1,"2021-09-02"); //  |  8179.92 | 0.00 |  8179.92 | WTHS WBP201B_FT6198
 $this->gwucto_move( "blava","banska","231688",1010.99,1,"2021-07-09"); // |  4043.96 | 0.00 |  4043.96 | WTHS WBP201C_BA0632
 $this->gwucto_move( "blava","banska","231691",1114.50,1,"2021-09-02"); // |  5572.50 | 0.00 |  5572.50 | WTHS WBP231B_BA0618
 $this->gwucto_move( "blava","banska","231690",953.41,1,"2021-08-05"); //  |  3813.64 | 0.00 |  3813.64 | WTHS WBP231D_BA0626



 $this->gwucto_move( "blava","banska","180756",2776.80,1,"2021-01-01"); // |  8330.40 | 0.00 |  8330.40 | BREI A1337111/BC29/168A
 $this->gwucto_move( "blava","banska","231272",1595.82,1,"2021-11-25"); // |  4787.46 | 0.00 |  4787.46 | BREI X82310A51B1S1
 $this->gwucto_move( "blava","banska","230230",345.36,1,"2021-09-27"); //  |  1036.08 | 0.00 |  1036.08 | CERS C001.007.36.116.02
 $this->gwucto_move( "blava","banska","207585",352.45,1,"2021-10-19"); //  |  1057.35 | 0.00 |  1057.35 | CERS C033.450.16.081.00
 $this->gwucto_move( "blava","banska","225492",216.50,1,"2021-01-01"); //  |   649.50 | 0.00 |   649.50 | CERS C034.451.11.057.00
 $this->gwucto_move( "blava","banska","109953",54.38,1,"2021-01-01"); //   |   163.14 | 0.00 |   163.14 | FEST F16716/2
 $this->gwucto_move( "blava","banska","215500",69.33,1,"2021-09-08"); //   |   207.99 | 0.00 |   207.99 | FEST F20285/4
 $this->gwucto_move( "blava","banska","228453",89.27,1,"2021-09-08"); //   |   267.81 | 0.00 |   267.81 | FEST F20330/6
 $this->gwucto_move( "blava","banska","219602",89.27,1,"2021-08-03"); //   |   267.81 | 0.00 |   267.81 | FEST F20361/2
 $this->gwucto_move( "blava","banska","228820",124.16,1,"2021-11-11"); //  |   372.48 | 0.00 |   372.48 | FEST F20364/1
 $this->gwucto_move( "blava","banska","218352",124.16,1,"2021-11-11"); //  |   620.80 | 0.00 |   620.80 | FEST F20364/3
 $this->gwucto_move( "blava","banska","218657",54.38,1,"2021-09-23"); //   |   163.14 | 0.00 |   163.14 | FEST F20382/1
 $this->gwucto_move( "blava","banska","218658",54.38,1,"2021-06-29"); //   |   163.14 | 0.00 |   163.14 | FEST F20382/2
 $this->gwucto_move( "blava","banska","218675",64.35,1,"2021-12-22"); //   |   193.05 | 0.00 |   193.05 | FEST F20383/1
 $this->gwucto_move( "blava","banska","220660",64.35,1,"2021-01-01"); //   |   193.05 | 0.00 |   193.05 | FEST F20383/2
 $this->gwucto_move( "blava","banska","219105",89.27,1,"2021-11-10"); //   |   357.08 | 0.00 |   357.08 | FEST F20392/2
 $this->gwucto_move( "blava","banska","219145",114.19,1,"2021-11-10"); //  |   342.57 | 0.00 |   342.57 | FEST F20394/1
 $this->gwucto_move( "blava","banska","219100",89.27,1,"2021-11-10"); //   |   357.08 | 0.00 |   357.08 | FEST F20402/1
 $this->gwucto_move( "blava","banska","223522",147.84,1,"2021-11-10"); //  |   443.52 | 0.00 |   443.52 | FEST F20492/2
 $this->gwucto_move( "blava","banska","229912",133.00,1,"2021-10-19"); //  |   532.00 | 0.00 |   532.00 | FEST F20520/2
 $this->gwucto_move( "blava","banska","229839",133.00,1,"2021-10-19"); //  |   399.00 | 0.00 |   399.00 | FEST F20520/3
 $this->gwucto_move( "blava","banska","229843",128.06,1,"2021-10-19"); //  |   384.18 | 0.00 |   384.18 | FEST F20521/4
 $this->gwucto_move( "blava","banska","204148",129.38,1,"2021-01-01"); //  |   388.14 | 0.00 |   388.14 | JMON 113395
 $this->gwucto_move( "blava","banska","208193",112.50,1,"2021-01-01"); //  |   337.50 | 0.00 |   337.50 | JMON 114764
 $this->gwucto_move( "blava","banska","208196",112.50,1,"2021-01-01"); //  |   337.50 | 0.00 |   337.50 | JMON 114768
 $this->gwucto_move( "blava","banska","219650",103.13,1,"2021-01-01"); //  |   309.39 | 0.00 |   309.39 | JMON 118596
 $this->gwucto_move( "blava","banska","223615",1132.50,1,"2021-01-01"); // |  3397.50 | 0.00 |  3397.50 | JMON 123773
 $this->gwucto_move( "blava","banska","223319",112.50,1,"2021-01-01"); //  |   337.50 | 0.00 |   337.50 | JMON 123799
 $this->gwucto_move( "blava","banska","223257",101.25,1,"2021-09-16"); //  |   303.75 | 0.00 |   303.75 | JMON 123808
 $this->gwucto_move( "blava","banska","207901",7.76,1,"2021-01-01"); //    |   310.40 | 0.00 |   310.40 | JTRE 023191
 $this->gwucto_move( "blava","banska","207899",5.75,1,"2021-01-01"); //    |    28.75 | 0.00 |    28.75 | JTRE 023369
 $this->gwucto_move( "blava","banska","207990",52.50,1,"2021-10-19"); //   |   157.50 | 0.00 |   157.50 | LMON 114557
 $this->gwucto_move( "blava","banska","225489",125.63,1,"2021-02-10"); //  |   376.89 | 0.00 |   376.89 | LMON 123719
 $this->gwucto_move( "blava","banska","223616",301.88,1,"2021-01-01"); //  |   905.64 | 0.00 |   905.64 | LMON 124085
 $this->gwucto_move( "blava","banska","231698",99.38,1,"2021-05-20"); //   |   298.14 | 0.00 |   298.14 | LMON 126014
 $this->gwucto_move( "blava","banska","232250",106.88,1,"2021-07-29"); //  |   320.64 | 0.00 |   320.64 | LMON 126018
 $this->gwucto_move( "blava","banska","230453",78.75,1,"2021-01-01"); //   |   236.25 | 0.00 |   236.25 | LMON 126259
 $this->gwucto_move( "blava","banska","231822",99.38,1,"2021-06-14"); //   |   298.14 | 0.00 |   298.14 | LMON 126664
 $this->gwucto_move( "blava","banska","232295",125.63,1,"2021-10-19"); //  |   376.89 | 0.00 |   376.89 | LMON 128576
 $this->gwucto_move( "blava","banska","231733",110.63,1,"2021-05-21"); //  |   331.89 | 0.00 |   331.89 | LMON 128585
 $this->gwucto_move( "blava","banska","233735",121.88,1,"2021-12-07"); //  |   365.64 | 0.00 |   365.64 | LMON 128588
 $this->gwucto_move( "blava","banska","231883",345.00,1,"2021-06-18"); //  |  1035.00 | 0.00 |  1035.00 | LMON 128605
 $this->gwucto_move( "blava","banska","192585",95.63,1,"2021-12-17"); //   |   286.89 | 0.00 |   286.89 | LMON 35798
 $this->gwucto_move( "blava","banska","188295",4.00,1,"2021-01-01"); //    |    12.00 | 0.00 |    12.00 | MORE MRED



$this->gwucto_move( "blava","banska","228427",1.75,1,"2021-01-01"); //    |     5.25 | 0.00 |     5.25 | SPPC HC1126
 $this->gwucto_move( "blava","banska","230547",3.85,1,"2021-01-01"); //    |    11.55 | 0.00 |    11.55 | SPPC HC1144
 $this->gwucto_move( "blava","banska","231004",1.02,1,"2021-01-01"); //    |     3.06 | 0.00 |     3.06 | SPPC HC1148
 $this->gwucto_move( "blava","banska","107279",1.68,1,"2021-01-01"); //    |     5.04 | 0.00 |     5.04 | SPPC HC1150
 $this->gwucto_move( "blava","banska","125080",1.25,1,"2021-01-01"); //    |     3.75 | 0.00 |     3.75 | SPPC HF1017
 $this->gwucto_move( "blava","banska","125080",1.18,1,"2021-01-11"); //    |     3.54 | 0.00 |     3.54 | SPPC HF1017
 $this->gwucto_move( "blava","banska","133010",1.34,1,"2021-01-01"); //    |     4.02 | 0.00 |     4.02 | SPPC HF1051
 $this->gwucto_move( "blava","banska","231001",0.99,1,"2021-09-10"); //    |     2.97 | 0.00 |     2.97 | SPPC HF1067
 $this->gwucto_move( "blava","banska","229271",1.00,1,"2021-01-01"); //    |     3.00 | 0.00 |     3.00 | SPPC HF5011
 $this->gwucto_move( "blava","banska","120732",1.24,1,"2021-01-01"); //    |     3.72 | 0.00 |     3.72 | SPPC HG1064
 $this->gwucto_move( "blava","banska","229506",0.89,1,"2021-01-01"); //    |     2.67 | 0.00 |     2.67 | SPPC HG1092
 $this->gwucto_move( "blava","banska","229506",1.12,1,"2021-01-01"); //    |     3.36 | 0.00 |     3.36 | SPPC HG1092
 $this->gwucto_move( "blava","banska","187039",0.87,1,"2021-07-12"); //    |     2.61 | 0.00 |     2.61 | SPPC HG1094
 $this->gwucto_move( "blava","banska","190034",1.28,1,"2021-01-01"); //    |     3.84 | 0.00 |     3.84 | SPPC HG1117
 $this->gwucto_move( "blava","banska","190034",1.46,1,"2021-01-11"); //    |     4.38 | 0.00 |     4.38 | SPPC HG1117
 $this->gwucto_move( "blava","banska","229507",0.96,1,"2021-01-01"); //    |     2.88 | 0.00 |     2.88 | SPPC HG1118
 $this->gwucto_move( "blava","banska","229507",0.89,1,"2021-01-01"); //    |     2.67 | 0.00 |     2.67 | SPPC HG1118
 $this->gwucto_move( "blava","banska","229507",1.06,1,"2021-01-01"); //    |     3.18 | 0.00 |     3.18 | SPPC HG1118
 $this->gwucto_move( "blava","banska","230537",1.00,1,"2021-01-01"); //    |     3.00 | 0.00 |     3.00 | SPPC HG1150
 $this->gwucto_move( "blava","banska","108440",1.73,1,"2021-01-01"); //    |     5.19 | 0.00 |     5.19 | SPPC HG1162
 $this->gwucto_move( "blava","banska","108440",1.77,1,"2021-01-01"); //    |     5.31 | 0.00 |     5.31 | SPPC HG1162
 $this->gwucto_move( "blava","banska","230227",1.40,1,"2021-01-01"); //    |     4.20 | 0.00 |     4.20 | SPPC HG1173
 $this->gwucto_move( "blava","banska","230536",4.35,1,"2021-06-07"); //    |    13.05 | 0.00 |    13.05 | SPPC HG1230
 $this->gwucto_move( "blava","banska","229514",22.80,1,"2021-01-01"); //   |    68.40 | 0.00 |    68.40 | SPPC MBA00010
 $this->gwucto_move( "blava","banska","157897",1.33,1,"2021-01-01"); //    |     3.99 | 0.00 |     3.99 | SPPC MT0005
 $this->gwucto_move( "blava","banska","205338",245.30,1,"2021-01-01"); //  |   735.90 | 0.00 |   735.90 | TISS T055.430.11.047.00
 $this->gwucto_move( "blava","banska","220697",141.93,1,"2021-10-19"); //  |   425.79 | 0.00 |   425.79 | TISS T058.109.36.031.00
 $this->gwucto_move( "blava","banska","122125",189.23,1,"2021-12-13"); //  |   567.69 | 0.00 |   567.69 | TISS T063.617.16.037.00
 $this->gwucto_move( "blava","banska","107736",184.50,1,"2021-01-01"); //  |   553.50 | 0.00 |   553.50 | TISS T094.210.22.111.01
 $this->gwucto_move( "blava","banska","219267",184.51,1,"2021-11-08"); //  |   553.53 | 0.00 |   553.53 | TISS T101.417.11.041.00
 $this->gwucto_move( "blava","banska","219426",123.00,1,"2021-11-08"); //  |   369.00 | 0.00 |   369.00 | TISS T109.410.11.033.00
 $this->gwucto_move( "blava","banska","216184",167.95,1,"2021-08-27"); //  |   503.85 | 0.00 |   503.85 | TISS T116.617.11.037.00
 $this->gwucto_move( "blava","banska","230891",198.69,1,"2021-12-17"); //  |   596.07 | 0.00 |   596.07 | TISS T116.617.22.041.00
 $this->gwucto_move( "blava","banska","214148",158.48,1,"2021-12-13"); //  |   475.44 | 0.00 |   475.44 | TISS T116.617.36.047.00
 $this->gwucto_move( "blava","banska","233167",158.48,1,"2021-10-04"); //  |   475.44 | 0.00 |   475.44 | TISS T116.617.36.052.00
 $this->gwucto_move( "blava","banska","222308",236.54,1,"2021-12-21"); //  |   709.62 | 0.00 |   709.62 | TISS T120.417.17.041.00
 $this->gwucto_move( "blava","banska","106659",236.54,1,"2021-10-04"); //  |   709.62 | 0.00 |   709.62 | TISS T120.417.17.051.01
 $this->gwucto_move( "blava","banska","223571",218.90,1,"2021-01-01"); //  |   656.70 | 0.00 |   656.70 | TISS T123.610.16.057.00
 $this->gwucto_move( "blava","banska","232103",170.31,1,"2021-12-17"); //  |   510.93 | 0.00 |   510.93 | TISS T125.610.11.051.00
 $this->gwucto_move( "blava","banska","231628",146.66,1,"2021-12-14"); //  |   439.98 | 0.00 |   439.98 | TISS T125.610.16.041.00
 $this->gwucto_move( "blava","banska","233688",243.64,1,"2021-12-17"); //  |   730.92 | 0.00 |   730.92 | TISS T133.210.36.056.00
 $this->gwucto_move( "blava","banska","232407",316.97,1,"2021-12-21"); //  |   950.91 | 0.00 |   950.91 | TISS T137.407.11.041.00
 $this->gwucto_move( "blava","banska","231295",167.95,1,"2021-12-17"); //  |   503.85 | 0.00 |   503.85 | TISS T137.410.11.041.00
 $this->gwucto_move( "blava","banska","216603",2139.32,1,"2021-03-05"); // |  6417.96 | 0.00 |  6417.96 | WTHS CAW211R_FC6401
 $this->gwucto_move( "blava","banska","206044",719.85,1,"2021-09-02"); //  |  2159.55 | 0.00 |  2159.55 | WTHS CAY1110_BA0927
 $this->gwucto_move( "blava","banska","207659",764.58,1,"2021-10-07"); //  |  2293.74 | 0.00 |  2293.74 | WTHS CAY111A_BA0927
 $this->gwucto_move( "blava","banska","228531",694.59,1,"2021-10-07"); //  |  2083.77 | 0.00 |  2083.77 | WTHS CAZ101AC_BA0842
 $this->gwucto_move( "blava","banska","228531",703.98,1,"2021-11-04"); //  |  2111.94 | 0.00 |  2111.94 | WTHS CAZ101AC_BA0842
 $this->gwucto_move( "blava","banska","228532",651.94,1,"2021-11-04"); //  |  1955.82 | 0.00 |  1955.82 | WTHS CAZ101AC_FT8024
 $this->gwucto_move( "blava","banska","231457",686.64,1,"2021-09-10"); //  |  2059.92 | 0.00 |  2059.92 | WTHS CAZ101AG_BA0842
 $this->gwucto_move( "blava","banska","195942",1053.23,1,"2021-05-20"); // |  3159.69 | 0.00 |  3159.69 | WTHS WAR201C_BA0723
 $this->gwucto_move( "blava","banska","207657",612.55,1,"2021-05-20"); //  |  1837.65 | 0.00 |  1837.65 | WTHS WAY111A_BA0928
 $this->gwucto_move( "blava","banska","201941",444.80,1,"2021-09-02"); //  |  1334.40 | 0.00 |  1334.40 | WTHS WAZ1110_BA0875
 $this->gwucto_move( "blava","banska","201941",457.96,1,"2021-11-04"); //  |  1373.88 | 0.00 |  1373.88 | WTHS WAZ1110_BA0875
 $this->gwucto_move( "blava","banska","221864",635.32,1,"2021-01-01"); //  |  1905.96 | 0.00 |  1905.96 | WTHS WBK1311_FC8261
 $this->gwucto_move( "blava","banska","228504",977.59,1,"2021-09-10"); //  |  2932.77 | 0.00 |  2932.77 | WTHS WBN2010_BA0640
 $this->gwucto_move( "blava","banska","228505",1003.00,1,"2021-11-04"); // |  3009.00 | 0.00 |  3009.00 | WTHS WBN2012_BA0640
 $this->gwucto_move( "blava","banska","228505",977.59,1,"2021-09-10"); //  |  2932.77 | 0.00 |  2932.77 | WTHS WBN2012_BA0640

 */

	    $this->gwucto_move( "blava","banska","228535",970.67,1,"2021-07-09"); //  |  1941.34 | 0.00 |  1941.34 | WTHC SBG8A10_BA0646
 $this->gwucto_move( "blava","banska","229476",881.97,1,"2021-10-07"); //  |  1763.94 | 0.00 |  1763.94 | WTHC SBG8A11_BT6220
 $this->gwucto_move( "blava","banska","216603",2191.53,1,"2021-07-09"); // |  4383.06 | 0.00 |  4383.06 | WTHS CAW211R_FC6401
 $this->gwucto_move( "blava","banska","205038",640.50,1,"2021-01-01"); //  |  1281.00 | 0.00 |  1281.00 | WTHS CAY1110_FT6041
 $this->gwucto_move( "blava","banska","206045",1126.01,1,"2021-11-04"); // |  2252.02 | 0.00 |  2252.02 | WTHS CAY2112_BA0927
 $this->gwucto_move( "blava","banska","207344",492.97,1,"2021-11-04"); //  |   985.94 | 0.00 |   985.94 | WTHS CAZ1010_FT8024
 $this->gwucto_move( "blava","banska","207483",746.85,1,"2021-10-07"); //  |  1493.70 | 0.00 |  1493.70 | WTHS CAZ1011_BA0843
 $this->gwucto_move( "blava","banska","231458",642.09,1,"2021-08-05"); //  |  1284.18 | 0.00 |  1284.18 | WTHS CAZ101AG_FC8304
 $this->gwucto_move( "blava","banska","233180",781.38,1,"2021-10-07"); //  |  1562.76 | 0.00 |  1562.76 | WTHS CAZ101AJ_FC6487
 $this->gwucto_move( "blava","banska","216592",1422.54,1,"2021-10-07"); // |  2845.08 | 0.00 |  2845.08 | WTHS CBK2112_BA0715
 $this->gwucto_move( "blava","banska","221881",2163.89,1,"2021-10-07"); // |  4327.78 | 0.00 |  4327.78 | WTHS CBL2111_FC6453
 $this->gwucto_move( "blava","banska","230632",2289.55,1,"2021-10-07"); // |  4579.10 | 0.00 |  4579.10 | WTHS CBL2113_BA0644
 $this->gwucto_move( "blava","banska","230632",2198.55,1,"2021-01-01"); // |  4397.10 | 0.00 |  4397.10 | WTHS CBL2113_BA0644
 $this->gwucto_move( "blava","banska","228501",1804.25,1,"2021-10-07"); // |  3608.50 | 0.00 |  3608.50 | WTHS CBN2010_BA0642
 $this->gwucto_move( "blava","banska","228513",1994.95,1,"2021-12-09"); // |  3989.90 | 0.00 |  3989.90 | WTHS CBN2A1B_BA0643
 $this->gwucto_move( "blava","banska","228513",1939.52,1,"2021-08-05"); // |  3879.04 | 0.00 |  3879.04 | WTHS CBN2A1B_BA0643
 $this->gwucto_move( "blava","banska","228514",2232.96,1,"2021-10-07"); // |  4465.92 | 0.00 |  4465.92 | WTHS CBN2A5A_FC6481
 $this->gwucto_move( "blava","banska","228514",2215.64,1,"2021-08-26"); // |  4431.28 | 0.00 |  4431.28 | WTHS CBN2A5A_FC6481
 $this->gwucto_move( "blava","banska","210984",1531.72,1,"2021-01-01"); // |  3063.44 | 0.00 |  3063.44 | WTHS CV2A1AC_FC6380
 $this->gwucto_move( "blava","banska","195967",537.70,1,"2021-08-05"); //  |  1075.40 | 0.00 |  1075.40 | WTHS WAR1311_BA0778
 $this->gwucto_move( "blava","banska","195938",953.40,1,"2021-08-05"); //  |  1906.80 | 0.00 |  1906.80 | WTHS WAR201A_BA0723
 $this->gwucto_move( "blava","banska","204257",933.76,1,"2021-06-04"); //  |  1867.52 | 0.00 |  1867.52 | WTHS WAR201E_BA0723
 $this->gwucto_move( "blava","banska","204258",954.92,1,"2021-04-09"); //  |  1909.84 | 0.00 |  1909.84 | WTHS WAR201E_FC6292
 $this->gwucto_move( "blava","banska","108817",1132.18,1,"2021-05-20"); // |  2264.36 | 0.00 |  2264.36 | WTHS WAR2414_BA0776
 $this->gwucto_move( "blava","banska","211974",649.53,1,"2021-05-20"); //  |  1299.06 | 0.00 |  1299.06 | WTHS WAY101A_BA0746
 $this->gwucto_move( "blava","banska","216554",611.22,1,"2021-01-01"); //  |  1222.44 | 0.00 |  1222.44 | WTHS WAY108A_FT6141
 $this->gwucto_move( "blava","banska","207658",612.55,1,"2021-05-20"); //  |  1225.10 | 0.00 |  1225.10 | WTHS WAY111C_BA0928
 $this->gwucto_move( "blava","banska","211993",742.97,1,"2021-04-09"); //  |  1485.94 | 0.00 |  1485.94 | WTHS WAY131L_BA0748
 $this->gwucto_move( "blava","banska","216558",844.09,1,"2021-01-01"); //  |  1688.18 | 0.00 |  1688.18 | WTHS WAY201B_FT6150
 $this->gwucto_move( "blava","banska","207618",346.83,1,"2021-01-01"); //  |   693.66 | 0.00 |   693.66 | WTHS WAZ1010_FC8197
 $this->gwucto_move( "blava","banska","211971",625.52,1,"2021-10-07"); //  |  1251.04 | 0.00 |  1251.04 | WTHS WAZ2014_BA0842
 $this->gwucto_move( "blava","banska","212029",1020.98,1,"2021-11-04"); // |  2041.96 | 0.00 |  2041.96 | WTHS WBC2112_BA0603
 $this->gwucto_move( "blava","banska","216564",530.93,1,"2021-09-02"); //  |  1061.86 | 0.00 |  1061.86 | WTHS WBD1112_BA0928
 $this->gwucto_move( "blava","banska","212002",504.20,1,"2021-10-07"); //  |  1008.40 | 0.00 |  1008.40 | WTHS WBD1310_BA0740
 $this->gwucto_move( "blava","banska","216567",763.65,1,"2021-01-22"); //  |  1527.30 | 0.00 |  1527.30 | WTHS WBD131B_BA0748
 $this->gwucto_move( "blava","banska","212009",463.51,1,"2021-09-02"); //  |   927.02 | 0.00 |   927.02 | WTHS WBD1411_BA0741
 $this->gwucto_move( "blava","banska","212012",712.96,1,"2021-06-04"); //  |  1425.92 | 0.00 |  1425.92 | WTHS WBD1414_BA0741
 $this->gwucto_move( "blava","banska","221869",1303.10,1,"2021-01-01"); // |  2606.20 | 0.00 |  2606.20 | WTHS WBK1316_FC8261
 $this->gwucto_move( "blava","banska","228504",977.11,1,"2021-09-02"); //  |  1954.22 | 0.00 |  1954.22 | WTHS WBN2010_BA0640
 $this->gwucto_move( "blava","banska","228506",1013.16,1,"2021-11-04"); // |  2026.32 | 0.00 |  2026.32 | WTHS WBN2011_FC6484
 $this->gwucto_move( "blava","banska","228510",909.46,1,"2021-09-10"); //  |  1818.92 | 0.00 |  1818.92 | WTHS WBN2111_BA0639
 $this->gwucto_move( "blava","banska","228508",977.59,1,"2021-09-10"); //  |  1955.18 | 0.00 |  1955.18 | WTHS WBN2113_BA0639
 $this->gwucto_move( "blava","banska","231687",1011.00,1,"2021-07-09"); // |  2022.00 | 0.00 |  2022.00 | WTHS WBP201B_BA0632
 $this->gwucto_move( "blava","banska","232421",1114.50,1,"2021-09-02"); // |  2229.00 | 0.00 |  2229.00 | WTHS WBP201D_FT6197

  $this->gwucto_move( "blava","banska","223089",2600.00,1,"2021-01-01"); // |  5200.00 | 0.00 |  5200.00 | BREI A13316101B1X1
 $this->gwucto_move( "blava","banska","217845",1872.00,1,"2021-01-01"); // |  3744.00 | 0.00 |  3744.00 | BREI A17314101C1X1
 $this->gwucto_move( "blava","banska","217846",1310.00,1,"2021-01-01"); // |  2620.00 | 0.00 |  2620.00 | BREI A17388101C1A1
 $this->gwucto_move( "blava","banska","231524",3812.12,1,"2021-11-25"); // |  7624.24 | 0.00 |  7624.24 | BREI AB0930D31L1P1
 $this->gwucto_move( "blava","banska","106646",2496.78,1,"2021-11-02"); // |  4993.56 | 0.00 |  4993.56 | BREI AB2010121B1A1
 $this->gwucto_move( "blava","banska","214959",2303.80,1,"2021-01-01"); // |  4607.60 | 0.00 |  4607.60 | BREI AB201012/BF73/279S
 $this->gwucto_move( "blava","banska","230748",3294.72,1,"2021-11-17"); // |  6589.44 | 0.00 |  6589.44 | BREI U77310101A1U1
 $this->gwucto_move( "blava","banska","229314",3014.10,1,"2021-11-25"); // |  6028.20 | 0.00 |  6028.20 | BREI UB2010121B1A1
 $this->gwucto_move( "blava","banska","230934",1594.22,1,"2021-12-28"); // |  3188.44 | 0.00 |  3188.44 | BREI X82310D51B1S1

  $this->gwucto_move( "blava","banska","190382",201.70,1,"2021-01-01"); //  |   403.40 | 0.00 |   403.40 | CERS C001.410.36.037.01
 $this->gwucto_move( "blava","banska","199330",396.10,1,"2021-01-01"); //  |   792.20 | 0.00 |   792.20 | CERS C001.647.11.057.00
 $this->gwucto_move( "blava","banska","208527",366.64,1,"2021-01-01"); //  |   733.28 | 0.00 |   733.28 | CERS C001.647.16.037.01
 $this->gwucto_move( "blava","banska","183386",282.90,1,"2021-01-01"); //  |   565.80 | 0.00 |   565.80 | CERS C004.217.11.036.00
 $this->gwucto_move( "blava","banska","228488",421.05,1,"2021-08-06"); //  |   842.10 | 0.00 |   842.10 | CERS C029.426.11.091.60
 $this->gwucto_move( "blava","banska","209290",310.00,1,"2021-01-01"); //  |   620.00 | 0.00 |   620.00 | CERS C029.807.16.081.01
 $this->gwucto_move( "blava","banska","220481",222.35,1,"2021-09-27"); //  |   444.70 | 0.00 |   444.70 | CERS C032.051.11.036.00
 $this->gwucto_move( "blava","banska","220342",198.69,1,"2021-08-16"); //  |   397.38 | 0.00 |   397.38 | CERS C032.051.16.056.00
 $this->gwucto_move( "blava","banska","231569",222.35,1,"2021-09-27"); //  |   444.70 | 0.00 |   444.70 | CERS C032.251.11.091.09
 $this->gwucto_move( "blava","banska","206173",359.20,1,"2021-01-01"); //  |   718.40 | 0.00 |   718.40 | CERS C032.417.11.041.00
 $this->gwucto_move( "blava","banska","223483",307.51,1,"2021-12-13"); //  |   615.02 | 0.00 |   615.02 | CERS C032.430.16.041.00
 $this->gwucto_move( "blava","banska","223483",319.80,1,"2021-01-01"); //  |   639.60 | 0.00 |   639.60 | CERS C032.430.16.041.00
 $this->gwucto_move( "blava","banska","230408",272.03,1,"2021-09-27"); //  |   544.06 | 0.00 |   544.06 | CERS C032.451.11.047.00
 $this->gwucto_move( "blava","banska","218652",267.30,1,"2021-12-13"); //  |   534.60 | 0.00 |   534.60 | CERS C032.851.44.087.00
 $this->gwucto_move( "blava","banska","232355",193.96,1,"2021-09-27"); //  |   387.92 | 0.00 |   387.92 | CERS C033.051.11.118.01
 $this->gwucto_move( "blava","banska","219293",287.80,1,"2021-01-01"); //  |   575.60 | 0.00 |   575.60 | CERS C033.234.36.048.01
 $this->gwucto_move( "blava","banska","215308",287.80,1,"2021-01-01"); //  |   575.60 | 0.00 |   575.60 | CERS C033.234.36.118.00
 $this->gwucto_move( "blava","banska","214719",206.60,1,"2021-01-01"); //  |   413.20 | 0.00 |   413.20 | CERS C033.251.36.111.00
 $this->gwucto_move( "blava","banska","209932",366.64,1,"2021-01-01"); //  |   733.28 | 0.00 |   733.28 | CERS C033.450.11.041.00
 $this->gwucto_move( "blava","banska","230828",222.35,1,"2021-09-27"); //  |   444.70 | 0.00 |   444.70 | CERS C033.851.36.057.00
 $this->gwucto_move( "blava","banska","230238",227.09,1,"2021-09-27"); //  |   454.18 | 0.00 |   454.18 | CERS C034.417.11.047.00
 $this->gwucto_move( "blava","banska","106937",227.09,1,"2021-10-04"); //  |   454.18 | 0.00 |   454.18 | CERS C034.417.11.097.00
 $this->gwucto_move( "blava","banska","210058",248.50,1,"2021-01-01"); //  |   497.00 | 0.00 |   497.00 | CERS C034.417.36.057.00
 $this->gwucto_move( "blava","banska","210058",238.91,1,"2021-10-04"); //  |   477.82 | 0.00 |   477.82 | CERS C034.417.36.057.00
 $this->gwucto_move( "blava","banska","210914",310.00,1,"2021-01-01"); //  |   620.00 | 0.00 |   620.00 | CERS C034.417.44.087.00
 $this->gwucto_move( "blava","banska","230231",227.09,1,"2021-01-01"); //  |   454.18 | 0.00 |   454.18 | CERS C035.417.11.057.00
 $this->gwucto_move( "blava","banska","230829",212.89,1,"2021-10-04"); //  |   425.78 | 0.00 |   425.78 | CERS C035.417.16.037.01
 $this->gwucto_move( "blava","banska","231849",404.49,1,"2021-09-27"); //  |   808.98 | 0.00 |   808.98 | CERS C036.407.16.040.00
 $this->gwucto_move( "blava","banska","220749",321.70,1,"2021-10-04"); //  |   643.40 | 0.00 |   643.40 | CERS C036.407.16.050.00



   }


   // Prepis si D1 na druhy den...

  // Yii::$app->db->createCommand( "update yuctomodel set d1='2020-01-02' where m_type_id=0 and d1='2020-01-01'" )->execute();


   // SHOW RESULTS

    $sumres =   " select sum(pcs) xpcs,sum(pcs*n_price) xsum ,sum(banska*n_price) xbb,sum(blava*n_price) xba,sum(blava) xxba,sum(banska) xxbb from yuctomodel where xx<>'!'";
    $xsumres = Yii::$app->db->createCommand($sumres)->queryOne();
    print_r($xsumres);
    echo "test to zero: ";
    echo $xsumres['xbb']+$xsumres['xba']-$xsumres['xsum'];
    echo "\n\n";
    return ExitCode::OK;
  }
}

