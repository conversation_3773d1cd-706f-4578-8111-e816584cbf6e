<?php

namespace app\commands\ucto;

use Yii;
use yii\console\Controller;
use yii\console\ExitCode;

class NrrecountracioController extends Controller
{


  public function actionIndex()
  {

  require_once( "racioba.inc" );

$pole = array(  'banska','blava' );

//$pole = array(  'x3405x0','x3731x0' );


foreach( $pole as $par_sklad ) {
  
  //DOC_NR recount prijemky

  $s2 = Yii::$app->db->createCommand( 
    "SELECT row_number()  OVER () as  nn,yucto.move_id as xmove_id,
      yucto.d1 as xd1 from
      ".$tabulkaModel." yucto 
        where 
          yucto.move_id in (select move_id from movement)
          and
      ".$par_sklad." <>0 and
      yucto.n_price <> 0 and
      yucto.xx = 'R'
      group by move_id,d1 order by
      yucto.d1 asc,
      yucto.move_id asc
  ")->queryAll();

  $l2=1;

   foreach ($s2 as $re) {
    $p1 = $re['xmove_id'];
    $p2 = $re['nn'];
    $p4 = $re['xd1'];
    Yii::$app->db->createCommand("update ".$tabulkaModel." set doc_nr= ".$p2." where move_id=".$p1." and d1='".$p4."'")->execute();
    echo $l2.".";
    $l2++;
  }

  //DOC_NR recount vydajky

  $s2 = Yii::$app->db->createCommand( 
    "SELECT row_number() OVER ()  as nn,yucto.move_id as xmove_id,
      yucto.d1 as xd1 from
      ".$tabulkaModel." yucto 
        where 
          yucto.move_id in (select move_id from movement)
          and
      ".$par_sklad." <>0 and
      yucto.n_price <> 0 and
      yucto.xx = 'F'
      group by move_id,d1 order by
      yucto.d1 asc,
      yucto.move_id asc
  ")->queryAll();

  $l2=1;
   foreach ($s2 as $re) {
    $p1 = $re['xmove_id'];
    $p2 = $re['nn'];
    $p4 = $re['xd1'];
    $yy= "update ".$tabulkaModel." set doc_nr= ".$p2." where move_id=".$p1." and d1='".$p4."'";
    echo $l2.".";
    Yii::$app->db->createCommand( $yy )->execute();
  $l2++;
  }


}
/////END DOC_NR

//PRECISLOVANIE PREVODOV ma spolocne cisla, mimo slucky skladov teda
  $s3 = Yii::$app->db->createCommand( 
  "SELECT row_number() OVER () as  nn,d1 as xd1 from ".$tabulkaModel." where move_id<0 group by d1,doc_nr order by d1" )->queryAll();
  $l3=1;
   foreach ($s3 as $re) {
    $p2 = $re['nn'];
    $p4 = $re['xd1'];
    $yy = "update ".$tabulkaModel." set doc_nr= ".$p2." where move_id<0 and  d1='".$p4."'";
    echo $yy;
    Yii::$app->db->createCommand( $yy )->execute();
    $l3++;
  }

    return ExitCode::OK;
  }
}


