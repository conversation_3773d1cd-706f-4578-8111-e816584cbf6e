{"auto_complete": {"selected_items": [["sto", "stock_id1"], ["mt", "mtypes"], ["stock", "stock_id1"], ["ser", "service"], ["quer", "queryScalar"], ["succe", "success"], ["Products", "productsearchModel"], ["pas", "password_hash"], ["pass", "password"], ["isUpda", "isUpdateField"], ["cl", "col-sm-6"], ["user", "user_name"], ["str", "str<PERSON><PERSON>er"], ["_", "_query"], ["sta", "statusfilter"], ["use", "user_name"], ["us", "user_name"], ["isUse", "isUserShop"], ["te", "text2"], ["SOR", "SORT_ASC"], ["orde", "orderBy"], ["mul", "multiaddLabel"], ["mu", "multiaddPlaceholder"], ["que", "_query"], ["mod", "modelconfig"], ["cli", "clip_id"], ["clip", "clip_id"], ["contr", "controllers"], ["adr", "adr_id"], ["mov", "movement"], ["stoc", "stock_id2"], ["ba", "background-color\tproperty"], ["var", "var_dump"], ["qu", "queryOne"], ["usern", "usernames"], ["emp", "emp_id"], ["Da", "DateRangePicker"], ["dater", "daterange"], ["he", "heading"], ["q", "query"], ["be", "beforeValidate"], ["id", "id"], ["qy", "queryx"], ["base", "base64_decode"], ["is_n", "is_null"], ["execu", "execute"], ["SaveC", "SaveClipForm"], ["dat", "dataSumProvider"], ["sum", "sumofclip"], ["tex", "text1"], ["mo", "movelistsize"], ["val", "value"], ["size", "sizeof"], ["for", "foreach\tforeach …"], ["m_", "m_detail"], ["m_d", "m_detailx"], ["array_m", "array_merge"], ["ht", "htmlspecialchars_decode"], ["exec", "exec"], ["tempmo", "tempmove_info"], ["my", "mtypename"], ["mty", "mtype"], ["m", "mtypename"], ["move", "move_id"], ["isn", "is_null"], ["gri", "gridColumn"], ["sear", "searchModel"], ["mtyp", "mtypename"], ["data", "dataProvider"], ["vali", "validatePassword"], ["print", "print_r"], ["strto", "strtotime"], ["activ", "ActiveDataProvider"], ["card", "card_nr"], ["car", "card_type"], ["ty", "typeof"]]}, "buffers": [], "build_system": "", "build_system_choices": [], "build_varint": "", "command_palette": {"height": 0.0, "last_filter": "", "selected_items": [["git status", "Set Syntax: <PERSON>it Attributes"], ["Snippet: yii", "Snippet: Yii2 - TimestampBehaviorr"], ["json", "Pretty JSON: JSON 2 XML"], ["\\", "Pretty JSON: JSON query with ./jq"], ["diff", "Set Syntax: Diff"], ["jso", "Pretty JSON: Format (Pretty Print) JSON"], ["pack", "Package Control: Install Package"], ["Snippeyii2", "Snippet: Yii2 - Yii::\\$app->controller"], ["Snippet: <PERSON><PERSON>", "Snippet: Yii2 - Html::mailto()"], ["install package Pr", "Package Control: Install Package"], ["packag", "Install Package Control"], ["", "Indentation: Reindent Lines"]], "width": 0.0}, "console": {"height": 0.0, "history": []}, "distraction_free": {"menu_visible": true, "show_minimap": false, "show_open_files": false, "show_tabs": false, "side_bar_visible": false, "status_bar_visible": false}, "file_history": ["/Users/<USER>/Projects/pgestock/controllers/TempmoveController.php", "/Users/<USER>/Projects/pgestock/views/sql/stat1.php", "/Users/<USER>/Projects/pgestock/views/tempmove/clipView.php", "/Users/<USER>/Projects/pgestock/views/statistics/view.php", "/Users/<USER>/Projects/pgestock/views/product/_form.php", "/Users/<USER>/Projects/pgestock/views/tempmove/createFromShop.php", "/Users/<USER>/Projects/pgestock/views/kod/index.php", "/Users/<USER>/Projects/pgestock/models/TempmoveSearch.php", "/Users/<USER>/Projects/pgestock/models/WowCardsSearch.php", "/Users/<USER>/Projects/pgestock/controllers/SqlController.php", "/Users/<USER>/Projects/pgestock/controllers/PrintController.php", "/Users/<USER>/Projects/pgestock/controllers/SalesController.php", "/Users/<USER>/Projects/pgestock/controllers/MovementController.php", "/Users/<USER>/Projects/pgestock/controllers/DbaController.php", "/Users/<USER>/Projects/pgestock/views/tempmove/_formSqlclipdisc.php", "/Users/<USER>/Projects/pgestock/views/tempmove/index.php", "/Users/<USER>/Projects/pgestock/views/movement/_pdf.php", "/Users/<USER>/Projects/pgestock/views/movement/_form.php", "/Users/<USER>/Projects/pgestock/views/movement/_detail.php", "/Users/<USER>/Projects/pgestock/views/tempmove/view.php", "/Users/<USER>/Projects/pgestock/controllers/EshopController.php", "/Users/<USER>/Projects/pgestock/models/Tempmove.php", "/Users/<USER>/Projects/pgestock/views/address/_form.php", "/Users/<USER>/Projects/pgestock/views/address/index.php", "/Users/<USER>/Projects/pgestock/views/address/view.php", "/Users/<USER>/Projects/pgestock/models/Address.php", "/Users/<USER>/Projects/pgestock/controllers/AdrcontactController.php", "/Users/<USER>/Projects/pgestock/controllers/AddressController.php", "/Users/<USER>/Projects/pgestock/models/MUserConfigSearch.php", "/Users/<USER>/Projects/pgestock/models/MUserConfig.php", "/Users/<USER>/Projects/pgestock/views/stockdetail/view.php", "/Users/<USER>/Projects/pgestock/views/dba/update.php", "/Users/<USER>/Projects/pgestock/controllers/CustomersController.php", "/Users/<USER>/Projects/pgestock/views/dba/_form.php", "/Users/<USER>/Projects/pgestock/views/muserconfig/_form.php", "/Users/<USER>/Projects/pgestock/models/AddressSearch.php", "/Users/<USER>/Projects/pgestock/controllers/EstockTools.php", "/Users/<USER>/Projects/pgestock/views/tempmove/_search.php", "/Users/<USER>/Projects/pgestock/views/tempmove/_pdf.php", "/Users/<USER>/Projects/pgestock/views/tempmove/_form.php", "/Users/<USER>/Projects/pgestock/views/tempmove/create.php", "/Users/<USER>/Projects/pgestock/views/product/indexclipsearch.php", "/Users/<USER>/Projects/pgestock/views/product/index.php", "/Users/<USER>/Projects/pgestock/views/movement/onstock.php", "/Users/<USER>/Projects/pgestock/views/layouts/left.php", "/Users/<USER>/Projects/pgestock/models/User.php", "/Users/<USER>/Projects/pgestock/views/dba/index.php", "/Users/<USER>/Projects/pgestock/views/stockdetail/index.php", "/Users/<USER>/Projects/pgestock/config/web.php", "/Users/<USER>/Projects/pgestock/views/site/unsearch.php", "/Users/<USER>/Projects/pgestock/controllers/SiteController.php", "/Users/<USER>/Projects/pgestock/views/sales/_form.php", "/Users/<USER>/Projects/pgestock/controllers/ProductController.php", "/Users/<USER>/Projects/pgestock/models/ProductSearch.php", "/Users/<USER>/Projects/pgestock/models/CustomersSearch.php", "/Users/<USER>/Projects/pgestock/models/DocumentSearch.php", "/Users/<USER>/Projects/pgestock/models/EshopSearch.php", "/Users/<USER>/Projects/pgestock/models/KodSearch.php", "/Users/<USER>/Projects/pgestock/models/MdetailprodSearch.php", "/Users/<USER>/Projects/pgestock/models/MDetailSearch.php", "/Users/<USER>/Projects/pgestock/models/MovementSearch.php", "/Users/<USER>/Projects/pgestock/models/NbsratesSearch.php", "/Users/<USER>/Projects/pgestock/views/tempmove/_formClip.php", "/Users/<USER>/Projects/pgestock/views/tempmove/_formSqlclip.php", "/Users/<USER>/Projects/pgestock/mail/layouts/sendwstockpwd.php", "/Users/<USER>/Projects/pgestock/config/Dbinit.php", "/Users/<USER>/Projects/pgestock/models/SqlclipForm.php", "/Users/<USER>/Projects/pgestock/models/SqlclipdiscForm.php", "/Users/<USER>/Projects/pgestock/views/tempmove/shopView.php", "/Users/<USER>/Projects/pgestock/views/layouts/content.php", "/Users/<USER>/Projects/pgestock/views/stockdetail/_form.php", "/Users/<USER>/Projects/pgestock/models/OnstockForm.php", "/Users/<USER>/Projects/pgestock/views/product/create.php", "/Users/<USER>/Projects/pgestock/controllers/MuserconfigController.php", "/Users/<USER>/Projects/pgestock/models/SearchWowSales.php", "/Users/<USER>/Projects/pgestock/controllers/StockdetailController.php", "/Users/<USER>/Projects/pgestock/views/movement/mymoves.php", "/Users/<USER>/Projects/pgestock/models/MUser.php", "/Users/<USER>/Projects/pgestock/p.dat", "/Users/<USER>/Projects/pgestock/views/document/index.php", "/Users/<USER>/Projects/pgestock/models/Product.php", "/Users/<USER>/Projects/pgestock/controllers/DocumentController.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/controllers/MuserconfigController.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/controllers/MovementController.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/controllers/NbsratesController.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/views/mdetailprod/_actions.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/views/product/view.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/views/product/_detail.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/views/product/_form.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/views/product/_search.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/views/product/index.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/models/ProductSearch.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/models/Movement.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/controllers/SqlController.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/views/product/_pdf.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/controllers/ProductController.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/models/MultiaddForm.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/views/product/_stockcardtitle.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/views/product/_stockcard2.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/models/TempmoveSearch.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/views/product/create.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/models/SaveClipForm.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/views/mdetailprod/_detail.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/views/tempmove/clipView.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/views/tempmove/create.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/models/Eshop.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/views/movement/mymoves.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/views/movement/index.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/views/eshop/index.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/views/layouts/left.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/controllers/PrintController.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/views/tempmove/_formClip.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/views/tempmove/_form.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/config/web.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/config/Dbinit.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/views/tempmove/shopView.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/views/address/_pdf.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/views/muserconfig/_form.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/views/muserconfig/index.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/controllers/SalesController.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/views/product/_expand.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/controllers/EstockTools.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/views/thserials/indexall.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/views/movement/serials.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/views/movement/_expand.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/models/MUserConfig.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/models/Tempmove.php", "/Users/<USER>/server1/var/www/vhosts/pgestock/views/product/indexclipsearch.php"], "find": {"height": 24.0}, "find_in_files": {"height": 101.0, "where_history": []}, "find_state": {"case_sensitive": false, "find_history": [], "highlight": true, "in_selection": false, "preserve_case": false, "regex": true, "replace_history": [], "reverse": false, "show_context": true, "use_buffer2": true, "whole_word": false, "wrap": true}, "groups": [{"sheets": []}], "incremental_find": {"height": 24.0}, "input": {"height": 36.0}, "layout": {"cells": [[0, 0, 1, 1]], "cols": [0.0, 1.0], "rows": [0.0, 1.0]}, "menu_visible": true, "output.find_results": {"height": 0.0}, "pinned_build_system": "", "project": "composer.sublime-project", "replace": {"height": 44.0}, "save_all_on_build": true, "select_file": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "select_project": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "select_symbol": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "selected_group": 0, "settings": {}, "show_minimap": true, "show_open_files": false, "show_tabs": true, "side_bar_visible": true, "side_bar_width": 297.0, "status_bar_visible": true, "template_settings": {}}