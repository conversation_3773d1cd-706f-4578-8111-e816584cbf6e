{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "81866863a2b238bd49747dd00169da2c", "packages": [{"name": "almasaeed2010/adminlte", "version": "v2.4.18", "source": {"type": "git", "url": "https://github.com/ColorlibHQ/AdminLTE.git", "reference": "e7ffa67a4649dc08d2018708a38604a6c0a02ab6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ColorlibHQ/AdminLTE/zipball/e7ffa67a4649dc08d2018708a38604a6c0a02ab6", "reference": "e7ffa67a4649dc08d2018708a38604a6c0a02ab6", "shasum": ""}, "require": {"bower-asset/jquery": ">=1.9.0 <4.0.0"}, "type": "template", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "AdminLTE - admin control panel and dashboard that's based on Bootstrap 3", "homepage": "https://adminlte.io/", "keywords": ["JS", "admin", "back-end", "css", "less", "responsive", "template", "theme", "web"], "support": {"issues": "https://github.com/almasaeed2010/AdminLTE/issues", "source": "https://github.com/ColorlibHQ/AdminLTE/tree/v2.4.18"}, "time": "2019-08-29T08:20:20+00:00"}, {"name": "bower-asset/bootstrap", "version": "v3.4.1", "source": {"type": "git", "url": "https://github.com/twbs/bootstrap.git", "reference": "68b0d231a13201eb14acd3dc84e51543d16e5f7e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twbs/bootstrap/zipball/68b0d231a13201eb14acd3dc84e51543d16e5f7e", "reference": "68b0d231a13201eb14acd3dc84e51543d16e5f7e"}, "require": {"bower-asset/jquery": ">=1.9.1,<4.0"}, "type": "bower-asset", "license": ["MIT"]}, {"name": "bower-asset/inputmask", "version": "5.0.8", "source": {"type": "git", "url": "**************:RobinHerbots/Inputmask.git", "reference": "e0f39e0c93569c6b494c3a57edef2c59313a6b64"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/RobinHerbots/Inputmask/zipball/e0f39e0c93569c6b494c3a57edef2c59313a6b64", "reference": "e0f39e0c93569c6b494c3a57edef2c59313a6b64"}, "require": {"bower-asset/jquery": ">=1.7"}, "type": "bower-asset", "license": ["http://opensource.org/licenses/mit-license.php"]}, {"name": "bower-asset/jquery", "version": "3.7.1", "source": {"type": "git", "url": "https://github.com/jquery/jquery-dist.git", "reference": "fde1f76e2799dd877c176abde0ec836553246991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jquery/jquery-dist/zipball/fde1f76e2799dd877c176abde0ec836553246991", "reference": "fde1f76e2799dd877c176abde0ec836553246991"}, "type": "bower-asset", "license": ["MIT"]}, {"name": "bower-asset/jquery-ui", "version": "1.12.1", "source": {"type": "git", "url": "**************:components/jqueryui.git", "reference": "44ecf3794cc56b65954cc19737234a3119d036cc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/components/jqueryui/zipball/44ecf3794cc56b65954cc19737234a3119d036cc", "reference": "44ecf3794cc56b65954cc19737234a3119d036cc"}, "require": {"bower-asset/jquery": ">=1.6"}, "type": "bower-asset", "license": ["MIT"]}, {"name": "bower-asset/punycode", "version": "v2.3.1", "source": {"type": "git", "url": "https://github.com/mathiasbynens/punycode.js.git", "reference": "9e1b2cda98d215d3a73fcbfe93c62e021f4ba768"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mathiasbynens/punycode.js/zipball/9e1b2cda98d215d3a73fcbfe93c62e021f4ba768", "reference": "9e1b2cda98d215d3a73fcbfe93c62e021f4ba768"}, "type": "bower-asset"}, {"name": "bower-asset/yii2-pjax", "version": "2.0.8", "source": {"type": "git", "url": "**************:yiisoft/jquery-pjax.git", "reference": "a9298d57da63d14a950f1b94366a864bc62264fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/jquery-pjax/zipball/a9298d57da63d14a950f1b94366a864bc62264fb", "reference": "a9298d57da63d14a950f1b94366a864bc62264fb"}, "require": {"bower-asset/jquery": ">=1.8"}, "type": "bower-asset", "license": ["MIT"]}, {"name": "cebe/markdown", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/cebe/markdown.git", "reference": "9bac5e971dd391e2802dca5400bbeacbaea9eb86"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cebe/markdown/zipball/9bac5e971dd391e2802dca5400bbeacbaea9eb86", "reference": "9bac5e971dd391e2802dca5400bbeacbaea9eb86", "shasum": ""}, "require": {"lib-pcre": "*", "php": ">=5.4.0"}, "require-dev": {"cebe/indent": "*", "facebook/xhprof": "*@dev", "phpunit/phpunit": "4.1.*"}, "bin": ["bin/markdown"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"cebe\\markdown\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://cebe.cc/", "role": "Creator"}], "description": "A super fast, highly extensible markdown parser for PHP", "homepage": "https://github.com/cebe/markdown#readme", "keywords": ["extensible", "fast", "gfm", "markdown", "markdown-extra"], "support": {"issues": "https://github.com/cebe/markdown/issues", "source": "https://github.com/cebe/markdown"}, "time": "2018-03-26T11:24:36+00:00"}, {"name": "cebe/yii2-gravatar", "version": "1.1", "target-dir": "cebe/gravatar", "source": {"type": "git", "url": "https://github.com/cebe/yii2-gravatar.git", "reference": "c9c01bd14c9bdee9e5ae1ef1aad23f80c182c057"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cebe/yii2-gravatar/zipball/c9c01bd14c9bdee9e5ae1ef1aad23f80c182c057", "reference": "c9c01bd14c9bdee9e5ae1ef1aad23f80c182c057", "shasum": ""}, "require": {"yiisoft/yii2": "*"}, "type": "yii2-extension", "autoload": {"psr-0": {"cebe\\gravatar\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://cebe.cc/", "role": "Core framework development"}], "description": "<PERSON><PERSON><PERSON><PERSON> Widget for <PERSON><PERSON> 2", "keywords": ["gravatar", "yii"], "support": {"irc": "irc://irc.freenode.net/yii", "issues": "https://github.com/cebe/yii2-gravatar/issues", "source": "https://github.com/cebe/yii2-gravatar"}, "time": "2013-12-10T17:49:58+00:00"}, {"name": "dmstr/yii2-adminlte-asset", "version": "2.6.2", "source": {"type": "git", "url": "https://github.com/dmstr/yii2-adminlte-asset.git", "reference": "c96336e1960ebc6c1e72487a7c6ca1a1589519fe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dmstr/yii2-adminlte-asset/zipball/c96336e1960ebc6c1e72487a7c6ca1a1589519fe", "reference": "c96336e1960ebc6c1e72487a7c6ca1a1589519fe", "shasum": ""}, "require": {"almasaeed2010/adminlte": "^2.4.0", "cebe/yii2-gravatar": "1.*", "rmrevin/yii2-fontawesome": "~2.9", "yiisoft/yii2": "2.*", "yiisoft/yii2-bootstrap": "~2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"dmstr\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "AdminLTE backend theme asset bundle for Yii 2.0 Framework", "keywords": ["AdminLTE", "admin", "asset", "backend", "css", "extension", "less", "theme", "yii2"], "support": {"issues": "https://github.com/dmstr/yii2-adminlte-asset/issues", "source": "https://github.com/dmstr/yii2-adminlte-asset/tree/master"}, "time": "2018-07-24T14:47:13+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab", "reference": "dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9", "phpstan/phpstan": "1.4.10 || 1.10.15", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psalm/plugin-phpunit": "0.18.4", "psr/log": "^1 || ^2 || ^3", "vimeo/psalm": "4.30.0 || 5.12.0"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "lib/Doctrine/Deprecations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.3"}, "time": "2024-01-30T19:34:25+00:00"}, {"name": "doctrine/lexer", "version": "2.1.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6", "reference": "861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^4.11 || ^5.21"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/2.1.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2024-02-05T11:35:39+00:00"}, {"name": "egulias/email-validator", "version": "3.2.6", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7", "reference": "e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7", "shasum": ""}, "require": {"doctrine/lexer": "^1.2|^2", "php": ">=7.2", "symfony/polyfill-intl-idn": "^1.15"}, "require-dev": {"phpunit/phpunit": "^8.5.8|^9.3.3", "vimeo/psalm": "^4"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/3.2.6"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2023-06-01T07:04:22+00:00"}, {"name": "ezyang/htmlpurifier", "version": "v4.17.0", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "bbc513d79acf6691fa9cf10f192c90dd2957f18c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/bbc513d79acf6691fa9cf10f192c90dd2957f18c", "reference": "bbc513d79acf6691fa9cf10f192c90dd2957f18c", "shasum": ""}, "require": {"php": "~5.6.0 || ~7.0.0 || ~7.1.0 || ~7.2.0 || ~7.3.0 || ~7.4.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0"}, "require-dev": {"cerdic/css-tidy": "^1.7 || ^2.0", "simpletest/simpletest": "dev-master"}, "suggest": {"cerdic/css-tidy": "If you want to use the filter 'Filter.ExtractStyleBlocks'.", "ext-bcmath": "Used for unit conversion and imagecrash protection", "ext-iconv": "Converts text to and from non-UTF-8 encodings", "ext-tidy": "Used for pretty-printing HTML"}, "type": "library", "autoload": {"files": ["library/HTMLPurifier.composer.php"], "psr-0": {"HTMLPurifier": "library/"}, "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "support": {"issues": "https://github.com/ezyang/htmlpurifier/issues", "source": "https://github.com/ezyang/htmlpurifier/tree/v4.17.0"}, "time": "2023-11-17T15:01:25+00:00"}, {"name": "fortawesome/font-awesome", "version": "v4.7.0", "source": {"type": "git", "url": "https://github.com/FortAwesome/Font-Awesome.git", "reference": "a8386aae19e200ddb0f6845b5feeee5eb7013687"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FortAwesome/Font-Awesome/zipball/a8386aae19e200ddb0f6845b5feeee5eb7013687", "reference": "a8386aae19e200ddb0f6845b5feeee5eb7013687", "shasum": ""}, "require-dev": {"jekyll": "1.0.2", "lessc": "1.4.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.6.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["OFL-1.1", "MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://twitter.com/davegandy", "role": "Developer"}], "description": "The iconic font and CSS framework", "homepage": "http://fontawesome.io/", "keywords": ["FontAwesome", "awesome", "bootstrap", "font", "icon"], "support": {"issues": "https://github.com/FortAwesome/Font-Awesome/issues", "source": "https://github.com/FortAwesome/Font-Awesome/tree/v4.7.0"}, "time": "2016-10-24T15:52:54+00:00"}, {"name": "hscstudio/yii2-export", "version": "v1.2.2", "source": {"type": "git", "url": "https://github.com/hscstudio/yii2-export.git", "reference": "1fd85e2e3672a411109f51a32235f4280c650d26"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hscstudio/yii2-export/zipball/1fd85e2e3672a411109f51a32235f4280c650d26", "reference": "1fd85e2e3672a411109f51a32235f4280c650d26", "shasum": ""}, "require": {"phpoffice/phpexcel": "1.8.*", "yiisoft/yii2": "*"}, "type": "yii2-extension", "autoload": {"files": ["libraries/tbs/tbs_class.php", "libraries/tbs/plugins/tbs_plugin_opentbs.php", "libraries/mpdf/mpdf.php"], "psr-4": {"hscstudio\\export\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "hafid<PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "description": "Excel, Word, PDF", "keywords": ["extension", "yii2"], "support": {"issues": "https://github.com/hscstudio/yii2-export/issues", "source": "https://github.com/hscstudio/yii2-export/tree/master"}, "abandoned": true, "time": "2016-02-13T08:57:40+00:00"}, {"name": "kartik-v/bootstrap-fileinput", "version": "v5.5.4", "source": {"type": "git", "url": "https://github.com/kartik-v/bootstrap-fileinput.git", "reference": "8de1bed638823c70272b2578847e2b31e42677ba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/bootstrap-fileinput/zipball/8de1bed638823c70272b2578847e2b31e42677ba", "reference": "8de1bed638823c70272b2578847e2b31e42677ba", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.5.x-dev"}}, "autoload": {"psr-4": {"kartik\\plugins\\fileinput\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced HTML 5 file input for Bootstrap 5.x, 4.x, and 3.x with features for file preview for many file types, multiple selection, ajax uploads, and more.", "homepage": "https://github.com/kartik-v/bootstrap-fileinput", "keywords": ["ajax", "bootstrap", "delete", "file", "image", "input", "j<PERSON>y", "multiple", "preview", "progress", "upload"], "support": {"issues": "https://github.com/kartik-v/bootstrap-fileinput/issues", "source": "https://github.com/kartik-v/bootstrap-fileinput/tree/v5.5.4"}, "funding": [{"url": "https://opencollective.com/bootstrap-fileinput", "type": "open_collective"}], "time": "2024-04-09T03:30:45+00:00"}, {"name": "kartik-v/bootstrap-popover-x", "version": "v1.5.4", "source": {"type": "git", "url": "https://github.com/kartik-v/bootstrap-popover-x.git", "reference": "879def62529797833e10885b6e77864e83e9e240"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/bootstrap-popover-x/zipball/879def62529797833e10885b6e77864e83e9e240", "reference": "879def62529797833e10885b6e77864e83e9e240", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "autoload": {"psr-4": {"kartik\\plugins\\popover\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Bootstrap Popover Extended - Popover with modal behavior, styling enhancements and more.", "homepage": "https://github.com/kartik-v/bootstrap-popover-x", "keywords": ["bootstrap", "extended", "j<PERSON>y", "modal", "modal-popover", "popover", "popover-x"], "support": {"issues": "https://github.com/kartik-v/bootstrap-popover-x/issues", "source": "https://github.com/kartik-v/bootstrap-popover-x/tree/v1.5.4"}, "funding": [{"url": "https://opencollective.com/bootstrap-popover-x", "type": "open_collective"}], "time": "2024-03-12T13:23:15+00:00"}, {"name": "kartik-v/bootstrap-star-rating", "version": "v4.1.2", "source": {"type": "git", "url": "https://github.com/kartik-v/bootstrap-star-rating.git", "reference": "c301efed4c82e9d5f11a0845ae428ba60931b44e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/bootstrap-star-rating/zipball/c301efed4c82e9d5f11a0845ae428ba60931b44e", "reference": "c301efed4c82e9d5f11a0845ae428ba60931b44e", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.1.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A simple yet powerful JQuery star rating plugin for Bootstrap.", "homepage": "https://github.com/kartik-v/bootstrap-star-rating", "keywords": ["Rating", "awesome", "bootstrap", "font", "glyphicon", "star", "svg"], "support": {"issues": "https://github.com/kartik-v/bootstrap-star-rating/issues", "source": "https://github.com/kartik-v/bootstrap-star-rating/tree/v4.1.2"}, "funding": [{"url": "https://opencollective.com/bootstrap-star-rating", "type": "open_collective"}], "time": "2021-09-20T03:06:01+00:00"}, {"name": "kartik-v/bootstrap-tabs-x", "version": "v1.3.5", "source": {"type": "git", "url": "https://github.com/kartik-v/bootstrap-tabs-x.git", "reference": "12e5eeceda5ecbdf1bc7e429c4cea69c0e0b5267"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/bootstrap-tabs-x/zipball/12e5eeceda5ecbdf1bc7e429c4cea69c0e0b5267", "reference": "12e5eeceda5ecbdf1bc7e429c4cea69c0e0b5267", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"kartik\\plugins\\tabs\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Extended Bootstrap Tabs with ability to align tabs in multiple ways, add borders, rotated titles, and more.", "homepage": "https://github.com/kartik-v/bootstrap-tabs-x", "keywords": ["bootstrap", "extended", "j<PERSON>y", "modal", "modal-tabs", "tabs", "tabs-x"], "support": {"issues": "https://github.com/kartik-v/bootstrap-tabs-x/issues", "source": "https://github.com/kartik-v/bootstrap-tabs-x/tree/v1.3.5"}, "time": "2021-09-21T02:01:51+00:00"}, {"name": "kartik-v/dependent-dropdown", "version": "v1.4.9", "source": {"type": "git", "url": "https://github.com/kartik-v/dependent-dropdown.git", "reference": "54a8806002ee21b744508a2edb95ed01d35c6cf9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/dependent-dropdown/zipball/54a8806002ee21b744508a2edb95ed01d35c6cf9", "reference": "54a8806002ee21b744508a2edb95ed01d35c6cf9", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-4": {"kartik\\plugins\\depdrop\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A multi level dependent dropdown JQuery plugin that allows nested dependencies.", "homepage": "https://github.com/kartik-v/dependent-dropdown", "keywords": ["dependent", "dropdown", "j<PERSON>y", "option", "select"], "support": {"issues": "https://github.com/kartik-v/dependent-dropdown/issues", "source": "https://github.com/kartik-v/dependent-dropdown/tree/master"}, "time": "2019-03-09T10:53:11+00:00"}, {"name": "kartik-v/strength-meter", "version": "v1.1.4", "source": {"type": "git", "url": "https://github.com/kartik-v/strength-meter.git", "reference": "100147f588b12ff819014b445007a0decfd95fbb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/strength-meter/zipball/100147f588b12ff819014b445007a0decfd95fbb", "reference": "100147f588b12ff819014b445007a0decfd95fbb", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"kartik\\plugins\\strengthmeter\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A dynamic strength meter for password input validation with various configurable options.", "homepage": "https://github.com/kartik-v/strength-meter", "keywords": ["bootstrap", "j<PERSON>y", "meter", "password", "strength"], "support": {"issues": "https://github.com/kartik-v/strength-meter/issues", "source": "https://github.com/kartik-v/strength-meter/tree/master"}, "time": "2018-01-20T17:52:01+00:00"}, {"name": "kartik-v/yii2-builder", "version": "v1.6.9", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-builder.git", "reference": "616c2f8d322a45c037a4d32f0ee1eb1457865e73"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-builder/zipball/616c2f8d322a45c037a4d32f0ee1eb1457865e73", "reference": "616c2f8d322a45c037a4d32f0ee1eb1457865e73", "shasum": ""}, "require": {"kartik-v/yii2-grid": ">=3.2.5", "kartik-v/yii2-helpers": ">=1.3.8", "kartik-v/yii2-widget-activeform": ">=1.6.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "autoload": {"psr-4": {"kartik\\builder\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Build forms (single-row or multi-row/tabular) easily for Yii Framework 2.0", "homepage": "https://github.com/kartik-v/yii2-builder", "keywords": ["builder", "extension", "form", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-builder/issues", "source": "https://github.com/kartik-v/yii2-builder/tree/v1.6.9"}, "funding": [{"url": "https://opencollective.com/kartik-v", "type": "open_collective"}], "time": "2022-01-05T14:09:44+00:00"}, {"name": "kartik-v/yii2-date-range", "version": "v1.7.3", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-date-range.git", "reference": "7d80d6e2598e252487fe1acf911b8cb43f45e3b1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-date-range/zipball/7d80d6e2598e252487fe1acf911b8cb43f45e3b1", "reference": "7d80d6e2598e252487fe1acf911b8cb43f45e3b1", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=3.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.7.x-dev"}}, "autoload": {"psr-4": {"kartik\\daterange\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An advanced Yii 2 date range picker input for based on bootstrap-daterangepicker plugin.", "homepage": "https://github.com/kartik-v/yii2-date-range", "keywords": ["bootstrap", "bootstrap 3", "date", "date-range", "extension", "range", "time", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-date-range/issues", "source": "https://github.com/kartik-v/yii2-date-range/tree/v1.7.3"}, "time": "2021-09-01T12:16:39+00:00"}, {"name": "kartik-v/yii2-dialog", "version": "v1.0.6", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-dialog.git", "reference": "510c3a35ffe79987cde9a9366cedbff545fd92d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-dialog/zipball/510c3a35ffe79987cde9a9366cedbff545fd92d4", "reference": "510c3a35ffe79987cde9a9366cedbff545fd92d4", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=3.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\dialog\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An asset bundle for bootstrap3-dialog for Yii 2.0 framework.", "homepage": "https://github.com/kartik-v/yii2-dialog", "keywords": ["alert", "bootstrap", "dialog", "extension", "modal", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-dialog/issues", "source": "https://github.com/kartik-v/yii2-dialog/tree/v1.0.6"}, "time": "2021-09-02T08:26:37+00:00"}, {"name": "kartik-v/yii2-dynagrid", "version": "v1.5.5", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-dynagrid.git", "reference": "4d81fda0775a2994df3d863b1259dc5edf1e8952"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-dynagrid/zipball/4d81fda0775a2994df3d863b1259dc5edf1e8952", "reference": "4d81fda0775a2994df3d863b1259dc5edf1e8952", "shasum": ""}, "require": {"kartik-v/yii2-grid": ">= 3.5.2", "kartik-v/yii2-krajee-base": ">= 3.0.4", "kartik-v/yii2-sortable": "~1.2", "kartik-v/yii2-widget-activeform": ">= 1.6.2", "kartik-v/yii2-widget-select2": ">= 2.2.3"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "autoload": {"psr-4": {"kartik\\dynagrid\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Turbo charge the Yii 2 GridView with personalized columns, page size, and themes.", "homepage": "https://github.com/kartik-v/yii2-dynagrid", "keywords": ["columns", "dynamic", "extension", "grid", "hide", "order", "reorder", "show", "sort", "visibility", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-dynagrid/issues", "source": "https://github.com/kartik-v/yii2-dynagrid/tree/v1.5.5"}, "time": "2023-07-25T08:41:55+00:00"}, {"name": "kartik-v/yii2-editable", "version": "v1.8.0", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-editable.git", "reference": "ae4cc2384e667ba9dfe8bfb0098716caeb2500a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-editable/zipball/ae4cc2384e667ba9dfe8bfb0098716caeb2500a8", "reference": "ae4cc2384e667ba9dfe8bfb0098716caeb2500a8", "shasum": ""}, "require": {"kartik-v/yii2-popover-x": "~1.3", "kartik-v/yii2-widget-activeform": ">=1.6.2"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.8.x-dev"}}, "autoload": {"psr-4": {"kartik\\editable\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced editable widget for Yii 2.0 that allows easy editing of displayed data with numerous configuration possibilities.", "homepage": "https://github.com/kartik-v/yii2-editable", "keywords": ["bootstrap", "editable", "input", "j<PERSON>y", "popover", "popover-x", "widget"], "support": {"issues": "https://github.com/kartik-v/yii2-editable/issues", "source": "https://github.com/kartik-v/yii2-editable/tree/v1.8.0"}, "time": "2022-04-29T12:51:01+00:00"}, {"name": "kartik-v/yii2-export", "version": "v1.4.3", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-export.git", "reference": "5c033d407631f8f4aa1f47e4a53e0204d45bb29c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-export/zipball/5c033d407631f8f4aa1f47e4a53e0204d45bb29c", "reference": "5c033d407631f8f4aa1f47e4a53e0204d45bb29c", "shasum": ""}, "require": {"kartik-v/yii2-dynagrid": ">=1.5.5", "kartik-v/yii2-mpdf": ">=1.0", "phpoffice/phpspreadsheet": ">=1.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-4": {"kartik\\export\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A library to export server/db data in various formats (e.g. excel, html, pdf, csv etc.)", "homepage": "https://github.com/kartik-v/yii2-export", "keywords": ["OpenXML", "csv", "export", "extension", "html", "json", "pdf", "spreadsheet", "text", "widget", "xls", "xlsx", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-export/issues", "source": "https://github.com/kartik-v/yii2-export/tree/v1.4.3"}, "funding": [{"url": "https://opencollective.com/yii2-export", "type": "open_collective"}], "time": "2023-07-25T11:05:33+00:00"}, {"name": "kartik-v/yii2-grid", "version": "v3.5.3", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-grid.git", "reference": "cfc1a8e18bddfe22668783abb70f08583abab0a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-grid/zipball/cfc1a8e18bddfe22668783abb70f08583abab0a9", "reference": "cfc1a8e18bddfe22668783abb70f08583abab0a9", "shasum": ""}, "require": {"kartik-v/yii2-dialog": "~1.0", "kartik-v/yii2-krajee-base": ">=3.0.3"}, "suggest": {"kartik-v/yii2-bootstrap4-dropdown": "For enabling dropdown support when using with Bootstrap v4.x", "kartik-v/yii2-bootstrap5-dropdown": "For enabling dropdown support when using with Bootstrap v5.x", "kartik-v/yii2-mpdf": "For exporting grids to PDF"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "3.5.x-dev"}}, "autoload": {"psr-4": {"kartik\\grid\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Yii 2 GridView on steroids. Various enhancements and utilities for the Yii 2.0 GridView widget.", "homepage": "https://github.com/kartik-v/yii2-grid", "keywords": ["extension", "grid", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-grid/issues", "source": "https://github.com/kartik-v/yii2-grid/tree/v3.5.3"}, "funding": [{"url": "https://opencollective.com/yii2-grid", "type": "open_collective"}], "time": "2023-07-25T11:41:33+00:00"}, {"name": "kartik-v/yii2-helpers", "version": "v1.3.9", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-helpers.git", "reference": "0bec7a97bf9d0293d96be5c496fe9654c4dec94b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-helpers/zipball/0bec7a97bf9d0293d96be5c496fe9654c4dec94b", "reference": "0bec7a97bf9d0293d96be5c496fe9654c4dec94b", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"kartik\\helpers\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A collection of useful helper functions for Yii Framework 2.0", "homepage": "https://github.com/kartik-v/yii2-helpers", "keywords": ["bootstrap", "extension", "helper", "utilities", "yii", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-helpers/issues", "source": "https://github.com/kartik-v/yii2-helpers/tree/master"}, "time": "2018-10-09T08:03:44+00:00"}, {"name": "kartik-v/yii2-krajee-base", "version": "v3.0.5", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-krajee-base.git", "reference": "5c095126d1be47e0bb1f92779b7dc099f6feae31"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-krajee-base/zipball/5c095126d1be47e0bb1f92779b7dc099f6feae31", "reference": "5c095126d1be47e0bb1f92779b7dc099f6feae31", "shasum": ""}, "suggest": {"yiisoft/yii2-bootstrap": "for Krajee extensions to work with Bootstrap 3.x version", "yiisoft/yii2-bootstrap4": "for Krajee extensions to work with Bootstrap 4.x version", "yiisoft/yii2-bootstrap5": "for Krajee extensions to work with Bootstrap 5.x version"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\base\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Base library and foundation components for all Yii2 Krajee extensions.", "homepage": "https://github.com/kartik-v/yii2-krajee-base", "keywords": ["base", "extension", "foundation", "krajee", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-krajee-base/issues", "source": "https://github.com/kartik-v/yii2-krajee-base/tree/v3.0.5"}, "time": "2022-06-01T14:05:39+00:00"}, {"name": "kartik-v/yii2-mpdf", "version": "v1.0.6", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-mpdf.git", "reference": "7ea3a5f9bcf5bcf6d681bd82cbe843c356d1428c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-mpdf/zipball/7ea3a5f9bcf5bcf6d681bd82cbe843c356d1428c", "reference": "7ea3a5f9bcf5bcf6d681bd82cbe843c356d1428c", "shasum": ""}, "require": {"mpdf/mpdf": "~8.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\mpdf\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A Yii2 wrapper component for the mPDF library which generates PDF files from UTF-8 encoded HTML.", "homepage": "https://github.com/kartik-v/yii2-mpdf", "keywords": ["component", "extension", "html", "mpdf", "pdf", "utf8", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-mpdf/issues", "source": "https://github.com/kartik-v/yii2-mpdf/tree/master"}, "funding": [{"url": "https://opencollective.com/yii2-mpdf", "type": "open_collective"}], "time": "2020-04-14T08:55:18+00:00"}, {"name": "kartik-v/yii2-password", "version": "v1.5.7", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-password.git", "reference": "59910c89c5e21f231dc2947b412196d22bcd47e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-password/zipball/59910c89c5e21f231dc2947b412196d22bcd47e2", "reference": "59910c89c5e21f231dc2947b412196d22bcd47e2", "shasum": ""}, "require": {"kartik-v/strength-meter": "~1.1", "kartik-v/yii2-krajee-base": ">=3.0.4"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "autoload": {"psr-4": {"kartik\\password\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Useful password strength validation utilities for Yii Framework 2.0", "homepage": "https://github.com/kartik-v/yii2-password", "keywords": ["auth", "bootstrap", "extension", "form", "input", "j<PERSON>y", "password", "strength", "yii", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-password/issues", "source": "https://github.com/kartik-v/yii2-password/tree/v1.5.7"}, "time": "2022-05-16T05:38:31+00:00"}, {"name": "kartik-v/yii2-popover-x", "version": "v1.3.5", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-popover-x.git", "reference": "b0320d1315bbfce31ec8907882c6f4abed223a28"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-popover-x/zipball/b0320d1315bbfce31ec8907882c6f4abed223a28", "reference": "b0320d1315bbfce31ec8907882c6f4abed223a28", "shasum": ""}, "require": {"kartik-v/bootstrap-popover-x": ">=1.4", "kartik-v/yii2-krajee-base": ">=2.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"kartik\\popover\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An extended bootstrap 3.0 popover widget which combines both the bootstrap popover and modal features and includes various new styling enhancements.", "homepage": "https://github.com/kartik-v/yii2-popover-x", "keywords": ["bootstrap", "extended", "j<PERSON>y", "modal", "modal-popover", "popover", "popover-x"], "support": {"issues": "https://github.com/kartik-v/yii2-popover-x/issues", "source": "https://github.com/kartik-v/yii2-popover-x/tree/master"}, "time": "2020-04-02T17:20:29+00:00"}, {"name": "kartik-v/yii2-sortable", "version": "v1.2.2", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-sortable.git", "reference": "a6f9a9adb873d6345de92bbe5e436ac1a1db30bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-sortable/zipball/a6f9a9adb873d6345de92bbe5e436ac1a1db30bc", "reference": "a6f9a9adb873d6345de92bbe5e436ac1a1db30bc", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"kartik\\sortable\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Create sortable lists and grids using HTML5 drag and drop API for Yii 2.0.", "homepage": "https://github.com/kartik-v/yii2-sortable", "keywords": ["bootstrap", "extension", "j<PERSON>y", "range", "sortable", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-sortable/issues", "source": "https://github.com/kartik-v/yii2-sortable/tree/master"}, "time": "2018-10-09T13:25:05+00:00"}, {"name": "kartik-v/yii2-tabs-x", "version": "v1.2.9", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-tabs-x.git", "reference": "20e2a2b41ca43e09574caab408004e5ac4e00a7d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-tabs-x/zipball/20e2a2b41ca43e09574caab408004e5ac4e00a7d", "reference": "20e2a2b41ca43e09574caab408004e5ac4e00a7d", "shasum": ""}, "require": {"kartik-v/bootstrap-tabs-x": "~1.3", "kartik-v/yii2-krajee-base": ">=3.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"kartik\\tabs\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A supercharged Bootstrap tabs widget with ability to align tabs in multiple ways, add borders, rotated titles, and more.", "homepage": "https://github.com/kartik-v/yii2-tabs-x", "keywords": ["bootstrap", "extended", "j<PERSON>y", "modal", "modal-tabs", "tabs", "tabs-x"], "support": {"issues": "https://github.com/kartik-v/yii2-tabs-x/issues", "source": "https://github.com/kartik-v/yii2-tabs-x/tree/v1.2.9"}, "time": "2022-10-15T11:14:01+00:00"}, {"name": "kartik-v/yii2-widget-activeform", "version": "v1.6.4", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-activeform.git", "reference": "697407c8fa9c81593a7bb9bef4b7ad53f7d38b79"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-activeform/zipball/697407c8fa9c81593a7bb9bef4b7ad53f7d38b79", "reference": "697407c8fa9c81593a7bb9bef4b7ad53f7d38b79", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=3.0.3"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "autoload": {"psr-4": {"kartik\\form\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 active-form and active-field with full bootstrap styling support (sub repo split from yii2-widgets).", "homepage": "https://github.com/kartik-v/yii2-widget-activeform", "keywords": ["activefield", "activeform", "extension", "field", "form", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-activeform/issues", "source": "https://github.com/kartik-v/yii2-widget-activeform/tree/v1.6.4"}, "funding": [{"url": "https://opencollective.com/yii2-widgets", "type": "open_collective"}], "time": "2023-07-31T11:33:59+00:00"}, {"name": "kartik-v/yii2-widget-affix", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-affix.git", "reference": "2184119bfa518c285406156f744769b13b861712"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-affix/zipball/2184119bfa518c285406156f744769b13b861712", "reference": "2184119bfa518c285406156f744769b13b861712", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"kartik\\affix\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A scrollspy and affixed enhanced navigation to highlight page sections (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-affix", "keywords": ["affix", "bootstrap", "extension", "j<PERSON>y", "navigation", "plugin", "scrollspy", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-affix/issues", "source": "https://github.com/kartik-v/yii2-widget-affix/tree/master"}, "time": "2014-11-09T04:56:27+00:00"}, {"name": "kartik-v/yii2-widget-alert", "version": "v1.1.5", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-alert.git", "reference": "6a45d7dc294eecd578cf8cb9acb671d1cafa0727"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-alert/zipball/6a45d7dc294eecd578cf8cb9acb671d1cafa0727", "reference": "6a45d7dc294eecd578cf8cb9acb671d1cafa0727", "shasum": ""}, "require": {"kartik-v/yii2-widget-growl": ">=1.1.2"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"kartik\\alert\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A widget to generate alert based notifications using bootstrap-alert plugin (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-alert", "keywords": ["alert", "block", "bootstrap", "extension", "flash", "j<PERSON>y", "notification", "plugin", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-alert/issues", "source": "https://github.com/kartik-v/yii2-widget-alert/tree/v1.1.5"}, "time": "2021-10-16T10:23:22+00:00"}, {"name": "kartik-v/yii2-widget-colorinput", "version": "v1.0.6", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-colorinput.git", "reference": "e35e6c7615a735b65557d6c38d112b77e2628c69"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-colorinput/zipball/e35e6c7615a735b65557d6c38d112b77e2628c69", "reference": "e35e6c7615a735b65557d6c38d112b77e2628c69", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=1.9"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\color\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced Yii 2 widget encapsulating the HTML 5 color input (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-colorinput", "keywords": ["HTML5", "color", "extension", "form", "input", "j<PERSON>y", "plugin", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-colorinput/issues", "source": "https://github.com/kartik-v/yii2-widget-colorinput/tree/v1.0.6"}, "time": "2020-10-23T17:50:44+00:00"}, {"name": "kartik-v/yii2-widget-datepicker", "version": "v1.4.8", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-datepicker.git", "reference": "f5f8b396cf03d4a383aad5e7b338f8cb065abf66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-datepicker/zipball/f5f8b396cf03d4a383aad5e7b338f8cb065abf66", "reference": "f5f8b396cf03d4a383aad5e7b338f8cb065abf66", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-4": {"kartik\\date\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 wrapper for the bootstrap datepicker plugin (sub repo split from yii2-widgets).", "homepage": "https://github.com/kartik-v/yii2-widget-datepicker", "keywords": ["date", "extension", "form", "j<PERSON>y", "picker", "plugin", "select2", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-datepicker/issues", "source": "https://github.com/kartik-v/yii2-widget-datepicker/tree/v1.4.8"}, "time": "2021-10-28T03:58:09+00:00"}, {"name": "kartik-v/yii2-widget-datetimepicker", "version": "v1.5.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-datetimepicker.git", "reference": "85b22d38553ca207f86be198f37e6531347e9a23"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-datetimepicker/zipball/85b22d38553ca207f86be198f37e6531347e9a23", "reference": "85b22d38553ca207f86be198f37e6531347e9a23", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=3.0.4"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "autoload": {"psr-4": {"kartik\\datetime\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 wrapper for the bootstrap datetimepicker plugin (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-datetimepicker", "keywords": ["datetime", "extension", "form", "j<PERSON>y", "picker", "plugin", "select2", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-datetimepicker/issues", "source": "https://github.com/kartik-v/yii2-widget-datetimepicker/tree/v1.5.1"}, "time": "2022-03-18T17:42:22+00:00"}, {"name": "kartik-v/yii2-widget-depdrop", "version": "v1.0.6", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-depdrop.git", "reference": "ea347e3793fbd8273cc9bd1eb94c4b32bb55d318"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-depdrop/zipball/ea347e3793fbd8273cc9bd1eb94c4b32bb55d318", "reference": "ea347e3793fbd8273cc9bd1eb94c4b32bb55d318", "shasum": ""}, "require": {"kartik-v/dependent-dropdown": "~1.4", "kartik-v/yii2-krajee-base": ">=2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\depdrop\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Widget that enables setting up dependent dropdowns with nested dependencies (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-depdrop", "keywords": ["dependent", "dropdown", "extension", "form", "j<PERSON>y", "plugin", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-depdrop/issues", "source": "https://github.com/kartik-v/yii2-widget-depdrop/tree/v1.0.6"}, "time": "2019-04-19T07:02:48+00:00"}, {"name": "kartik-v/yii2-widget-fileinput", "version": "v1.1.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-fileinput.git", "reference": "b5500b6855526837154694c2afab8dbc3afc8abd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-fileinput/zipball/b5500b6855526837154694c2afab8dbc3afc8abd", "reference": "b5500b6855526837154694c2afab8dbc3afc8abd", "shasum": ""}, "require": {"kartik-v/bootstrap-fileinput": ">=5.5.0", "kartik-v/yii2-krajee-base": ">=3.0.5"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"kartik\\file\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced FileInput widget for Bootstrap 3.x, 4.x & 5.x with file preview, multiple selection, and more features (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-fileinput", "keywords": ["extension", "file", "form", "input", "j<PERSON>y", "plugin", "upload", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-fileinput/issues", "source": "https://github.com/kartik-v/yii2-widget-fileinput/tree/v1.1.1"}, "funding": [{"url": "https://opencollective.com/yii2-widget-fileinput", "type": "open_collective"}], "time": "2022-06-28T04:31:04+00:00"}, {"name": "kartik-v/yii2-widget-growl", "version": "v1.1.2", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-growl.git", "reference": "37e8f9f10d3bc9d71f3ef64c4aaa0e2fc83dd5dc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-growl/zipball/37e8f9f10d3bc9d71f3ef64c4aaa0e2fc83dd5dc", "reference": "37e8f9f10d3bc9d71f3ef64c4aaa0e2fc83dd5dc", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"kartik\\growl\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A widget to generate growl based notifications using bootstrap-growl plugin (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-growl", "keywords": ["alert", "bootstrap", "extension", "growl", "j<PERSON>y", "notification", "plugin", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-growl/issues", "source": "https://github.com/kartik-v/yii2-widget-growl/tree/v1.1.2"}, "time": "2021-05-19T12:44:49+00:00"}, {"name": "kartik-v/yii2-widget-rangeinput", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-rangeinput.git", "reference": "dd9019bab7e5bf570a02870d9e74387891bbdb32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-rangeinput/zipball/dd9019bab7e5bf570a02870d9e74387891bbdb32", "reference": "dd9019bab7e5bf570a02870d9e74387891bbdb32", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=1.9"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\range\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced Yii 2 widget encapsulating the HTML 5 range input (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-rangeinput", "keywords": ["HTML5", "extension", "form", "input", "j<PERSON>y", "plugin", "range", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-rangeinput/issues", "source": "https://github.com/kartik-v/yii2-widget-rangeinput/tree/master"}, "time": "2018-09-07T10:05:08+00:00"}, {"name": "kartik-v/yii2-widget-rating", "version": "v1.0.5", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-rating.git", "reference": "d3d7249490044f80e65f8f3938191f39a76586b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-rating/zipball/d3d7249490044f80e65f8f3938191f39a76586b2", "reference": "d3d7249490044f80e65f8f3938191f39a76586b2", "shasum": ""}, "require": {"kartik-v/bootstrap-star-rating": "~4.0", "kartik-v/yii2-krajee-base": ">=1.9"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\rating\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A Yii2 widget for the simple yet powerful bootstrap-star-rating plugin with fractional rating support (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-rating", "keywords": ["Rating", "bootstrap", "extension", "form", "input", "j<PERSON>y", "plugin", "star", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-rating/issues", "source": "https://github.com/kartik-v/yii2-widget-rating/tree/v1.0.5"}, "time": "2021-11-20T05:26:05+00:00"}, {"name": "kartik-v/yii2-widget-select2", "version": "v2.2.5", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-select2.git", "reference": "4b8ef7dd9780531fc997fa23a53a38a1f7674bec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-select2/zipball/4b8ef7dd9780531fc997fa23a53a38a1f7674bec", "reference": "4b8ef7dd9780531fc997fa23a53a38a1f7674bec", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=3.0.4", "select2/select2": ">=4.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "autoload": {"psr-4": {"kartik\\select2\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 wrapper for the Select2 jQuery plugin (sub repo split from yii2-widgets).", "homepage": "https://github.com/kartik-v/yii2-widget-select2", "keywords": ["dropdown", "extension", "form", "j<PERSON>y", "plugin", "select2", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-select2/issues", "source": "https://github.com/kartik-v/yii2-widget-select2/tree/v2.2.5"}, "funding": [{"url": "https://opencollective.com/yii2-widget-select2", "type": "open_collective"}], "time": "2023-06-22T07:43:31+00:00"}, {"name": "kartik-v/yii2-widget-sidenav", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-sidenav.git", "reference": "87e9c815624aa966d70bb4507b3d53c158db0d43"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-sidenav/zipball/87e9c815624aa966d70bb4507b3d53c158db0d43", "reference": "87e9c815624aa966d70bb4507b3d53c158db0d43", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\sidenav\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "An enhanced side navigation menu styled for bootstrap (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-sidenav", "keywords": ["bootstrap", "extension", "j<PERSON>y", "menu", "navigation", "plugin", "sidenav", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-sidenav/issues", "source": "https://github.com/kartik-v/yii2-widget-sidenav/tree/v1.0.1"}, "funding": [{"url": "https://opencollective.com/yii2-widget-sidenav", "type": "open_collective"}], "time": "2021-04-08T17:49:26+00:00"}, {"name": "kartik-v/yii2-widget-spinner", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-spinner.git", "reference": "eb10dad17a107bf14f173c99994770ca23c548a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-spinner/zipball/eb10dad17a107bf14f173c99994770ca23c548a6", "reference": "eb10dad17a107bf14f173c99994770ca23c548a6", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\spinner\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A widget to render animated CSS3 loading spinners with VML fallback for IE (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-spinner", "keywords": ["CSS3", "extension", "j<PERSON>y", "loading", "plugin", "spinner", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-spinner/issues", "source": "https://github.com/kartik-v/yii2-widget-spinner/tree/master"}, "time": "2018-10-09T11:54:03+00:00"}, {"name": "kartik-v/yii2-widget-switchinput", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-switchinput.git", "reference": "7d8ee999d79bcdc1601da5cd59439ac7eb1f5ea6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-switchinput/zipball/7d8ee999d79bcdc1601da5cd59439ac7eb1f5ea6", "reference": "7d8ee999d79bcdc1601da5cd59439ac7eb1f5ea6", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"kartik\\switchinput\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD 3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A Yii2 wrapper widget for the Bootstrap Switch plugin to use checkboxes & radios as toggle switchinputes (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-switchinput", "keywords": ["bootstrap", "extension", "form", "input", "j<PERSON>y", "plugin", "switchinput", "toggle", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-switchinput/issues", "source": "https://github.com/kartik-v/yii2-widget-switchinput/tree/master"}, "time": "2016-01-10T16:47:35+00:00"}, {"name": "kartik-v/yii2-widget-timepicker", "version": "v1.0.5", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-timepicker.git", "reference": "680aec2d79846e926c072da455cf6f33e1c3bb12"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-timepicker/zipball/680aec2d79846e926c072da455cf6f33e1c3bb12", "reference": "680aec2d79846e926c072da455cf6f33e1c3bb12", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\time\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 wrapper for the bootstrap timepicker plugin (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-timepicker", "keywords": ["bootstrap", "extension", "form", "j<PERSON>y", "picker", "plugin", "time", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-timepicker/issues", "source": "https://github.com/kartik-v/yii2-widget-timepicker/tree/v1.0.5"}, "time": "2021-10-28T03:49:56+00:00"}, {"name": "kartik-v/yii2-widget-touchspin", "version": "v1.2.4", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-touchspin.git", "reference": "1eec4c3f3a8bf9a170e1e0682c2c89f2929d65e9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-touchspin/zipball/1eec4c3f3a8bf9a170e1e0682c2c89f2929d65e9", "reference": "1eec4c3f3a8bf9a170e1e0682c2c89f2929d65e9", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=3.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"kartik\\touchspin\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "A Yii2 wrapper widget for the Bootstrap Switch plugin to use checkboxes & radios as toggle touchspines (sub repo split from yii2-widgets)", "homepage": "https://github.com/kartik-v/yii2-widget-touchspin", "keywords": ["bootstrap", "extension", "form", "input", "j<PERSON>y", "plugin", "spinner", "touch", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-touchspin/issues", "source": "https://github.com/kartik-v/yii2-widget-touchspin/tree/v1.2.4"}, "time": "2021-09-02T12:50:50+00:00"}, {"name": "kartik-v/yii2-widget-typeahead", "version": "v1.0.4", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widget-typeahead.git", "reference": "7b7041a3cbbeb2db0a608e9f6c9b3f4f63b0069d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widget-typeahead/zipball/7b7041a3cbbeb2db0a608e9f6c9b3f4f63b0069d", "reference": "7b7041a3cbbeb2db0a608e9f6c9b3f4f63b0069d", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": ">=2.0.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\typeahead\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Enhanced Yii2 wrapper for the Twitter Typeahead plugin (sub repo split from yii2-widgets).", "homepage": "https://github.com/kartik-v/yii2-widget-typeahead", "keywords": ["dropdown", "extension", "form", "j<PERSON>y", "plugin", "typeahead", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widget-typeahead/issues", "source": "https://github.com/kartik-v/yii2-widget-typeahead/tree/master"}, "time": "2019-05-29T12:06:56+00:00"}, {"name": "kartik-v/yii2-widgets", "version": "v3.4.1", "source": {"type": "git", "url": "https://github.com/kartik-v/yii2-widgets.git", "reference": "e5a030d700243a90eccf96a070380bd3b76e17a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kartik-v/yii2-widgets/zipball/e5a030d700243a90eccf96a070380bd3b76e17a3", "reference": "e5a030d700243a90eccf96a070380bd3b76e17a3", "shasum": ""}, "require": {"kartik-v/yii2-krajee-base": "*", "kartik-v/yii2-widget-activeform": "*", "kartik-v/yii2-widget-affix": "*", "kartik-v/yii2-widget-alert": "*", "kartik-v/yii2-widget-colorinput": "*", "kartik-v/yii2-widget-datepicker": "*", "kartik-v/yii2-widget-datetimepicker": "*", "kartik-v/yii2-widget-depdrop": "*", "kartik-v/yii2-widget-fileinput": "*", "kartik-v/yii2-widget-growl": "*", "kartik-v/yii2-widget-rangeinput": "*", "kartik-v/yii2-widget-rating": "*", "kartik-v/yii2-widget-select2": "*", "kartik-v/yii2-widget-sidenav": "*", "kartik-v/yii2-widget-spinner": "*", "kartik-v/yii2-widget-switchinput": "*", "kartik-v/yii2-widget-timepicker": "*", "kartik-v/yii2-widget-touchspin": "*", "kartik-v/yii2-widget-typeahead": "*"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"kartik\\widgets\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.krajee.com/"}], "description": "Collection of useful widgets for Yii Framework 2.0 extending functionalities for Bootstrap", "homepage": "https://github.com/kartik-v/yii2-widgets", "keywords": ["extension", "form", "widget", "yii2"], "support": {"issues": "https://github.com/kartik-v/yii2-widgets/issues", "source": "https://github.com/kartik-v/yii2-widgets/tree/master"}, "time": "2018-10-09T17:40:19+00:00"}, {"name": "maennchen/zipstream-php", "version": "3.1.1", "source": {"type": "git", "url": "https://github.com/maennchen/ZipStream-PHP.git", "reference": "6187e9cc4493da94b9b63eb2315821552015fca9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maennchen/ZipStream-PHP/zipball/6187e9cc4493da94b9b63eb2315821552015fca9", "reference": "6187e9cc4493da94b9b63eb2315821552015fca9", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-zlib": "*", "php-64bit": "^8.1"}, "require-dev": {"ext-zip": "*", "friendsofphp/php-cs-fixer": "^3.16", "guzzlehttp/guzzle": "^7.5", "mikey179/vfsstream": "^1.6", "php-coveralls/php-coveralls": "^2.5", "phpunit/phpunit": "^10.0", "vimeo/psalm": "^5.0"}, "suggest": {"guzzlehttp/psr7": "^2.4", "psr/http-message": "^2.0"}, "type": "library", "autoload": {"psr-4": {"ZipStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Jonatan Männchen", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.", "keywords": ["stream", "zip"], "support": {"issues": "https://github.com/maennchen/ZipStream-PHP/issues", "source": "https://github.com/maennchen/ZipStream-PHP/tree/3.1.1"}, "funding": [{"url": "https://github.com/maennchen", "type": "github"}], "time": "2024-10-10T12:33:01+00:00"}, {"name": "markbaker/complex", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Complex\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "support": {"issues": "https://github.com/MarkBaker/PHPComplex/issues", "source": "https://github.com/MarkBaker/PHPComplex/tree/3.0.2"}, "time": "2022-12-06T16:21:08+00:00"}, {"name": "markbaker/matrix", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "728434227fe21be27ff6d86621a1b13107a2562c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/728434227fe21be27ff6d86621a1b13107a2562c", "reference": "728434227fe21be27ff6d86621a1b13107a2562c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "sebastian/phpcpd": "^4.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "support": {"issues": "https://github.com/MarkBaker/PHPMatrix/issues", "source": "https://github.com/MarkBaker/PHPMatrix/tree/3.0.1"}, "time": "2022-12-02T22:17:43+00:00"}, {"name": "miloschuman/yii2-highcharts-widget", "version": "v11.0", "source": {"type": "git", "url": "https://github.com/miloschuman/yii2-highcharts.git", "reference": "2ef01ffec361a1c581de3f38ebad4ba19541f390"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/miloschuman/yii2-highcharts/zipball/2ef01ffec361a1c581de3f38ebad4ba19541f390", "reference": "2ef01ffec361a1c581de3f38ebad4ba19541f390", "shasum": ""}, "require": {"npm-asset/highcharts": "^11.0", "yiisoft/yii2": "*"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "yii2-extension", "autoload": {"psr-4": {"miloschuman\\highcharts\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Highcharts widget for Yii 2 Framework.", "homepage": "https://github.com/miloschuman/yii2-highcharts", "keywords": ["extension", "highcharts", "highmaps", "highstock", "widget", "yii2"], "support": {"issues": "https://github.com/miloschuman/yii2-highcharts/issues", "source": "https://github.com/miloschuman/yii2-highcharts/tree/v11.0"}, "time": "2024-09-18T22:10:48+00:00"}, {"name": "mootensai/yii2-enhanced-gii", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/mootensai/yii2-enhanced-gii.git", "reference": "a00e4e19fe191da621a2291db95634942039f7bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mootensai/yii2-enhanced-gii/zipball/a00e4e19fe191da621a2291db95634942039f7bf", "reference": "a00e4e19fe191da621a2291db95634942039f7bf", "shasum": ""}, "require": {"kartik-v/yii2-builder": "*", "kartik-v/yii2-export": "*", "kartik-v/yii2-grid": "*", "kartik-v/yii2-tabs-x": "*", "kartik-v/yii2-widgets": "*", "mootensai/yii2-jsblock": "*", "mootensai/yii2-optimistic-lock-validator": "*", "mootensai/yii2-relation-trait": "*", "mootensai/yii2-uuid-behavior": "*", "yiisoft/yii2": ">=2.0.38"}, "default-branch": true, "type": "yii2-extension", "extra": {"bootstrap": "mootensai\\enhancedgii\\Bootstrap"}, "autoload": {"psr-4": {"mootensai\\enhancedgii\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jiwan<PERSON><PERSON>@gmail.com"}], "description": "Generate Relational (hasMany, hasOne, belongsTo, & nested) Models & CRUD.", "keywords": ["Enhanced", "gii", "relation", "yii", "yii2"], "support": {"issues": "https://github.com/mootensai/yii2-enhanced-gii/issues", "source": "https://github.com/mootensai/yii2-enhanced-gii/tree/master"}, "time": "2021-03-08T04:19:13+00:00"}, {"name": "mootensai/yii2-jsblock", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/mootensai/yii2-jsblock.git", "reference": "d27864ba27760ffa634c01081f3e47ebddace9d5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mootensai/yii2-jsblock/zipball/d27864ba27760ffa634c01081f3e47ebddace9d5", "reference": "d27864ba27760ffa634c01081f3e47ebddace9d5", "shasum": ""}, "type": "yii2-extension", "autoload": {"psr-4": {"mootensai\\components\\": ""}}, "notification-url": "https://packagist.org/downloads/", "authors": [{"name": "mdmunir", "email": "<EMAIL>"}], "description": "Embed Javascript block with IDE checking", "keywords": ["block", "javascript", "yii2"], "support": {"issues": "https://github.com/mootensai/yii2-jsblock/issues", "source": "https://github.com/mootensai/yii2-jsblock/tree/master"}, "time": "2015-06-27T03:49:10+00:00"}, {"name": "mootensai/yii2-optimistic-lock-validator", "version": "0.0.1", "source": {"type": "git", "url": "https://github.com/mootensai/yii2-optimistic-lock-validator.git", "reference": "422b012a87973bb296080f3572e1e45c7643a31a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mootensai/yii2-optimistic-lock-validator/zipball/422b012a87973bb296080f3572e1e45c7643a31a", "reference": "422b012a87973bb296080f3572e1e45c7643a31a", "shasum": ""}, "require": {"yiisoft/yii2": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"mootensai\\components\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jiwan<PERSON><PERSON>@gmail.com"}], "description": "Yii 2 Optimistic Lock validator", "keywords": ["lock", "optimistic", "optimistic-lock", "validator", "yii", "yii2"], "support": {"issues": "https://github.com/mootensai/yii2-optimistic-lock-validator/issues", "source": "https://github.com/mootensai/yii2-optimistic-lock-validator/tree/master"}, "time": "2015-08-15T03:27:57+00:00"}, {"name": "mootensai/yii2-relation-trait", "version": "1.1.8", "source": {"type": "git", "url": "https://github.com/mootensai/yii2-relation-trait.git", "reference": "8149499fb4dc168c13bb1b97ebc23b6f570c425a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mootensai/yii2-relation-trait/zipball/8149499fb4dc168c13bb1b97ebc23b6f570c425a", "reference": "8149499fb4dc168c13bb1b97ebc23b6f570c425a", "shasum": ""}, "require": {"php": ">=5.4.0", "yiisoft/yii2": "~2.0"}, "type": "yii2-extension", "autoload": {"psr-4": {"mootensai\\relation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Yii 2 Models load with relation, & transaction save with relation", "homepage": "http://github.com/mootensai/yii2-relation-trait", "keywords": ["load", "loadall", "loadwithrelation", "related", "relation", "save", "saveall", "savewithrelation", "transaction", "yii2"], "support": {"issues": "https://github.com/mootensai/yii2-relation-trait/issues", "source": "https://github.com/mootensai/yii2-relation-trait"}, "time": "2017-10-18T04:48:35+00:00"}, {"name": "mootensai/yii2-uuid-behavior", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/mootensai/yii2-uuid-behavior.git", "reference": "aaeb5c6268e77647af48ddc4757c5cb3fb726e55"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mootensai/yii2-uuid-behavior/zipball/aaeb5c6268e77647af48ddc4757c5cb3fb726e55", "reference": "aaeb5c6268e77647af48ddc4757c5cb3fb726e55", "shasum": ""}, "require": {"php": ">=5.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"mootensai\\behaviors\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "jiwan<PERSON><PERSON>@gmail.com", "role": "Developer"}], "description": "Set UUID before save on Yii 2 models", "homepage": "http://github.com/mootensai/yii2-uuid-behavior", "keywords": ["before", "beforesave", "save", "uuid", "yii2"], "support": {"issues": "https://github.com/mootensai/yii2-uuid-behavior/issues", "source": "https://github.com/mootensai/yii2-uuid-behavior/tree/master"}, "time": "2015-06-22T02:06:14+00:00"}, {"name": "mpdf/mpdf", "version": "v8.2.4", "source": {"type": "git", "url": "https://github.com/mpdf/mpdf.git", "reference": "9e3ff91606fed11cd58a130eabaaf60e56fdda88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/mpdf/zipball/9e3ff91606fed11cd58a130eabaaf60e56fdda88", "reference": "9e3ff91606fed11cd58a130eabaaf60e56fdda88", "shasum": ""}, "require": {"ext-gd": "*", "ext-mbstring": "*", "mpdf/psr-http-message-shim": "^1.0 || ^2.0", "mpdf/psr-log-aware-trait": "^2.0 || ^3.0", "myclabs/deep-copy": "^1.7", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": "^5.6 || ^7.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0", "psr/http-message": "^1.0 || ^2.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "setasign/fpdi": "^2.1"}, "require-dev": {"mockery/mockery": "^1.3.0", "mpdf/qrcode": "^1.1.0", "squizlabs/php_codesniffer": "^3.5.0", "tracy/tracy": "~2.5", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"ext-bcmath": "Needed for generation of some types of barcodes", "ext-xml": "Needed mainly for SVG manipulation", "ext-zlib": "Needed for compression of embedded resources, such as fonts"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Mpdf\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-only"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>, maintainer"}, {"name": "<PERSON>", "role": "<PERSON><PERSON><PERSON> (retired)"}], "description": "PHP library generating PDF files from UTF-8 encoded HTML", "homepage": "https://mpdf.github.io", "keywords": ["pdf", "php", "utf-8"], "support": {"docs": "http://mpdf.github.io", "issues": "https://github.com/mpdf/mpdf/issues", "source": "https://github.com/mpdf/mpdf"}, "funding": [{"url": "https://www.paypal.me/mpdf", "type": "custom"}], "time": "2024-06-14T16:06:41+00:00"}, {"name": "mpdf/psr-http-message-shim", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/mpdf/psr-http-message-shim.git", "reference": "f25a0153d645e234f9db42e5433b16d9b113920f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/psr-http-message-shim/zipball/f25a0153d645e234f9db42e5433b16d9b113920f", "reference": "f25a0153d645e234f9db42e5433b16d9b113920f", "shasum": ""}, "require": {"psr/http-message": "^2.0"}, "type": "library", "autoload": {"psr-4": {"Mpdf\\PsrHttpMessageShim\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Shim to allow support of different psr/message versions.", "support": {"issues": "https://github.com/mpdf/psr-http-message-shim/issues", "source": "https://github.com/mpdf/psr-http-message-shim/tree/v2.0.1"}, "time": "2023-10-02T14:34:03+00:00"}, {"name": "mpdf/psr-log-aware-trait", "version": "v3.0.0", "source": {"type": "git", "url": "https://github.com/mpdf/psr-log-aware-trait.git", "reference": "a633da6065e946cc491e1c962850344bb0bf3e78"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/psr-log-aware-trait/zipball/a633da6065e946cc491e1c962850344bb0bf3e78", "reference": "a633da6065e946cc491e1c962850344bb0bf3e78", "shasum": ""}, "require": {"psr/log": "^3.0"}, "type": "library", "autoload": {"psr-4": {"Mpdf\\PsrLogAwareTrait\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Trait to allow support of different psr/log versions.", "support": {"issues": "https://github.com/mpdf/psr-log-aware-trait/issues", "source": "https://github.com/mpdf/psr-log-aware-trait/tree/v3.0.0"}, "time": "2023-05-03T06:19:36+00:00"}, {"name": "mpdf/qrcode", "version": "v1.2.1", "source": {"type": "git", "url": "https://github.com/mpdf/qrcode.git", "reference": "5320c512776aa3c199bd8be8f707ec83d9779d85"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/qrcode/zipball/5320c512776aa3c199bd8be8f707ec83d9779d85", "reference": "5320c512776aa3c199bd8be8f707ec83d9779d85", "shasum": ""}, "require": {"paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": "^5.6 || ^7.0 || ^8.0"}, "require-dev": {"mockery/mockery": "^0.9.5", "squizlabs/php_codesniffer": "^3.4", "tracy/tracy": "^2.5", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"ext-gd": "To output QR codes to PNG files", "ext-simplexml": "To output QR codes to SVG files"}, "type": "library", "autoload": {"psr-4": {"Mpdf\\QrCode\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "role": "maintainer"}, {"name": "<PERSON>", "role": "author"}], "description": "QR code generator for mPDF", "keywords": ["mpdf", "pdf", "php", "qr", "qrcode"], "support": {"issues": "https://github.com/mpdf/qrcode/issues", "source": "https://github.com/mpdf/qrcode/tree/v1.2.1"}, "funding": [{"url": "https://www.paypal.me/mpdf", "type": "custom"}], "time": "2024-06-04T13:40:39+00:00"}, {"name": "myclabs/deep-copy", "version": "1.12.0", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "3a6b9a42cd8f8771bd4295d13e1423fa7f3d942c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/3a6b9a42cd8f8771bd4295d13e1423fa7f3d942c", "reference": "3a6b9a42cd8f8771bd4295d13e1423fa7f3d942c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3 <3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpspec/prophecy": "^1.10", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.12.0"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2024-06-12T14:39:25+00:00"}, {"name": "npm-asset/highcharts", "version": "11.4.8", "dist": {"type": "tar", "url": "https://registry.npmjs.org/highcharts/-/highcharts-11.4.8.tgz"}, "type": "npm-asset", "license": ["https://www.highcharts.com/license"]}, {"name": "paragonie/random_compat", "version": "v9.99.100", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2020-10-15T08:29:30+00:00"}, {"name": "phpoffice/phpexcel", "version": "1.8.1", "source": {"type": "git", "url": "https://github.com/PHPOffice/PHPExcel.git", "reference": "372c7cbb695a6f6f1e62649381aeaa37e7e70b32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PHPExcel/zipball/372c7cbb695a6f6f1e62649381aeaa37e7e70b32", "reference": "372c7cbb695a6f6f1e62649381aeaa37e7e70b32", "shasum": ""}, "require": {"ext-xml": "*", "ext-xmlwriter": "*", "php": ">=5.2.0"}, "type": "library", "autoload": {"psr-0": {"PHPExcel": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "http://blog.maartenballiauw.be"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://blog.rootslabs.net"}, {"name": "<PERSON>"}], "description": "PHPExcel - OpenXML - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "http://phpexcel.codeplex.com", "keywords": ["OpenXML", "excel", "php", "spreadsheet", "xls", "xlsx"], "support": {"issues": "https://github.com/PHPOffice/PHPExcel/issues", "source": "https://github.com/PHPOffice/PHPExcel/tree/master"}, "abandoned": "phpoffice/phpspreadsheet", "time": "2015-05-01T07:00:55+00:00"}, {"name": "phpoffice/phpspreadsheet", "version": "3.3.0", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "87ddd21eb0b6b7ad20a11d314348ef307475f547"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/87ddd21eb0b6b7ad20a11d314348ef307475f547", "reference": "87ddd21eb0b6b7ad20a11d314348ef307475f547", "shasum": ""}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "maennchen/zipstream-php": "^2.1 || ^3.0", "markbaker/complex": "^3.0", "markbaker/matrix": "^3.0", "php": "^8.1", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/simple-cache": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-main", "dompdf/dompdf": "^2.0 || ^3.0", "friendsofphp/php-cs-fixer": "^3.2", "mitoteam/jpgraph": "^10.3", "mpdf/mpdf": "^8.1.1", "phpcompatibility/php-compatibility": "^9.3", "phpstan/phpstan": "^1.1", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^9.6 || ^10.5", "squizlabs/php_codesniffer": "^3.7", "tecnickcom/tcpdf": "^6.5"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer", "ext-intl": "PHP Internationalization Functions", "mitoteam/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "support": {"issues": "https://github.com/PHPOffice/PhpSpreadsheet/issues", "source": "https://github.com/PHPOffice/PhpSpreadsheet/tree/3.3.0"}, "time": "2024-09-29T07:04:07+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "2.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "time": "2023-04-04T09:54:51+00:00"}, {"name": "psr/log", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.2"}, "time": "2024-09-11T13:17:53+00:00"}, {"name": "psr/simple-cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/764e0b3939f5ca87cb904f570ef9be2d78a07865", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/3.0.0"}, "time": "2021-10-29T13:26:27+00:00"}, {"name": "rikudou/iban", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/RikudouSage/IBAN.git", "reference": "7fe69bf9274792c37d5a8d9d38ef5cb000f8377a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/RikudouSage/IBAN/zipball/7fe69bf9274792c37d5a8d9d38ef5cb000f8377a", "reference": "7fe69bf9274792c37d5a8d9d38ef5cb000f8377a", "shasum": ""}, "require": {"php": "^7.3 | ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "php-coveralls/php-coveralls": "^2.1", "phpstan/phpstan": "^0.12.63", "phpunit/phpunit": "^9"}, "type": "library", "autoload": {"psr-4": {"Rikudou\\Iban\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["WTFPL"], "authors": [{"name": "Dominik Chrástecký", "email": "<EMAIL>"}], "description": "Library for working with IBANs", "homepage": "https://github.com/RikudouSage/IBAN", "keywords": ["IBAN"], "support": {"issues": "https://github.com/RikudouSage/IBAN/issues", "source": "https://github.com/RikudouSage/IBAN/tree/v1.3.0"}, "funding": [{"url": "https://ko-fi.com/dominik_ch", "type": "ko_fi"}, {"url": "https://liberapay.com/dominik_ch", "type": "liberapay"}], "time": "2023-09-11T07:54:00+00:00"}, {"name": "rikudou/qr-payment-interface", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/RikudouSage/QrPaymentInterface.git", "reference": "752f7a6bf1190c7d65ead90b5989f61927436c89"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/RikudouSage/QrPaymentInterface/zipball/752f7a6bf1190c7d65ead90b5989f61927436c89", "reference": "752f7a6bf1190c7d65ead90b5989f61927436c89", "shasum": ""}, "require": {"php": ">=7.1", "rikudou/iban": "^1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.16"}, "type": "library", "autoload": {"psr-4": {"Rikudou\\QrPayment\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Dominik Chrástecký", "email": "<EMAIL>"}], "description": "Common interface for my qr payment libraries", "keywords": ["payment", "qr"], "support": {"issues": "https://github.com/RikudouSage/QrPaymentInterface/issues", "source": "https://github.com/RikudouSage/QrPaymentInterface/tree/v1.1.0"}, "time": "2021-04-27T20:05:08+00:00"}, {"name": "rikudou/qr-payment-qr-code-provider", "version": "v1.1.1", "source": {"type": "git", "url": "https://github.com/RikudouSage/QrPaymentQrCodeProvider.git", "reference": "06e77aca04f3e6bb41da57eb9e880d7ec664cb90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/RikudouSage/QrPaymentQrCodeProvider/zipball/06e77aca04f3e6bb41da57eb9e880d7ec664cb90", "reference": "06e77aca04f3e6bb41da57eb9e880d7ec664cb90", "shasum": ""}, "require": {"php": ">=7.3", "rikudou/qr-payment-interface": "^1.0"}, "require-dev": {"bacon/bacon-qr-code": "^2.0", "chillerlan/php-qrcode": "^4.3", "endroid/qr-code": "^4.3", "friendsofphp/php-cs-fixer": "^3.1", "phpstan/phpstan": "^0.12.99"}, "type": "library", "autoload": {"psr-4": {"Rikudou\\QrPaymentQrCodeProvider\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "QR code provider for QR payment libraries", "support": {"issues": "https://github.com/RikudouSage/QrPaymentQrCodeProvider/issues", "source": "https://github.com/RikudouSage/QrPaymentQrCodeProvider/tree/v1.1.1"}, "time": "2021-09-27T23:12:37+00:00"}, {"name": "rikudou/skqrpayment", "version": "v4.2.1", "source": {"type": "git", "url": "https://github.com/RikudouSage/QrPaymentSK.git", "reference": "6d106fad831099dda24a33207eba647ad57530aa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/RikudouSage/QrPaymentSK/zipball/6d106fad831099dda24a33207eba647ad57530aa", "reference": "6d106fad831099dda24a33207eba647ad57530aa", "shasum": ""}, "require": {"php": "^7.3 || ^8.0", "rikudou/iban": "^1.0", "rikudou/qr-payment-interface": "^1.0", "rikudou/qr-payment-qr-code-provider": "^1.0"}, "require-dev": {"endroid/qr-code": "^3.9", "friendsofphp/php-cs-fixer": "^2.16", "phpstan/phpstan": "^0.12.32", "phpunit/phpunit": "^8.5"}, "suggest": {"endroid/qr-code": "For getting QR code image", "rikudou/pay-by-square-decoder": "If you want to decode Pay By Square encoded data"}, "type": "library", "autoload": {"psr-4": {"rikudou\\SkQrPayment\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Dominik Chrástecký", "email": "<EMAIL>"}], "description": "QR payment library for Slovak accounts", "homepage": "https://github.com/RikudouSage/QrPaymentSK", "keywords": ["payment", "qr"], "support": {"issues": "https://github.com/RikudouSage/QrPaymentSK/issues", "source": "https://github.com/RikudouSage/QrPaymentSK/tree/v4.2.1"}, "funding": [{"url": "https://ko-fi.com/dominik_ch", "type": "ko_fi"}, {"url": "https://liberapay.com/dominik_ch", "type": "liberapay"}], "time": "2023-02-09T11:58:29+00:00"}, {"name": "rmrevin/yii2-fontawesome", "version": "2.17.1", "source": {"type": "git", "url": "https://github.com/rmrevin/yii2-fontawesome.git", "reference": "65ce306da864f4d558348aeba040ed7876878090"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/rmrevin/yii2-fontawesome/zipball/65ce306da864f4d558348aeba040ed7876878090", "reference": "65ce306da864f4d558348aeba040ed7876878090", "shasum": ""}, "require": {"fortawesome/font-awesome": "~4.7", "php": ">=5.4.0", "yiisoft/yii2": "2.0.*"}, "type": "yii2-extension", "extra": {"asset-installer-paths": {"npm-asset-library": "vendor/npm", "bower-asset-library": "vendor/bower"}}, "autoload": {"psr-4": {"rmrevin\\yii\\fontawesome\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>in <PERSON>", "email": "<EMAIL>", "homepage": "https://rmrevin.com/"}], "description": "Asset Bundle for Yii2 with Font Awesome", "keywords": ["asset", "awesome", "bundle", "font", "yii"], "support": {"issues": "https://github.com/rmrevin/yii2-fontawesome/issues", "source": "https://github.com/rmrevin/yii2-fontawesome"}, "time": "2017-01-11T14:05:47+00:00"}, {"name": "select2/select2", "version": "4.0.13", "source": {"type": "git", "url": "https://github.com/select2/select2.git", "reference": "45f2b83ceed5231afa7b3d5b12b58ad335edd82e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/select2/select2/zipball/45f2b83ceed5231afa7b3d5b12b58ad335edd82e", "reference": "45f2b83ceed5231afa7b3d5b12b58ad335edd82e", "shasum": ""}, "type": "component", "extra": {"component": {"scripts": ["dist/js/select2.js"], "styles": ["dist/css/select2.css"], "files": ["dist/js/select2.js", "dist/js/i18n/*.js", "dist/css/select2.css"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Select2 is a jQuery based replacement for select boxes.", "homepage": "https://select2.org/", "support": {"issues": "https://github.com/select2/select2/issues", "source": "https://github.com/select2/select2/tree/4.0.13"}, "time": "2020-01-28T05:01:22+00:00"}, {"name": "setasign/fpdi", "version": "v2.6.1", "source": {"type": "git", "url": "https://github.com/Setasign/FPDI.git", "reference": "09a816004fcee9ed3405bd164147e3fdbb79a56f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDI/zipball/09a816004fcee9ed3405bd164147e3fdbb79a56f", "reference": "09a816004fcee9ed3405bd164147e3fdbb79a56f", "shasum": ""}, "require": {"ext-zlib": "*", "php": "^5.6 || ^7.0 || ^8.0"}, "conflict": {"setasign/tfpdf": "<1.31"}, "require-dev": {"phpunit/phpunit": "~5.7", "setasign/fpdf": "~1.8.6", "setasign/tfpdf": "~1.33", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "~6.2"}, "suggest": {"setasign/fpdf": "FPDI will extend this class but as it is also possible to use TCPDF or tFPDF as an alternative. There's no fixed dependency configured."}, "type": "library", "autoload": {"psr-4": {"setasign\\Fpdi\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "description": "FPDI is a collection of PHP classes facilitating developers to read pages from existing PDF documents and use them as templates in FPDF. Because it is also possible to use FPDI with TCPDF, there are no fixed dependencies defined. Please see suggestions for packages which evaluates the dependencies automatically.", "homepage": "https://www.setasign.com/fpdi", "keywords": ["fpdf", "fpdi", "pdf"], "support": {"issues": "https://github.com/Setasign/FPDI/issues", "source": "https://github.com/Setasign/FPDI/tree/v2.6.1"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/setasign/fpdi", "type": "tidelift"}], "time": "2024-09-02T10:17:15+00:00"}, {"name": "swiftmailer/swiftmailer", "version": "v6.3.0", "source": {"type": "git", "url": "https://github.com/swiftmailer/swiftmailer.git", "reference": "8a5d5072dca8f48460fce2f4131fcc495eec654c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swiftmailer/swiftmailer/zipball/8a5d5072dca8f48460fce2f4131fcc495eec654c", "reference": "8a5d5072dca8f48460fce2f4131fcc495eec654c", "shasum": ""}, "require": {"egulias/email-validator": "^2.0|^3.1", "php": ">=7.0.0", "symfony/polyfill-iconv": "^1.0", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"mockery/mockery": "^1.0", "symfony/phpunit-bridge": "^4.4|^5.4"}, "suggest": {"ext-intl": "Needed to support internationalized email addresses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "autoload": {"files": ["lib/swift_required.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "https://swiftmailer.symfony.com", "keywords": ["email", "mail", "mailer"], "support": {"issues": "https://github.com/swiftmailer/swiftmailer/issues", "source": "https://github.com/swiftmailer/swiftmailer/tree/v6.3.0"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/swiftmailer/swiftmailer", "type": "tidelift"}], "abandoned": "symfony/mailer", "time": "2021-10-18T15:26:12+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.5.0", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1", "reference": "0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.5.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-18T09:32:20+00:00"}, {"name": "symfony/event-dispatcher", "version": "v7.1.6", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "87254c78dd50721cfd015b62277a8281c5589702"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/87254c78dd50721cfd015b62277a8281c5589702", "reference": "87254c78dd50721cfd015b62277a8281c5589702", "shasum": ""}, "require": {"php": ">=8.2", "symfony/event-dispatcher-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/service-contracts": "<2.5"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/error-handler": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.1.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.5.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "8f93aec25d41b72493c6ddff14e916177c9efc50"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/8f93aec25d41b72493c6ddff14e916177c9efc50", "reference": "8f93aec25d41b72493c6ddff14e916177c9efc50", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.5.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-18T09:32:20+00:00"}, {"name": "symfony/mailer", "version": "v7.1.6", "source": {"type": "git", "url": "https://github.com/symfony/mailer.git", "reference": "69c9948451fb3a6a4d47dc8261d1794734e76cdd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mailer/zipball/69c9948451fb3a6a4d47dc8261d1794734e76cdd", "reference": "69c9948451fb3a6a4d47dc8261d1794734e76cdd", "shasum": ""}, "require": {"egulias/email-validator": "^2.1.10|^3|^4", "php": ">=8.2", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<6.4", "symfony/messenger": "<6.4", "symfony/mime": "<6.4", "symfony/twig-bridge": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/twig-bridge": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mailer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps sending emails", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/mailer/tree/v7.1.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/mime", "version": "v7.1.6", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "caa1e521edb2650b8470918dfe51708c237f0598"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/caa1e521edb2650b8470918dfe51708c237f0598", "reference": "caa1e521edb2650b8470918dfe51708c237f0598", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<6.4", "symfony/serializer": "<6.4.3|>7.0,<7.0.3"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/serializer": "^6.4.3|^7.0.3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v7.1.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-25T15:11:02+00:00"}, {"name": "symfony/polyfill-iconv", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-iconv.git", "reference": "48becf00c920479ca2e910c22a5a39e5d47ca956"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/48becf00c920479ca2e910c22a5a39e5d47ca956", "reference": "48becf00c920479ca2e910c22a5a39e5d47ca956", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-iconv": "*"}, "suggest": {"ext-iconv": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-iconv/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "c36586dcf89a12315939e00ec9b4474adcb1d773"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/c36586dcf89a12315939e00ec9b4474adcb1d773", "reference": "c36586dcf89a12315939e00ec9b4474adcb1d773", "shasum": ""}, "require": {"php": ">=7.2", "symfony/polyfill-intl-normalizer": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/85181ba99b2345b0ef10ce42ecac37612d9fd341", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/service-contracts", "version": "v3.5.0", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "bd1d9e59a81d8fa4acdcea3f617c581f7475a80f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/bd1d9e59a81d8fa4acdcea3f617c581f7475a80f", "reference": "bd1d9e59a81d8fa4acdcea3f617c581f7475a80f", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.5.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-18T09:32:20+00:00"}, {"name": "tecnickcom/tcpdf", "version": "6.7.7", "source": {"type": "git", "url": "https://github.com/tecnickcom/TCPDF.git", "reference": "cfbc0028cc23f057f2baf9e73bdc238153c22086"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tecnickcom/TCPDF/zipball/cfbc0028cc23f057f2baf9e73bdc238153c22086", "reference": "cfbc0028cc23f057f2baf9e73bdc238153c22086", "shasum": ""}, "require": {"php": ">=5.5.0"}, "type": "library", "autoload": {"classmap": ["config", "include", "tcpdf.php", "tcpdf_parser.php", "tcpdf_import.php", "tcpdf_barcodes_1d.php", "tcpdf_barcodes_2d.php", "include/tcpdf_colors.php", "include/tcpdf_filters.php", "include/tcpdf_font_data.php", "include/tcpdf_fonts.php", "include/tcpdf_images.php", "include/tcpdf_static.php", "include/barcodes/datamatrix.php", "include/barcodes/pdf417.php", "include/barcodes/qrcode.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "TCPDF is a PHP class for generating PDF documents and barcodes.", "homepage": "http://www.tcpdf.org/", "keywords": ["PDFD32000-2008", "TCPDF", "barcodes", "datamatrix", "pdf", "pdf417", "qrcode"], "support": {"issues": "https://github.com/tecnickcom/TCPDF/issues", "source": "https://github.com/tecnickcom/TCPDF/tree/6.7.7"}, "funding": [{"url": "https://www.paypal.com/cgi-bin/webscr?cmd=_donations&currency_code=GBP&business=<EMAIL>&item_name=donation%20for%20tcpdf%20project", "type": "custom"}], "time": "2024-10-26T12:15:02+00:00"}, {"name": "tinybutstrong/opentbs", "version": "v1.12.1", "source": {"type": "git", "url": "https://github.com/Skrol29/opentbs.git", "reference": "7525dcfd9d9afb10e46b2afe58c20bedb544afe1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Skrol29/opentbs/zipball/7525dcfd9d9afb10e46b2afe58c20bedb544afe1", "reference": "7525dcfd9d9afb10e46b2afe58c20bedb544afe1", "shasum": ""}, "require": {"php": ">=5.0", "tinybutstrong/tinybutstrong": ">=3.15.1"}, "type": "library", "autoload": {"classmap": ["tbs_plugin_opentbs.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-only"], "authors": [{"name": "Skrol29", "email": "<EMAIL>"}], "description": "With OpenTBS you can merge LibreOffice, OpenOffice and Ms Office documents with PHP using the TinyButStrong template engine. Simple use LibreOffice, OpenOffice or Ms Office to edit your templates : DOCX, XLSX, PPTX, ODT, OSD, ODP and other formats.", "homepage": "http://www.tinybutstrong.com/opentbs.php", "keywords": ["docx", "odp", "ods", "odt", "pptx", "templating", "xlsx"], "support": {"issues": "https://github.com/Skrol29/opentbs/issues", "source": "https://github.com/Skrol29/opentbs/tree/v1.12.1"}, "time": "2024-03-07T02:19:17+00:00"}, {"name": "tinybutstrong/tinybutstrong", "version": "v3.15.2", "source": {"type": "git", "url": "https://github.com/Skrol29/tinybutstrong.git", "reference": "c4344b3599570cdf283692b2ad0d4682dc85ae52"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Skrol29/tinybutstrong/zipball/c4344b3599570cdf283692b2ad0d4682dc85ae52", "reference": "c4344b3599570cdf283692b2ad0d4682dc85ae52", "shasum": ""}, "require": {"php": ">=5.0"}, "type": "library", "autoload": {"classmap": ["tbs_class.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-only"], "authors": [{"name": "Skrol29", "email": "<EMAIL>"}], "description": "Template Engine for Pro and Beginners ", "homepage": "http://www.tinybutstrong.com", "keywords": ["templating"], "support": {"issues": "https://github.com/Skrol29/tinybutstrong/issues", "source": "https://github.com/Skrol29/tinybutstrong/tree/v3.15.2"}, "time": "2024-05-08T01:00:58+00:00"}, {"name": "uskur/pdf-label", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/Uskur/PdfLabel.git", "reference": "6e61618aa9af292e95866ac7bc362042b28459a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Uskur/PdfLabel/zipball/6e61618aa9af292e95866ac7bc362042b28459a2", "reference": "6e61618aa9af292e95866ac7bc362042b28459a2", "shasum": ""}, "require": {"tecnickcom/tcpdf": "^6.2"}, "default-branch": true, "type": "library", "autoload": {"psr-4": {"Uskur\\PdfLabel\\": "/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0-or-later"], "authors": [{"name": "Burak USGURLU", "email": "<EMAIL>"}], "description": "TCPDF Class to print labels in Avery or custom formats", "support": {"issues": "https://github.com/Uskur/PdfLabel/issues", "source": "https://github.com/Uskur/PdfLabel/tree/master"}, "time": "2019-03-02T18:45:15+00:00"}, {"name": "weesee/yii2-pdflabel", "version": "v0.2.1", "source": {"type": "git", "url": "https://github.com/WeeSee/yii2-pdflabel.git", "reference": "5c83fd6fdfb62ce8cf17a3fc820bcb0400ad1bc1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/WeeSee/yii2-pdflabel/zipball/5c83fd6fdfb62ce8cf17a3fc820bcb0400ad1bc1", "reference": "5c83fd6fdfb62ce8cf17a3fc820bcb0400ad1bc1", "shasum": ""}, "require": {"php": ">=7.0", "uskur/pdf-label": "*", "yiisoft/yii2": "~2.0.13"}, "type": "yii2-extension", "autoload": {"psr-4": {"weesee\\pdflabel\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0-only"], "authors": [{"name": "WeeSee", "email": "<EMAIL>"}], "description": "Extension to print labels with PDF", "keywords": ["label", "pdf", "print", "yii2"], "support": {"issues": "https://github.com/weesee/yii2-pdflabel/issues", "source": "https://github.com/weesee/yii2-pdflabel"}, "time": "2018-01-23T22:40:42+00:00"}, {"name": "yii2tech/csv-grid", "version": "1.0.5", "source": {"type": "git", "url": "https://github.com/yii2tech/csv-grid.git", "reference": "6d10b2b7590affd95ae2bacf20562c24d59d0ece"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yii2tech/csv-grid/zipball/6d10b2b7590affd95ae2bacf20562c24d59d0ece", "reference": "6d10b2b7590affd95ae2bacf20562c24d59d0ece", "shasum": ""}, "require": {"yiisoft/yii2": "~2.0.13"}, "require-dev": {"phpunit/phpunit": "4.8.27 || ^5.0 || ^6.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"yii2tech\\csvgrid\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Yii2 extension data export to CSV file", "keywords": ["csv", "export", "large data", "yii2"], "support": {"forum": "http://www.yiiframework.com/forum/", "issues": "https://github.com/yii2tech/csv-grid/issues", "source": "https://github.com/yii2tech/csv-grid", "wiki": "https://github.com/yii2tech/csv-grid/wiki"}, "funding": [{"url": "https://github.com/klimov-paul", "type": "github"}, {"url": "https://www.patreon.com/klimov_paul", "type": "patreon"}], "abandoned": true, "time": "2020-03-04T11:17:11+00:00"}, {"name": "yiisoft/yii2", "version": "2.0.51", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-framework.git", "reference": "ea1f112f4dc9a9824e77b788019e2d53325d823c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-framework/zipball/ea1f112f4dc9a9824e77b788019e2d53325d823c", "reference": "ea1f112f4dc9a9824e77b788019e2d53325d823c", "shasum": ""}, "require": {"bower-asset/inputmask": "^5.0.8 ", "bower-asset/jquery": "3.7.*@stable | 3.6.*@stable | 3.5.*@stable | 3.4.*@stable | 3.3.*@stable | 3.2.*@stable | 3.1.*@stable | 2.2.*@stable | 2.1.*@stable | 1.11.*@stable | 1.12.*@stable", "bower-asset/punycode": "^2.2", "bower-asset/yii2-pjax": "~2.0.1", "cebe/markdown": "~1.0.0 | ~1.1.0 | ~1.2.0", "ext-ctype": "*", "ext-mbstring": "*", "ezyang/htmlpurifier": "^4.17", "lib-pcre": "*", "paragonie/random_compat": ">=1", "php": ">=7.3.0", "yiisoft/yii2-composer": "~2.0.4"}, "bin": ["yii"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.yiiframework.com/", "role": "Founder and project lead"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://rmcreative.ru/", "role": "Core framework development"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://mdomba.info/", "role": "Core framework development"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.cebe.cc/", "role": "Core framework development"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://resurtm.com/", "role": "Core framework development"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Core framework development"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Core framework development"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://dynasource.eu", "role": "Core framework development"}], "description": "Yii PHP Framework Version 2", "homepage": "https://www.yiiframework.com/", "keywords": ["framework", "yii2"], "support": {"forum": "https://forum.yiiframework.com/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/yii2/issues?state=open", "source": "https://github.com/yiisoft/yii2", "wiki": "https://www.yiiframework.com/wiki"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2", "type": "tidelift"}], "time": "2024-07-18T19:50:00+00:00"}, {"name": "yiisoft/yii2-authclient", "version": "2.2.16", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-authclient.git", "reference": "38ead33797311f8e1c7768bbcf8b5606e1bf0944"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-authclient/zipball/38ead33797311f8e1c7768bbcf8b5606e1bf0944", "reference": "38ead33797311f8e1c7768bbcf8b5606e1bf0944", "shasum": ""}, "require": {"paragonie/random_compat": ">=1", "yiisoft/yii2": "~2.0.13", "yiisoft/yii2-httpclient": "~2.0.5"}, "require-dev": {"cweagans/composer-patches": "^1.7", "phpunit/phpunit": "4.8.34"}, "suggest": {"web-token/jwt-checker": "required for JWS, JWT or JWK related flows like OpenIDConnect", "web-token/jwt-key-mgmt": "required for JWS, JWT or JWK related flows like OpenIDConnect", "web-token/jwt-signature": "required for JWS, JWT or JWK related flows like OpenIDConnect", "web-token/jwt-signature-algorithm-ecdsa": "required for JWS, JWT or JWK related flows like OpenIDConnect", "web-token/jwt-signature-algorithm-hmac": "required for JWS, JWT or JWK related flows like OpenIDConnect", "web-token/jwt-signature-algorithm-rsa": "required for JWS, JWT or JWK related flows like OpenIDConnect"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}, "patches": {"phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}, "phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch"}}}, "autoload": {"psr-4": {"yii\\authclient\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "External authentication via OAuth and OpenID for the Yii framework", "keywords": ["OpenID Connect", "OpenId", "api", "auth", "o<PERSON>h", "yii2"], "support": {"forum": "https://www.yiiframework.com/forum/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/yii2-authclient/issues", "source": "https://github.com/yiisoft/yii2-authclient", "wiki": "https://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-authclient", "type": "tidelift"}], "time": "2024-05-10T11:16:16+00:00"}, {"name": "yiisoft/yii2-bootstrap", "version": "2.0.11", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-bootstrap.git", "reference": "83d144f4089adaa7064ad60dc4c1436daa2eb30e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-bootstrap/zipball/83d144f4089adaa7064ad60dc4c1436daa2eb30e", "reference": "83d144f4089adaa7064ad60dc4c1436daa2eb30e", "shasum": ""}, "require": {"bower-asset/bootstrap": "3.4.* | 3.3.* | 3.2.* | 3.1.*", "yiisoft/yii2": "~2.0.6"}, "require-dev": {"cweagans/composer-patches": "^1.7", "phpunit/phpunit": "4.8.34"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}, "patches": {"phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}, "phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch"}}}, "autoload": {"psr-4": {"yii\\bootstrap\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.yiiframework.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://rmcreative.ru/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Twitter Bootstrap extension for the Yii framework", "keywords": ["bootstrap", "yii2"], "support": {"forum": "http://www.yiiframework.com/forum/", "irc": "irc://irc.freenode.net/yii", "issues": "https://github.com/yiisoft/yii2-bootstrap/issues", "source": "https://github.com/yiisoft/yii2-bootstrap", "wiki": "http://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-bootstrap", "type": "tidelift"}], "time": "2021-08-09T20:54:06+00:00"}, {"name": "yiisoft/yii2-composer", "version": "2.0.10", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-composer.git", "reference": "94bb3f66e779e2774f8776d6e1bdeab402940510"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-composer/zipball/94bb3f66e779e2774f8776d6e1bdeab402940510", "reference": "94bb3f66e779e2774f8776d6e1bdeab402940510", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 | ^2.0"}, "require-dev": {"composer/composer": "^1.0 | ^2.0@dev", "phpunit/phpunit": "<7"}, "type": "composer-plugin", "extra": {"class": "yii\\composer\\Plugin", "branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\composer\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The composer plugin for Yii extension installer", "keywords": ["composer", "extension installer", "yii2"], "support": {"forum": "http://www.yiiframework.com/forum/", "irc": "irc://irc.freenode.net/yii", "issues": "https://github.com/yiisoft/yii2-composer/issues", "source": "https://github.com/yiisoft/yii2-composer", "wiki": "http://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-composer", "type": "tidelift"}], "time": "2020-06-24T00:04:01+00:00"}, {"name": "yiisoft/yii2-httpclient", "version": "2.0.15", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-httpclient.git", "reference": "5a8350e15f2db3555ba52830c9c701587c136e87"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-httpclient/zipball/5a8350e15f2db3555ba52830c9c701587c136e87", "reference": "5a8350e15f2db3555ba52830c9c701587c136e87", "shasum": ""}, "require": {"paragonie/random_compat": ">=1", "yiisoft/yii2": "~2.0.13"}, "require-dev": {"cweagans/composer-patches": "^1.7", "phpunit/phpunit": "4.8.34"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}, "composer-exit-on-patch-failure": true, "patches": {"phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}, "phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch"}}}, "autoload": {"psr-4": {"yii\\httpclient\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "HTTP client extension for the Yii framework", "keywords": ["curl", "http", "httpclient", "yii2"], "support": {"forum": "http://www.yiiframework.com/forum/", "irc": "irc://irc.freenode.net/yii", "issues": "https://github.com/yiisoft/yii2-httpclient/issues", "source": "https://github.com/yiisoft/yii2-httpclient", "wiki": "http://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-httpclient", "type": "tidelift"}], "time": "2023-05-22T18:32:24+00:00"}, {"name": "yiisoft/yii2-jui", "version": "2.0.7", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-jui.git", "reference": "ce45c16d4fbbe7d1c516d8d0e8311e07f6138eed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-jui/zipball/ce45c16d4fbbe7d1c516d8d0e8311e07f6138eed", "reference": "ce45c16d4fbbe7d1c516d8d0e8311e07f6138eed", "shasum": ""}, "require": {"bower-asset/jquery-ui": "~1.12.1", "yiisoft/yii2": "~2.0.4"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\jui\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The Jquery UI extension for the Yii framework", "keywords": ["jQuery <PERSON>", "yii2"], "support": {"forum": "http://www.yiiframework.com/forum/", "irc": "irc://irc.freenode.net/yii", "issues": "https://github.com/yiisoft/yii2-jui/issues", "source": "https://github.com/yiisoft/yii2-jui", "wiki": "http://www.yiiframework.com/wiki/"}, "time": "2017-11-25T15:32:29+00:00"}, {"name": "yiisoft/yii2-swiftmailer", "version": "2.1.3", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-swiftmailer.git", "reference": "7b7ec871b4a63c0abbcd10e1ee3fb5be22f8b340"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-swiftmailer/zipball/7b7ec871b4a63c0abbcd10e1ee3fb5be22f8b340", "reference": "7b7ec871b4a63c0abbcd10e1ee3fb5be22f8b340", "shasum": ""}, "require": {"swiftmailer/swiftmailer": "~6.0", "yiisoft/yii2": ">=2.0.4"}, "require-dev": {"cweagans/composer-patches": "^1.7", "phpunit/phpunit": "4.8.34"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}, "composer-exit-on-patch-failure": true, "patches": {"phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}, "phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch"}}}, "autoload": {"psr-4": {"yii\\swiftmailer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The SwiftMailer integration for the Yii framework", "keywords": ["email", "mail", "mailer", "swift", "swiftmailer", "yii2"], "support": {"forum": "http://www.yiiframework.com/forum/", "irc": "irc://irc.freenode.net/yii", "issues": "https://github.com/yiisoft/yii2-swiftmailer/issues", "source": "https://github.com/yiisoft/yii2-swiftmailer", "wiki": "http://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-swiftmailer", "type": "tidelift"}], "time": "2021-12-30T08:48:48+00:00"}, {"name": "yiisoft/yii2-symfonymailer", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-symfonymailer.git", "reference": "291c00979a9bf14e89bb5230ddd700a6dc130e51"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-symfonymailer/zipball/291c00979a9bf14e89bb5230ddd700a6dc130e51", "reference": "291c00979a9bf14e89bb5230ddd700a6dc130e51", "shasum": ""}, "require": {"php": ">=7.4.0", "symfony/mailer": ">=5.4.0", "yiisoft/yii2": ">=2.0.4"}, "require-dev": {"captainhook/captainhook": "^5.10", "captainhook/plugin-composer": "^5.3", "phpunit/phpunit": "9.5.10", "symplify/easy-coding-standard": "^10.1", "vimeo/psalm": "^4.22"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\symfonymailer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The SymfonyMailer integration for the Yii framework", "keywords": ["email", "mail", "mailer", "symfony", "symfonymailer", "yii2"], "support": {"forum": "http://www.yiiframework.com/forum/", "irc": "irc://irc.freenode.net/yii", "issues": "https://github.com/yiisoft/yii2-symfonymailer/issues", "source": "https://github.com/yiisoft/yii2-symfonymailer", "wiki": "http://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-symfonymailer", "type": "tidelift"}], "time": "2022-12-05T08:20:13+00:00"}], "packages-dev": [{"name": "codeception/specify", "version": "0.4.6", "source": {"type": "git", "url": "https://github.com/Codeception/Specify.git", "reference": "21b586f503ca444aa519dd9cafb32f113a05f286"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/Specify/zipball/21b586f503ca444aa519dd9cafb32f113a05f286", "reference": "21b586f503ca444aa519dd9cafb32f113a05f286", "shasum": ""}, "require": {"myclabs/deep-copy": "~1.1", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "autoload": {"psr-0": {"Codeception\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "BDD code blocks for PHPUnit and Codeception", "support": {"issues": "https://github.com/Codeception/Specify/issues", "source": "https://github.com/Codeception/Specify/tree/master"}, "time": "2016-10-21T09:42:00+00:00"}, {"name": "codeception/verify", "version": "0.3.3", "source": {"type": "git", "url": "https://github.com/Codeception/Verify.git", "reference": "5d649dda453cd814dadc4bb053060cd2c6bb4b4c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/Verify/zipball/5d649dda453cd814dadc4bb053060cd2c6bb4b4c", "reference": "5d649dda453cd814dadc4bb053060cd2c6bb4b4c", "shasum": ""}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "autoload": {"files": ["src/Codeception/function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "BDD assertion library for PHPUnit", "support": {"issues": "https://github.com/Codeception/Verify/issues", "source": "https://github.com/Codeception/Verify/tree/master"}, "time": "2017-01-09T10:58:51+00:00"}, {"name": "fakerphp/faker", "version": "v1.23.1", "source": {"type": "git", "url": "https://github.com/FakerPHP/Faker.git", "reference": "bfb4fe148adbf78eff521199619b93a52ae3554b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FakerPHP/Faker/zipball/bfb4fe148adbf78eff521199619b93a52ae3554b", "reference": "bfb4fe148adbf78eff521199619b93a52ae3554b", "shasum": ""}, "require": {"php": "^7.4 || ^8.0", "psr/container": "^1.0 || ^2.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "conflict": {"fzaninotto/faker": "*"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "doctrine/persistence": "^1.3 || ^2.0", "ext-intl": "*", "phpunit/phpunit": "^9.5.26", "symfony/phpunit-bridge": "^5.4.16"}, "suggest": {"doctrine/orm": "Required to use Faker\\ORM\\Doctrine", "ext-curl": "Required by Faker\\Provider\\Image to download images.", "ext-dom": "Required by Faker\\Provider\\HtmlLorem for generating random HTML.", "ext-iconv": "Required by Faker\\Provider\\ru_RU\\Text::realText() for generating real Russian text.", "ext-mbstring": "Required for multibyte Unicode string functionality."}, "type": "library", "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.23.1"}, "time": "2024-01-02T13:46:09+00:00"}, {"name": "phpspec/php-diff", "version": "v1.1.3", "source": {"type": "git", "url": "https://github.com/phpspec/php-diff.git", "reference": "fc1156187f9f6c8395886fe85ed88a0a245d72e9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/php-diff/zipball/fc1156187f9f6c8395886fe85ed88a0a245d72e9", "reference": "fc1156187f9f6c8395886fe85ed88a0a245d72e9", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-0": {"Diff": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "http://github.com/chrisboulton"}], "description": "A comprehensive library for generating differences between two hashable objects (strings or arrays).", "support": {"source": "https://github.com/phpspec/php-diff/tree/v1.1.3"}, "time": "2020-09-18T13:47:07+00:00"}, {"name": "yiisoft/yii2-debug", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-debug.git", "reference": "c063a75c5ec76e4e726e0e6f38f1e7b9daabba6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-debug/zipball/c063a75c5ec76e4e726e0e6f38f1e7b9daabba6e", "reference": "c063a75c5ec76e4e726e0e6f38f1e7b9daabba6e", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.4", "yiisoft/yii2": "~2.0.13"}, "require-dev": {"cweagans/composer-patches": "^1.7", "phpunit/phpunit": "4.8.34", "yiisoft/yii2-coding-standards": "~2.0", "yiisoft/yii2-swiftmailer": "*"}, "default-branch": true, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}, "composer-exit-on-patch-failure": true, "patches": {"phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}, "phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch", "Fix PHP 8.1 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php81.patch"}}}, "autoload": {"psr-4": {"yii\\debug\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The debugger extension for the Yii framework", "keywords": ["debug", "debugger", "dev", "yii2"], "support": {"forum": "https://www.yiiframework.com/forum/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/yii2-debug/issues", "source": "https://github.com/yiisoft/yii2-debug", "wiki": "https://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-debug", "type": "tidelift"}], "time": "2024-02-27T07:02:03+00:00"}, {"name": "yiisoft/yii2-faker", "version": "2.0.5", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-faker.git", "reference": "8c361657143bfaea58ff7dcc9bf51f1991a46f5d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-faker/zipball/8c361657143bfaea58ff7dcc9bf51f1991a46f5d", "reference": "8c361657143bfaea58ff7dcc9bf51f1991a46f5d", "shasum": ""}, "require": {"fakerphp/faker": "~1.9|~1.10", "yiisoft/yii2": "~2.0.0"}, "require-dev": {"cweagans/composer-patches": "^1.7", "phpunit/phpunit": "4.8.34"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}, "composer-exit-on-patch-failure": true, "patches": {"phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}, "phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch"}}}, "autoload": {"psr-4": {"yii\\faker\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Fixture generator. The Faker integration for the Yii framework.", "keywords": ["Fixture", "faker", "yii2"], "support": {"forum": "http://www.yiiframework.com/forum/", "irc": "irc://irc.freenode.net/yii", "issues": "https://github.com/yiisoft/yii2-faker/issues", "source": "https://github.com/yiisoft/yii2-faker", "wiki": "http://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-faker", "type": "tidelift"}], "time": "2020-11-10T12:27:35+00:00"}, {"name": "yiisoft/yii2-gii", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-gii.git", "reference": "c025c29a15ce7011a9fed14efadc9cea56cf0ca8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-gii/zipball/c025c29a15ce7011a9fed14efadc9cea56cf0ca8", "reference": "c025c29a15ce7011a9fed14efadc9cea56cf0ca8", "shasum": ""}, "require": {"phpspec/php-diff": "^1.1.0", "yiisoft/yii2": "~2.0.46"}, "require-dev": {"cweagans/composer-patches": "^1.7", "phpunit/phpunit": "4.8.34", "yiisoft/yii2-coding-standards": "~2.0"}, "default-branch": true, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}, "composer-exit-on-patch-failure": true, "patches": {"phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}, "phpunit/php-file-iterator": {"Fix PHP 8.1 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_path_file_iterator.patch"}, "phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch", "Fix PHP 8.1 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php81.patch"}}}, "autoload": {"psr-4": {"yii\\gii\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The Gii extension for the Yii framework", "keywords": ["code generator", "dev", "gii", "yii2"], "support": {"forum": "https://www.yiiframework.com/forum/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/yii2-gii/issues", "source": "https://github.com/yiisoft/yii2-gii", "wiki": "https://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-gii", "type": "tidelift"}], "time": "2024-08-05T20:04:54+00:00"}], "aliases": [], "minimum-stability": "dev", "stability-flags": {"mootensai/yii2-enhanced-gii": 20, "yiisoft/yii2-debug": 20, "yiisoft/yii2-gii": 20}, "prefer-stable": true, "prefer-lowest": false, "platform": {"php": ">=5.4.0"}, "platform-dev": {}, "plugin-api-version": "2.6.0"}