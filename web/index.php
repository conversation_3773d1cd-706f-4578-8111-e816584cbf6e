<?php

// comment out the following two lines when deployed to production
defined('YII_DEBUG') or define('YII_DEBUG', true);
defined('YII_ENV') or define('YII_ENV', 'dev');

require __DIR__ . '/../vendor/autoload.php';
require __DIR__ . '/../vendor/yiisoft/yii2/Yii.php';

if( $_SERVER["HTTP_HOST"] == "dev.klub.wdl.sk" )
    $config = require __DIR__ . '/../config/dev.php';
else if( $_SERVER["HTTP_HOST"] == "klub.wdl.hu" )
    $config = require __DIR__ . '/../config/klubwdlhu.php';
else if( $_SERVER["HTTP_HOST"] == "wstock.wikarska.com" )
    $config = require __DIR__ . '/../config/wikarska.php';
else if( $_SERVER["HTTP_HOST"] == "b2b.racio.com" )
    $config = require __DIR__ . '/../config/b2b.php';
else if( $_SERVER["HTTP_HOST"] == "dagmar.wikarski.com" )
    $config = require __DIR__ . '/../config/wstdev.php';
else if( $_SERVER["HTTP_HOST"] == "wikarska.grapph.com" )
    $config = require __DIR__ . '/../config/wikarska.php';
else if( $_SERVER["HTTP_HOST"] == "wstockucto.racio.com" )
    $config = require __DIR__ . '/../config/ucto.php';
else
    $config = require __DIR__ . '/../config/wikarska.php';
//    $config = require __DIR__ . '/../config/ucto.php';

//TOTO....
    $config = require __DIR__ . '/../config/web-local.php';


(new yii\web\Application($config))->run();
