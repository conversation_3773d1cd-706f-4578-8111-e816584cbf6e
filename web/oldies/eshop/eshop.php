<?php

//$db = new PDO("pgsql:dbname=estock;host=localhost", 'postgres', 'Rin1!!!andBeyond');
$db = new PDO("pgsql:dbname=estock;host=localhost", 'robot', 'jw3409rhsolcd32ui7fhcsi' );
//$db = new PDO("sqlanywhere:eng={$config['db']['dbname']};charset=utf8;COMMLINKS=tcpip{host=*************;port=2637}",
//          $config['db']['username'], $config['db']['password']);

header('Content-Type: application/json');

$datetime = new DateTime();

echo '{"created":"'. $datetime->format(DateTime::ATOM);
echo '","products":
[';;
$first = true;
//and x.kod in ('BREI','FCAS','CERT','ESPR','RADO','OMEG','WRAY','WTHS','TISS','ZENI','WPRD')
//group by code,p1,p6,x.kod
//order by code"
/*
$select = "select p.mat_id code,'x' shops, p1 basepriceEur, if(p6=0.00) then
( if p.kod in ('BREI','CERT','RADO','OMEG','WRAY','WTHS','TISS','ZENI','WPRD') then
round(p1*27,-2) else round(p1*27,-1) end if) else p6 end if
 basepriceCzk from dba.product p left outer join dba.dyninfox x
on p.mat_id=x.mat_id
where 
p.kod in ('AMON','BREI','CERS','ESPR','FCAS','FEST','JMON','LMON','MONJ','MONL','MONN','MONP','MONT','OMEG','OMON','PMON','RADO','TISS','WLOX','WMON','WPRD','WRAY','WTHS','ZENI')
group by code,p1,p6,p.kod
order by code"; */
/*
$select = "select x.mat_id code,'x' shops, p0e basepriceEur, if(p6=0.00) then
( if x.kod in ('BREI','CERT','RADO','OMEG','WRAY','WTHS','TISS','ZENI','WPRD') then
round(p0e*27,-2) else round(p0e*27,-1) end if) else p6 end if
 basepriceCzk from dba.dyninfox x left outer join dba.product p
on p.mat_id=x.mat_id
where 
x.kod in ('ATHS','AMON','BALB','BREI','CERS','ESPR','FCAS','FEST','JMON','LMON','MONJ','MONL','MONN','MONP','MONT','OMEG','OMON','PMON','RADO','TISS','WLOX','WMON','WPRD','WRAY','WTHS','ZENI','ESHV')
group by code,p0e,p6,x.kod
order by code";
*/

$select = "select x.mat_id code,'x' shops, p0e basepriceEur, p0f basepriceCzk from dyninfox x left outer join product p on p.mat_id=x.mat_id
where /*shop like '%0' and */
x.kod in ('ATHS','AMON','BALB','BREI','CERS','ESPR','FEST','JMON','LMON','MONJ','MONL','MONN','MONP','MONT','OMEG','OMON','PMON','RADO','TISS','WLOX','WMON','WPRD','WRAY','WTHS','ZENI','ESHV','MIDO','WIWC' )  or ( remains>0 and x.kod in ('FCAS') )
group by code,p0e,p0f,p6,x.kod
order by code";


foreach($db->query($select, PDO::FETCH_ASSOC) as $row) {

//    echo  json_encode($row,JSON_FORCE_OBJECT);
    $row['basepriceeur'] = floatval($row['basepriceeur']);
    $row['basepriceczk'] = floatval($row['basepriceczk']);
    $rowshops = array('s0'=>0,'s1'=>0,'s2'=>0,'s3'=>0,'s4'=>0,'s5'=>0);
    foreach($db->query("select shop,sum(remains) from dyninfox where shop like '%0' and mat_id=".$row['code']." group by shop order by shop",  PDO::FETCH_NUM) as $shop ){
	switch( $shop[0] ){
	    case "s0":
		$rowshops['s0'] = floatval($shop[1]);
		break;
	    case "WATCH DE LUXE - AUPA 3624 0":
		$rowshops['s1'] = floatval($shop[1]);
		break;
	    case "Watch de Luxe, Eurov 5039 0":
		$rowshops['s2'] = floatval($shop[1]);
		break;
	    case "TAG Heuer,Eurovea 5040 0":
		$rowshops['s3'] = floatval($shop[1]);
		break;
	    case "WOW  BB,RACIO,Export 4413 0":
		$rowshops['s4'] = floatval($shop[1]);
		break;
	    case "W-WATCH PREV. OPTIMA 3731 0":
		$rowshops['s5'] = floatval($shop[1]);
		break;
	}

    }
//    $row['shops'] = array(implode(",",$rowshops));
    $row['shops'] = $rowshops;

//    echo print_r($row,true);
    if( $rowshops['s0'] != 0 || $rowshops['s1'] != 0 || $rowshops['s2'] != 0 || $rowshops['s3'] != 0 || $rowshops['s4'] != 0 || $rowshops['s5'] != 0 ){
        if( !$first ){
    	echo ",\n";
	} else {
	    $first = false;
	}
        echo  json_encode($row,JSON_FORCE_OBJECT);
    }
}

echo "]}";

