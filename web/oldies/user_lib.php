<?php
    /*
    ** e-as
    ** Some handy functions for the user part of the www
    */
     
    if (!defined('EAS_BASE')) define('EAS_BASE', '/var/www/vhosts/wdl.sk.ssl/e-stock2/trunk');
         
    require_once EAS_BASE . "/include/config.php" ;
     
    require_once EAS_BASE . '/include/Lang.php';

if (!function_exists('set_magic_quotes_runtime')) {
    function set_magic_quotes_runtime($new_setting) {
        return true;
    }
}

     
    // Browser detection library
    require_once EAS_BASE . '/include/Browser.php';
    $browser = new Browser();
     
    // Initialize the localization routines. Not needed if PDF stream...
    if ( !defined('PDF_STREAM') ){
        $language = Lang::select();
	putenv("LANGUAGE=$language");
        putenv("LANG=$language");
    }
    
    if ($language == 'sk' )
    setlocale(LC_ALL, 'sk_SK.utf8');
    elseif($language == 'hu' ){
	setlocale(LC_ALL, 'hu_HU.utf8');
    }
    elseif($language == 'cz' )
    setlocale(LC_ALL, 'sk_SK.utf8');
    else
        setlocale(LC_ALL, 'en_US.utf8');
     
    $er = setlocale(LC_NUMERIC, "en_US" );
    $err = setlocale(LC_MONETARY, "en_US" );

    //echo $er;
    //echo $err;
     
    bindtextdomain("e-stock", EAS_BASE . "/locale/");
    textdomain("e-stock");
     
    /******************
    Show edit page of the user
    *******************/
    function oops($display_error = "" ) {
         
        ///Kriticke errors je mozno vyhodnocovat s moznostou automatickej ochrany napr. proti zaplavam alebo hackom
        ///VYpisy by mali byt podla pouziteho jazyka v aktualnom template systeme
         
        if ($fp = fopen($GLOBALS['cfg_error_log_file'], "a")) {
             
            fputs($fp, "---------------------\n".date ("Y.m.d h:i:s A ", time()).$GLOBALS['HTTP_REFERER']." ".$GLOBALS['REMOTE_ADDR']."  ".$GLOBALS['SCRIPT_FILENAME']."\n\n".$display_error."\n\n" , strlen($display_error) + 500 );
             
             
            fclose($fp);
             
        } else {
            ///Kde inde by sme to pisali?
            echo _("Error write log file");
        }
        //Ak Error, tak nielen log...
        /*   if( substr($display_error,0, 4 ) != "Warn" ){
        show_white_text(_("Oops... Error occured")."<br>");
        show_white_text( $display_error );
         
        ///Iba default pata stranky...
        echo "</body></html>";
         
        exit;
        }
        */
        return;
    }
     
    /******************
    Database select
    ******************/
    function db_select($to_select = "" ) {
         
        //echo $my_db_to_select."--";
        $set_cookie = true;
        /* Use a directly supplied parameter, if given. */
        if (!Empty($to_select) ) {
            $db_to_select = strtolower($to_select);
        }
        if (!Empty($db_to_select) ) {
            $db_to_select = strtolower($db_to_select);
             
            /* If we have already set a cookie, simply use that. */
        } elseif (isset($_COOKIE['my_db_to_select']) ) {
            $db_to_select = $_COOKIE['my_db_to_select'];
            $set_cookie = false;
        } else {
            //      $arrx = array_keys($GLOBALS['cfg_db_all']);
            //            $db_to_select = $GLOBALS['cfg_db_all'][$arrx[0]][0];
       $remote = getenv("HTTP_HOST");
        $dbname = "e-stock";

        if ($remote == "estock.racio.com"  ){
            $dbname = "e-stock";
        }

        if ($remote == "estock.chrono.hu"  ){
            $dbname = "wdl";
        }

        if ($remote == "intrex.grapph.com"  ){
            $dbname = "intrex";
        }


            $db_to_select = "db_".$dbname;
        }
         
        /* Set the db_to_select cookie to expire in a year. */
        if ($set_cookie) {
            setcookie('my_db_to_select', $db_to_select, time() + 31536000, '/', '', 0);
        }
         
        return $db_to_select;
         
    }
     

    /******************
    SKIN css select
    ******************/
    function css_select($to_select = "" ) {
         
        //echo $my_db_to_select."--";
        $set_cookie = true;
        /* Use a directly supplied parameter, if given. */
        if (!Empty($to_select) ) {
            $css_to_select = strtolower($to_select);
        }
        if (!Empty($css_to_select) ) {
            $css_to_select = strtolower($css_to_select);
             
            /* If we have already set a cookie, simply use that. */
        } elseif (isset($_COOKIE['css_to_select']) ) {
            $db_to_select = $_COOKIE['css_to_select'];
            $set_cookie = false;
        } else {
            //      $arrx = array_keys($GLOBALS['cfg_db_all']);
            //            $db_to_select = $GLOBALS['cfg_db_all'][$arrx[0]][0];
            $css_to_select = "style.css";
        }
         
        /* Set the db_to_select cookie to expire in a year. */
        if ($set_cookie) {
            setcookie('css_to_select', $css_to_select, time() + 31536000, '/', '', 0);
        }
         	
        return $css_to_select;
         
    }
     

    /******************
    Sybase_Connect
    ******************/
    function db_connect($dns_name, $nm='', $pw='' ) {
        if (Empty($dns_name ) ) {
            $dns_name = $GLOBALS['cfg_db_all'][db_select()][0];
        }
        if (Empty($nm ) ) {
            $nm = $_SESSION['__auth']['userID'];
        }
        if (Empty($pw ) ) {
            $pw = $_SESSION['__auth']['userPwd'];
        }
//print_r($GLOBALS);
//	    echo $dns_name."xx".$_SESSION['__auth']['userID']."yy".$_SESSION['__auth']['userPwd']."ff";

      switch ( DB_ENGINE ){
        case "SAODBC":         
	    $map_connection = odbc_connect($dns_name, $nm, $pw );
	    break;	
	case "SASQL":
	    $pport='2637';
	    if( $dns_name<>'e-stock') $pport = '2638';
	    $map_connection = sasql_connect( "UID=".$nm.";PWD=".$pw.";ENG=".$dns_name.";charset=utf8;CommLinks=tcpip(host=localhost:".$pport.")" );
	    //sasql_set_option( $map_connection, 'row_counts', 'TRUE');
	    break;
	}
	
        if (!$map_connection) {
            return false;
        } else {
            return $map_connection;
        }
    }
     
    /******************
    Show text in the formated form
    ******************/
    function show_white_text($txtstring ) {
	echo "<div align=\"center\"><font face=\"Arial, Helvetica, sans-serif\" size=\"2\" color=\"#AAAAee\">".$txtstring."</font></div>";
    }
     
     
    /******************
    Handy debug function
    *******************/
    function mysql_error_display() {
        echo mysql_error();
    }
     
    function my_sybase_rows($con, $query, $lines = -1 ) {
         
      switch ( DB_ENGINE ){
        case "SAODBC":         
	    $res = odbc_exec($con, $query);
            if ($res == true ) {
    	        $result = array();
	        if ($lines == -1 ) {
                    while ($ref = odbc_fetch_into($res, $row) ) {
    	    		$result[] = $row;
	            }
                } else {
            	    $cntr = 0;
            	    while ($ref = odbc_fetch_into($res, $row) && $cntr < $lines ) {
                	$result[] = $row;
                	$cntr++;
            	    }
        	}
             
    		return $result;
    	    } else {
	        return false;
            }
	    break;	
	case "SASQL":
	    $res = sasql_query( $con, $query );
            if ($res == true ) {
    	        $result = array();
	        if ($lines == -1 ) {
                    while ( $row = sasql_fetch_row( $res ) ){
    	    		$result[] = $row;
	            }
                } else {
            	    $cntr = 0;
            	    while (($row = sasql_fetch_row( $res )) && $cntr < $lines ) {
                	$result[] = $row;
                	$cntr++;
            	    }
        	}
             
    		return $result;
    	    } else {
	        return false;
            }
	    break;	

	}


    }
     
    function my_sybase_row($con, $query ) {
      
      switch ( DB_ENGINE ){
        case "SAODBC":
         
            $res = @odbc_exec($con, $query);
	    if ($res == true ) {
    		$result = array();
        	$ref = odbc_fetch_into($res, $row) ;
        	return $row;
            } else {
//		    echo $query;
    		return false;
    	    }
	    break;
	
	case "SASQL":
	    $res = sasql_query( $con, $query );
	    if( ! $res ) {
		return false;
	    } else {
		$row = sasql_fetch_row( $res );
		//sqlanywhere_free_result( $res );
		return $row;			
	    }
	    break;
	}
    }
     
    function tep_in_array($lookup_value, $lookup_array) {
        if (function_exists('in_array')) {
            if (in_array($lookup_value, $lookup_array)) return true;
        } else {
            reset($lookup_array);
            while (list($key, $value) = each($lookup_array)) {
                if ($value == $lookup_value) return true;
            }
        }
         
        return false;
    }
     
     
    function tep_get_all_get_params($exclude_array = '') {
        global $_GET;
        global $_POST;
         
        if ($exclude_array == '') $exclude_array = array();
             
        $get_url = '';
         
        if (is_array($_POST)) {
            reset($_POST);
            while (list($key, $value) = each($_POST)) {
                if (($key != session_name()) && ($key != 'error') && (!tep_in_array($key, $exclude_array))) {
                    $xval = "";
                    if (is_array($value ) ) {
                        reset($value);
                        while (list($xkey, $xvalue) = each($value)) {
                            $xval .= $key . rawurlencode("["). $xkey . rawurlencode("]") . '=' . rawurlencode(StripSlashes($xvalue)) . '&';
                        }
                        $get_url .= $xval;
                    } else {
                        $get_url .= $key . '=' . rawurlencode(StripSlashes($value)) . '&';
                    }
                }
            }
        }
         
        if (is_array($_GET)) {
            reset($_GET);
            while (list($key, $value) = each($_GET)) {
                if (($key != session_name()) && ($key != 'error') && (!tep_in_array($key, $exclude_array))) {
                    $xval = "";
                    if (is_array($value ) ) {
                        reset($value);
                        while (list($xkey, $xvalue) = each($value)) {
                            $xval .= $key . rawurlencode("["). $xkey . rawurlencode("]") . '=' . rawurlencode(StripSlashes($xvalue)) . '&';
                        }
                        $get_url .= $xval;
                    } else {
                        $get_url .= $key . '=' . rawurlencode(StripSlashes($value)) . '&';
                    }
                }
            }
        }
         
        return $get_url;
    }
     
    function my_show_pagenr($res, $last = "no", $maxl = MAXLINES ) {
        global $strana;
        $num_rows = 0;
	$stranx = 50;
        switch ( DB_ENGINE ){
    	    case "SAODBC":
    		while ($ref = odbc_fetch_row($res ) ) {
        	    $num_rows++;
    		}
    		odbc_fetch_row($res, 0 );
		break;	
		
	    case "SASQL":
		$num_rows = abs(sasql_num_rows( $res ));
/*                    while ( $row = sqlanywhere_fetch_row( $res ) ){
        		$num_rows++;
	            }
		    sqlanywhere_data_seek( $res, 0 );
		    */
		break;	
    	} 
        if ($num_rows == 0 ) {
            echo _("Not found<br>");
            return -1;
        } else {
            echo "($num_rows) ";
             
            if (Empty($strana) || $strana == 0 ) {
                $strana = 1;
                if ($last == "last" ) {
                    $strana = 1 + floor(($num_rows-1)/$maxl);
                }
            }
            $y = 1;
            if ($num_rows > $maxl ) {
                echo _("Page: ");
                FOR ($x = 1; $x <= $num_rows; $x += $maxl ) {
                    if ($strana == $y ){
                	echo "<b>$y</b> ";
		    }  elseif ( !($y%10) or $y<10 or abs($y-$strana)<10) {
			if( ($y<100 or $y> (1 + floor(($num_rows-1)/$maxl))-100) or abs($y-$strana)<200  ){
			    echo "<a href=".$_SERVER['PHP_SELF']."?strana=$y&".tep_get_all_get_params(array('strana', 'clipsort', 'movesort', 'action', 'disctype', 'perc', 'amount', 'filter', 'cmdline', 'sqltype', 'x', 'y', 'isamount')).">$y</a> ";
			}
		    }
                    //     echo "<a href=".$_SERVER['PHP_SELF']."?strana=$y&".tep_get_all_get_params(array('strana','clipsort','movesort')).">$y</a> ";
                    $y++;
                }
                echo "<hr>";
            }
             
        }
        $cntr = 1 + ($strana - 1 ) * $maxl;
        return $cntr;
    }
     
    function show_logo($fname = 'n/a', $fw = "100", $fh = "30" ) {
    //    $fname .= "?cfg_db_name=".rawurlencode(db_select());
	return "<table><tr><td><img width=\"".$fw."\" src=\"/estock3/estock2big.png\" alt=\"estock3\" onclick='document.location.replace(\"/estock3/\")'></td></tr><tr><td><small>".rawurlencode(db_select())."</small></td></tr></table>";
	/*
        return "
            <OBJECT classid=\"clsid:D27CDB6E-AE6D-11cf-96B8-444553540000\"
            codebase=\"https://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=5,0,0,0\"
            WIDTH=".$fw." HEIGHT=".$fh.">
            <PARAM NAME=movie VALUE=\"".$fname."\"> <PARAM NAME=quality VALUE=high> <PARAM NAME=bgcolor VALUE=#FFFFFF> <EMBED src=\"".$fname."\" quality=high bgcolor=#FFFFFF  WIDTH=100 HEIGHT=30 TYPE=\"application/x-shockwave-flash\" PLUGINSPAGE=\"https://www.macromedia.com/shockwave/download/index.cgi?P1_Prod_Version=ShockwaveFlash\"></EMBED>
            </OBJECT>";
	    */
    }
     
    function start_timer() {
        global $my_timer;
        $my_timer = microtime();
    }
     
    function show_timer() {
        global $my_timer;
        $koniec = microtime();
        $a = strval(substr($koniec, 11, strlen($koniec) ));
        $b = strval(substr($my_timer, 11, strlen($my_timer) ));
        $c = strval(substr($koniec, 0, 10 ));
        $d = strval(substr($my_timer, 0, 10 ));
        $e = round(($a + $c) - ($b + $d ), 5);
        echo "<a href=phpinfo.php>phpinfo</a>";
        echo "<br>"._("Time: ").$e." s<br>";
    }
     
     


/*
*
* Prepisanie ODBC funkcii na vseobecne
*/     



                                                                                                                                          
											  
											




    /*
    * len call bez vysledku, kvoli raiserror zaznamu
    */
    function db_call_exec($con, $query ) {
      
      switch ( DB_ENGINE ){
        case "SAODBC":         
            $res = odbc_exec($con, $query);
    	    return $res;
	    break;
	
	case "SASQL":
	    sasql_set_option($con, 'verbose_errors', false );
	    $stmt = sasql_prepare( $con, $query );
    	    if( ! $stmt ) {
		    echo sasql_error( $con );
		    return false;
	    }
	    if( sasql_stmt_execute( $stmt ) ) {
    		sasql_stmt_close( $stmt );
		return true;
    	    } else {                                                                                                                          
    		echo "<font color=red>PROBLEM:</font> Execute FAIL!\n";
	        echo sasql_error( $con );
	        return false;
	    }
	    break;
	}
    }
     


    function db_exec($con, $query ) {
      
      switch ( DB_ENGINE ){
        case "SAODBC":         
            $res = odbc_exec($con, $query);
    	    return $res;
	    break;
	
	case "SASQL":
	    $res = sasql_query( $con, $query );
	    if( ! $res ) {
        	$error_msg = sasql_error( $con );
        	echo "Query failed. Reason: $error_msg";
		return false;
	    } else {
		return $res;
	    }

//	    @oops( $query );
/*          $restm = sasql_prepare( $con, $query );
          $res = sasql_stmt_execute( $restm );
	  if( !res ) {
        	$error_msg = sasql_error( $con );
        	// return "Query failed. Reason: $error_msg";
		return false;
	    } else {
        	//$res = sasql_stmt_result_metadata( $restm );
		return $res;
	    }

*/
/*	    $res = sasql_query( $con, $query );
	    if( !res ) {
        	$error_msg = sasql_error( $con );
        	echo "Query failed. Reason: $error_msg";
		return false;
	    } else {
		return $res;
	    }
*/	    
	    break;
	}
    }
     

    function db_num_fields($sth) {
      
      switch ( DB_ENGINE ){
        case "SAODBC":         
            $res = odbc_num_fields($sth);
    	    return $res;
	    break;
	    
	case "SASQL":
	    $res = sasql_num_fields( $sth );
	    return $res;
	    break;
	}
    }
     
    function db_field_name($sth, $i) {
      
      switch ( DB_ENGINE ){
        case "SAODBC":         
            $res = odbc_field_name($sth, $i);
    	    return $res;
	    break;
	    
	case "SASQL":
	    $res = sasql_fetch_field( $sth, $i-1 );
	    return $res->name;
	    break;
	}
    }
     
    function db_fetch_into($sth, $i=0) {
      
      switch ( DB_ENGINE ){
        case "SAODBC":         
            $res = odbc_fetch_into($sth, $arr, $i);
    	    return $arr;
	    break;
	    
	case "SASQL":
	    $isyet = true;
	    if( $i > 0 ) {
		$isyet = sasql_data_seek( $sth, $i-1 );
		//$isyet = sasql_data_seek( $sth, $i-1 );
		//$isyet = sasql_stmt_data_seek( $sth, $i-1 );
	    }
	    if( $isyet ){
    		$res = sasql_fetch_row( $sth );
    		//$res = sasql_stmt_fetch( $sth );
		return $res;
	    } else {
		return false;
	    }
	    break;
	}
    }

    function db_num_rows($res ) {
        switch ( DB_ENGINE ){
    	    case "SAODBC":
    		return odbc_num_rows($res );
		break;	
	    case "SASQL":
                return sasql_num_rows( $res );
		break;	
    	} 
    }



    function db_fetch_array($sth, $rownr = -1 ) {
      
      switch ( DB_ENGINE ){
        case "SAODBC":         


        if (function_exists(odbc_fetch_array)){
	    if( $rovnr == -1 ) {
		return odbc_fetch_array( $sth );
	    } else {
		return odbc_fetch_array( $sth, $rownr );
	    }
	 } else {
     
    		if (PHP_VERSION > "4.1") {
    		    if ($rownumber < 0) {
            		odbc_fetch_into($result, $rs);
        	    } else {
            		odbc_fetch_into($result, $rs, $rownumber);
        	    }
    		} else {
        	    odbc_fetch_into($result, $rownumber, $rs);
    		}
    		foreach ($rs as $key => $value) {
        	    $rs_assoc[odbc_field_name($result, $key+1)] = $value;
    		}
    		return $rs_assoc;
	}
	break;
	
	case "SASQL":
	    $isyet = true;
	    if( $rownr > 0 )
		$isyet = sasql_data_seek( $sth, $rownr-1 );
	    if( $isyet ){
    		$res = sasql_fetch_array( $sth );
		return $res;
	    } else {
		return false;
	    }
	    break;
	}
    }


   function pdf_db_fetch_array( $sth, $rownr = -1 ) {
      switch ( DB_ENGINE ){
        case "SAODBC":         
	    return db_fetch_array($sth, $rownr );
	    break;
	case "SASQL":
//		return db_fetch_array( $sth, $rownr);
		if( $str = db_fetch_array($sth, $rownr ) ){
		    $ret = array();
		    foreach ( $str as $key=>$value ){
			$ret[$key] = iconv( "UTF-8", "WINDOWS-1250", $value );
		    }
//		    print_r( $ret );
		    return $ret;
		} else {
		    return false;
		}
	    break;
	}
    }


   function pdf_my_sybase_row( $con, $query ) {
      switch ( DB_ENGINE ){
        case "SAODBC":         
	    return my_sybase_row( $con, $query );
	    break;
	case "SASQL":
	    $ret =  my_sybase_row( $con, $query );
	    foreach ( $ret as $key=>$value ){
			$ret[$key] = iconv( "UTF-8", "WINDOWS-1250", $value );
		    }
	    return $ret;
	    break;
	}
    }


    function pdf_iconv( $a ) {	
	foreach ( $a as $key=>$value){
	    $a[$key] = iconv( "UTF-8","WINDOWS-1250", $value );
	}
	return $a;
    }


function csv_export( $query, $heading=1, $newline="unix", $showstr=0, $fnamestr='.csv' ){   
    global $con;
    $sth = db_exec($con, $query);
	$exptext = "";
    	for( $i=1; $i<=db_num_fields($sth); $i++) {
        	  $nm = db_field_name($sth,$i);
        	  $exptext .= ($i==1)?$nm:";".$nm;
	}
	$lines = 1;
	//Hack: nemame db_field_type
	$ftyp = "char";
	WHILE (($ref = db_fetch_into($sth, $lines) )  ) {
	    if( $lines == 1 && $heading <> 1 ) 	$exptext = ""; 
	    elseif ($newline <> "unix" )  $exptext .= ($lines==1 && $heading <> 1 )?"":"\r\n"; 
	    else  $exptext .= ($lines==1 && $heading <> 1 )?"":"\n";
	    for( $j=0; $j<($i-1); $j++) {
     		if( $ftyp == "char" || $ftyp == "varchar" || $ftyp  == "blob"  || $ftyp  == "date" ){
			$ktextu = "\"".$ref[$j]."\"";
		} else {
			$ktextu = $ref[$j];
		}
		$exptext .= ($j==0)?$ktextu:";".$ktextu;
	    }
	    $lines++;
	}

	if( $showstr == 2 ) { //Len cisty return
	    return $exptext;
	}
	

        $fs = fopen( PSRCATALOG."olstock/".$_SESSION['__auth']['userID']."_".getMstockValue('m_type_id')."_".$fnamestr, "w" );
	fwrite( $fs, $exptext );
	fclose( $fs );
	srand();
	echo "<table><tr><td>";
	echo _("Exported CSV File: ");
	echo "</td><td>";
	echo "<a href=".WWW_BASE."/psr/olstock/".$_SESSION['__auth']['userID']."_".getMstockValue('m_type_id')."_".$fnamestr."?r=".rand().">"._("Click Here"
)."</a>";
	echo "</td></tr></table>";
	if( $showstr == 1 ) echo "<pre>".$exptext."</pre>";

}


/*****
 * * If we need to compare dates in SQL format
*******/

function get_timestamp($date) {
    list($y, $m, $d) = explode('-', $date);
    return mktime(0, 0, 0, $m, $d, $y);
}

function is_skk_date($date){ //Ak datum je mensi ako prvy januar 2009...
        if( get_timestamp('2009-01-01')-get_timestamp($date) >0   )return true;
        else return false;
}



?>
