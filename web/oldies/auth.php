<?php
// Authentication functions.
// Standard system - modified horde.org IMP last version
//
/*
if( !getenv(HTTPS) ){
	echo "<html><body>SSL pages only...</body></html>";
	exit;
}
*/
    function checkAuth($userID)
    {
        if (isset($_SESSION['__auth'])) {
            if (!empty($_SESSION['__auth']['authenticated']) &&
                !empty($_SESSION['__auth']['userID']) &&
                $_SESSION['__auth']['userID'] == $userID) {
                return true;
            }
            
        }
     
        return false;
    }

    function setAuth($userID, $auth_type, $userPwd )
    {
	
        $GLOBALS['__auth'] = &$_SESSION['__auth'];
        $GLOBALS['__auth'] = array();
        $GLOBALS['__auth']['authenticated'] = true;
        $GLOBALS['__auth']['userID'] = $userID;
        $GLOBALS['__auth']['userPwd'] = $userPwd;
        $GLOBALS['__auth']['authType'] = $auth_type;
        $GLOBALS['__auth']['timestamp'] = time();

        if (!isset($_SESSION['__auth'])) {
            $_SESSION['__auth'] = $GLOBALS['__auth'];
        }
	
	// nedokoncene...
	//	$con = @db_connect();
	//$res=db_exec($con, "insert into user_login (id, pwd) values ('".$userID."','".$userPwd."')" );
	//echo $res;
        
        
    }
    
    /**
     * Clear any authentication tokens in the current session.
     */
    function clearAuth()
    {
    	if (isset($_SESSION['__auth'])) {
            $GLOBALS['__auth'] = &$_SESSION['__auth'];
            $GLOBALS['__auth'] = array();
            $GLOBALS['__auth']['authenticated'] = null;
        }
    }

    function Auth( $auth_type = "undef" ){

    	//MySQL authentifik. cast
	
	if( !checkAuth($_SESSION['__auth']['userID']) ){
		if( isset( $_POST['form_user_name'] ) && isset( $_POST['form_pwd'] ) ) {
			switch( $auth_type ){
				case "admin":
				case "mstock":
				case "shop":
					$map_connection = db_connect($GLOBALS['cfg_db_name'],$_POST['form_user_name'],$_POST['form_pwd']);
					if (!$map_connection) {
						$query = "";
					} else {
						$query = "select '".$_POST['form_user_name']."' as UNM from dummy";
					}
					break;
				default:
					$query = "";
			}
			$row = my_sybase_row ($map_connection, $query);
			if ( !$row ) {
				echo "SQL SERVER CONNECTION $map_connection PROBLEM, call administrator please, $query";
				oops( "Warning :".__FILE__." ".__LINE__." MysqlAuth: AuthHack: $auth_type uid: ".$GLOBALS['form_user_name']." pwd: ".$GLOBALS['form_pwd']." SQLError: ".mysql_error() );
				return false;
/*			}else {
				$ref = db_fetch_into($res, &$row);
				if ( !isset($ref) ) {
					oops( "Warning : ".__FILE__." ".__LINE__." AuthHack: $auth_type uid: ".$GLOBALS['form_user_name']." pwd: ".$GLOBALS['form_pwd'] );
					return false;
			        }
*/			   
			}
			switch( $row[0] ){
				case "gabor":
				echo "Your IP: ".$GLOBALS['REMOTE_ADDR'];
				  if( $GLOBALS['REMOTE_ADDR'] == "************" ){
					echo "Your IP: ".$GLOBALS['REMOTE_ADDR'];
					//mail( '<EMAIL>', 'test', 'xxx');
					//oops( "Warning : ".__FILE__." ".__LINE__." AuthHack: $auth_type uid: ".$GLOBALS['form_user_name']." pwd: ".$GLOBALS['form_pwd']." IP: ".$GLOBALS['REMOTE_ADDR'] );
					return false;
				  }
				  break;
			}
			setAuth($row[0], $auth_type, $_POST['form_pwd'] );
						
		} else {
			return false;
		}

	}
	return true;

    }

?>
