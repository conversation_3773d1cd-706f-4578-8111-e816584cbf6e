<?php
/* UNLOAD DIRECTORY */
$PATH_PREFIX = "/backup/";

$DPHHU="1.27";
$DPHSK="1.20";
$DPHCZ="1.19";
$DPHAT="1.20";

set_time_limit(600);
$maxlines=10;
require_once("./user_lib.php");
start_timer();

require_once( "./auth.php" );

session_name($cfg_session_name);
@session_start();


if($td == "logout") {

	session_destroy();
	clearAuth();
	header("Location: /");
	exit;
}

$SHOPADR = is_null($_GET['adr_id'])?3636:$_GET['adr_id'];
$SHOPREPLI = is_null($_GET['repli_id'])?0:$_GET['repli_id'];

if( isset($_GET['adr_id'])) {
	$SHOPADR = $_GET['adr_id'];
}
if( isset($_GET['repli'])) {
	$SHOPREPLI = $_GET['repli'];
}


/* Connect e-stock */

$con = db_connect("e-stock","onstockdummy","sdfEhwe234k");
IF (!$con) {
	echo "ERROR AUTH";
	exit;
}

/* Connect 2 */

$con2 = db_connect("wdl","onstockdummy","sdfEhwe234k");
IF (!$con2) {
	echo "ERROR AUTH";
	exit;
}



switch( $_GET['username'] ) {
	case "eurovea":
		$SHOPADR = "5039";
		break;
	case "theurovea":
		$SHOPADR = "5040";
		break;
	case 'aupark':
		$SHOPADR = "3624";
		break;
	case 'wowbb':
		$SHOPADR = "4413";
		break;
	case 'optima':
		$SHOPADR = "3731";
		break;
	case 'arena':
		$SHOPADR = "4585";
		$SHOPREPLI = "2";
		break;
	case "kriszta":
	case "davidsk":
	case "davidat":
	case "davidhu":
	case "gabor":
	case "gaborcz":
	case "gaborhu":
	case "gaborsk":
	case "zuzana":
	case "matuskova":
	case "ferkovake":
	case "zuhu":
	case "zafircz":
	case "zafirhu":
	case "zafirsk":
	case "matejek":
	case "janos":
	case "dba":
	case "orosz":
	case "wowbb":

	case "westwatch";
	case "thshop";
	case "shop";

	break;
	default:		
	echo "<html><body>"; 		 
	echo "You need special rights to access this page. Ask CEO...";
	echo "</body></html>";
	exit;
}

$whichcon = strtolower($_GET['username']) == 'shop' ? $con2: $con;
//$whichcon = $con;

#Prepocet od 2009 v SK prevadzkach
$EUR_KURZ="1";

//Ak nieco navyse co nie je v selecte
function additional_business_logic( $typeofsel = '' ){
	global $arr; //Tu mame uz vyratane pole zo selectu
	$fromdate = mktime( 0, 0, 0, substr($_POST['dfrom'],5,2), substr($_POST['dfrom'],8,2), substr($_POST['dfrom'],0,4) );
	$todate = mktime( 0, 0, 0, substr($_POST['dto'],5,2), substr($_POST['dto'],8,2), substr($_POST['dto'],0,4) );
	
	while (list($key, $value) = each($arr)) {

	    	if ( $typeofsel == 'months' ){//1% robime po mesiacoch
	    		$nasobok = $arr[$key][0];
	    	} else if ( $typeofsel == 'days' ){//1% robime po dnoch priblizne teda 30 dnovy mesiac
	    		$nasobok = round($arr[$key][0]/30,1);
	    	} else {
	    		$nasobok = 1;
	    	}


     	  	switch ($value[1]) { //adr_id, podla nej definujeme "note"
     	  		case 4172: //BENIX s.r.o. BRATISLAVA
//     	  			$arr[$key][7] = 'costs are minimum 60000 per month or turnover*0.2. Add also 50000 other costs. (here only fixed 110000 implemented)';
     	  		$arr[$key][7] = 'approx. 2006 costs';
     	  		break;
     	  		case 4413: // RACIO BB
//     	  			$arr[$key][7] = 'Shop opened since Dec 2006, please use costs only from this date.';
//     	  			$arr[$key][7] = 'approx. 2006 costs';
//     	  			$arr[$key][7] = '6thMay08: costs set from 4315 eur  to 5463 eur';
     	  		$arr[$key][8] = "1584.20"*$nasobok;
     	  		$arr[$key][9] = $arr[$key][6]-$arr[$key][8];
     	  		break;
     	  		case 3128: //ZLATNÍCTVO LON BRATISLAVA
     	  		$arr[$key][7] = 'costs are turnover*0.2. Add 1660 eur as other costs. (here only fixe 3651 eur  implemented)';
     	  		break;
     	  		case 3731: //Optima kosice od 15.5.2006 ma o 50000 menej.
//     	  			if( $todate > mktime (0,0,0,5,15,2006) ) {
//     	  				//date ("M-d-Y", $fromdate )." ".date ("M-d-Y", $todate );
//    	  				$arr[$key][7] = "since 15.5.2006 costs are 50000 lower per month. Please recount manualy.";
//    	  			}
//     	  			$arr[$key][7] = 'approx. 2006 costs';
     	  		$arr[$key][8] = "1510.39"*$nasobok;
     	  		$arr[$key][9] = $arr[$key][6]-$arr[$key][8];
     	  		break;
     	  		case 5039: // WOW EUROVEA
     	  		$arr[$key][8] = "3809.34"*$nasobok;
     	  		$arr[$key][9] = $arr[$key][6]-$arr[$key][8];
     	  		break;
     	  		case 3624: // WDL AUPARK
     	  		$arr[$key][8] = "3478.35"*$nasobok;
     	  		$arr[$key][9] = $arr[$key][6]-$arr[$key][8];
     	  		break;
     	  		case 3511: // WOW POLUS
     	  		$arr[$key][8] = "599.11"*$nasobok;
     	  		$arr[$key][9] = $arr[$key][6]-$arr[$key][8];
     	  		break;
     	  		case 3512: // WDL POLUS
     	  		$arr[$key][8] = "1149.92"*$nasobok;
     	  		$arr[$key][9] = $arr[$key][6]-$arr[$key][8];
     	  		break;
     	  		case 4370: // WOW AUPARK
     	  		$arr[$key][8] = "472.44"*$nasobok;
     	  		$arr[$key][9] = $arr[$key][6]-$arr[$key][8];
     	  		break;
     	  		case 5040: // TH BUTIK EUROVEA
     	  		$arr[$key][8] = "882.86"*$nasobok;
     	  		$arr[$key][9] = $arr[$key][6]-$arr[$key][8];
     	  		break;
			case 3450:	//G-WATCH-Ostrava zlava 25% v lete
			if( $todate > mktime (0,0,0,7,1,2006) and $fromdate < mktime (0,0,0,9,1,2006) ) {
				$arr[$key][7] = "For Juni and July is in Ostrava discount 25% for rent - it´s 14250,- CZK. Please recount manualy.";
			}
			break;
			case 4312:	//G-WATCH-Flora Praha ma 150000 ale po 1.6.2006 zvysene o 50000!
			if( $fromdate < mktime (0,0,0,1,1,2011) ) {
				$arr[$key][7] = "costs: 287000 (set 01.01.2011. Previous years recount manualy).";
			}
			$arr[$key][8] = "77180"*$nasobok;
			$arr[$key][9] = $arr[$key][6]-$arr[$key][8];
			break;
			case 4406:	//G-WATCH-LASO ostrava
			if( $fromdate < mktime (0,0,0,1,1,2011) ) {
				$arr[$key][7] = "Costs set to 186000/month since 01.01.2011. Previous years please recount manualy.";
			}
			$arr[$key][8] = "35360"*$nasobok;
			$arr[$key][9] = $arr[$key][6]-$arr[$key][8];
			break;

			case 4310:	//vankovka brno
			if( $fromdate < mktime (0,0,0,1,1,2011) ) {
				$arr[$key][7] = "Costs set to 262000/month since 01.01.2011. Previous years please recount manualy.";
			}
			$arr[$key][8] = "28120"*$nasobok;
			$arr[$key][9] = $arr[$key][6]-$arr[$key][8];

			break;
			case 3488:	//Nyíregyháza 
			$arr[$key][8] = "330000"*$nasobok;
			$arr[$key][9] = $arr[$key][6]-$arr[$key][8];
			break;
			case 4727:	//th hu
			$arr[$key][8] = "550000"*$nasobok;
			$arr[$key][9] = $arr[$key][6]-$arr[$key][8];
			break;
			case 4585:	//watch arena
			$arr[$key][8] = "1200000"*$nasobok;
			$arr[$key][9] = $arr[$key][6]-$arr[$key][8];
			break;
			case 6118:	//west-watch
			$arr[$key][8] = "800000"*$nasobok;
			$arr[$key][9] = $arr[$key][6]-$arr[$key][8];
			break;

			case 6234:	//eshop wdl.sk
			$arr[$key][7] = 'eshop wdl.sk. Costs not set yet.';
			break;

			default:
			break;
		}     	  	


	}

}


//function export_csv($sth, $fname, $alsoshow ){
function export_csv($arr, $my_fields, $fname, $alsoshow){

	$exptext = "";
	
		//hlavicka
	foreach( $my_fields as $key=>$value ){
		$exptext .= $value.";";
	}
		//detail
	while (list($key, $value) = each($arr)) {
		$exptext .= "\n";
		for( $counter = 0; $counter<sizeof($value); $counter++) {
			if( intval( $value[$counter] ) != 0  ){
				$ktextu = $value[$counter];
			} else {
				$ktextu = "\"".$value[$counter]."\"";
			}
			$exptext .= ($counter==0)?$ktextu:";".$ktextu;
		}

		$exptext .=$value[$counter].";";
	}



//	$fs = fopen( PSRCATALOG."olstock/".$_SESSION['__auth']['userID']."$fname.csv", "w" );
//	fwrite( $fs, $exptext );
//	fclose( $fs );
//	echo "<tr bgcolor=lightyellow><td><small><a href=".WWW_BASE."/psr/olstock/".$_SESSION['__auth']['userID']."$fname.csv>"._("Exported CSV File: ")."</a></small></TD></tr>";
//	if( $alsoshow == 'yes' ){
//		
//
//		<script language="JavaScript">
//			<!--
//			previewWin = window.open(' WWW_BASE.'/psr/olstock/'.$_SESSION['__auth']['userID'].'$fname.csv' ', '', 'width=780,height=580,scrollbars=yes,resizable=yes,titlebar=0,top=' + ((screen.availHeight-580)/2) + ',left=' + ((screen.availWidth-780)/2));
//	//-->
//</script>
//						
//}
}


function show_results($arr, $my_fields, $font_size=100, $my_sizes=array() ){


	echo "<table border=0>";
	$ct=0;
		//hlavicka
	echo "<tr bgcolor=lightblue>";
	foreach( $my_fields as $key=>$value )
		echo "<td>$value</td>";
	echo "</tr>";
		//detail
	while (list($key, $value) = each($arr)) {
		$ct++;
		echo "<tr bgcolor=";
		if($ct%2) echo "#E7DFE7>"; else echo "#FFFFFF>";
		for( $counter = 0; $counter<sizeof($value); $counter++){
			if( isset($my_sizes[$counter]) ) $font_size = $my_sizes[$counter];
			echo "<td style=\"font-size:$font_size%\">".$value[$counter]."</td>";
		}
		echo "</tr>";
	}
	echo "</table><br><br>";
}


//AKCIE
?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
	"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
	<title><b>Turnover statistics</b></title>
	<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-2">
</head>

<body>

<h4>For shop adr <?php echo $SHOPADR." ".$SHOPREPLI; ?></h4>
	<h2>Today</h2>

	<?php

	// Statisticke vysledky 
	echo "<table><tr bgcolor=yellow><td>".$_POST['db']."</TD></tr>";
	echo "<tr><td>";
	$mycommand =	"
	select adr_id,m_type_id,(select name from m_type where m_type_id=m.m_type_id) movement, sum(total) total, max(d1) from movement m where d1 = date(now()) and m.adr_id=".$SHOPADR." and m.adr_repli_id = ".$SHOPREPLI."
		and m_type_id in ( 87,88,97,101, 90,91,98,100, 30,31,99,106, 130,145,132,131,146,133 )
		 group by m.adr_id, m.adr_repli_id, m.m_type_id";

	$sth = db_exec($whichcon, $mycommand );
		if( $sth ){ //mame vysledok
			$arr = my_sybase_rows(	$whichcon, $mycommand );
			$my_fields = array( "adr_id","m_type_id","typ","turnover","last date" );
			additional_business_logic("days" );
		//	export_csv( $arr, $my_fields, "stat_main1", $alsoshow );
			show_results( $arr, $my_fields );
		}

		echo "</td></tr>";
		echo "</table>";

		echo "<table><tr bgcolor=yellow><td>".$_POST['db']."</TD></tr>";
		echo "<tr><td>";

		$mycommand =	"	sELECT kod, model, sum(pcs), md.price as selprice, sum(md.price*pcs-md.price * pcs * md.discount/100 ) as seltotal from m_detail md, movement m, product p where md.move_id = m.move_id and md.m_repli_id =m.repli_id and md.mat_id = p.mat_id and d1 = date(now()) and 
		m.adr_id=".$SHOPADR." and m.adr_repli_id = ".$SHOPREPLI." 
		and m_type_id in ( 87,88, 90,91, 30,31, 130,145,132 )
		group by kod, model, md.price, m.m_type_id, md.tax, p.price order by kod asc, model asc";

		$sth = db_exec($whichcon, $mycommand );
		if( $sth ){ //mame vysledok
			$arr = my_sybase_rows(	$whichcon, $mycommand );
			$my_fields = array( "kod","model","sum pcs","sel price","sel total netto" );
			additional_business_logic("days" );
		//	export_csv( $arr, $my_fields, "stat_main1", $alsoshow );
			show_results( $arr, $my_fields );
		}
		echo "</td></tr>";
		echo "</table>";


	echo "<h2>This month</h2>";



	// Statisticke vysledky 
	echo "<table><tr bgcolor=yellow><td>".$_POST['db']."</TD></tr>";
	echo "<tr><td>";
	$mycommand =	"
	select adr_id,m_type_id,(select name from m_type where m_type_id=m.m_type_id) movement, sum(total) total, max(d1) from movement m where year(d1) = year(now()) and month(d1) = month(now()) and m.adr_id=".$SHOPADR." and m.adr_repli_id = ".$SHOPREPLI."
		and m_type_id in ( 87,88,97,101, 90,91,98,100, 30,31,99,106, 130,145,132,131,146,133 )
		 group by m.adr_id, m.adr_repli_id, m.m_type_id";

	$sth = db_exec($whichcon, $mycommand );
		if( $sth ){ //mame vysledok
			$arr = my_sybase_rows(	$whichcon, $mycommand );
			$my_fields = array( "adr_id","m_type_id","typ","turnover","last date" );
			additional_business_logic("days" );
		//	export_csv( $arr, $my_fields, "stat_main1", $alsoshow );
			show_results( $arr, $my_fields );
		}

		echo "</td></tr>";
		echo "</table>";

		echo "<table><tr bgcolor=yellow><td>".$_POST['db']."</TD></tr>";
		echo "<tr><td>";

		$mycommand =	"	sELECT kod, model, sum(pcs), md.price as selprice, sum(md.price*pcs-md.price * pcs * md.discount/100 ) as seltotal from m_detail md, movement m, product p where md.move_id = m.move_id and md.m_repli_id =m.repli_id and md.mat_id = p.mat_id and year(d1) = year(now()) and month(d1) = month(now()) and 
		m.adr_id=".$SHOPADR." and m.adr_repli_id = ".$SHOPREPLI." 
		and m_type_id in ( 87,88, 90,91, 30,31, 130,145,132 )
		group by kod, model, md.price, m.m_type_id, md.tax, p.price order by kod asc, model asc";

		$sth = db_exec($whichcon, $mycommand );
		if( $sth ){ //mame vysledok
			$arr = my_sybase_rows(	$whichcon, $mycommand );
			$my_fields = array( "kod","model","sum pcs","sel price","sel total netto" );
			additional_business_logic("days" );
		//	export_csv( $arr, $my_fields, "stat_main1", $alsoshow );
			show_results( $arr, $my_fields );
		}
		echo "</td></tr>";
		echo "</table>";


		echo "<h2>Last year same month</h2>";




	// Statisticke vysledky 
	echo "<table><tr bgcolor=yellow><td>".$_POST['db']."</TD></tr>";
	echo "<tr><td>";
	$mycommand =	"
	select adr_id,m_type_id,(select name from m_type where m_type_id=m.m_type_id) movement, sum(total) total, max(d1) from movement m where year(d1) = year(now()-365) and month(d1) = month(now()-365) and m.adr_id=".$SHOPADR." and m.adr_repli_id = ".$SHOPREPLI." 
		and m_type_id in ( 87,88,97,101, 90,91,98,100, 30,31,99,106, 130,145,132,131,146,133 )
		group by m.adr_id, m.adr_repli_id, m.m_type_id";

	$sth = db_exec($whichcon, $mycommand );
		if( $sth ){ //mame vysledok
			$arr = my_sybase_rows(	$whichcon, $mycommand );
			$my_fields = array( "adr_id","m_type_id","typ","turnover","last date" );
			additional_business_logic("days" );
		//	export_csv( $arr, $my_fields, "stat_main1", $alsoshow );
			show_results( $arr, $my_fields );
		}

		echo "</td></tr>";
		echo "</table>";

		echo "<table><tr bgcolor=yellow><td>".$_POST['db']."</TD></tr>";
		echo "<tr><td>";

		$mycommand =	"sELECT kod, model, sum(pcs), md.price as selprice, sum(md.price*pcs-md.price * pcs * md.discount/100 ) as seltotal from m_detail md, movement m, product p where md.move_id = m.move_id and md.m_repli_id =m.repli_id and md.mat_id = p.mat_id and year(d1) = year(now()-365) and month(d1) = month(now()-365) and 
		m.adr_id=".$SHOPADR." and m.adr_repli_id = ".$SHOPREPLI." 
		and m_type_id in ( 87,88, 90,91, 30,31, 130,145,132 )
		group by kod, model, md.price, m.m_type_id, md.tax, p.price order by kod asc, model asc";
//echo $mycommand;
		$sth = db_exec($whichcon, $mycommand );
		if( $sth ){ //mame vysledok
			$arr = my_sybase_rows(	$whichcon, $mycommand );
			$my_fields = array( "kod","model","sum pcs","sel price","sel total netto" );
			additional_business_logic("days" );
		//	export_csv( $arr, $my_fields, "stat_main1", $alsoshow );
			show_results( $arr, $my_fields );
		}
		echo "</td></tr>";
		echo "</table>";



		echo "</body></html>";
?>
