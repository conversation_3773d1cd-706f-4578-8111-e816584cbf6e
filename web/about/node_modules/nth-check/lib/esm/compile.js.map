{"version": 3, "file": "compile.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/nth-check/639fd2a4000b69f82350aad8c34cb43f77e483ba/src/", "sources": ["compile.ts"], "names": [], "mappings": "AAAA,OAAO,QAAQ,MAAM,UAAU,CAAC;AAEhC;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAM,UAAU,OAAO,CACnB,MAA8B;IAE9B,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACpB,6DAA6D;IAC7D,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAExB;;;;;;OAMG;IACH,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;QAAE,OAAO,QAAQ,CAAC,SAAS,CAAC;IAE/C,mFAAmF;IACnF,IAAI,CAAC,KAAK,CAAC,CAAC;QAAE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,IAAI,CAAC,CAAC;IAC3C,IAAI,CAAC,KAAK,CAAC;QAAE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC;IAC3C,uDAAuD;IACvD,IAAI,CAAC,KAAK,CAAC;QAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,IAAI,CAAC,CAAC;IAEtE;;;;OAIG;IACH,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACzB,0CAA0C;IAC1C,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;IAExC,OAAO,CAAC,GAAG,CAAC;QACR,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,KAAK,IAAI;QAChD,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,KAAK,IAAI,CAAC;AACzD,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+BG;AACH,MAAM,UAAU,QAAQ,CAAC,MAA8B;IACnD,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACpB,6DAA6D;IAC7D,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAEtB,IAAI,CAAC,GAAG,CAAC,CAAC;IAEV,oDAAoD;IACpD,IAAI,CAAC,GAAG,CAAC,EAAE;QACP,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC;QAChB,gBAAgB;QAChB,MAAM,QAAQ,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;QAC5C,OAAO,GAAG,EAAE;YACR,MAAM,GAAG,GAAG,QAAQ,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC;YAElC,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;QAChC,CAAC,CAAC;KACL;IAED,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,GAAG,CAAC;YACR,CAAC,CAAC,6CAA6C;gBAC7C,GAAG,EAAE,CAAC,IAAI;YACZ,CAAC,CAAC,0BAA0B;gBAC1B,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAEvC,IAAI,CAAC,GAAG,CAAC,EAAE;QACP,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KAC9B;IAED,OAAO,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;AAC7B,CAAC"}