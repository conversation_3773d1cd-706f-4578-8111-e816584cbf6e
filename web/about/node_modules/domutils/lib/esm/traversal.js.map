{"version": 3, "file": "traversal.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/domutils/0ab8bcf1ecfc70dfc93291a4cb2496578ac25e9c/src/", "sources": ["traversal.ts"], "names": [], "mappings": "AAAA,OAAO,EACH,KAAK,EAKL,WAAW,GACd,MAAM,YAAY,CAAC;AAEpB;;;;;;GAMG;AACH,MAAM,UAAU,WAAW,CAAC,IAAa;IACrC,OAAO,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;AAClD,CAAC;AAGD;;;;;;GAMG;AACH,MAAM,UAAU,SAAS,CAAC,IAAa;IACnC,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC;AAC/B,CAAC;AAED;;;;;;;;;;GAUG;AACH,MAAM,UAAU,WAAW,CAAC,IAAa;IACrC,MAAM,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;IAC/B,IAAI,MAAM,IAAI,IAAI;QAAE,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC;IAE/C,MAAM,QAAQ,GAAG,CAAC,IAAI,CAAC,CAAC;IACxB,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;IAC1B,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC;QAClB,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACvB,CAAC,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;IACtB,CAAC;IACD,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC;QAClB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpB,CAAC,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;IACtB,CAAC;IACD,OAAO,QAAQ,CAAC;AACpB,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,iBAAiB,CAC7B,IAAa,EACb,IAAY;;IAEZ,OAAO,MAAA,IAAI,CAAC,OAAO,0CAAG,IAAI,CAAC,CAAC;AAChC,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,SAAS,CAAC,IAAa,EAAE,IAAY;IACjD,OAAO,CACH,IAAI,CAAC,OAAO,IAAI,IAAI;QACpB,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;QACxD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAC7B,CAAC;AACN,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,OAAO,CAAC,IAAa;IACjC,OAAO,IAAI,CAAC,IAAI,CAAC;AACrB,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,kBAAkB,CAAC,IAAa;IAC5C,IAAI,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;IACpB,OAAO,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QAAE,CAAC,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;IACxD,OAAO,IAAI,CAAC;AAChB,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,kBAAkB,CAAC,IAAa;IAC5C,IAAI,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;IACpB,OAAO,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QAAE,CAAC,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;IACxD,OAAO,IAAI,CAAC;AAChB,CAAC"}