/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"/Users/<USER>/Projects/pgestock/web/about/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Projects/pgestock/web/about/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Projects/pgestock/web/about/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbWlsb3N3aWthcnNraSUyRlByb2plY3RzJTJGcGdlc3RvY2slMkZ3ZWIlMkZhYm91dCUyRmFwcCUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3SUFBa0ciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93c3RvY2stbGFuZGluZy8/Nzk4MiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9taWxvc3dpa2Fyc2tpL1Byb2plY3RzL3BnZXN0b2NrL3dlYi9hYm91dC9hcHAvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Header */ \"(ssr)/./components/Header.tsx\");\n/* harmony import */ var _components_Hero__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Hero */ \"(ssr)/./components/Hero.tsx\");\n/* harmony import */ var _components_Features__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Features */ \"(ssr)/./components/Features.tsx\");\n/* harmony import */ var _components_TechStack__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/TechStack */ \"(ssr)/./components/TechStack.tsx\");\n/* harmony import */ var _components_UseCases__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/UseCases */ \"(ssr)/./components/UseCases.tsx\");\n/* harmony import */ var _components_Security__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Security */ \"(ssr)/./components/Security.tsx\");\n/* harmony import */ var _components_Pricing__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/Pricing */ \"(ssr)/./components/Pricing.tsx\");\n/* harmony import */ var _components_Contact__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/Contact */ \"(ssr)/./components/Contact.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/Footer */ \"(ssr)/./components/Footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/pgestock/web/about/app/page.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Hero__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/pgestock/web/about/app/page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Features__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/pgestock/web/about/app/page.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TechStack__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/pgestock/web/about/app/page.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UseCases__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/pgestock/web/about/app/page.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Security__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/pgestock/web/about/app/page.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Pricing__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/pgestock/web/about/app/page.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Contact__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/pgestock/web/about/app/page.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/pgestock/web/about/app/page.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/pgestock/web/about/app/page.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQW1Cd0M7QUFDSjtBQUNRO0FBQ0U7QUFDRjtBQUNBO0FBQ0Y7QUFDQTtBQUNGO0FBRXpCLFNBQVNTO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFLQyxXQUFVOzswQkFDZCw4REFBQ1gsMERBQU1BOzs7OzswQkFDUCw4REFBQ0Msd0RBQUlBOzs7OzswQkFDTCw4REFBQ0MsNERBQVFBOzs7OzswQkFDVCw4REFBQ0MsNkRBQVNBOzs7OzswQkFDViw4REFBQ0MsNERBQVFBOzs7OzswQkFDVCw4REFBQ0MsNERBQVFBOzs7OzswQkFDVCw4REFBQ0MsMkRBQU9BOzs7OzswQkFDUiw4REFBQ0MsMkRBQU9BOzs7OzswQkFDUiw4REFBQ0MsMERBQU1BOzs7Ozs7Ozs7OztBQUdiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd3N0b2NrLWxhbmRpbmcvLi9hcHAvcGFnZS50c3g/NzYwMyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgbW90aW9uIH0gZnJvbSAnZnJhbWVyLW1vdGlvbidcbmltcG9ydCB7IFxuICBQYWNrYWdlLCBcbiAgQmFyQ2hhcnQzLCBcbiAgR2xvYmUsIFxuICBTaGllbGQsIFxuICBaYXAsIFxuICBVc2VycywgXG4gIEFycm93UmlnaHQsXG4gIENoZWNrQ2lyY2xlLFxuICBTdGFyLFxuICBUcmVuZGluZ1VwLFxuICBEYXRhYmFzZSxcbiAgQ2xvdWQsXG4gIExvY2ssXG4gIFNtYXJ0cGhvbmVcbn0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuaW1wb3J0IEhlYWRlciBmcm9tICdAL2NvbXBvbmVudHMvSGVhZGVyJ1xuaW1wb3J0IEhlcm8gZnJvbSAnQC9jb21wb25lbnRzL0hlcm8nXG5pbXBvcnQgRmVhdHVyZXMgZnJvbSAnQC9jb21wb25lbnRzL0ZlYXR1cmVzJ1xuaW1wb3J0IFRlY2hTdGFjayBmcm9tICdAL2NvbXBvbmVudHMvVGVjaFN0YWNrJ1xuaW1wb3J0IFVzZUNhc2VzIGZyb20gJ0AvY29tcG9uZW50cy9Vc2VDYXNlcydcbmltcG9ydCBTZWN1cml0eSBmcm9tICdAL2NvbXBvbmVudHMvU2VjdXJpdHknXG5pbXBvcnQgUHJpY2luZyBmcm9tICdAL2NvbXBvbmVudHMvUHJpY2luZydcbmltcG9ydCBDb250YWN0IGZyb20gJ0AvY29tcG9uZW50cy9Db250YWN0J1xuaW1wb3J0IEZvb3RlciBmcm9tICdAL2NvbXBvbmVudHMvRm9vdGVyJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lKCkge1xuICByZXR1cm4gKFxuICAgIDxtYWluIGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy13aGl0ZVwiPlxuICAgICAgPEhlYWRlciAvPlxuICAgICAgPEhlcm8gLz5cbiAgICAgIDxGZWF0dXJlcyAvPlxuICAgICAgPFRlY2hTdGFjayAvPlxuICAgICAgPFVzZUNhc2VzIC8+XG4gICAgICA8U2VjdXJpdHkgLz5cbiAgICAgIDxQcmljaW5nIC8+XG4gICAgICA8Q29udGFjdCAvPlxuICAgICAgPEZvb3RlciAvPlxuICAgIDwvbWFpbj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkhlYWRlciIsIkhlcm8iLCJGZWF0dXJlcyIsIlRlY2hTdGFjayIsIlVzZUNhc2VzIiwiU2VjdXJpdHkiLCJQcmljaW5nIiwiQ29udGFjdCIsIkZvb3RlciIsIkhvbWUiLCJtYWluIiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Contact.tsx":
/*!********************************!*\
  !*** ./components/Contact.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Contact)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hook-form */ \"(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(ssr)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zod */ \"(ssr)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Mail_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Mail,Send,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Mail_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Mail,Send,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Mail_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Mail,Send,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Mail_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Mail,Send,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Mail_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Mail,Send,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst contactSchema = zod__WEBPACK_IMPORTED_MODULE_3__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().min(2, \"Name must be at least 2 characters\"),\n    email: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().email(\"Please enter a valid email address\"),\n    company: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().min(2, \"Company name must be at least 2 characters\"),\n    phone: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().optional(),\n    message: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().min(10, \"Message must be at least 10 characters\"),\n    inquiryType: zod__WEBPACK_IMPORTED_MODULE_3__.z[\"enum\"]([\n        \"trial\",\n        \"demo\",\n        \"pricing\",\n        \"support\",\n        \"other\"\n    ])\n});\nfunction Contact() {\n    const [isSubmitted, setIsSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { register, handleSubmit, formState: { errors }, reset } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_4__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(contactSchema)\n    });\n    const onSubmit = async (data)=>{\n        setIsSubmitting(true);\n        // Simulate form submission\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        // In a real implementation, you would send <NAME_EMAIL>\n        console.log(\"Form submitted:\", data);\n        setIsSubmitted(true);\n        setIsSubmitting(false);\n        reset();\n    };\n    const contactInfo = [\n        {\n            icon: _barrel_optimize_names_CheckCircle_Clock_Mail_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: \"Email Us\",\n            content: \"<EMAIL>\",\n            description: \"Get in touch for trials and inquiries\"\n        },\n        {\n            icon: _barrel_optimize_names_CheckCircle_Clock_Mail_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            title: \"Response Time\",\n            content: \"< 24 hours\",\n            description: \"We respond to all inquiries quickly\"\n        },\n        {\n            icon: _barrel_optimize_names_CheckCircle_Clock_Mail_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            title: \"Expert Support\",\n            content: \"Dedicated Team\",\n            description: \"Our experts are here to help you succeed\"\n        }\n    ];\n    const inquiryTypes = [\n        {\n            value: \"trial\",\n            label: \"Start Free Trial\"\n        },\n        {\n            value: \"demo\",\n            label: \"Schedule Demo\"\n        },\n        {\n            value: \"pricing\",\n            label: \"Pricing Information\"\n        },\n        {\n            value: \"support\",\n            label: \"Technical Support\"\n        },\n        {\n            value: \"other\",\n            label: \"Other Inquiry\"\n        }\n    ];\n    if (isSubmitted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            id: \"contact\",\n            className: \"py-20 bg-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        scale: 0.8\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1\n                    },\n                    className: \"max-w-2xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-green-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Mail_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-10 w-10 text-green-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-4\",\n                            children: \"Thank You!\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 mb-8\",\n                            children: \"We have received your inquiry and will get back to you within 24 hours. Our team is excited to help you transform your inventory management!\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                            onClick: ()=>setIsSubmitted(false),\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            className: \"px-6 py-3 bg-wstock-green text-white rounded-lg hover:bg-green-600 transition-colors duration-200\",\n                            children: \"Send Another Message\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                lineNumber: 89,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n            lineNumber: 88,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"contact\",\n        className: \"py-20 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl sm:text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Get Started Today\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                            children: \"Ready to revolutionize your inventory management? Contact us for a free trial or to learn more about how WSTOCK can transform your business.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            transition: {\n                                duration: 0.6\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-8\",\n                                    children: \"Let's Talk About Your Needs\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6 mb-8\",\n                                    children: contactInfo.map((info, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: index * 0.1\n                                            },\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-wstock-green w-12 h-12 rounded-lg flex items-center justify-center mr-4 flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(info.icon, {\n                                                        className: \"h-6 w-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-lg font-semibold text-gray-900 mb-1\",\n                                                            children: info.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                            lineNumber: 165,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-wstock-green font-medium mb-1\",\n                                                            children: info.content\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                            lineNumber: 168,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 text-sm\",\n                                                            children: info.description\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.3\n                                    },\n                                    className: \"bg-gray-50 rounded-xl p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                            children: \"Why Choose WSTOCK?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Mail_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-5 w-5 text-green-500 mr-3 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"30-day free trial with full features\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Mail_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-5 w-5 text-green-500 mr-3 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"No setup fees or hidden costs\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Mail_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-5 w-5 text-green-500 mr-3 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Expert onboarding and support\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Mail_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-5 w-5 text-green-500 mr-3 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Scalable solution that grows with you\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.2\n                            },\n                            className: \"bg-gray-50 rounded-2xl p-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit(onSubmit),\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Full Name *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        ...register(\"name\"),\n                                                        type: \"text\",\n                                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-wstock-green focus:border-transparent transition-colors duration-200\",\n                                                        placeholder: \"Your full name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-500 text-sm mt-1\",\n                                                        children: errors.name.message\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Email Address *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        ...register(\"email\"),\n                                                        type: \"email\",\n                                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-wstock-green focus:border-transparent transition-colors duration-200\",\n                                                        placeholder: \"<EMAIL>\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-500 text-sm mt-1\",\n                                                        children: errors.email.message\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Company Name *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        ...register(\"company\"),\n                                                        type: \"text\",\n                                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-wstock-green focus:border-transparent transition-colors duration-200\",\n                                                        placeholder: \"Your company\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.company && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-500 text-sm mt-1\",\n                                                        children: errors.company.message\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Phone Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        ...register(\"phone\"),\n                                                        type: \"tel\",\n                                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-wstock-green focus:border-transparent transition-colors duration-200\",\n                                                        placeholder: \"+****************\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Inquiry Type *\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                ...register(\"inquiryType\"),\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-wstock-green focus:border-transparent transition-colors duration-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Select inquiry type\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    inquiryTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: type.value,\n                                                            children: type.label\n                                                        }, type.value, false, {\n                                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.inquiryType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm mt-1\",\n                                                children: errors.inquiryType.message\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Message *\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                ...register(\"message\"),\n                                                rows: 4,\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-wstock-green focus:border-transparent transition-colors duration-200 resize-none\",\n                                                placeholder: \"Tell us about your inventory management needs...\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm mt-1\",\n                                                children: errors.message.message\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        className: \"w-full bg-wstock-green text-white py-4 px-6 rounded-lg font-semibold hover:bg-green-600 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center\",\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Sending...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                \"Send Message\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Mail_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"ml-2 h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n            lineNumber: 121,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./components/Contact.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Features.tsx":
/*!*********************************!*\
  !*** ./components/Features.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Features)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Database_FileText_Globe_Package_Settings_ShoppingCart_Smartphone_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Database,FileText,Globe,Package,Settings,ShoppingCart,Smartphone,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Database_FileText_Globe_Package_Settings_ShoppingCart_Smartphone_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Database,FileText,Globe,Package,Settings,ShoppingCart,Smartphone,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Database_FileText_Globe_Package_Settings_ShoppingCart_Smartphone_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Database,FileText,Globe,Package,Settings,ShoppingCart,Smartphone,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Database_FileText_Globe_Package_Settings_ShoppingCart_Smartphone_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Database,FileText,Globe,Package,Settings,ShoppingCart,Smartphone,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Database_FileText_Globe_Package_Settings_ShoppingCart_Smartphone_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Database,FileText,Globe,Package,Settings,ShoppingCart,Smartphone,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Database_FileText_Globe_Package_Settings_ShoppingCart_Smartphone_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Database,FileText,Globe,Package,Settings,ShoppingCart,Smartphone,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Database_FileText_Globe_Package_Settings_ShoppingCart_Smartphone_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Database,FileText,Globe,Package,Settings,ShoppingCart,Smartphone,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Database_FileText_Globe_Package_Settings_ShoppingCart_Smartphone_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Database,FileText,Globe,Package,Settings,ShoppingCart,Smartphone,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Database_FileText_Globe_Package_Settings_ShoppingCart_Smartphone_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Database,FileText,Globe,Package,Settings,ShoppingCart,Smartphone,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Database_FileText_Globe_Package_Settings_ShoppingCart_Smartphone_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Database,FileText,Globe,Package,Settings,ShoppingCart,Smartphone,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Database_FileText_Globe_Package_Settings_ShoppingCart_Smartphone_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Database,FileText,Globe,Package,Settings,ShoppingCart,Smartphone,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Database_FileText_Globe_Package_Settings_ShoppingCart_Smartphone_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Database,FileText,Globe,Package,Settings,ShoppingCart,Smartphone,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Features() {\n    const features = [\n        {\n            icon: _barrel_optimize_names_BarChart3_Clock_Database_FileText_Globe_Package_Settings_ShoppingCart_Smartphone_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            title: \"Multi-Location Inventory\",\n            description: \"Track stock across multiple warehouses, shops, and locations in real-time with automatic synchronization.\",\n            color: \"bg-blue-500\"\n        },\n        {\n            icon: _barrel_optimize_names_BarChart3_Clock_Database_FileText_Globe_Package_Settings_ShoppingCart_Smartphone_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            title: \"Advanced Analytics\",\n            description: \"Comprehensive reporting and analytics with customizable dashboards and real-time insights.\",\n            color: \"bg-green-500\"\n        },\n        {\n            icon: _barrel_optimize_names_BarChart3_Clock_Database_FileText_Globe_Package_Settings_ShoppingCart_Smartphone_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            title: \"E-commerce Integration\",\n            description: \"Seamless integration with Shopify, WooCommerce, and custom e-commerce platforms.\",\n            color: \"bg-purple-500\"\n        },\n        {\n            icon: _barrel_optimize_names_BarChart3_Clock_Database_FileText_Globe_Package_Settings_ShoppingCart_Smartphone_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            title: \"B2B Customer Portal\",\n            description: \"Dedicated portal for business customers with order history, invoices, and product catalogs.\",\n            color: \"bg-orange-500\"\n        },\n        {\n            icon: _barrel_optimize_names_BarChart3_Clock_Database_FileText_Globe_Package_Settings_ShoppingCart_Smartphone_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: \"Sales Tracking\",\n            description: \"Monitor sales performance, track movements, and generate detailed sales reports.\",\n            color: \"bg-red-500\"\n        },\n        {\n            icon: _barrel_optimize_names_BarChart3_Clock_Database_FileText_Globe_Package_Settings_ShoppingCart_Smartphone_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            title: \"Mobile Responsive\",\n            description: \"Access your inventory management system from any device with our responsive design.\",\n            color: \"bg-indigo-500\"\n        },\n        {\n            icon: _barrel_optimize_names_BarChart3_Clock_Database_FileText_Globe_Package_Settings_ShoppingCart_Smartphone_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            title: \"Product Catalog\",\n            description: \"Comprehensive product management with EAN13 support, pricing tiers, and detailed specifications.\",\n            color: \"bg-teal-500\"\n        },\n        {\n            icon: _barrel_optimize_names_BarChart3_Clock_Database_FileText_Globe_Package_Settings_ShoppingCart_Smartphone_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            title: \"Order Management\",\n            description: \"Complete order lifecycle management from creation to fulfillment and delivery.\",\n            color: \"bg-pink-500\"\n        },\n        {\n            icon: _barrel_optimize_names_BarChart3_Clock_Database_FileText_Globe_Package_Settings_ShoppingCart_Smartphone_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            title: \"Document Generation\",\n            description: \"Automated invoice, delivery note, and document generation with customizable templates.\",\n            color: \"bg-yellow-500\"\n        },\n        {\n            icon: _barrel_optimize_names_BarChart3_Clock_Database_FileText_Globe_Package_Settings_ShoppingCart_Smartphone_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            title: \"Role-Based Access\",\n            description: \"Granular user permissions and role-based access control for different team members.\",\n            color: \"bg-gray-500\"\n        },\n        {\n            icon: _barrel_optimize_names_BarChart3_Clock_Database_FileText_Globe_Package_Settings_ShoppingCart_Smartphone_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            title: \"Automated Workflows\",\n            description: \"Streamline operations with automated reorder points, notifications, and stock alerts.\",\n            color: \"bg-cyan-500\"\n        },\n        {\n            icon: _barrel_optimize_names_BarChart3_Clock_Database_FileText_Globe_Package_Settings_ShoppingCart_Smartphone_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            title: \"Real-Time Updates\",\n            description: \"Instant stock updates across all locations and platforms with real-time synchronization.\",\n            color: \"bg-emerald-500\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"features\",\n        className: \"py-20 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl sm:text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Powerful Features for Modern Business\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Features.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                            children: \"WSTOCK provides everything you need to manage your inventory efficiently, from basic stock tracking to advanced e-commerce integration.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Features.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Features.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: index * 0.1\n                            },\n                            whileHover: {\n                                y: -5\n                            },\n                            className: \"bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `${feature.color} w-12 h-12 rounded-lg flex items-center justify-center mb-4`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                        className: \"h-6 w-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Features.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Features.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-3\",\n                                    children: feature.title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Features.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 leading-relaxed\",\n                                    children: feature.description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Features.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Features.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Features.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.5\n                    },\n                    className: \"text-center mt-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 mb-6\",\n                            children: \"Ready to experience these features in action?\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Features.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.a, {\n                            href: \"#contact\",\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            className: \"inline-flex items-center px-8 py-4 bg-wstock-green text-white font-semibold rounded-lg hover:bg-green-600 transition-colors duration-200 shadow-lg\",\n                            children: \"Start Your Free Trial\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Features.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Features.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Features.tsx\",\n            lineNumber: 97,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Features.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Features.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Footer.tsx":
/*!*******************************!*\
  !*** ./components/Footer.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowUp_Facebook_Github_Linkedin_Mail_MapPin_Package_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUp,Facebook,Github,Linkedin,Mail,MapPin,Package,Phone,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUp_Facebook_Github_Linkedin_Mail_MapPin_Package_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUp,Facebook,Github,Linkedin,Mail,MapPin,Package,Phone,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUp_Facebook_Github_Linkedin_Mail_MapPin_Package_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUp,Facebook,Github,Linkedin,Mail,MapPin,Package,Phone,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUp_Facebook_Github_Linkedin_Mail_MapPin_Package_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUp,Facebook,Github,Linkedin,Mail,MapPin,Package,Phone,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUp_Facebook_Github_Linkedin_Mail_MapPin_Package_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUp,Facebook,Github,Linkedin,Mail,MapPin,Package,Phone,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUp_Facebook_Github_Linkedin_Mail_MapPin_Package_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUp,Facebook,Github,Linkedin,Mail,MapPin,Package,Phone,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUp_Facebook_Github_Linkedin_Mail_MapPin_Package_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUp,Facebook,Github,Linkedin,Mail,MapPin,Package,Phone,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUp_Facebook_Github_Linkedin_Mail_MapPin_Package_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUp,Facebook,Github,Linkedin,Mail,MapPin,Package,Phone,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUp_Facebook_Github_Linkedin_Mail_MapPin_Package_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUp,Facebook,Github,Linkedin,Mail,MapPin,Package,Phone,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-up.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Footer() {\n    const scrollToTop = ()=>{\n        window.scrollTo({\n            top: 0,\n            behavior: \"smooth\"\n        });\n    };\n    const footerLinks = {\n        product: [\n            {\n                name: \"Features\",\n                href: \"#features\"\n            },\n            {\n                name: \"Technology\",\n                href: \"#technology\"\n            },\n            {\n                name: \"Security\",\n                href: \"#security\"\n            },\n            {\n                name: \"Pricing\",\n                href: \"#pricing\"\n            },\n            {\n                name: \"API Documentation\",\n                href: \"#\"\n            },\n            {\n                name: \"Integrations\",\n                href: \"#\"\n            }\n        ],\n        company: [\n            {\n                name: \"About Us\",\n                href: \"#\"\n            },\n            {\n                name: \"Careers\",\n                href: \"#\"\n            },\n            {\n                name: \"Blog\",\n                href: \"#\"\n            },\n            {\n                name: \"Press\",\n                href: \"#\"\n            },\n            {\n                name: \"Partners\",\n                href: \"#\"\n            },\n            {\n                name: \"Contact\",\n                href: \"#contact\"\n            }\n        ],\n        support: [\n            {\n                name: \"Help Center\",\n                href: \"#\"\n            },\n            {\n                name: \"Documentation\",\n                href: \"#\"\n            },\n            {\n                name: \"System Status\",\n                href: \"#\"\n            },\n            {\n                name: \"Community\",\n                href: \"#\"\n            },\n            {\n                name: \"Training\",\n                href: \"#\"\n            },\n            {\n                name: \"Webinars\",\n                href: \"#\"\n            }\n        ],\n        legal: [\n            {\n                name: \"Privacy Policy\",\n                href: \"#\"\n            },\n            {\n                name: \"Terms of Service\",\n                href: \"#\"\n            },\n            {\n                name: \"Cookie Policy\",\n                href: \"#\"\n            },\n            {\n                name: \"GDPR\",\n                href: \"#\"\n            },\n            {\n                name: \"Security\",\n                href: \"#\"\n            },\n            {\n                name: \"Compliance\",\n                href: \"#\"\n            }\n        ]\n    };\n    const socialLinks = [\n        {\n            name: \"Twitter\",\n            icon: _barrel_optimize_names_ArrowUp_Facebook_Github_Linkedin_Mail_MapPin_Package_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            href: \"#\"\n        },\n        {\n            name: \"LinkedIn\",\n            icon: _barrel_optimize_names_ArrowUp_Facebook_Github_Linkedin_Mail_MapPin_Package_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            href: \"#\"\n        },\n        {\n            name: \"Facebook\",\n            icon: _barrel_optimize_names_ArrowUp_Facebook_Github_Linkedin_Mail_MapPin_Package_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            href: \"#\"\n        },\n        {\n            name: \"GitHub\",\n            icon: _barrel_optimize_names_ArrowUp_Facebook_Github_Linkedin_Mail_MapPin_Package_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            href: \"#\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"py-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-6 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            duration: 0.6\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-wstock-green p-2 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUp_Facebook_Github_Linkedin_Mail_MapPin_Package_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-6 w-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                                            lineNumber: 79,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                                        lineNumber: 78,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: \"WSTOCK\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300 mb-6 leading-relaxed\",\n                                                children: \"Advanced stock management and e-commerce integration platform. Streamline your inventory operations with real-time tracking, powerful analytics, and seamless integrations.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center text-gray-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUp_Facebook_Github_Linkedin_Mail_MapPin_Package_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-3 text-wstock-green\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                                                lineNumber: 90,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"<EMAIL>\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                                                lineNumber: 91,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                                        lineNumber: 89,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center text-gray-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUp_Facebook_Github_Linkedin_Mail_MapPin_Package_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-3 text-wstock-green\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                                                lineNumber: 94,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Available via email\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                                                lineNumber: 95,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                                        lineNumber: 93,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center text-gray-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUp_Facebook_Github_Linkedin_Mail_MapPin_Package_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-3 text-wstock-green\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                                                lineNumber: 98,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Global Support\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                                                lineNumber: 99,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-6\",\n                                            children: \"Product\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3\",\n                                            children: footerLinks.product.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: link.href,\n                                                        className: \"text-gray-300 hover:text-wstock-green transition-colors duration-200\",\n                                                        children: link.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, index, false, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-6\",\n                                            children: \"Company\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3\",\n                                            children: footerLinks.company.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: link.href,\n                                                        className: \"text-gray-300 hover:text-wstock-green transition-colors duration-200\",\n                                                        children: link.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, index, false, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.3\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-6\",\n                                            children: \"Support\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3\",\n                                            children: footerLinks.support.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: link.href,\n                                                        className: \"text-gray-300 hover:text-wstock-green transition-colors duration-200\",\n                                                        children: link.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, index, false, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.4\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-6\",\n                                            children: \"Legal\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3\",\n                                            children: footerLinks.legal.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: link.href,\n                                                        className: \"text-gray-300 hover:text-wstock-green transition-colors duration-200\",\n                                                        children: link.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, index, false, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.5\n                        },\n                        className: \"border-t border-gray-800 py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-2xl mx-auto text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-4\",\n                                    children: \"Stay Updated\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-6\",\n                                    children: \"Get the latest updates on new features, integrations, and inventory management best practices.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            placeholder: \"Enter your email\",\n                                            className: \"flex-1 px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:ring-2 focus:ring-wstock-green focus:border-transparent text-white placeholder-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            className: \"px-6 py-3 bg-wstock-green text-white rounded-lg hover:bg-green-600 transition-colors duration-200 font-medium\",\n                                            children: \"Subscribe\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-gray-800 py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.6\n                                    },\n                                    className: \"text-gray-400 text-sm mb-4 md:mb-0\",\n                                    children: \"\\xa9 2024 WSTOCK. All rights reserved. Built with ❤️ for modern businesses.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.1\n                                    },\n                                    className: \"flex items-center space-x-4\",\n                                    children: socialLinks.map((social, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.a, {\n                                            href: social.href,\n                                            whileHover: {\n                                                scale: 1.1\n                                            },\n                                            whileTap: {\n                                                scale: 0.9\n                                            },\n                                            className: \"text-gray-400 hover:text-wstock-green transition-colors duration-200\",\n                                            \"aria-label\": social.name,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(social.icon, {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                onClick: scrollToTop,\n                whileHover: {\n                    scale: 1.1\n                },\n                whileTap: {\n                    scale: 0.9\n                },\n                className: \"fixed bottom-8 right-8 bg-wstock-green text-white p-3 rounded-full shadow-lg hover:bg-green-600 transition-colors duration-200 z-50\",\n                initial: {\n                    opacity: 0,\n                    y: 100\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    delay: 1\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUp_Facebook_Github_Linkedin_Mail_MapPin_Package_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-6 w-6\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Menu_Package_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Package,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Package_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Package,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Package_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Package,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Header() {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [scrolled, setScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setScrolled(window.scrollY > 50);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    const navItems = [\n        {\n            href: \"#features\",\n            label: \"Features\"\n        },\n        {\n            href: \"#technology\",\n            label: \"Technology\"\n        },\n        {\n            href: \"#use-cases\",\n            label: \"Use Cases\"\n        },\n        {\n            href: \"#security\",\n            label: \"Security\"\n        },\n        {\n            href: \"#pricing\",\n            label: \"Pricing\"\n        },\n        {\n            href: \"#contact\",\n            label: \"Contact\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.header, {\n        initial: {\n            y: -100\n        },\n        animate: {\n            y: 0\n        },\n        className: `fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${scrolled ? \"bg-white/95 backdrop-blur-md shadow-lg\" : \"bg-transparent\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-wstock-green p-2 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Package_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-6 w-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Header.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Header.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl font-bold text-gray-900\",\n                                    children: \"WSTOCK\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Header.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Header.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: [\n                                navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.a, {\n                                        href: item.href,\n                                        whileHover: {\n                                            y: -2\n                                        },\n                                        className: \"text-gray-700 hover:text-wstock-green transition-colors duration-200 font-medium\",\n                                        children: item.label\n                                    }, item.href, false, {\n                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Header.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.a, {\n                                    href: \"#contact\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    className: \"bg-wstock-green text-white px-6 py-2 rounded-lg font-medium hover:bg-green-600 transition-colors duration-200\",\n                                    children: \"Start Free Trial\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Header.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Header.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsOpen(!isOpen),\n                                className: \"text-gray-700 hover:text-wstock-green transition-colors duration-200\",\n                                children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Package_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Header.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 25\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Package_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Header.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 53\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Header.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Header.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Header.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this),\n                isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    animate: {\n                        opacity: 1,\n                        height: \"auto\"\n                    },\n                    exit: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    className: \"md:hidden bg-white border-t border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pt-2 pb-3 space-y-1\",\n                        children: [\n                            navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: item.href,\n                                    onClick: ()=>setIsOpen(false),\n                                    className: \"block px-3 py-2 text-gray-700 hover:text-wstock-green transition-colors duration-200 font-medium\",\n                                    children: item.label\n                                }, item.href, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Header.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 17\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#contact\",\n                                onClick: ()=>setIsOpen(false),\n                                className: \"block w-full text-center bg-wstock-green text-white px-6 py-2 rounded-lg font-medium hover:bg-green-600 transition-colors duration-200 mt-4\",\n                                children: \"Start Free Trial\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Header.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Header.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Header.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Header.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Header.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Hero.tsx":
/*!*****************************!*\
  !*** ./components/Hero.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Hero)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Play_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Play,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Play_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Play,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Play_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Play,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Play_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Play,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Hero() {\n    const stats = [\n        {\n            number: \"10,000+\",\n            label: \"Products Managed\"\n        },\n        {\n            number: \"50+\",\n            label: \"Locations Supported\"\n        },\n        {\n            number: \"99.9%\",\n            label: \"Uptime Guarantee\"\n        },\n        {\n            number: \"24/7\",\n            label: \"Support Available\"\n        }\n    ];\n    const features = [\n        \"Multi-location inventory tracking\",\n        \"Real-time stock monitoring\",\n        \"E-commerce integration\",\n        \"Advanced analytics & reporting\",\n        \"B2B customer portal\",\n        \"Automated workflows\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-blue-50 via-white to-green-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0\",\n                    style: {\n                        backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 sm:px-6 lg:px-8 pt-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -50\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            className: \"text-center lg:text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 0.2\n                                    },\n                                    className: \"inline-flex items-center space-x-2 bg-green-100 text-green-800 px-4 py-2 rounded-full text-sm font-medium mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Play_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Trusted by 500+ businesses worldwide\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.h1, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 0.3\n                                    },\n                                    className: \"text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6\",\n                                    children: [\n                                        \"Advanced\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"gradient-text\",\n                                            children: \"Stock Management\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        \"for Modern Business\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.p, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 0.4\n                                    },\n                                    className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                    children: \"WSTOCK revolutionizes inventory management with real-time tracking, seamless e-commerce integration, and powerful analytics. Scale your business with confidence.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 0.5\n                                    },\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-3 mb-8\",\n                                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Play_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"h-5 w-5 text-green-500 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-700\",\n                                                    children: feature\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 0.6\n                                    },\n                                    className: \"flex flex-col sm:flex-row gap-4 mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.a, {\n                                            href: \"#contact\",\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            className: \"inline-flex items-center justify-center px-8 py-4 bg-wstock-green text-white font-semibold rounded-lg hover:bg-green-600 transition-colors duration-200 shadow-lg\",\n                                            children: [\n                                                \"Start Free Trial\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Play_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"ml-2 h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            className: \"inline-flex items-center justify-center px-8 py-4 border-2 border-gray-300 text-gray-700 font-semibold rounded-lg hover:border-gray-400 transition-colors duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Play_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"mr-2 h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Watch Demo\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 0.7\n                                    },\n                                    className: \"grid grid-cols-2 lg:grid-cols-4 gap-6\",\n                                    children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center lg:text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: stat.number\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: stat.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 50\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.2\n                            },\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative bg-white rounded-2xl shadow-2xl p-6 border border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-100 rounded-lg h-64 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500 mt-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: \"/dashboard.png\",\n                                                    alt: \"Dashboard Preview\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                    animate: {\n                                        y: [\n                                            0,\n                                            -10,\n                                            0\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 3,\n                                        repeat: Infinity\n                                    },\n                                    className: \"absolute -top-4 -right-4 bg-blue-500 text-white p-3 rounded-lg shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-semibold\",\n                                        children: \"Real-time Updates\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                    animate: {\n                                        y: [\n                                            0,\n                                            10,\n                                            0\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 3,\n                                        repeat: Infinity,\n                                        delay: 1.5\n                                    },\n                                    className: \"absolute -bottom-4 -left-4 bg-green-500 text-white p-3 rounded-lg shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-semibold\",\n                                        children: \"Multi-location\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Hero.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Pricing.tsx":
/*!********************************!*\
  !*** ./components/Pricing.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Pricing)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Check_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Check,Star,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Check_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Check,Star,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Check_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Check,Star,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Check_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Check,Star,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Check_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Check,Star,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Pricing() {\n    const plans = [\n        {\n            name: \"Starter\",\n            icon: _barrel_optimize_names_ArrowRight_Building_Check_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            price: \"Free\",\n            period: \"30-day trial\",\n            description: \"Perfect for small businesses getting started with inventory management\",\n            features: [\n                \"Up to 1,000 products\",\n                \"2 locations\",\n                \"Basic reporting\",\n                \"Email support\",\n                \"Mobile app access\",\n                \"Basic integrations\"\n            ],\n            cta: \"Start Free Trial\",\n            popular: false,\n            color: \"border-gray-200\"\n        },\n        {\n            name: \"Professional\",\n            icon: _barrel_optimize_names_ArrowRight_Building_Check_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            price: \"Custom\",\n            period: \"per month\",\n            description: \"Advanced features for growing businesses with multiple locations\",\n            features: [\n                \"Unlimited products\",\n                \"Unlimited locations\",\n                \"Advanced analytics\",\n                \"Priority support\",\n                \"API access\",\n                \"All integrations\",\n                \"Custom workflows\",\n                \"Multi-user access\"\n            ],\n            cta: \"Contact Sales\",\n            popular: true,\n            color: \"border-wstock-green ring-2 ring-wstock-green\"\n        },\n        {\n            name: \"Enterprise\",\n            icon: _barrel_optimize_names_ArrowRight_Building_Check_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            price: \"Custom\",\n            period: \"contact us\",\n            description: \"Enterprise-grade solution with dedicated support and custom features\",\n            features: [\n                \"Everything in Professional\",\n                \"Dedicated account manager\",\n                \"Custom integrations\",\n                \"On-premise deployment\",\n                \"SLA guarantee\",\n                \"Training & onboarding\",\n                \"Custom development\",\n                \"White-label options\"\n            ],\n            cta: \"Contact Sales\",\n            popular: false,\n            color: \"border-gray-200\"\n        }\n    ];\n    const faqItems = [\n        {\n            question: \"Is the free trial really free?\",\n            answer: \"Yes! Our 30-day free trial includes full access to all Starter features with no credit card required.\"\n        },\n        {\n            question: \"Can I upgrade or downgrade my plan?\",\n            answer: \"Absolutely. You can change your plan at any time, and we'll prorate the charges accordingly.\"\n        },\n        {\n            question: \"What kind of support do you offer?\",\n            answer: \"We offer email support for all plans, priority support for Professional, and dedicated account management for Enterprise customers.\"\n        },\n        {\n            question: \"Do you offer custom integrations?\",\n            answer: \"Yes, our Enterprise plan includes custom integrations. We can also discuss custom development for specific business needs.\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"pricing\",\n        className: \"py-20 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl sm:text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Simple, Transparent Pricing\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                            children: \"Start with our free trial and scale as you grow. No hidden fees, no long-term contracts, just powerful inventory management.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-8 mb-20\",\n                    children: plans.map((plan, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: index * 0.1\n                            },\n                            whileHover: {\n                                y: -5\n                            },\n                            className: `relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border-2 ${plan.color}`,\n                            children: [\n                                plan.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-4 left-1/2 transform -translate-x-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-wstock-green text-white px-4 py-2 rounded-full text-sm font-medium\",\n                                        children: \"Most Popular\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `w-12 h-12 rounded-lg flex items-center justify-center ${plan.popular ? \"bg-wstock-green\" : \"bg-gray-100\"}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(plan.icon, {\n                                                    className: `h-6 w-6 ${plan.popular ? \"text-white\" : \"text-gray-600\"}`\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                            children: plan.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-4xl font-bold text-gray-900\",\n                                                    children: plan.price\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 19\n                                                }, this),\n                                                plan.period && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600 ml-2\",\n                                                    children: [\n                                                        \"/ \",\n                                                        plan.period\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: plan.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-4 mb-8\",\n                                    children: plan.features.map((feature, featureIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building_Check_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-5 w-5 text-green-500 mr-3 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-700\",\n                                                    children: feature\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, featureIndex, true, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.a, {\n                                    href: \"#contact\",\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    className: `block w-full text-center py-4 px-6 rounded-lg font-semibold transition-colors duration-200 ${plan.popular ? \"bg-wstock-green text-white hover:bg-green-600\" : \"bg-gray-100 text-gray-900 hover:bg-gray-200\"}`,\n                                    children: plan.cta\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.3\n                    },\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold text-gray-900 text-center mb-12\",\n                            children: \"Frequently Asked Questions\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                            children: faqItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: index * 0.1\n                                    },\n                                    className: \"bg-white rounded-lg p-6 shadow-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-3\",\n                                            children: item.question\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 leading-relaxed\",\n                                            children: item.answer\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.5\n                    },\n                    className: \"text-center mt-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-wstock-green to-green-600 rounded-2xl p-8 text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold mb-4\",\n                                children: \"Ready to Transform Your Inventory Management?\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg mb-6 text-green-100\",\n                                children: \"Join hundreds of businesses already using WSTOCK to streamline their operations.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.a, {\n                                href: \"#contact\",\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                className: \"inline-flex items-center px-8 py-4 bg-white text-wstock-green font-semibold rounded-lg hover:bg-gray-100 transition-colors duration-200 shadow-lg\",\n                                children: [\n                                    \"Start Your Free Trial Today\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building_Check_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"ml-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n            lineNumber: 96,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Pricing.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Security.tsx":
/*!*********************************!*\
  !*** ./components/Security.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Security)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Eye_FileCheck_Key_Lock_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Eye,FileCheck,Key,Lock,Server,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Eye_FileCheck_Key_Lock_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Eye,FileCheck,Key,Lock,Server,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Eye_FileCheck_Key_Lock_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Eye,FileCheck,Key,Lock,Server,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Eye_FileCheck_Key_Lock_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Eye,FileCheck,Key,Lock,Server,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Eye_FileCheck_Key_Lock_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Eye,FileCheck,Key,Lock,Server,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Eye_FileCheck_Key_Lock_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Eye,FileCheck,Key,Lock,Server,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Eye_FileCheck_Key_Lock_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Eye,FileCheck,Key,Lock,Server,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Eye_FileCheck_Key_Lock_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Eye,FileCheck,Key,Lock,Server,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Security() {\n    const securityFeatures = [\n        {\n            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Eye_FileCheck_Key_Lock_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            title: \"Enterprise Security\",\n            description: \"Bank-grade encryption and security protocols protect your sensitive business data.\",\n            details: [\n                \"AES-256 encryption\",\n                \"SSL/TLS protocols\",\n                \"Secure data centers\",\n                \"Regular security audits\"\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Eye_FileCheck_Key_Lock_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            title: \"Access Control\",\n            description: \"Granular role-based permissions ensure users only access what they need.\",\n            details: [\n                \"Role-based access\",\n                \"Multi-factor authentication\",\n                \"Session management\",\n                \"IP restrictions\"\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Eye_FileCheck_Key_Lock_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            title: \"Audit Trails\",\n            description: \"Complete audit logs track every action for compliance and security monitoring.\",\n            details: [\n                \"Activity logging\",\n                \"Change tracking\",\n                \"User monitoring\",\n                \"Compliance reports\"\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Eye_FileCheck_Key_Lock_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            title: \"Compliance Ready\",\n            description: \"Built-in compliance features for GDPR, SOX, and industry-specific regulations.\",\n            details: [\n                \"GDPR compliance\",\n                \"Data retention policies\",\n                \"Privacy controls\",\n                \"Regulatory reporting\"\n            ]\n        }\n    ];\n    const deploymentFeatures = [\n        {\n            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Eye_FileCheck_Key_Lock_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: \"Cloud & On-Premise\",\n            description: \"Deploy in the cloud, on-premise, or hybrid environments based on your needs.\",\n            color: \"bg-blue-500\"\n        },\n        {\n            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Eye_FileCheck_Key_Lock_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            title: \"Single Sign-On\",\n            description: \"Integrate with your existing identity providers for seamless authentication.\",\n            color: \"bg-green-500\"\n        },\n        {\n            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Eye_FileCheck_Key_Lock_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            title: \"Disaster Recovery\",\n            description: \"Automated backups and disaster recovery ensure business continuity.\",\n            color: \"bg-orange-500\"\n        },\n        {\n            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Eye_FileCheck_Key_Lock_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            title: \"99.9% Uptime\",\n            description: \"High availability architecture with redundancy and failover protection.\",\n            color: \"bg-purple-500\"\n        }\n    ];\n    const certifications = [\n        {\n            name: \"SOC 2 Type II\",\n            icon: \"\\uD83D\\uDEE1️\"\n        },\n        {\n            name: \"ISO 27001\",\n            icon: \"\\uD83D\\uDD12\"\n        },\n        {\n            name: \"GDPR Compliant\",\n            icon: \"\\uD83C\\uDDEA\\uD83C\\uDDFA\"\n        },\n        {\n            name: \"HIPAA Ready\",\n            icon: \"\\uD83C\\uDFE5\"\n        },\n        {\n            name: \"PCI DSS\",\n            icon: \"\\uD83D\\uDCB3\"\n        },\n        {\n            name: \"AWS Certified\",\n            icon: \"☁️\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"security\",\n        className: \"py-20 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl sm:text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Enterprise-Grade Security & Deployment\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                            children: \"Your data security is our top priority. WSTOCK implements industry-leading security measures and deployment options to protect your business.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8 mb-20\",\n                    children: securityFeatures.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: index * 0.1\n                            },\n                            className: \"bg-gray-50 rounded-xl p-6 hover:shadow-lg transition-all duration-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-wstock-green w-12 h-12 rounded-lg flex items-center justify-center mr-4 flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                                    children: feature.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-4\",\n                                                    children: feature.description\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: feature.details.map((detail, detailIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Eye_FileCheck_Key_Lock_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-500 mr-2 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 21\n                                                }, this),\n                                                detail\n                                            ]\n                                        }, detailIndex, true, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    className: \"mb-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold text-gray-900 text-center mb-12\",\n                            children: \"Flexible Deployment Options\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: deploymentFeatures.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: index * 0.1\n                                    },\n                                    whileHover: {\n                                        y: -5\n                                    },\n                                    className: \"bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `${feature.color} w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: feature.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm\",\n                                            children: feature.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.3\n                    },\n                    className: \"bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold text-gray-900 text-center mb-8\",\n                            children: \"Certifications & Compliance\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6\",\n                            children: certifications.map((cert, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.8\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.4,\n                                        delay: index * 0.1\n                                    },\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    className: \"bg-white rounded-lg p-4 text-center shadow-md hover:shadow-lg transition-all duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl mb-2\",\n                                            children: cert.icon\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: cert.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.5\n                    },\n                    className: \"text-center mt-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-wstock-green/10 rounded-2xl p-8 max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Eye_FileCheck_Key_Lock_Server_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                className: \"h-12 w-12 text-wstock-green mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                children: \"Our Security Promise\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-600 mb-6\",\n                                children: \"We understand that your inventory data is critical to your business. That's why we've built WSTOCK with security at its core, not as an afterthought.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.a, {\n                                href: \"#contact\",\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                className: \"inline-flex items-center px-8 py-4 bg-wstock-green text-white font-semibold rounded-lg hover:bg-green-600 transition-colors duration-200 shadow-lg\",\n                                children: \"Learn More About Security\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Security.tsx\n");

/***/ }),

/***/ "(ssr)/./components/TechStack.tsx":
/*!**********************************!*\
  !*** ./components/TechStack.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TechStack)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Brain_Cloud_Container_Database_Server_Shield_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Cloud,Container,Database,Server,Shield,Workflow!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/container.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Cloud_Container_Database_Server_Shield_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Cloud,Container,Database,Server,Shield,Workflow!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Cloud_Container_Database_Server_Shield_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Cloud,Container,Database,Server,Shield,Workflow!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/workflow.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Cloud_Container_Database_Server_Shield_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Cloud,Container,Database,Server,Shield,Workflow!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Cloud_Container_Database_Server_Shield_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Cloud,Container,Database,Server,Shield,Workflow!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/cloud.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Cloud_Container_Database_Server_Shield_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Cloud,Container,Database,Server,Shield,Workflow!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Cloud_Container_Database_Server_Shield_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Cloud,Container,Database,Server,Shield,Workflow!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction TechStack() {\n    const technologies = [\n        {\n            icon: _barrel_optimize_names_Brain_Cloud_Container_Database_Server_Shield_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            name: \"Docker\",\n            description: \"Containerized deployment for consistent environments across all platforms\",\n            category: \"Deployment\"\n        },\n        {\n            icon: _barrel_optimize_names_Brain_Cloud_Container_Database_Server_Shield_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            name: \"Supabase\",\n            description: \"Modern PostgreSQL database with real-time subscriptions and authentication\",\n            category: \"Database\"\n        },\n        {\n            icon: _barrel_optimize_names_Brain_Cloud_Container_Database_Server_Shield_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            name: \"n8n\",\n            description: \"Workflow automation for seamless integrations and business process automation\",\n            category: \"Automation\"\n        },\n        {\n            icon: _barrel_optimize_names_Brain_Cloud_Container_Database_Server_Shield_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            name: \"AI Integration\",\n            description: \"Machine learning for demand forecasting and intelligent inventory optimization\",\n            category: \"Intelligence\"\n        },\n        {\n            icon: _barrel_optimize_names_Brain_Cloud_Container_Database_Server_Shield_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            name: \"Cloud Native\",\n            description: \"Built for the cloud with auto-scaling and high availability architecture\",\n            category: \"Infrastructure\"\n        },\n        {\n            icon: _barrel_optimize_names_Brain_Cloud_Container_Database_Server_Shield_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            name: \"Enterprise Security\",\n            description: \"Bank-grade security with encryption, audit logs, and compliance features\",\n            category: \"Security\"\n        }\n    ];\n    const integrations = [\n        {\n            name: \"Shopify\",\n            logo: \"\\uD83D\\uDECD️\"\n        },\n        {\n            name: \"WooCommerce\",\n            logo: \"\\uD83D\\uDED2\"\n        },\n        {\n            name: \"Magento\",\n            logo: \"\\uD83C\\uDFEA\"\n        },\n        {\n            name: \"Amazon\",\n            logo: \"\\uD83D\\uDCE6\"\n        },\n        {\n            name: \"eBay\",\n            logo: \"\\uD83C\\uDFF7️\"\n        },\n        {\n            name: \"Etsy\",\n            logo: \"\\uD83C\\uDFA8\"\n        },\n        {\n            name: \"Facebook Shop\",\n            logo: \"\\uD83D\\uDCD8\"\n        },\n        {\n            name: \"Google Shopping\",\n            logo: \"\\uD83D\\uDD0D\"\n        },\n        {\n            name: \"Stripe\",\n            logo: \"\\uD83D\\uDCB3\"\n        },\n        {\n            name: \"PayPal\",\n            logo: \"\\uD83D\\uDCB0\"\n        },\n        {\n            name: \"QuickBooks\",\n            logo: \"\\uD83D\\uDCCA\"\n        },\n        {\n            name: \"Xero\",\n            logo: \"\\uD83D\\uDCC8\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"technology\",\n        className: \"py-20 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl sm:text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Modern Technology Stack\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/TechStack.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                            children: \"Built with cutting-edge technologies for scalability, security, and performance. WSTOCK leverages the best tools to deliver enterprise-grade solutions.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/TechStack.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/TechStack.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20\",\n                    children: technologies.map((tech, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: index * 0.1\n                            },\n                            whileHover: {\n                                y: -5\n                            },\n                            className: \"bg-gradient-to-br from-gray-50 to-white rounded-xl p-6 border border-gray-200 hover:shadow-lg transition-all duration-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-wstock-green w-12 h-12 rounded-lg flex items-center justify-center mr-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tech.icon, {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/TechStack.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/TechStack.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                    children: tech.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/TechStack.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-wstock-green font-medium\",\n                                                    children: tech.category\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/TechStack.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/TechStack.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/TechStack.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 leading-relaxed\",\n                                    children: tech.description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/TechStack.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/TechStack.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/TechStack.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                            children: \"Seamless Integrations\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/TechStack.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 mb-12 max-w-2xl mx-auto\",\n                            children: \"Connect with your favorite platforms and tools. WSTOCK integrates with leading e-commerce, payment, and accounting solutions.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/TechStack.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-6\",\n                            children: integrations.map((integration, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.8\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.4,\n                                        delay: index * 0.05\n                                    },\n                                    whileHover: {\n                                        scale: 1.1\n                                    },\n                                    className: \"bg-white rounded-lg p-4 shadow-md hover:shadow-lg transition-all duration-300 border border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl mb-2\",\n                                            children: integration.logo\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/TechStack.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: integration.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/TechStack.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/TechStack.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/TechStack.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/TechStack.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.3\n                    },\n                    className: \"mt-20 bg-gradient-to-r from-blue-50 to-green-50 rounded-2xl p-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-blue-500 to-green-500 w-16 h-16 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Cloud_Container_Database_Server_Shield_Workflow_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-8 w-8 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/TechStack.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/TechStack.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/TechStack.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                            children: \"Enterprise-Grade Architecture\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/TechStack.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n                            children: \"Our microservices architecture ensures 99.9% uptime, horizontal scalability, and robust security. Built to handle everything from small businesses to enterprise-level operations with millions of transactions.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/TechStack.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/TechStack.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/TechStack.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/TechStack.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/TechStack.tsx\n");

/***/ }),

/***/ "(ssr)/./components/UseCases.tsx":
/*!*********************************!*\
  !*** ./components/UseCases.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UseCases)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Building_Factory_Globe2_ShoppingBag_Store_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Factory,Globe2,ShoppingBag,Store,Truck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/store.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Factory_Globe2_ShoppingBag_Store_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Factory,Globe2,ShoppingBag,Store,Truck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/factory.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Factory_Globe2_ShoppingBag_Store_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Factory,Globe2,ShoppingBag,Store,Truck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Factory_Globe2_ShoppingBag_Store_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Factory,Globe2,ShoppingBag,Store,Truck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Factory_Globe2_ShoppingBag_Store_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Factory,Globe2,ShoppingBag,Store,Truck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Factory_Globe2_ShoppingBag_Store_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Factory,Globe2,ShoppingBag,Store,Truck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction UseCases() {\n    const useCases = [\n        {\n            icon: _barrel_optimize_names_Building_Factory_Globe2_ShoppingBag_Store_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            title: \"Retail Chains\",\n            description: \"Manage inventory across multiple retail locations with real-time synchronization and centralized control.\",\n            features: [\n                \"Multi-store management\",\n                \"POS integration\",\n                \"Customer analytics\",\n                \"Loyalty programs\"\n            ],\n            color: \"from-blue-500 to-blue-600\"\n        },\n        {\n            icon: _barrel_optimize_names_Building_Factory_Globe2_ShoppingBag_Store_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            title: \"Manufacturing\",\n            description: \"Track raw materials, work-in-progress, and finished goods across your entire production pipeline.\",\n            features: [\n                \"Bill of materials\",\n                \"Production planning\",\n                \"Quality control\",\n                \"Supplier management\"\n            ],\n            color: \"from-green-500 to-green-600\"\n        },\n        {\n            icon: _barrel_optimize_names_Building_Factory_Globe2_ShoppingBag_Store_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            title: \"Distribution\",\n            description: \"Optimize warehouse operations and distribution networks with advanced logistics management.\",\n            features: [\n                \"Warehouse optimization\",\n                \"Route planning\",\n                \"Delivery tracking\",\n                \"Fleet management\"\n            ],\n            color: \"from-purple-500 to-purple-600\"\n        },\n        {\n            icon: _barrel_optimize_names_Building_Factory_Globe2_ShoppingBag_Store_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            title: \"E-commerce\",\n            description: \"Seamlessly integrate with online marketplaces and manage omnichannel inventory.\",\n            features: [\n                \"Marketplace sync\",\n                \"Order fulfillment\",\n                \"Dropshipping\",\n                \"Returns management\"\n            ],\n            color: \"from-orange-500 to-orange-600\"\n        },\n        {\n            icon: _barrel_optimize_names_Building_Factory_Globe2_ShoppingBag_Store_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: \"Wholesale\",\n            description: \"Manage B2B relationships with dedicated customer portals and bulk order processing.\",\n            features: [\n                \"B2B portal\",\n                \"Bulk pricing\",\n                \"Credit management\",\n                \"Custom catalogs\"\n            ],\n            color: \"from-red-500 to-red-600\"\n        },\n        {\n            icon: _barrel_optimize_names_Building_Factory_Globe2_ShoppingBag_Store_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            title: \"International\",\n            description: \"Handle multi-currency, multi-language operations with global compliance features.\",\n            features: [\n                \"Multi-currency\",\n                \"Tax compliance\",\n                \"Customs integration\",\n                \"Local regulations\"\n            ],\n            color: \"from-indigo-500 to-indigo-600\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"use-cases\",\n        className: \"py-20 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl sm:text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Perfect for Every Business Model\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                            children: \"Whether you're a small retailer or a global enterprise, WSTOCK adapts to your specific industry needs and business requirements.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: useCases.map((useCase, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: index * 0.1\n                            },\n                            whileHover: {\n                                y: -5\n                            },\n                            className: \"bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `bg-gradient-to-r ${useCase.color} p-6 text-white`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(useCase.icon, {\n                                                    className: \"h-8 w-8 mr-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold\",\n                                                    children: useCase.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/90 leading-relaxed\",\n                                            children: useCase.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-3\",\n                                            children: \"Key Features:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2\",\n                                            children: useCase.features.map((feature, featureIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-wstock-green rounded-full mr-3 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                                                            lineNumber: 108,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        feature\n                                                    ]\n                                                }, featureIndex, true, {\n                                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.3\n                    },\n                    className: \"mt-20 bg-white rounded-2xl p-8 shadow-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                    children: \"Success Stories\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600\",\n                                    children: \"See how businesses like yours have transformed their operations with WSTOCK\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-wstock-green mb-2\",\n                                            children: \"85%\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Reduction in stock-outs\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-wstock-green mb-2\",\n                                            children: \"60%\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Faster order processing\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-wstock-green mb-2\",\n                                            children: \"40%\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Cost savings on inventory\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.5\n                    },\n                    className: \"text-center mt-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 mb-6\",\n                            children: \"Ready to see how WSTOCK can transform your business?\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.a, {\n                            href: \"#contact\",\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            className: \"inline-flex items-center px-8 py-4 bg-wstock-green text-white font-semibold rounded-lg hover:bg-green-600 transition-colors duration-200 shadow-lg\",\n                            children: \"Schedule a Demo\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n            lineNumber: 61,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/UseCases.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"3c2d528e6ba0\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93c3RvY2stbGFuZGluZy8uL2FwcC9nbG9iYWxzLmNzcz9jZTIwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiM2MyZDUyOGU2YmEwXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"WSTOCK - Advanced Stock Management System | Free Trial Available\",\n    description: \"WSTOCK is a comprehensive stock management and e-commerce integration platform. Multi-location inventory tracking, real-time analytics, B2B portal, and seamless e-commerce integration. Try it free today.\",\n    keywords: [\n        \"stock management\",\n        \"inventory management\",\n        \"e-commerce integration\",\n        \"warehouse management\",\n        \"B2B portal\",\n        \"multi-location inventory\",\n        \"real-time analytics\",\n        \"product catalog\",\n        \"sales tracking\",\n        \"business automation\",\n        \"ERP system\",\n        \"inventory tracking\",\n        \"stock control\",\n        \"supply chain management\"\n    ],\n    authors: [\n        {\n            name: \"WSTOCK Team\"\n        }\n    ],\n    creator: \"WSTOCK\",\n    publisher: \"WSTOCK\",\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    openGraph: {\n        type: \"website\",\n        locale: \"en_US\",\n        url: \"https://wstock.com\",\n        title: \"WSTOCK - Advanced Stock Management System\",\n        description: \"Comprehensive stock management and e-commerce integration platform with multi-location tracking and real-time analytics.\",\n        siteName: \"WSTOCK\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"WSTOCK - Stock Management System\"\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"WSTOCK - Advanced Stock Management System\",\n        description: \"Comprehensive stock management and e-commerce integration platform with multi-location tracking and real-time analytics.\",\n        images: [\n            \"/og-image.jpg\"\n        ]\n    },\n    viewport: {\n        width: \"device-width\",\n        initialScale: 1,\n        maximumScale: 1\n    },\n    verification: {\n        google: \"your-google-verification-code\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"scroll-smooth\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/app/layout.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/app/layout.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/favicon-32x32.png\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/app/layout.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/favicon-16x16.png\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/app/layout.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/site.webmanifest\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/app/layout.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#00a65a\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/app/layout.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify({\n                                \"@context\": \"https://schema.org\",\n                                \"@type\": \"SoftwareApplication\",\n                                \"name\": \"WSTOCK\",\n                                \"description\": \"Advanced stock management and e-commerce integration system\",\n                                \"applicationCategory\": \"BusinessApplication\",\n                                \"operatingSystem\": \"Web\",\n                                \"offers\": {\n                                    \"@type\": \"Offer\",\n                                    \"price\": \"0\",\n                                    \"priceCurrency\": \"USD\",\n                                    \"description\": \"Free trial available\"\n                                },\n                                \"provider\": {\n                                    \"@type\": \"Organization\",\n                                    \"name\": \"WSTOCK\"\n                                }\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/pgestock/web/about/app/layout.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/pgestock/web/about/app/layout.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/pgestock/web/about/app/layout.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/pgestock/web/about/app/layout.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Projects/pgestock/web/about/app/page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/framer-motion","vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/zod","vendor-chunks/@swc","vendor-chunks/@hookform","vendor-chunks/react-hook-form"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmiloswikarski%2FProjects%2Fpgestock%2Fweb%2Fabout&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();