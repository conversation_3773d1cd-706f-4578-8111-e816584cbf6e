[{"/Users/<USER>/Projects/pgestock/web/about/app/layout.tsx": "1", "/Users/<USER>/Projects/pgestock/web/about/app/page.tsx": "2", "/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx": "3", "/Users/<USER>/Projects/pgestock/web/about/components/Features.tsx": "4", "/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx": "5", "/Users/<USER>/Projects/pgestock/web/about/components/Header.tsx": "6", "/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx": "7", "/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx": "8", "/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx": "9", "/Users/<USER>/Projects/pgestock/web/about/components/TechStack.tsx": "10", "/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx": "11"}, {"size": 3536, "mtime": 1749123340008, "results": "12", "hashOfConfig": "13"}, {"size": 900, "mtime": 1749123353230, "results": "14", "hashOfConfig": "13"}, {"size": 13053, "mtime": 1749128204196, "results": "15", "hashOfConfig": "13"}, {"size": 5459, "mtime": 1749123427454, "results": "16", "hashOfConfig": "13"}, {"size": 10192, "mtime": 1749123656238, "results": "17", "hashOfConfig": "13"}, {"size": 4001, "mtime": 1749123371255, "results": "18", "hashOfConfig": "13"}, {"size": 7339, "mtime": 1749123400678, "results": "19", "hashOfConfig": "13"}, {"size": 8603, "mtime": 1749123553532, "results": "20", "hashOfConfig": "13"}, {"size": 8820, "mtime": 1749128156714, "results": "21", "hashOfConfig": "13"}, {"size": 6604, "mtime": 1749123457675, "results": "22", "hashOfConfig": "13"}, {"size": 6841, "mtime": 1749128171778, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "rwpuhb", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Projects/pgestock/web/about/app/layout.tsx", [], [], "/Users/<USER>/Projects/pgestock/web/about/app/page.tsx", [], [], "/Users/<USER>/Projects/pgestock/web/about/components/Contact.tsx", [], [], "/Users/<USER>/Projects/pgestock/web/about/components/Features.tsx", [], [], "/Users/<USER>/Projects/pgestock/web/about/components/Footer.tsx", [], [], "/Users/<USER>/Projects/pgestock/web/about/components/Header.tsx", [], [], "/Users/<USER>/Projects/pgestock/web/about/components/Hero.tsx", [], [], "/Users/<USER>/Projects/pgestock/web/about/components/Pricing.tsx", [], [], "/Users/<USER>/Projects/pgestock/web/about/components/Security.tsx", [], [], "/Users/<USER>/Projects/pgestock/web/about/components/TechStack.tsx", [], [], "/Users/<USER>/Projects/pgestock/web/about/components/UseCases.tsx", [], []]