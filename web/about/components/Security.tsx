'use client'

import { motion } from 'framer-motion'
import { 
  Shield, 
  Lock, 
  Eye, 
  FileCheck,
  Server,
  Key,
  AlertTriangle,
  CheckCircle
} from 'lucide-react'

export default function Security() {
  const securityFeatures = [
    {
      icon: Shield,
      title: 'Enterprise Security',
      description: 'Bank-grade encryption and security protocols protect your sensitive business data.',
      details: ['AES-256 encryption', 'SSL/TLS protocols', 'Secure data centers', 'Regular security audits']
    },
    {
      icon: Lock,
      title: 'Access Control',
      description: 'Granular role-based permissions ensure users only access what they need.',
      details: ['Role-based access', 'Multi-factor authentication', 'Session management', 'IP restrictions']
    },
    {
      icon: Eye,
      title: 'Audit Trails',
      description: 'Complete audit logs track every action for compliance and security monitoring.',
      details: ['Activity logging', 'Change tracking', 'User monitoring', 'Compliance reports']
    },
    {
      icon: FileCheck,
      title: 'Compliance Ready',
      description: 'Built-in compliance features for GDPR, SOX, and industry-specific regulations.',
      details: ['GDPR compliance', 'Data retention policies', 'Privacy controls', 'Regulatory reporting']
    }
  ]

  const deploymentFeatures = [
    {
      icon: Server,
      title: 'Cloud & On-Premise',
      description: 'Deploy in the cloud, on-premise, or hybrid environments based on your needs.',
      color: 'bg-blue-500'
    },
    {
      icon: Key,
      title: 'Single Sign-On',
      description: 'Integrate with your existing identity providers for seamless authentication.',
      color: 'bg-green-500'
    },
    {
      icon: AlertTriangle,
      title: 'Disaster Recovery',
      description: 'Automated backups and disaster recovery ensure business continuity.',
      color: 'bg-orange-500'
    },
    {
      icon: CheckCircle,
      title: '99.9% Uptime',
      description: 'High availability architecture with redundancy and failover protection.',
      color: 'bg-purple-500'
    }
  ]

  const certifications = [
    { name: 'SOC 2 Type II', icon: '🛡️' },
    { name: 'ISO 27001', icon: '🔒' },
    { name: 'GDPR Compliant', icon: '🇪🇺' },
    { name: 'HIPAA Ready', icon: '🏥' },
    { name: 'PCI DSS', icon: '💳' },
    { name: 'AWS Certified', icon: '☁️' }
  ]

  return (
    <section id="security" className="py-20 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            Enterprise-Grade Security & Deployment
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Your data security is our top priority. WSTOCK implements industry-leading 
            security measures and deployment options to protect your business.
          </p>
        </motion.div>

        {/* Security Features */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-20">
          {securityFeatures.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="bg-gray-50 rounded-xl p-6 hover:shadow-lg transition-all duration-300"
            >
              <div className="flex items-start mb-4">
                <div className="bg-wstock-green w-12 h-12 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                  <feature.icon className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {feature.description}
                  </p>
                </div>
              </div>
              <ul className="space-y-2">
                {feature.details.map((detail, detailIndex) => (
                  <li key={detailIndex} className="flex items-center text-sm text-gray-600">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                    {detail}
                  </li>
                ))}
              </ul>
            </motion.div>
          ))}
        </div>

        {/* Deployment Options */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mb-20"
        >
          <h3 className="text-2xl font-bold text-gray-900 text-center mb-12">
            Flexible Deployment Options
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {deploymentFeatures.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ y: -5 }}
                className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 text-center"
              >
                <div className={`${feature.color} w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4`}>
                  <feature.icon className="h-6 w-6 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h4>
                <p className="text-gray-600 text-sm">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Certifications */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-8"
        >
          <h3 className="text-2xl font-bold text-gray-900 text-center mb-8">
            Certifications & Compliance
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {certifications.map((cert, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                whileHover={{ scale: 1.05 }}
                className="bg-white rounded-lg p-4 text-center shadow-md hover:shadow-lg transition-all duration-300"
              >
                <div className="text-2xl mb-2">{cert.icon}</div>
                <div className="text-sm font-medium text-gray-700">{cert.name}</div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Security Promise */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="text-center mt-16"
        >
          <div className="bg-wstock-green/10 rounded-2xl p-8 max-w-4xl mx-auto">
            <Shield className="h-12 w-12 text-wstock-green mx-auto mb-4" />
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Our Security Promise
            </h3>
            <p className="text-lg text-gray-600 mb-6">
              We understand that your inventory data is critical to your business. 
              That&apos;s why we&apos;ve built WSTOCK with security at its core, not as an afterthought.
            </p>
            <motion.a
              href="#contact"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="inline-flex items-center px-8 py-4 bg-wstock-green text-white font-semibold rounded-lg hover:bg-green-600 transition-colors duration-200 shadow-lg"
            >
              Learn More About Security
            </motion.a>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
