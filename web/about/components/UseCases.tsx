'use client'

import { motion } from 'framer-motion'
import { 
  Store, 
  Factory, 
  Truck, 
  ShoppingBag,
  Building,
  Globe2
} from 'lucide-react'

export default function UseCases() {
  const useCases = [
    {
      icon: Store,
      title: 'Retail Chains',
      description: 'Manage inventory across multiple retail locations with real-time synchronization and centralized control.',
      features: ['Multi-store management', 'POS integration', 'Customer analytics', 'Loyalty programs'],
      color: 'from-blue-500 to-blue-600'
    },
    {
      icon: Factory,
      title: 'Manufacturing',
      description: 'Track raw materials, work-in-progress, and finished goods across your entire production pipeline.',
      features: ['Bill of materials', 'Production planning', 'Quality control', 'Supplier management'],
      color: 'from-green-500 to-green-600'
    },
    {
      icon: Truck,
      title: 'Distribution',
      description: 'Optimize warehouse operations and distribution networks with advanced logistics management.',
      features: ['Warehouse optimization', 'Route planning', 'Delivery tracking', 'Fleet management'],
      color: 'from-purple-500 to-purple-600'
    },
    {
      icon: ShoppingBag,
      title: 'E-commerce',
      description: 'Seamlessly integrate with online marketplaces and manage omnichannel inventory.',
      features: ['Marketplace sync', 'Order fulfillment', 'Dropshipping', 'Returns management'],
      color: 'from-orange-500 to-orange-600'
    },
    {
      icon: Building,
      title: 'Wholesale',
      description: 'Manage B2B relationships with dedicated customer portals and bulk order processing.',
      features: ['B2B portal', 'Bulk pricing', 'Credit management', 'Custom catalogs'],
      color: 'from-red-500 to-red-600'
    },
    {
      icon: Globe2,
      title: 'International',
      description: 'Handle multi-currency, multi-language operations with global compliance features.',
      features: ['Multi-currency', 'Tax compliance', 'Customs integration', 'Local regulations'],
      color: 'from-indigo-500 to-indigo-600'
    }
  ]

  return (
    <section id="use-cases" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            Perfect for Every Business Model
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Whether you&apos;re a small retailer or a global enterprise, WSTOCK adapts to your 
            specific industry needs and business requirements.
          </p>
        </motion.div>

        {/* Use Cases Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {useCases.map((useCase, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{ y: -5 }}
              className="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300"
            >
              {/* Header with gradient */}
              <div className={`bg-gradient-to-r ${useCase.color} p-6 text-white`}>
                <div className="flex items-center mb-4">
                  <useCase.icon className="h-8 w-8 mr-3" />
                  <h3 className="text-xl font-semibold">{useCase.title}</h3>
                </div>
                <p className="text-white/90 leading-relaxed">
                  {useCase.description}
                </p>
              </div>

              {/* Features List */}
              <div className="p-6">
                <h4 className="font-semibold text-gray-900 mb-3">Key Features:</h4>
                <ul className="space-y-2">
                  {useCase.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-gray-600">
                      <div className="w-2 h-2 bg-wstock-green rounded-full mr-3 flex-shrink-0"></div>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Success Stories Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="mt-20 bg-white rounded-2xl p-8 shadow-lg"
        >
          <div className="text-center mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Success Stories
            </h3>
            <p className="text-lg text-gray-600">
              See how businesses like yours have transformed their operations with WSTOCK
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="text-3xl font-bold text-wstock-green mb-2">85%</div>
              <div className="text-gray-600">Reduction in stock-outs</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-wstock-green mb-2">60%</div>
              <div className="text-gray-600">Faster order processing</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-wstock-green mb-2">40%</div>
              <div className="text-gray-600">Cost savings on inventory</div>
            </div>
          </div>
        </motion.div>

        {/* CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="text-center mt-16"
        >
          <p className="text-lg text-gray-600 mb-6">
            Ready to see how WSTOCK can transform your business?
          </p>
          <motion.a
            href="#contact"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="inline-flex items-center px-8 py-4 bg-wstock-green text-white font-semibold rounded-lg hover:bg-green-600 transition-colors duration-200 shadow-lg"
          >
            Schedule a Demo
          </motion.a>
        </motion.div>
      </div>
    </section>
  )
}
