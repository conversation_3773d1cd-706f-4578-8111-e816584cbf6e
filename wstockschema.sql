--
-- PostgreSQL database dump
--

-- Dumped from database version 17.2 (Homebrew)
-- Dumped by pg_dump version 17.2 (Homebrew)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: unaccent; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS unaccent WITH SCHEMA public;


--
-- Name: EXTENSION unaccent; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION unaccent IS 'text search dictionary that removes accents';


--
-- Name: xml2; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS xml2 WITH SCHEMA public;


--
-- Name: EXTENSION xml2; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION xml2 IS 'XPath querying and XSLT';


--
-- Name: addshoptoclipbrdasuser(integer, integer, character varying, character varying); Type: PROCEDURE; Schema: public; Owner: dba
--

CREATE PROCEDURE public.addshoptoclipbrdasuser(IN xadr_id integer, IN xrepli_id integer, IN kodfilter character varying, IN username character varying)
    LANGUAGE plpgsql
    AS $$
  declare MCursor scroll cursor for select mat_id as mmatid,onshop(mat_id,xadr_id,xrepli_id) as mpcs,kod as xkod,model as xmodel from product where kod like kodfilter and onshop(mat_id,xadr_id,xrepli_id) > 0;
begin
  --Deviaty clipboard
  --  delete from tempmove where clip_id = 9 and user_name = username;
  for rec in mCursor LOOP
    if((select 1 from tempmove where mat_id = rec.mmatid and tempmove_info = '' and clip_id = 9 and user_name = username) = 1) then
      update tempmove set pcs = pcs+mpcs,discount = 0 where mat_id = rec.mmatid and clip_id = 9 and tempmove_info = '' and user_name = username;
    else
      insert into tempmove( mat_id,
        pcs,price,tax,discount,kod,model,user_name,clip_id,tempmove_info ) 
        values( rec.mmatid,rec.mpcs,0,0,0,rec.xkod,rec.xmodel,username,9,'' );
    end if;
  end loop;
end
$$;


ALTER PROCEDURE public.addshoptoclipbrdasuser(IN xadr_id integer, IN xrepli_id integer, IN kodfilter character varying, IN username character varying) OWNER TO dba;

--
-- Name: bata(numeric); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.bata(xcc numeric) RETURNS numeric
    LANGUAGE plpgsql
    AS $$
begin
  --BATA PRICES
  if xcc = 0 then
    return(0);
  elseif xcc < 500 then
    return(round(xcc*2,-1)/2-1);
  elseif xcc < 5000 then
    return((round(xcc/5,-1)/2-1)*10);
  elseif xcc < 100000 then
    return((round(xcc/50,-1)/2-1)*100);
  else
    return(round(xcc*2,-1)/2-1);
  end if;
  return(-1);
end;
$$;


ALTER FUNCTION public.bata(xcc numeric) OWNER TO dba;

--
-- Name: byamount(numeric); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.byamount(xcc numeric) RETURNS numeric
    LANGUAGE plpgsql
    AS $$
begin
  if xcc = 0 then
    return(0);
  elseif xcc < 10 then
    return(floor(xcc*10+.5)/10);
  elseif xcc < 100 then
    return(floor(xcc+.5));
  elseif xcc < 1000 then
    return(floor(xcc/5+.5)*5);
  else
    return(floor(xcc/10+.5)*10);
  end if;
  return(-1);
end;
$$;


ALTER FUNCTION public.byamount(xcc numeric) OWNER TO dba;

--
-- Name: check_serial_pcs(); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.check_serial_pcs() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
 declare xpcs integer;
    BEGIN
     select sum(pcs) - (select count(*) from thserials where move_id=NEW.move_id and mat_id=NEW.mat_id) into strict xpcs from m_detail where move_id=NEW.move_id and mat_id=NEW.mat_id;
        IF xpcs < 0 THEN
            RAISE EXCEPTION 'Number of PCS for this mat_id in move_id exceeded';
        END IF;
        RETURN NEW; -- result is ignored since this is an AFTER trigger
    END;
$$;


ALTER FUNCTION public.check_serial_pcs() OWNER TO dba;

--
-- Name: check_serial_string(); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.check_serial_string() RETURNS trigger
    LANGUAGE plpgsql
    AS $$		
		BEGIN
		IF NEW.thserial = '' THEN
			RAISE EXCEPTION 'Serial must be set';
		END IF;
        RETURN NEW;
END
$$;


ALTER FUNCTION public.check_serial_string() OWNER TO dba;

--
-- Name: check_serial_string_in_clipboard(); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.check_serial_string_in_clipboard() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
	IF NEW.thserial = '' THEN
			NEW.thserial = null;
	END IF;
	RETURN NEW;
END
$$;


ALTER FUNCTION public.check_serial_string_in_clipboard() OWNER TO dba;

--
-- Name: convert_to_integer(text); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.convert_to_integer(v_input text) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE v_int_value INTEGER DEFAULT NULL;
BEGIN
    BEGIN
        v_int_value := v_input::INTEGER;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Invalid integer value: "%".  Returning NULL.', v_input;
        RETURN NULL;
    END;
RETURN v_int_value;
END;
$$;


ALTER FUNCTION public.convert_to_integer(v_input text) OWNER TO dba;

--
-- Name: delmove(integer); Type: PROCEDURE; Schema: public; Owner: dba
--

CREATE PROCEDURE public.delmove(IN ccislo integer)
    LANGUAGE plpgsql
    AS $$
declare deleted integer;
begin
  delete from thserials where move_id = ccislo;
  GET DIAGNOSTICS deleted=ROW_COUNT;
  RAISE LOG 'delmove deleted % thserials for move_id=%',deleted,ccislo;
  select count(*) into deleted from m_detail where move_id=ccislo;
  RAISE LOG 'delmove will be deleted % rows in m_detail for move_id=%',deleted,ccislo;
  delete from movement where move_id = ccislo;
  GET DIAGNOSTICS deleted=ROW_COUNT;
  RAISE LOG 'delmove deleted % movement for move_id=%',deleted,ccislo;
  delete from m_detail where move_id = ccislo;

end;
$$;


ALTER PROCEDURE public.delmove(IN ccislo integer) OWNER TO dba;

--
-- Name: delnorm(integer, integer); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.delnorm(xnormid integer, xrepliid integer) RETURNS void
    LANGUAGE plpgsql
    AS $$
begin
  delete from norm where norm_id = xnormid and norm_repli_id = xrepliid;
end;
$$;


ALTER FUNCTION public.delnorm(xnormid integer, xrepliid integer) OWNER TO dba;

--
-- Name: discountasuser(integer, numeric, numeric, character varying, integer, character varying); Type: PROCEDURE; Schema: public; Owner: dba
--

CREATE PROCEDURE public.discountasuser(IN xadding integer, IN xpercent numeric, IN xamount numeric, IN xfilter character varying, IN clipid integer, IN username character varying)
    LANGUAGE plpgsql
    AS $$

  declare xxsql character varying;
  xxd0 numeric;
  xxd1 numeric;
  isMore numeric;
begin
  if xadding = 1 then 
    if xpercent = 1 then 
      xxsql := 'update tempmove set discount = discount + ' || xamount::text || ', fifo_price=' || xamount::text || '  where clip_id = ' || clipid || ' and user_name = '''||username||'''';
    else 
      select sum(pcs*price*(1-discount/100)) into xxd0 from tempmove where clip_id = clipid and user_name = username;
      select sum(pcs*price) into xxd1 from tempmove where clip_id = clipid and user_name = username;
      xxsql := 'update tempmove set discount = ' || (100*(xamount-xxd0+xxd1)/xxd1)::text || ', fifo_price=fifo_price+' || (100*(xamount-xxd0+xxd1)/xxd1)::text || ' where clip_id = ' || clipid || ' and user_name = '''||username||'''';
    end if;
  else 
    if xpercent = 1 then 
      xxsql := 'update tempmove set discount = ' || xamount::text || ', fifo_price=0 where clip_id = ' || clipid || ' and user_name = '''||username||'''';
    else 
      if length(xfilter) <= 1 then
	select sum(pcs*price) into xxd0 from tempmove where clip_id = clipid and user_name = username;
      	select sum(pcs*price) into xxd1 from tempmove where clip_id = clipid and user_name = username;
      else
	select sum(pcs*price) into xxd0 from tempmove where clip_id = clipid and user_name = username and not ( xfilter  );
      	select sum(pcs*price) into xxd1 from tempmove where clip_id = clipid and user_name = username and not ( xfilter  );
      end if;
      xxsql := 'update tempmove set discount = ' || (100*xamount/xxd0)::text || ', fifo_price=0 where clip_id = ' || clipid || ' and user_name = '''||username||'''';
    end if;
  end if;
  if length(xfilter) > 1 then
    xxsql := xxsql || ' and ( ' || xfilter || ' ) ';
  end if;
  xxsql := xxsql  || ' and  mat_id not in (241209) ';
  execute xxsql;

end
$$;


ALTER PROCEDURE public.discountasuser(IN xadding integer, IN xpercent numeric, IN xamount numeric, IN xfilter character varying, IN clipid integer, IN username character varying) OWNER TO dba;

--
-- Name: dyninfo(character varying, character varying, integer, integer); Type: PROCEDURE; Schema: public; Owner: dba
--

CREATE OR REPLACE PROCEDURE public.dyninfo(IN mmodel character varying, IN mkod character varying, IN mmatid integer, IN mrepli integer)
    LANGUAGE plpgsql
    AS $$
  declare likemodel varchar(50);
  declare likemodel2 varchar(50);
  likekod varchar(50);
  altercom TEXT;
  last_nr integer;
  msCursor3 scroll cursor for select shop as xshop from dyninfoy where shop not ilike '% 0' and shop not ilike '% 1' and shop not ilike '% 2'  and shop <> 's2d' and shop <> 's2f' and shop <> 'S2f' and shop <> 'O.G.'  group by shop order by shop asc;
  msCursor scroll cursor for select shop as xshop from dyninfoy where shop ilike '% 0' group by shop order by shop asc;
  msCursor2 scroll cursor for select shop as xshop from dyninfoy where shop ilike '% 1' group by shop order by shop asc;
  msCursor33 scroll cursor for select shop as xshop from dyninfoy where shop ilike '% 2' group by shop order by shop asc;
  xdCursor scroll cursor for select mat_id as xmat_id,kod as xkod,model as xmodel from dyninfoy group by mat_id,kod,model;
  ydCursor scroll cursor (xmmatid integer) for select shop,remains from dyninfoy where mat_id = xmmatid;
  rec RECORD;
  rec2 RECORD;
begin
  -- Do we need a recounting?
  -- WARNING, ONLY IF TOO OLD.... SEE the constant
   select coalesce( max(move_id),0 )-200 into strict last_nr from movement where stock_id1 is not null or stock_id2 is not null;
  if(last_nr > (select remains from dyninfox where mat_id = 0)) then
    call dyninfo_recount_all();
  end if;
  -- Default filter
  if mmodel <> '' then 
    likemodel := mmodel || '%';
    likemodel2 := replace(mmodel,'.','') || '%';
  end if;
  if mkod <> '' then 
    likekod := mkod || '%';
  end if;
  drop table if EXISTS dyninfo;
  drop table if EXISTS dyninfoy;
  create unlogged table dyninfoy(
    mat_id integer not null,
    repli_id integer not null,
    kod varchar(255) null,
    model varchar(255) not null,
    shop varchar(255) not null,
    remains integer not null,
    primary key(mat_id,repli_id,shop)
    );
  raise notice 'dyninfoy done';
  if mrepli = -1 then
    insert into dyninfoy select mat_id,'0',kod,model,shop,sum(remains) from dyninfox where (model ilike likemodel or model ilike likemodel2 or kod ilike likekod or mat_id = mmatid) and mat_id <> 0 and shop <> 's2d' and shop <> 's2f' and shop <> 'S2f' and shop <> 'O.G.' group by mat_id,kod,model,shop on conflict do nothing;
  else
    insert into dyninfoy select mat_id,repli_id,kod,model,shop,remains from dyninfox where repli_id = mrepli and(model ilike likemodel or model ilike likemodel2 or kod ilike likekod or mat_id = mmatid) and mat_id <> 0  and shop <> 's2d' and shop <> 's2f' and shop <> 'S2f' and shop <> 'O.G.' group by repli_id,mat_id,kod,model,shop,remains on conflict do nothing ;
  end if;
  altercom := '';
  raise notice 'ac done';
  for rec in msCursor3 LOOP
    raise notice 'fetchx done % % %',rec.xshop,rec.xshop,rec.xshop;    
    altercom := altercom || ', "' || rec.xshop || '" integer ';
  end loop;
  raise notice 'l1 done';
  for rec in msCursor LOOP
    altercom := altercom || ', "' || rec.xshop || '" integer ';
  end loop;
  raise notice 'l2 done';
  for rec in msCursor2 LOOP
    altercom := altercom || ', "' || rec.xshop || '" integer ';
  end loop;
  raise notice 'l3 done';
  for rec in msCursor33 LOOP
    altercom := altercom || ', "' || rec.xshop || '" integer ';
  end loop;
  altercom := 'create table dyninfo(mat_id integer not null,kod varchar(255) null,model varchar(255) null' || altercom || ',primary key(mat_id))';
  raise notice ' % ', altercom;
  execute altercom;
  for rec in xdCursor LOOP
    insert into dyninfo( mat_id,kod,model ) values ( rec.xmat_id,rec.xkod,rec.xmodel ) on conflict do nothing;
    altercom := '';
    for rec2 in ydCursor(xmmatid:=rec.xmat_id) LOOP
      altercom := altercom || ', "' || rec2.shop || '"=' || rec2.remains;
    end loop;
    altercom := 'update dyninfo set kod=kod ' || altercom || ' where mat_id=' || rec.xmat_id;
    raise notice ' % ', altercom;
    execute altercom;
  end loop;
  raise notice ' last insert';
insert into dyninfo( mat_id,model ) values( 0,' (' || mmodel || ') (' || mkod || ') (' || mmatid || ') ' || mrepli ); 
end;
$$;


ALTER PROCEDURE public.dyninfo(IN mmodel character varying, IN mkod character varying, IN mmatid integer, IN mrepli integer) OWNER TO dba;

--
-- Name: dyninfo_recount_all(); Type: PROCEDURE; Schema: public; Owner: dba
--

CREATE PROCEDURE public.dyninfo_recount_all()
    LANGUAGE plpgsql
    AS $$
begin
  drop table if exists dyninfox;
  create unlogged table dyninfox(
    mat_id integer not null,
    repli_id integer not null,
    kod varchar(4) null,
    model varchar(255) null,
    shop varchar(255) not null,
    remains integer null,
    primary key(mat_id,repli_id,shop)
    );
  insert into dyninfox( mat_id,repli_id,shop,remains ) 
    select mat_id,repli_id,firma,sum(remains) as sr from stockremains3 group by mat_id,repli_id,firma having sum(remains) <> 0;
  insert into dyninfox( mat_id,repli_id,shop,remains ) 
    select mat_id,repli_id,cast(adr_id as varchar(255)),sum(remains) as sr from shopremains2 group by mat_id,repli_id,adr_id having sum(remains) <> 0;
  insert into dyninfox( mat_id,repli_id,shop,remains ) 
    values( 0,-1,'x',(select max(move_id) from movement where stock_id1 is not null or stock_id2 is not null) ) ;
	--  update dyninfox as d set kod=  (select kod from product where mat_id = d.mat_id), model= (select model from product where mat_id = d.mat_id);
    UPDATE dyninfox AS d SET kod = p.kod, model = p.model FROM product AS p WHERE p.mat_id = d.mat_id;
  --update dyninfox as d set shop= coalesce(( select firma || ' ' || adr_id from address where adr_id = coalesce((select adr_id from stock_detail where stock_id ilike d.shop),0)),d.shop) ;
  create index dyninfox_shop on dyninfox(shop asc);
  create index dyninfox_model on dyninfox(model asc);
  create index dyninfox_repli_id on dyninfox(repli_id asc);
  create index dyninfox_remains on dyninfox(remains asc);
end;
$$;


ALTER PROCEDURE public.dyninfo_recount_all() OWNER TO dba;

--
-- Name: dyninfo_recount_one_movement(integer[]); Type: PROCEDURE; Schema: public; Owner: dba
--

CREATE PROCEDURE public.dyninfo_recount_one_movement(IN matids integer[])
    LANGUAGE plpgsql
    AS $$
begin 
  delete from dyninfox where mat_id = ANY(matids);
  insert into dyninfox( mat_id,repli_id,shop,remains ) 
    select mat_id,repli_id,firma,sum(remains) as sr from stockremains3 where mat_id = ANY(matids) group by mat_id,repli_id,firma having sum(remains) <> 0;
  insert into dyninfox( mat_id,repli_id,shop,remains ) 
    select mat_id,repli_id,cast(adr_id as varchar(255)),sum(remains) as sr from shopremains2 where mat_id = ANY(matids) group by mat_id,repli_id,adr_id having sum(remains) <> 0;
    UPDATE dyninfox AS d SET kod = p.kod, model = p.model FROM product AS p WHERE p.mat_id = d.mat_id;
end;
$$;


ALTER PROCEDURE public.dyninfo_recount_one_movement(IN matids integer[]) OWNER TO dba;

--
-- Name: getlastreceiveddate(integer, character varying); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.getlastreceiveddate(xmatid integer, xstockid character varying) RETURNS text
    LANGUAGE plpgsql
    AS $$

  declare lastd date;
	
begin

	select d1 into lastd from movement m join m_detail d on m.move_id=d.move_id where d.mat_id=xmatid and m.stock_id2 = xstockid order by d1 desc limit 1;

  return lastd;
end;
$$;


ALTER FUNCTION public.getlastreceiveddate(xmatid integer, xstockid character varying) OWNER TO dba;

--
-- Name: getlastreceivedfromstock(integer, character varying); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.getlastreceivedfromstock(xmatid integer, xstockid character varying) RETURNS text
    LANGUAGE plpgsql
    AS $$

  declare laststock varchar(255);
	
begin

	select m.stock_id1 into laststock from movement m join m_detail d on m.move_id=d.move_id where d.mat_id=xmatid and m.stock_id2 = xstockid order by d1 desc limit 1;

  return laststock;
end;
$$;


ALTER FUNCTION public.getlastreceivedfromstock(xmatid integer, xstockid character varying) OWNER TO dba;

--
-- Name: getmovementserials(integer, integer); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.getmovementserials(xmatid integer, xmoveid integer) RETURNS text
    LANGUAGE plpgsql
    AS $$

  declare altercom text;
  rec RECORD;
  xCursor scroll cursor for
select thserial  from thserials t where mat_id=xmatid and t.move_id = xmoveid order by thserial;
begin

  altercom := '';
  for rec in xCursor LOOP
    altercom := altercom ||  ', ' || rec.thserial ;
  end loop;

  return(substring(altercom from 3));
end;
$$;


ALTER FUNCTION public.getmovementserials(xmatid integer, xmoveid integer) OWNER TO dba;

--
-- Name: getserials(integer, character varying); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.getserials(xmatid integer, xstockid character varying) RETURNS text
    LANGUAGE plpgsql
    AS $$

  declare altercom text;
  rec RECORD;
  xCursor scroll cursor for select case when sum(kusov)<0 then '-'||thserial else thserial end vv from (
select thserial, case when stock_id1=xstockid then -1 else 1 end as kusov from thserials t join movement m on m.move_id=t.move_id where mat_id=xmatid and t.move_id in (select move_id from movement where stock_id1=xstockid or stock_id2=xstockid) order by d1,t.move_id ) ss group by thserial having sum(kusov)<>0;
begin

  altercom := '';
  for rec in xCursor LOOP
    altercom := altercom ||  ', ' || rec.vv ;
  end loop;

  return(substring(altercom from 3));
end;
$$;


ALTER FUNCTION public.getserials(xmatid integer, xstockid character varying) OWNER TO dba;

--
-- Name: gw_karty3731model(); Type: PROCEDURE; Schema: public; Owner: dba
--

CREATE PROCEDURE public.gw_karty3731model()
    LANGUAGE plpgsql
    AS $$
  declare pom integer;
  declare ucto_rok integer;
  fsum integer;
  curMain scroll cursor for
    select md.pcs as xxtotalpcs,mo.number as xnumber,
      md.price*(1-md.discount/100) as xprice,mo.d1 as xd1,mo.m_type_id as xmtid,case when mo.m_type_id in( 87,117,152,147,149 ) then 'F' else 'R' end as plusminus,product.mat_id as xxkod,
      mo.move_id as xmoveid,md.mat_id as xmi,
	  case when mo.m_type_id = 117 then 'ANO' else 'NIE' end as dobropis
      from m_detail as md
        ,movement as mo
        ,product
      /* FILTER NA DATUM */
      where year(mo.d1) = ucto_rok
      and(md.move_id = mo.move_id)
      and(product.mat_id = md.mat_id)
      and((m_type_id in( 87,89,92,97 ) and mo.adr_id in( 3731 ) ) or m_type_id in (152,153) or(m_type_id in( 89,92,117 ) and mo.text2 = '3731')  or(m_type_id in( 147,149,148 ) and mo.emp_id = 'optima'))
      order by plusminus desc,dobropis asc, xd1 asc,xmtid desc,xprice desc;
  -- 20.6.2016 nech najprv 117tky vybavi som modifikoval order
  cur2 scroll cursor (xxxkod integer, xd1 date) for
    select matid as umatid,remains as upcs,n_price as uprice
      from yuctogw3731model
      where xx = 'R' and remains > 0 and mat_id = xxxkod and 
      (d1 <= xd1 or (year(d1) = year(xd1) and month(d1) = month(xd1) ) )
      order by d1 asc,n_price desc;
  -- 117tky potrebuju rovnaku nakupnu
  -- NONONO 22.5.2018 modifikoval som order na d1 desc, nech necha potrebne veci pre ostatne predaje
  cur3 scroll cursor (xxxkod integer, xd1 date, xprice decimal(11,2) )  for
    select matid as umatid,remains as upcs,n_price as uprice
      from yuctogw3731model
      where xx = 'R' and remains > 0 and mat_id = xxxkod and 
      (d1 <= xd1 or (year(d1) = year(xd1) and month(d1) = month(xd1) ) )
      and n_price = xprice
      order by d1 desc,n_price desc;
      rec RECORD;
      rec2 RECORD;
      rec3 RECORD;
begin
  ucto_rok := 2024;
    drop table if exists yuctogw3731model;
  create table yuctogw3731model(
    matid serial,
    xx varchar(1) null,
    kod integer null,
    n_price decimal(11,2) null default 0,
    s_price decimal(11,2) null default 0,
    d1 date null,
    fnumber integer null,
    pcs integer null,
    remains integer null,
    beforeonstock integer null,
    move_id integer null,
    mat_id integer null,
    doc_nr integer null default 0,
    primary key(matid)
    );
  create index xx3731idx on yuctogw3731model(xx asc);
  create index remains3731idx on yuctogw3731model(remains asc);
  create index kod3731idx on yuctogw3731model(kod asc);
  create index n_price3731idx on yuctogw3731model(n_price asc);
  create index pcs3731idx on yuctogw3731model(pcs asc);
  create index move_id3731idx on yuctogw3731model(move_id asc);
    for rec in curMain LOOP
    fsum := rec.xxtotalpcs;
    if( rec.xmtid in( 87,152,147,149 ) and fsum > 0) then
      for rec2 in cur2 ( xxxkod := rec.xxkod, xd1 := rec.xd1 ) LOOP
        if fsum > 0 then
          select sum(remains) into strict pom from yuctogw3731model where n_price = rec2.uprice and xx = 'R' and remains > 0 and mat_id = rec.xxkod and 
          (d1 <= rec.xd1 or (year(d1) = year(rec.xd1) and month(d1) = month(rec.xd1) ) );
          if rec2.upcs >= fsum then
            update yuctogw3731model set remains = remains-fsum where matid = rec2.umatid;
            insert into yuctogw3731model( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) 
              values( 'F',rec.xxkod,rec2.uprice,rec.xprice,rec.xd1,-fsum,0,pom,rec.xnumber,rec.xmoveid,rec.xmi ) ;
            fsum := 0;
          else
            update yuctogw3731model set remains = 0 where matid = rec2.umatid;
            insert into yuctogw3731model( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) 
              values( 'F',rec.xxkod,rec2.uprice,rec.xprice,rec.xd1,-rec2.upcs,0,pom,rec.xnumber,rec.xmoveid,rec.xmi ) ;
            fsum := fsum - rec2.upcs;
          end if;
        end if;
      end loop;
      if fsum <> 0 then
        insert into yuctogw3731model( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) 
          values( '!',rec.xxkod,0,rec.xprice,rec.xd1,-fsum,0,0,rec.xnumber,rec.xmoveid,rec.xmi ); 
      end if;
    elseif (rec.xmtid in( 117 ) and fsum > 0) then
      /*predaje 117 hladame rovnaku n_price */
      for rec3 in cur3  (xxxkod := rec.xxkod, xd1 := rec.xd1, xprice := rec.xprice ) LOOP  
        if fsum > 0 then
          select sum(remains) into strict pom from yuctogw3731model where n_price = rec3.uprice and xx = 'R' and remains > 0 and mat_id = rec.xxkod and 
          (d1 <= rec.xd1 or (year(d1) = year(rec.xd1) and month(d1) = month(rec.xd1) ) );
          if rec3.upcs >= fsum then
            update yuctogw3731model set remains = remains-fsum where matid = rec3.umatid;
            insert into yuctogw3731model( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) values 
              ( 'F',rec.xxkod,rec3.uprice,rec.xprice,rec.xd1,-fsum,0,pom,rec.xnumber,rec.xmoveid,rec.xmi ) ;
            fsum := 0;
          else
            update yuctogw3731model set remains = 0 where matid = rec3.umatid;
            insert into yuctogw3731model( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) values( 'F',rec.xxkod,rec3.uprice,rec.xprice,rec.xd1,-rec3.upcs,0,pom,rec.xnumber,rec.xmoveid,rec.xmi ) ;
            fsum := fsum - rec3.upcs;
          end if;
        end if;
      end loop;
      if fsum <> 0 then
        insert into yuctogw3731model( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) values( '!',rec.xxkod,0,rec.xprice,rec.xd1,-fsum,0,0,rec.xnumber,rec.xmoveid,rec.xmi ); 
      /* prijemky */
      end if;
    else 
	  if rec.xmtid in ( 87,117,152,147,149 ) then fsum := -fsum;
	  end if;
      insert into yuctogw3731model( xx,kod,n_price,d1,pcs,remains,fnumber,move_id,mat_id ) values( 'R',rec.xxkod,rec.xprice,rec.xd1,fsum,fsum,rec.xnumber,rec.xmoveid,rec.xmi ) ;
    end if;
  end loop;
end;
$$;


ALTER PROCEDURE public.gw_karty3731model() OWNER TO dba;

--
-- Name: gw_karty5039model(); Type: PROCEDURE; Schema: public; Owner: dba
--

CREATE PROCEDURE public.gw_karty5039model()
    LANGUAGE plpgsql
    AS $$
  declare pom integer;
  declare ucto_rok integer;
  fsum integer;
  curMain scroll cursor for
    select md.pcs as xxtotalpcs,mo.number as xnumber,
      md.price*(1-md.discount/100) as xprice,mo.d1 as xd1,mo.m_type_id as xmtid,case when mo.m_type_id in( 87,117,150,147,149,137,139,26 ) then 'F' else 'R' end as plusminus,product.mat_id as xxkod,
      mo.move_id as xmoveid,md.mat_id as xmi,
	  case when mo.m_type_id = 117 then 'ANO' else 'NIE' end as dobropis
      from m_detail as md
        ,movement as mo
        ,product
      /* FILTER NA DATUM */
      where year(mo.d1) = ucto_rok
      and(md.move_id = mo.move_id)
      and(product.mat_id = md.mat_id)
      and((m_type_id in( 87,89,92,97 ) and mo.adr_id in( 5039 ) ) or m_type_id in (150,151,137,138,139,140,26,138) or(m_type_id in( 89,92,117 ) and mo.text2 = '5039')  or(m_type_id in( 147,149,148 ) and mo.emp_id = 'eurovea'))
      order by plusminus desc,dobropis asc, xd1 asc,xmtid desc,xprice desc;
  -- 20.6.2016 nech najprv 117tky vybavi som modifikoval order
  cur2 scroll cursor (xxxkod integer, xd1 date) for
    select matid as umatid,remains as upcs,n_price as uprice
      from yuctogw5039model
      where xx = 'R' and remains > 0 and mat_id = xxxkod and 
      (d1 <= xd1 or (year(d1) = year(xd1) and month(d1) = month(xd1) ) )
      order by d1 asc,n_price desc;
  -- 117tky potrebuju rovnaku nakupnu
  -- 22.5.2018 modifikoval som order na d1 desc, nech necha potrebne veci pre ostatne predaje
  cur3 scroll cursor (xxxkod integer, xd1 date, xprice decimal(11,2) )  for
    select matid as umatid,remains as upcs,n_price as uprice
      from yuctogw5039model
      where xx = 'R' and remains > 0 and mat_id = xxxkod and 
      (d1 <= xd1 or (year(d1) = year(xd1) and month(d1) = month(xd1) ) )
      and n_price = xprice
      order by d1 desc,n_price desc;
      rec RECORD;
      rec2 RECORD;
      rec3 RECORD;
begin
  ucto_rok := 2024;
    drop table if exists yuctogw5039model;
  create table yuctogw5039model(
    matid serial,
    xx varchar(1) null,
    kod integer null,
    n_price decimal(11,2) null default 0,
    s_price decimal(11,2) null default 0,
    d1 date null,
    fnumber integer null,
    pcs integer null,
    remains integer null,
    beforeonstock integer null,
    move_id integer null,
    mat_id integer null,
    doc_nr integer null default 0,
    primary key(matid)
    );
  create index xx5039idx on yuctogw5039model(xx asc);
  create index remains5039idx on yuctogw5039model(remains asc);
  create index kod5039idx on yuctogw5039model(kod asc);
  create index n_price5039idx on yuctogw5039model(n_price asc);
  create index pcs5039idx on yuctogw5039model(pcs asc);
  create index move_id5039idx on yuctogw5039model(move_id asc);
    for rec in curMain LOOP
    fsum := rec.xxtotalpcs;
    if( rec.xmtid in( 87,150,147,149,137,139,26 ) and fsum > 0) then
      for rec2 in cur2 ( xxxkod := rec.xxkod, xd1 := rec.xd1 ) LOOP
        if fsum > 0 then
          select sum(remains) into strict pom from yuctogw5039model where n_price = rec2.uprice and xx = 'R' and remains > 0 and mat_id = rec.xxkod and 
          (d1 <= rec.xd1 or (year(d1) = year(rec.xd1) and month(d1) = month(rec.xd1) ) );
          if rec2.upcs >= fsum then
            update yuctogw5039model set remains = remains-fsum where matid = rec2.umatid;
            insert into yuctogw5039model( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) 
              values( 'F',rec.xxkod,rec2.uprice,rec.xprice,rec.xd1,-fsum,0,pom,rec.xnumber,rec.xmoveid,rec.xmi ) ;
            fsum := 0;
          else
            update yuctogw5039model set remains = 0 where matid = rec2.umatid;
            insert into yuctogw5039model( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) 
              values( 'F',rec.xxkod,rec2.uprice,rec.xprice,rec.xd1,-rec2.upcs,0,pom,rec.xnumber,rec.xmoveid,rec.xmi ) ;
            fsum := fsum - rec2.upcs;
          end if;
        end if;
      end loop;
      if fsum <> 0 then
        insert into yuctogw5039model( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) 
          values( '!',rec.xxkod,0,rec.xprice,rec.xd1,-fsum,0,0,rec.xnumber,rec.xmoveid,rec.xmi ); 
      end if;
    elseif (rec.xmtid in( 117 ) and fsum > 0) then
      /*predaje 117 hladame rovnaku n_price */
      for rec3 in cur3  (xxxkod := rec.xxkod, xd1 := rec.xd1, xprice := rec.xprice ) LOOP  
        if fsum > 0 then
          select sum(remains) into strict pom from yuctogw5039model where n_price = rec3.uprice and xx = 'R' and remains > 0 and mat_id = rec.xxkod and 
          (d1 <= rec.xd1 or (year(d1) = year(rec.xd1) and month(d1) = month(rec.xd1) ) );
          if rec3.upcs >= fsum then
            update yuctogw5039model set remains = remains-fsum where matid = rec3.umatid;
            insert into yuctogw5039model( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) values 
              ( 'F',rec.xxkod,rec3.uprice,rec.xprice,rec.xd1,-fsum,0,pom,rec.xnumber,rec.xmoveid,rec.xmi ) ;
            fsum := 0;
          else
            update yuctogw5039model set remains = 0 where matid = rec3.umatid;
            insert into yuctogw5039model( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) values( 'F',rec.xxkod,rec3.uprice,rec.xprice,rec.xd1,-rec3.upcs,0,pom,rec.xnumber,rec.xmoveid,rec.xmi ) ;
            fsum := fsum - rec3.upcs;
          end if;
        end if;
      end loop;
      if fsum <> 0 then
        insert into yuctogw5039model( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) values( '!',rec.xxkod,0,rec.xprice,rec.xd1,-fsum,0,0,rec.xnumber,rec.xmoveid,rec.xmi ); 
      /* prijemky */
      end if;
    else 
	  if rec.xmtid in ( 87,117,150,147,149,137,139,26 ) then fsum := -fsum;
	  end if;
      insert into yuctogw5039model( xx,kod,n_price,d1,pcs,remains,fnumber,move_id,mat_id ) values( 'R',rec.xxkod,rec.xprice,rec.xd1,fsum,fsum,rec.xnumber,rec.xmoveid,rec.xmi ) ;
    end if;
  end loop;
end;
$$;


ALTER PROCEDURE public.gw_karty5039model() OWNER TO dba;

--
-- Name: gw_karty5040model(); Type: PROCEDURE; Schema: public; Owner: dba
--

CREATE PROCEDURE public.gw_karty5040model()
    LANGUAGE plpgsql
    AS $$
  declare pom integer;
  declare ucto_rok integer;
  fsum integer;
  curMain scroll cursor for
    select md.pcs as xxtotalpcs,mo.number as xnumber,
      md.price*(1-md.discount/100) as xprice,mo.d1 as xd1,mo.m_type_id as xmtid,case when mo.m_type_id in( 87,117,156,147,149 ) then 'F' else 'R' end as plusminus,product.mat_id as xxkod,
      mo.move_id as xmoveid,md.mat_id as xmi,
	  case when mo.m_type_id = 117 then 'ANO' else 'NIE' end as dobropis
      from m_detail as md
        ,movement as mo
        ,product
      /* FILTER NA DATUM */
      where year(mo.d1) = ucto_rok
      and(md.move_id = mo.move_id)
      and(product.mat_id = md.mat_id)
      and((m_type_id in( 87,89,92,97 ) and mo.adr_id in( 5040 ) ) or m_type_id in (156,157) or(m_type_id in( 89,92,117 ) and mo.text2 = '5040')  or(m_type_id in( 147,149,148 ) and mo.emp_id = 'ebutik'))
      order by plusminus desc,dobropis asc, xd1 asc,xmtid desc,xprice desc;
  -- 20.6.2016 nech najprv 117tky vybavi som modifikoval order
  cur2 scroll cursor (xxxkod integer, xd1 date) for
    select matid as umatid,remains as upcs,n_price as uprice
      from yuctogw5040model
      where xx = 'R' and remains > 0 and mat_id = xxxkod and 
      (d1 <= xd1 or (year(d1) = year(xd1) and month(d1) = month(xd1) ) )
      order by d1 asc,n_price desc;
  -- 117tky potrebuju rovnaku nakupnu
  -- NO NO NO22.5.2018 modifikoval som order na d1 desc, nech necha potrebne veci pre ostatne predaje
  cur3 scroll cursor (xxxkod integer, xd1 date, xprice decimal(11,2) )  for
    select matid as umatid,remains as upcs,n_price as uprice
      from yuctogw5040model
      where xx = 'R' and remains > 0 and mat_id = xxxkod and 
      (d1 <= xd1 or (year(d1) = year(xd1) and month(d1) = month(xd1) ) )
      and n_price = xprice
      order by d1 desc,n_price desc;
      rec RECORD;
      rec2 RECORD;
      rec3 RECORD;
begin
  ucto_rok := 2024;
    drop table if exists yuctogw5040model;
  create table yuctogw5040model(
    matid serial,
    xx varchar(1) null,
    kod integer null,
    n_price decimal(11,2) null default 0,
    s_price decimal(11,2) null default 0,
    d1 date null,
    fnumber integer null,
    pcs integer null,
    remains integer null,
    beforeonstock integer null,
    move_id integer null,
    mat_id integer null,
    doc_nr integer null default 0,
    primary key(matid)
    );
  create index xx5040idx on yuctogw5040model(xx asc);
  create index remains5040idx on yuctogw5040model(remains asc);
  create index kod5040idx on yuctogw5040model(kod asc);
  create index n_price5040idx on yuctogw5040model(n_price asc);
  create index pcs5040idx on yuctogw5040model(pcs asc);
  create index move_id5040idx on yuctogw5040model(move_id asc);
    for rec in curMain LOOP
    fsum := rec.xxtotalpcs;
    if( rec.xmtid in( 87,156,147,149 ) and fsum > 0) then
      for rec2 in cur2 ( xxxkod := rec.xxkod, xd1 := rec.xd1 ) LOOP
        if fsum > 0 then
          select sum(remains) into strict pom from yuctogw5040model where n_price = rec2.uprice and xx = 'R' and remains > 0 and mat_id = rec.xxkod and 
          (d1 <= rec.xd1 or (year(d1) = year(rec.xd1) and month(d1) = month(rec.xd1) ) );
          if rec2.upcs >= fsum then
            update yuctogw5040model set remains = remains-fsum where matid = rec2.umatid;
            insert into yuctogw5040model( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) 
              values( 'F',rec.xxkod,rec2.uprice,rec.xprice,rec.xd1,-fsum,0,pom,rec.xnumber,rec.xmoveid,rec.xmi ) ;
            fsum := 0;
          else
            update yuctogw5040model set remains = 0 where matid = rec2.umatid;
            insert into yuctogw5040model( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) 
              values( 'F',rec.xxkod,rec2.uprice,rec.xprice,rec.xd1,-rec2.upcs,0,pom,rec.xnumber,rec.xmoveid,rec.xmi ) ;
            fsum := fsum - rec2.upcs;
          end if;
        end if;
      end loop;
      if fsum <> 0 then
        insert into yuctogw5040model( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) 
          values( '!',rec.xxkod,0,rec.xprice,rec.xd1,-fsum,0,0,rec.xnumber,rec.xmoveid,rec.xmi ); 
      end if;
    elseif (rec.xmtid in( 117 ) and fsum > 0) then
      /*predaje 117 hladame rovnaku n_price */
      for rec3 in cur3  (xxxkod := rec.xxkod, xd1 := rec.xd1, xprice := rec.xprice ) LOOP  
        if fsum > 0 then
          select sum(remains) into strict pom from yuctogw5040model where n_price = rec3.uprice and xx = 'R' and remains > 0 and mat_id = rec.xxkod and 
          (d1 <= rec.xd1 or (year(d1) = year(rec.xd1) and month(d1) = month(rec.xd1) ) );
          if rec3.upcs >= fsum then
            update yuctogw5040model set remains = remains-fsum where matid = rec3.umatid;
            insert into yuctogw5040model( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) values 
              ( 'F',rec.xxkod,rec3.uprice,rec.xprice,rec.xd1,-fsum,0,pom,rec.xnumber,rec.xmoveid,rec.xmi ) ;
            fsum := 0;
          else
            update yuctogw5040model set remains = 0 where matid = rec3.umatid;
            insert into yuctogw5040model( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) values( 'F',rec.xxkod,rec3.uprice,rec.xprice,rec.xd1,-rec3.upcs,0,pom,rec.xnumber,rec.xmoveid,rec.xmi ) ;
            fsum := fsum - rec3.upcs;
          end if;
        end if;
      end loop;
      if fsum <> 0 then
        insert into yuctogw5040model( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) values( '!',rec.xxkod,0,rec.xprice,rec.xd1,-fsum,0,0,rec.xnumber,rec.xmoveid,rec.xmi ); 
      /* prijemky */
      end if;
    else 
	  if rec.xmtid in ( 87,117,156,147,149 ) then fsum := -fsum;
	  end if;
      insert into yuctogw5040model( xx,kod,n_price,d1,pcs,remains,fnumber,move_id,mat_id ) values( 'R',rec.xxkod,rec.xprice,rec.xd1,fsum,fsum,rec.xnumber,rec.xmoveid,rec.xmi ) ;
    end if;
  end loop;
end;
$$;


ALTER PROCEDURE public.gw_karty5040model() OWNER TO dba;

--
-- Name: gw_karty5117model(); Type: PROCEDURE; Schema: public; Owner: dba
--

CREATE PROCEDURE public.gw_karty5117model()
    LANGUAGE plpgsql
    AS $$
  declare pom integer;
  ucto_rok integer;
  fsum integer;
  curMain  scroll cursor for
    select md.pcs as xxtotalpcs,mo."number" as xnumber,
      md.price*(1-md.discount/100) as xprice,mo.d1 as xd1,mo.m_type_id as xmtid,case when mo.m_type_id in( 130,117,145 ) then 'F' else 'R' end as plusminus,product.mat_id as xxkod,
      mo.move_id as xmoveid,md.mat_id as xmi,
          case when mo.m_type_id = 117 then 'ANO' else 'NIE' end as dobropis
      from m_detail as md
        ,movement as mo
        ,product
      where year(mo.d1) = ucto_rok
      and(md.move_id = mo.move_id)
      and(product.mat_id = md.mat_id)
      and (m_type_id in( 130,131,134,135,145,146) 
          or(m_type_id in( 117 ) and mo.text2 = '5117') )
      order by plusminus desc, dobropis asc, xd1 asc,xprice desc;
          -- 26.6.2023 doplnene 117 a preto aj order zmeneny podla prikladu z 5039 proc
  cur2  scroll cursor (xxxkod integer, xd1 date)  for
    select matid as umatid,remains as upcs,n_price as uprice
      from yuctogw5117model
      where xx = 'R' and remains > 0 and mat_id = xxxkod and
                (d1 <= xd1 or (year(d1) = year(xd1) and month(d1) = month(xd1) ) )
      order by d1 asc,n_price desc;
        -- pridavam cur3 podla prikladu z 5039   
  -- hack - specialny odpis dvoch vykricnikov kvoli kurzom
  -- and ( n_price = xprice or (n_price = 913.43 and xprice = 881.35) or (n_price = 814.52 and xprice = 801.00) )
        cur3 scroll cursor (xxxkod integer, xd1 date, xprice decimal(11,2) )  for
    select matid as umatid,remains as upcs,n_price as uprice
      from yuctogw5117model
      where xx = 'R' and remains > 0 and mat_id = xxxkod and 
      (d1 <= xd1 or (year(d1) = year(xd1) and month(d1) = month(xd1) ) )
      and n_price = xprice 
      order by d1 desc,n_price desc;
      rec RECORD;
      rec2 RECORD;
          rec3 RECORD;
begin
  ucto_rok := 2024;
    drop table if exists yuctogw5117model;
  create table yuctogw5117model(
    matid SERIAL,
    xx varchar(1) null,
    kod integer null,
    n_price decimal(11,2) null default 0,
    s_price decimal(11,2) null default 0,
    d1 date null,
    fnumber integer null,
    pcs integer null,
    remains integer null,
    beforeonstock integer null,
    move_id integer null,
    mat_id integer null,
    doc_nr integer null default 0,
    primary key(matid)
    );
  create index xxidx on yuctogw5117model(xx asc);
  create index remainsidx on yuctogw5117model(remains asc);
  create index kodidx on yuctogw5117model(kod asc);
  create index n_priceidx on yuctogw5117model(n_price asc);
  create index pcsidx on yuctogw5117model(pcs asc);
  create index move_ididx on yuctogw5117model(move_id asc);
  for rec in curMain LOOP
        fsum := rec.xxtotalpcs;
    if(rec.xmtid in( 130,117,145 ) and fsum > 0) then
      for rec2 in cur2(xxxkod := rec.xxkod, xd1 := rec.xd1) LOOP
        if fsum > 0 then
          select sum(remains) into strict pom from yuctogw5117model where xx = 'R' and remains > 0 and mat_id = rec.xxkod and
                  (d1 <= rec.xd1 or (year(d1) = year(rec.xd1) and month(d1) = month(rec.xd1) ) );
          if rec2.upcs >= fsum then
            update yuctogw5117model set remains = remains-fsum where matid = rec2.umatid;
            insert into yuctogw5117model( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) values( 'F',rec.xxkod,rec2.uprice,rec.xprice,rec.xd1,-fsum,0,pom,rec.xnumber,rec.xmoveid,rec.xmi ) ;
            fsum := 0;
          else
            update yuctogw5117model set remains = 0 where matid = rec2.umatid;
            insert into yuctogw5117model( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) values( 'F',rec.xxkod,rec2.uprice,rec.xprice,rec.xd1,-rec2.upcs,0,pom,rec.xnumber,rec.xmoveid,rec.xmi ) ;
            fsum := fsum - rec2.upcs;
          end if;
        end if;
      end loop;
      if fsum <> 0 then
        insert into yuctogw5117model( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) values( '!',rec.xxkod,0,rec.xprice,rec.xd1,-fsum,0,0,rec.xnumber, rec.xmoveid,rec.xmi ) ;
      end if;
          elseif (rec.xmtid in( 117 ) and fsum > 0) then
      /*predaje 117 hladame rovnaku n_price */
      for rec3 in cur3  (xxxkod := rec.xxkod, xd1 := rec.xd1, xprice := rec.xprice ) LOOP  
        if fsum > 0 then
          select sum(remains) into strict pom from yuctogw5117model where n_price = rec3.uprice and xx = 'R' and remains > 0 and mat_id = rec.xxkod and 
          (d1 <= rec.xd1 or (year(d1) = year(rec.xd1) and month(d1) = month(rec.xd1) ) );
          if rec3.upcs >= fsum then
            update yuctogw5117model set remains = remains-fsum where matid = rec3.umatid;
            insert into yuctogw5117model( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) values 
              ( 'F',rec.xxkod,rec3.uprice,rec.xprice,rec.xd1,-fsum,0,pom,rec.xnumber,rec.xmoveid,rec.xmi ) ;
            fsum := 0;
          else
            update yuctogw5117model set remains = 0 where matid = rec3.umatid;
            insert into yuctogw5117model( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) values( 'F',rec.xxkod,rec3.uprice,rec.xprice,rec.xd1,-rec3.upcs,0,pom,rec.xnumber,rec.xmoveid,rec.xmi ) ;
            fsum := fsum - rec3.upcs;
          end if;
        end if;
      end loop;
      if fsum <> 0 then
        insert into yuctogw5117model( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) values( '!',rec.xxkod,0,rec.xprice,rec.xd1,-fsum,0,0,rec.xnumber,rec.xmoveid,rec.xmi ); 
      /* prijemky */
      end if;
    else 
      if rec.xmtid in( 130,117,145 ) then fsum := -fsum;
      end if;
      insert into yuctogw5117model( xx,kod,n_price,d1,pcs,remains,fnumber,move_id,mat_id ) values( 'R',rec.xxkod,rec.xprice,rec.xd1,fsum,fsum,rec.xnumber,rec.xmoveid,rec.xmi ) ;
    end if;

  end loop;

end;

$$;


ALTER PROCEDURE public.gw_karty5117model() OWNER TO dba;

--
-- Name: gw_karty_prve_prijemky_model(); Type: PROCEDURE; Schema: public; Owner: dba
--

CREATE PROCEDURE public.gw_karty_prve_prijemky_model()
    LANGUAGE plpgsql
    AS $$
  declare pom integer;
  declare ucto_rok integer;
  fsum integer;
  curMain scroll cursor for
    select md.pcs as xxtotalpcs,mo.number as xnumber,
      md.price*(1-md.discount/100) as xprice,mo.d1 as xd1,mo.m_type_id as xmtid,case when mo.m_type_id in( 87,113,117,154,147,149 ) then 'F' else 'R' end as plusminus,product.mat_id as xxkod,
      mo.move_id as xmoveid,md.mat_id as xmi,
	  case when mo.m_type_id = 117 then 'ANO' else 'NIE' end as dobropis
      from m_detail as md
        ,movement as mo
        ,product
      /* FILTER NA DATUM */
      where year(mo.d1) = ucto_rok
      and(md.move_id = mo.move_id)
      and(product.mat_id = md.mat_id)
	  /* 2 predajne sa osamostatnili a spolu mame 4 predajne filtrovane na text2 */
      and(((m_type_id in( 87,113,117,89,92,97 ) and mo.adr_id in( 4370,3699,318,3257,3405,3208,3430,3408,3418,3434,3432,3445,3624,3444 ) ) 
		   or m_type_id in (89,92,154,155) or ( m_type_id in (147,149,148) and emp_id='aupark') )
		  )
      and mo.text2 <> '3511'
      and mo.text2 <> '3512'
      and mo.text2 <> '3731'
      and mo.text2 <> '5039'
      and mo.text2 <> '5040'
      and mo.text2 <> '5117'
      order by plusminus desc,dobropis asc, xd1 asc,xmtid desc,xprice desc;
  -- 20.6.2016 nech najprv 117tky vybavi som modifikoval order
  cur2 scroll cursor (xxxkod integer, xd1 date) for
    select matid as umatid,remains as upcs,n_price as uprice
      from yuctogwmodel
      where xx = 'R' and remains > 0 and mat_id = xxxkod and 
      (d1 <= xd1 or (year(d1) = year(xd1) and month(d1) = month(xd1) ) )
      order by d1 asc,n_price desc;
  -- 117tky potrebuju rovnaku nakupnu
  --  22.5.2018 modifikoval som order na d1 desc, nech necha potrebne veci pre ostatne predaje
  cur3 scroll cursor (xxxkod integer, xd1 date, xprice decimal(11,2) )  for
    select matid as umatid,remains as upcs,n_price as uprice
      from yuctogwmodel
      where xx = 'R' and remains > 0 and mat_id = xxxkod and 
      (d1 <= xd1 or (year(d1) = year(xd1) and month(d1) = month(xd1) ) )
      and n_price = xprice
      order by d1 desc,n_price desc;
      rec RECORD;
      rec2 RECORD;
      rec3 RECORD;
begin
  ucto_rok := 2024;
    --Hack 1 movement
--  update movement set d1='2020-07-31' where move_id=11033;

    drop table if exists yuctogwmodel;
  create table yuctogwmodel(
    matid serial,
    xx varchar(1) null,
    kod integer null,
    n_price decimal(11,2) null default 0,
    s_price decimal(11,2) null default 0,
    d1 date null,
    fnumber integer null,
    pcs integer null,
    remains integer null,
    beforeonstock integer null,
    move_id integer null,
    mat_id integer null,
    doc_nr integer null default 0,
    primary key(matid)
    );
  create index xxgwkartyidx on yuctogwmodel(xx asc);
  create index remainsgwkartyidx on yuctogwmodel(remains asc);
  create index kodgwkartyidx on yuctogwmodel(kod asc);
  create index n_pricegwkartyidx on yuctogwmodel(n_price asc);
  create index pcsgwkartyidx on yuctogwmodel(pcs asc);
  create index move_idgwkartyidx on yuctogwmodel(move_id asc);
    for rec in curMain LOOP
    fsum := rec.xxtotalpcs;
    if( rec.xmtid in( 87,113,154,147,149 ) and fsum > 0) then
      for rec2 in cur2 ( xxxkod := rec.xxkod, xd1 := rec.xd1 ) LOOP
        if fsum > 0 then
          select sum(remains) into strict pom from yuctogwmodel where n_price = rec2.uprice and xx = 'R' and remains > 0 and mat_id = rec.xxkod and 
          (d1 <= rec.xd1 or (year(d1) = year(rec.xd1) and month(d1) = month(rec.xd1) ) );
          if rec2.upcs >= fsum then
            update yuctogwmodel set remains = remains-fsum where matid = rec2.umatid;
            insert into yuctogwmodel( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) 
              values( 'F',rec.xxkod,rec2.uprice,rec.xprice,rec.xd1,-fsum,0,pom,rec.xnumber,rec.xmoveid,rec.xmi ) ;
            fsum := 0;
          else
            update yuctogwmodel set remains = 0 where matid = rec2.umatid;
            insert into yuctogwmodel( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) 
              values( 'F',rec.xxkod,rec2.uprice,rec.xprice,rec.xd1,-rec2.upcs,0,pom,rec.xnumber,rec.xmoveid,rec.xmi ) ;
            fsum := fsum - rec2.upcs;
          end if;
        end if;
      end loop;
      if fsum <> 0 then
        insert into yuctogwmodel( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) 
          values( '!',rec.xxkod,0,rec.xprice,rec.xd1,-fsum,0,0,rec.xnumber,rec.xmoveid,rec.xmi ); 
      end if;
    elseif (rec.xmtid in( 117 ) and fsum > 0) then
      /*predaje 117 hladame rovnaku n_price */
      for rec3 in cur3  (xxxkod := rec.xxkod, xd1 := rec.xd1, xprice := rec.xprice ) LOOP  
        if fsum > 0 then
          select sum(remains) into strict pom from yuctogwmodel where n_price = rec3.uprice and xx = 'R' and remains > 0 and mat_id = rec.xxkod and 
          (d1 <= rec.xd1 or (year(d1) = year(rec.xd1) and month(d1) = month(rec.xd1) ) );
          if rec3.upcs >= fsum then
            update yuctogwmodel set remains = remains-fsum where matid = rec3.umatid;
            insert into yuctogwmodel( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) values 
              ( 'F',rec.xxkod,rec3.uprice,rec.xprice,rec.xd1,-fsum,0,pom,rec.xnumber,rec.xmoveid,rec.xmi ) ;
            fsum := 0;
          else
            update yuctogwmodel set remains = 0 where matid = rec3.umatid;
            insert into yuctogwmodel( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) values( 'F',rec.xxkod,rec3.uprice,rec.xprice,rec.xd1,-rec3.upcs,0,pom,rec.xnumber,rec.xmoveid,rec.xmi ) ;
            fsum := fsum - rec3.upcs;
          end if;
        end if;
      end loop;
      if fsum <> 0 then
        insert into yuctogwmodel( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) values( '!',rec.xxkod,0,rec.xprice,rec.xd1,-fsum,0,0,rec.xnumber,rec.xmoveid,rec.xmi ); 
      /* prijemky */
      end if;
    else 
	  if rec.xmtid in ( 87,113,117,154,147,149 ) then fsum := -fsum;
	  end if;
      insert into yuctogwmodel( xx,kod,n_price,d1,pcs,remains,fnumber,move_id,mat_id ) values( 'R',rec.xxkod,rec.xprice,rec.xd1,fsum,fsum,rec.xnumber,rec.xmoveid,rec.xmi ) ;
    end if;
  end loop;
  
    --Hack 1 movement back
 -- update movement set d1='2020-08-01' where move_id=11033;

end;
$$;


ALTER PROCEDURE public.gw_karty_prve_prijemky_model() OWNER TO dba;

--
-- Name: headertotalrecount(integer); Type: PROCEDURE; Schema: public; Owner: miloswikarski
--

CREATE PROCEDURE public.headertotalrecount(IN ccislo integer)
    LANGUAGE plpgsql
    AS $$
  declare xxtax1 decimal(11,2);
  xxtax2 decimal(11,2);
  xxcount integer;
  xxround integer;
  xs0 decimal(11,2);
  xs1 decimal(11,2);
  xs2 decimal(11,2);
  dd1 decimal(11,2);
  dd2 decimal(11,2);
  xxza decimal(11,2);
  username varchar(255);
  adresa integer;

begin 
   select emp_id into strict username from movement where move_id=ccislo;
   select adr_id into strict adresa from movement where move_id=ccislo;
   select count(*) into strict xxcount from m_detail where move_id=ccislo group by tax;
   -- default tax podla toho co je v detaile pohybu
   select max(tax) into strict xxtax2 from m_detail where move_id=ccislo;
   -- ak tam nie je nic tak pre istotu z ciselnika office
   if xxtax2 = 0 then
        select tax2 into strict xxtax2 from office where user_name = username;
   end if;
   -- ak mame viac tax tak druha najvyssia je z ciselnika office
   if xxcount >=2 then
        select max(tax) into strict xxtax1 from m_detail where move_id=ccislo and tax <> xxtax2;
   else 
        xxtax1 := 0;
   end if;
   if adresa in (select adr_id from address where firma like '_ Export%') then
                select perc into strict xxtax2 from vat where country=(select iso from address where adr_id=adresa) and category=2; 
   end if;
   select decrounding into xxround from office where user_name = username;
   select sum(pcs*(price*(1-discount/100))) into strict xs0 from m_detail where move_id = ccislo and tax = 0;
   select sum(pcs*(price*(1-discount/100))) into strict xs1 from m_detail where move_id = ccislo and tax <> 0 and tax = xxtax1;
   select sum(pcs*(price*(1-discount/100))) into strict xs2 from m_detail where move_id = ccislo and tax <> 0 and tax = xxtax2;
  xs0 := coalesce(xs0,0);
  xs1 := coalesce(xs1,0);
  xs2 := coalesce(xs2,0);
  dd1 := coalesce(Round(xs1*(xxtax1/100),xxround),0);
  dd2 := coalesce(Round(xs2*(xxtax2/100),xxround),0);
  xxza := coalesce(Round(xs0+xs1+xs2,xxround)-xs0-xs1-xs2,0);
  update movement set total0 = xs0,total1 = xs1,total2 = xs2,
    tax1 = dd1,tax2 = dd2,rounding = xxza,total = xs0+xs1+xs2+dd1+dd2+xxza
    where move_id = ccislo;

end
;
$$;


ALTER PROCEDURE public.headertotalrecount(IN ccislo integer) OWNER TO miloswikarski;

--
-- Name: labels2(); Type: PROCEDURE; Schema: public; Owner: dba
--

CREATE PROCEDURE public.labels2()
    LANGUAGE plpgsql
    AS $$
  declare xxpocet integer;
  rec RECORD;
  xxlabel varchar(60);
  MCursor scroll cursor for select pcs as xxpcs,kod as xxkod,model as xxmodel,mat_id as xxmatid,price as xxprice from tempmove order by model asc;
begin
  delete from labels;
  <<ccc>>
  for rec in mCursor LOOP
    xxpocet := rec.xxpcs;
    while xxpocet > 0 loop
      xxpocet := xxpocet-1;
      select label1 into strict xxlabel from kod where kod = rec.xxkod;
      raise notice 'ddd % % % % % % %',rec.xxmodel, xxlabel, rec.xxkod, rec.xxmatid, rec.xxprice, rec.xxpcs, '\\';
      insert into labels( line1,line2,line3,line4,line5,line6 ) values( xxlabel,rec.xxmodel,rec.xxmatid,rec.xxprice,rec.xxpcs,'\' );
    end loop;
    end loop ccc;
end;
$$;


ALTER PROCEDURE public.labels2() OWNER TO dba;

--
-- Name: month(timestamp with time zone); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.month(d1 timestamp with time zone) RETURNS integer
    LANGUAGE plpgsql
    AS $$
begin
  return extract(month from d1);
end
$$;


ALTER FUNCTION public.month(d1 timestamp with time zone) OWNER TO dba;

--
-- Name: newpaper(integer, character varying, date, date, date, numeric, character varying, character varying, integer, character varying, character varying, character varying, character varying, integer, integer); Type: PROCEDURE; Schema: public; Owner: dba
--

CREATE PROCEDURE public.newpaper(IN typ_pohybu integer, IN typ_dokladu character varying, IN datum1 date, IN datum2 date, IN datum3 date, IN zlava numeric, IN ppozn character varying, IN ttext character varying, IN adresa integer, IN username character varying, IN ffrom character varying, IN tto character varying, IN mmoving character varying, IN adrrepli integer, IN clipid integer)
    LANGUAGE plpgsql
    AS $$

 declare xxtax1 decimal(8,6);
  xxtax2 decimal(8,6);
  xxround integer;
  rem integer;
  xs0 decimal(11,2);
  xs1 decimal(11,2);
  xs2 decimal(11,2);
  dd1 decimal(11,2);
  dd2 decimal(11,2);
  za decimal(11,2);
  rest integer;
  ccislo integer;
  ccislomlog integer;
  cdokl integer;
  cdokl2 integer;
  xxregid integer;
  xfifogroup varchar(3);
  centrala integer;
    xxcount integer;
  sprava text;
  chrono_retail_account integer;
  final_stock character varying;
  MCurxshop scroll cursor for select '   '||mat_id || ' ' || model || ' Need: ' || pcs || '/' || onstock(mat_id,ffrom) as ostavashop from tempmove where clip_id = clipid and user_name = username and pcs > onstock(mat_id,ffrom);
begin
  --Rewrite all parameters which are redundant
  sprava := '';
  -- mmoving prepisane historicky, takze vyuzijem na final_stock do mlog
  final_stock := mmoving;
  select doc_id into strict typ_dokladu from m_type where m_type_id = typ_pohybu;
  select moving into strict mmoving from m_type where m_type_id = typ_pohybu;
  select fifogroup into strict xfifogroup from m_type where m_type_id = typ_pohybu;
  if(xfifogroup like 'S%' or typ_pohybu = 14) then
    --You can not sell or move less than onstock
    select count(1) into strict rest from tempmove where clip_id = clipid and user_name = username and mat_id<>241209 and pcs > onstock(mat_id,ffrom);
    if rest > 0 then
      for rec in MCurxshop LOOP
        sprava := sprava || rec.ostavashop;
      end loop;
      raise exception 'Not enough PCS: in  % lines %',rest, sprava;
      return;
    end if;
  end if; /* call fifosave(typ_pohybu,clipid) */
  if(xfifogroup like 'R%' or xfifogroup like 'S%') then
    /* Dokument musi byt len s kladnymi kusmi */
     select count(1) into strict rest from tempmove where clip_id = clipid and user_name = username and pcs < 0;
    if rest > 0 then 
      raise exception 'PCS less than 0 on clipboard EXISTS!';
      return;
    end if;
  end if;
    
  if( typ_pohybu in (87,97,147,149)) then
    /* Dokument musi byt bez DPH */
     select count(1) into strict rest from tempmove where clip_id = clipid and user_name = username and tax <> 0;
    if rest > 0 then 
      raise exception 'DOKUMENT MUSI BYT BEZ DPH! VAT MUST BE ZERO!';
      return;
    end if;
     end if;
  

   select decrounding into xxround from office where user_name = username;
   select office_nr into rem from office where user_name = username;
   select coalesce(max(move_id)+1,0) into strict ccislo from movement;
   select coalesce(max(move_id)+1,0) into strict ccislomlog from mlog;
   if ccislomlog > ccislo then
                ccislo := ccislomlog;
   end if;
   raise notice 'select number into strict cdokl from document where doc_id = %',typ_dokladu;
   select number into strict cdokl from document where doc_id = typ_dokladu;
  update document set number = number+1 where doc_id = typ_dokladu;
  --UVB discount system
  update tempmove set discount = (10000-(100-discount)*(100-all1))/100 where clip_id = clipid and user_name = username;
  insert into movement( move_id,adr_id,d1,d2,d3,doc_id,number,emp_id,
    m_type_id,discount,text1,text2,adr_repli_id,repli_id,"X" ) 
    values( ccislo,adresa,datum1,datum2,datum3,typ_dokladu,cdokl,username,
    typ_pohybu,zlava,ppozn,ttext,0,rem,adrrepli ) ;
  insert into m_detail( move_id,mat_id,price,pcs,discount,fifo_repli_id,tax,currency,m_repli_id,detail_info,fifo_price,fifo_move_id,fifo_currency ) 
    select ccislo,mat_id,price,pcs,discount,0,
      tax,currency,rem,tempmove_info,fifo_price,fifo_move_id,fifo_currency from tempmove where clip_id = clipid and user_name = username;


   select count(*) into strict xxcount from (select count(*) from m_detail where move_id=ccislo group by tax) x; 

   -- default tax podla toho co je v detaile pohybu
   select max(tax) into strict xxtax2 from m_detail where move_id=ccislo;
   -- ak tam nie je nic tak pre istotu z ciselnika office
   if xxtax2 = 0 then
        select tax2 into strict xxtax2 from office where user_name = username;
   end if;
   -- ak mame viac tax tak druha najvyssia je z ciselnika office
  if xxcount >=2 then
        select max(tax) into strict xxtax1 from m_detail where move_id=ccislo and tax <> xxtax2;
   else
        xxtax1 := 0;
   end if;
   if adresa in (select adr_id from address where firma like '_ Export%') then
                select perc into strict xxtax2 from vat where country=(select iso from address where adr_id=adresa) and category=2;
   end if;


   select sum(pcs*(price*(1-discount/100))) into strict xs0 from m_detail where move_id = ccislo and m_repli_id = rem and tax = 0;
   select sum(pcs*(price*(1-discount/100))) into strict xs1 from m_detail where move_id = ccislo and m_repli_id = rem and tax <> 0 and tax = xxtax1;
   select sum(pcs*(price*(1-discount/100))) into strict xs2 from m_detail where move_id = ccislo and m_repli_id = rem and tax <> 0 and tax = xxtax2;
  xs0 := coalesce(xs0,0);
  xs1 := coalesce(xs1,0);
  xs2 := coalesce(xs2,0);
  dd1 := coalesce(Round(xs1*(xxtax1/100),xxround),0);
  dd2 := coalesce(Round(xs2*(xxtax2/100),xxround),0);
  za := coalesce(Round(xs0+xs1+xs2,xxround)-xs0-xs1-xs2,0);
  --Import SK without rounding
  if typ_pohybu = 86 or typ_pohybu = 89 then
    za := 0;
  end if;
  update movement set total0 = xs0,total1 = xs1,total2 = xs2,
    tax1 = dd1,tax2 = dd2,rounding = za,total = xs0+xs1+xs2+dd1+dd2+za
    where move_id = ccislo and repli_id = rem;
  if(mmoving = 'S' or mmoving = 'M' or mmoving = 'X' or mmoving = 'E') then 
    update movement set stock_id1 = ffrom  where move_id = ccislo and repli_id = rem;
  end if;
  if(mmoving = 'R' or mmoving = 'M') then 
    update movement set stock_id2 = tto where move_id = ccislo and repli_id = rem;
  end if;
  -- mmoving vo wstock sa posielal vsade 'x' takze nova funkcionalita final_stock moze byt takto filtrovana:
  if( ( typ_pohybu = 14 or typ_pohybu = 27  or typ_pohybu = 16 ) and final_stock <> 'x' ) then 
    insert into  mlog (move_id,flags,note,user_name) values (ccislo,'OnTheWay,'||final_stock,'This movement will be finalized to stock '||final_stock,username);
  elseif( ( typ_pohybu = 14 or typ_pohybu = 27 or typ_pohybu = 16 ) and final_stock = 'x' ) then 
    insert into  mlog (move_id,flags,note,user_name) values (ccislo,'OnTheWay','On the way to stock '||tto,username);
  end if;
  insert into thserials (mat_id,move_id,thserial) select mat_id,ccislo,thserial from tempmove where clip_id = clipid and user_name = username and thserial is not null;
  -- minfo default values only for selected movements and 17
  -- select m_type_id from m_type where moving='S' and usergroup like '%S%' 
  -- plus pridane 150,151,152,153,154,155,156,157, 69,131,138,140,109,180-183
  if typ_pohybu in (20,69,131,145,146,138,140,109,23,64,66,88,94,95,102,115,116,117,118,137,139,167,168,169,170,24,87,36,132,110,130,147,17,150,151,152,153,154,155,156,157,180,181,182,183) then
    select case when firma_id=0 then adr_id else firma_id end xcentrala into strict centrala from address where adr_id=adresa;
        insert into minfo (move_id,icompany,icity,izip,istreet,iico,idic,iicdph,
                                          shipment,payment,
                                          dcompany,dcity,dzip,dstreet,icountry,dcountry) values (ccislo,
                (select firma from address where adr_id=centrala),
                (select city from address where adr_id=centrala),
                (select zip from address where adr_id=centrala),
                (select street from address where adr_id=centrala),                                     
                (select ico from address where adr_id=centrala),                                        
                (select drc2 from address where adr_id=centrala),                                       
                (select drc1 from address where adr_id=centrala),                                       
                'Osobný odber','Prevodom',
                (select firma from address where adr_id=adresa),
                (select city from address where adr_id=adresa),
                (select zip from address where adr_id=adresa),
                (select street from address where adr_id=adresa),
                (select printable_name from country where iso=(select upper(iso) from address where adr_id=centrala) ),
                (select printable_name from country where iso=(select upper(iso) from address where adr_id=adresa) )
                                                                          );
  end if;
  if typ_pohybu = 20 then
    update minfo set incoterms = 'DAP' where move_id = ccislo;
  end if;

  RAISE LOG 'newpaper % % % % % % % % % % % % % % % move_id=%',typ_pohybu,typ_dokladu, datum1, datum2, datum3, zlava, ppozn,ttext, adresa, username, ffrom, tto, mmoving, adrrepli, clipid, ccislo;

end;
$$;


ALTER PROCEDURE public.newpaper(IN typ_pohybu integer, IN typ_dokladu character varying, IN datum1 date, IN datum2 date, IN datum3 date, IN zlava numeric, IN ppozn character varying, IN ttext character varying, IN adresa integer, IN username character varying, IN ffrom character varying, IN tto character varying, IN mmoving character varying, IN adrrepli integer, IN clipid integer) OWNER TO dba;

--
-- Name: nr2text(integer); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.nr2text(cc integer) RETURNS character varying
    LANGUAGE plpgsql
    AS $$

  declare vysl varchar(255);
  mil varchar(255);
  ipom varchar(7);
  Pom2 integer;
  Pom integer;
  i integer;
 begin
  vysl := '';
  mil
 := '';
  if cc = 0 then
    vysl := 'Nula';
  elseif cc > 999999 then
    i := Round(cc/1000000,0);
    mil := nr2text(i);
    cc := cc-i*1000000;
    if i = 1 then
      mil := mil||'milión ';
    elseif i < 5 then
      mil := mil||'milióny ';
    else
      mil := mil||'miliónov ';
    end if;
  end if;
  ipom := cast(cc as varchar);
  <<ccc>>
  while LENGTH(ipom) < 7 loop
    ipom := '0'||ipom;
  end loop ccc;
  Pom := cast(RIGHT(ipom,2) as integer);
  Pom2 := cast(SUBSTR(ipom,5,1) as integer);
  i := 0;
  <<xxx>>
  while i < 2 loop
    if Pom = 2 and i = 1 then
      vysl := 'dve'||vysl;
    elseif Pom < 10 then
      vysl := slovo(pom+1)||vysl;
    elseif Pom = 10 then
      vysl := 'desať'||vysl;
    elseif Pom = 11 then
      vysl := 'jedenásť'||vysl;
    elseif Pom = 14 then
      vysl := 'strnásť'||vysl;
    elseif Pom > 11 and Pom < 20 then
      vysl := slovo(MOD(Pom,10)+1)||'násť'||vysl;
    elseif Pom < 50 then
      vysl := slovo(Pom/10+1)||'dsať'||slovo(MOD(Pom,10)+1)||vysl;
    else
      vysl := slovo(Pom/10+1)||'desiat'||slovo(MOD(Pom,10)+1)||vysl;
    end if;
    if Pom2 = 1 then
      vysl := 'sto'||vysl;
    elseif Pom2 = 2 then
      vysl := 'dvesto'||vysl;
    elseif Pom2 <> 0 then
      vysl := slovo(Pom2+1)||'sto'||vysl;
    end if;
    if cc < 1000 then
      exit xxx;
    end if;
    if i <> 1 then
      vysl := 'tisíc'||vysl;
      Pom := cast(SUBSTR(ipom,3,2) as integer);
      Pom2 := cast(SUBSTR(ipom,2,1) as integer);
    end if;
    i := i+1;
  end loop;
  return(mil||vysl);
end;
$$;


ALTER FUNCTION public.nr2text(cc integer) OWNER TO dba;

--
-- Name: onaccount(integer, integer, integer, integer); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.onaccount(xaccid integer, xaccrepliid integer, accregid integer, accrepliid integer) RETURNS numeric
    LANGUAGE plpgsql
    AS $$

  declare xxzvysok decimal(11,3);
  xxsum decimal(11,2);
  actual_date date;
begin
   select trans_date from acc_register where acc_reg_id = accregid and acc_repli_id = accrepliid;
   select balance from accounts where acc_id = XACCID and repli_id = XACCREPLIID;
   select sum(case when trans_acc_id is null or trans_acc_id <> XACCID or trans_repli_id <> XACCREPLIID then
        ar.amount else-ar.amount end)
      from acc_register as ar where((ar.acc_id = XACCID and ar.repli_id = XACCREPLIID) or(ar.trans_acc_id = XACCID and ar.trans_repli_id = XACCREPLIID)) and ar.valid = 1
      and ar.trans_date <= actual_date and ar.acc_reg_id <= accregid;
  xxsum := xxsum+xxzvysok;
  return xxsum;
end;
$$;


ALTER FUNCTION public.onaccount(xaccid integer, xaccrepliid integer, accregid integer, accrepliid integer) OWNER TO dba;

--
-- Name: ongwatchskdate(integer, date); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.ongwatchskdate(matid integer, date2 date) RETURNS integer
    LANGUAGE plpgsql
    AS $$

  declare xx integer;
  yy integer;
begin
   select sum(pcs) from m_detail as d,movement as m where d.move_id = m.move_id
      and d.m_repli_id = m.repli_id and m.stock_id1 = 'sj'
      and mat_id = matid and m.adr_id in( 3408,3418,3430,3208,3731,3624,3405,3511,3512 ) and m.adr_repli_id = 0 and d1 <= date2;
   select sum(pcs) from m_detail as d,movement as m where d.move_id = m.move_id
      and d.m_repli_id = m.repli_id and m.stock_id2 = 'sj'
      and mat_id = matid and m.adr_id in( 3408,3418,3430,3208,3731,3624,3405,3511,3512 ) and m.adr_repli_id = 0 and d1 <= date2;
  return(ifnull(yy,0,yy)-ifnull(xx,0,xx));
end;
$$;


ALTER FUNCTION public.ongwatchskdate(matid integer, date2 date) OWNER TO dba;

--
-- Name: onprice(integer, character varying); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.onprice(matid integer, priceid character varying) RETURNS numeric
    LANGUAGE plpgsql
    AS $$

  declare xx numeric(11,2);
  mojselect varchar(255);
begin
  mojselect := 'select ' || priceid || ' into xx from product where mat_id = ' || matid;
  execute immediate mojselect;
  return(ifnull(xx,0,xx));
end;
$$;


ALTER FUNCTION public.onprice(matid integer, priceid character varying) OWNER TO dba;

--
-- Name: onshop(integer, integer, integer); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.onshop(matid integer, adrid integer, repli integer) RETURNS integer
    LANGUAGE plpgsql
    AS $$

  declare xx integer;
  yy integer;
  zz varchar(2);
begin
  if adrid = 3734 and repli = 1 then
    zz := 'sc';
  else
    zz := 'sj';
  end if;
   select sum(pcs) into xx from m_detail as d,movement as m where d.move_id = m.move_id
      and d.m_repli_id = m.repli_id and m.stock_id1 = zz
      and mat_id = matid and m.adr_id = adrid and m.adr_repli_id = repli;
   select sum(pcs) into yy from m_detail as d,movement as m where d.move_id = m.move_id
      and d.m_repli_id = m.repli_id and m.stock_id2 = zz
      and mat_id = matid and m.adr_id = adrid and m.adr_repli_id = repli;
  return(coalesce(yy,0)-coalesce(xx,0));
end;
$$;


ALTER FUNCTION public.onshop(matid integer, adrid integer, repli integer) OWNER TO dba;

--
-- Name: onshopdate(integer, integer, integer, date, date); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.onshopdate(matid integer, adrid integer, repli integer, date1 date, date2 date) RETURNS integer
    LANGUAGE plpgsql
    AS $$

  declare xx integer;
  yy integer;
begin
   select sum(pcs) from m_detail as d,movement as m where d.move_id = m.move_id
      and d.m_repli_id = m.repli_id and m.stock_id1 = 'sj'
      and mat_id = matid and m.adr_id = adrid and m.adr_repli_id = repli and d1 between date1 and date2;
   select sum(pcs) from m_detail as d,movement as m where d.move_id = m.move_id
      and d.m_repli_id = m.repli_id and m.stock_id2 = 'sj'
      and mat_id = matid and m.adr_id = adrid and m.adr_repli_id = repli and d1 between date1 and date2;
  return(ifnull(yy,0,yy)-ifnull(xx,0,xx));
end;
$$;


ALTER FUNCTION public.onshopdate(matid integer, adrid integer, repli integer, date1 date, date2 date) OWNER TO dba;

--
-- Name: onshopdate2(integer, integer, integer, date); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.onshopdate2(matid integer, adrid integer, repli integer, date2 date) RETURNS integer
    LANGUAGE plpgsql
    AS $$

  declare xx integer;
begin
  --declare sqlko text;
   select sum(case when m.stock_id1 = 'sj' then pcs else case when m.stock_id2 = 'sj' then-pcs else 0 end end) from m_detail as d,movement as m where d.move_id = m.move_id
      and m.stock_id1 = 'sj'
      and mat_id = matid and m.adr_id = adrid and m.adr_repli_id = repli and d1 <= date2;
  return(ifnull(xx,0,xx));
end;
$$;


ALTER FUNCTION public.onshopdate2(matid integer, adrid integer, repli integer, date2 date) OWNER TO dba;

--
-- Name: onshopdatemaxprice(integer, integer, integer, date, date); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.onshopdatemaxprice(matid integer, adrid integer, repli integer, date1 date, date2 date) RETURNS numeric
    LANGUAGE plpgsql
    AS $$

  declare yy integer;
begin
   select max(price*(select rate from currencies where cur = d.currency)) from m_detail as d,movement as m where d.move_id = m.move_id
      and d.m_repli_id = m.repli_id and m.stock_id2 = 'sj'
      and mat_id = matid and m.adr_id = adrid and m.adr_repli_id = repli and d1 between date1 and date2;
  return(ifnull(yy,0,yy));
end;
$$;


ALTER FUNCTION public.onshopdatemaxprice(matid integer, adrid integer, repli integer, date1 date, date2 date) OWNER TO dba;

--
-- Name: onshopdateminprice(integer, integer, integer, date, date); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.onshopdateminprice(matid integer, adrid integer, repli integer, date1 date, date2 date) RETURNS numeric
    LANGUAGE plpgsql
    AS $$

  declare yy integer;
begin
   select min(price*(select rate from currencies where cur = d.currency)) from m_detail as d,movement as m where d.move_id = m.move_id
      and d.m_repli_id = m.repli_id and m.stock_id2 = 'sj'
      and mat_id = matid and m.adr_id = adrid and m.adr_repli_id = repli and d1 between date1 and date2;
  return(ifnull(yy,0,yy));
end;
$$;


ALTER FUNCTION public.onshopdateminprice(matid integer, adrid integer, repli integer, date1 date, date2 date) OWNER TO dba;

--
-- Name: onshopdateprice(integer, integer, integer, date, date); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.onshopdateprice(matid integer, adrid integer, repli integer, date1 date, date2 date) RETURNS numeric
    LANGUAGE plpgsql
    AS $$

  declare yy integer;
begin
   select sum(price*pcs*(select rate from currencies where cur = d.currency))/sum(pcs) from m_detail as d,movement as m where d.move_id = m.move_id
      and d.m_repli_id = m.repli_id and m.stock_id2 = 'sj'
      and mat_id = matid and m.adr_id = adrid and m.adr_repli_id = repli and d1 between date1 and date2;
  return(ifnull(yy,0,yy));
end;
$$;


ALTER FUNCTION public.onshopdateprice(matid integer, adrid integer, repli integer, date1 date, date2 date) OWNER TO dba;

--
-- Name: onshopsdate(integer, character varying, integer, date); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.onshopsdate(matid integer, adrgroup character varying, repli integer, date2 date) RETURNS integer
    LANGUAGE plpgsql
    AS $$

  declare xx integer;
  yy integer;
begin
   select sum(pcs) from m_detail as d,movement as m where d.move_id = m.move_id
      and d.m_repli_id = m.repli_id and m.stock_id1 = 'sj'
      and mat_id = matid and m.adr_id in( adrgroup ) and m.adr_repli_id = repli and d1 <= date2;
   select sum(pcs) from m_detail as d,movement as m where d.move_id = m.move_id
      and d.m_repli_id = m.repli_id and m.stock_id2 = 'sj'
      and mat_id = matid and m.adr_id in( adrgroup ) and m.adr_repli_id = repli and d1 <= date2;
  return(ifnull(yy,0,yy)-ifnull(xx,0,xx));
end;
$$;


ALTER FUNCTION public.onshopsdate(matid integer, adrgroup character varying, repli integer, date2 date) OWNER TO dba;

--
-- Name: onstock(integer, character varying); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.onstock(matid integer, stockid character varying) RETURNS integer
    LANGUAGE plpgsql
    AS $$

  declare xx integer;
  xkod varchar(255);
  yy integer;
begin
   select kod into xkod from product where mat_id=matid;
   if xkod = 'SERV' then
                return 999999;
    end if;
   select sum(pcs) into xx from m_detail as d,movement as m where d.move_id = m.move_id
      and m.stock_id1 = stockid
      and mat_id = matid;
   select sum(pcs) into yy from m_detail as d,movement as m where d.move_id = m.move_id
      and m.stock_id2 = stockid
      and mat_id = matid;
  return(coalesce(yy,0)-coalesce(xx,0));
end;
$$;


ALTER FUNCTION public.onstock(matid integer, stockid character varying) OWNER TO dba;

--
-- Name: onstockdate(integer, character varying, date, date); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.onstockdate(matid integer, stockid character varying, date1 date, date2 date) RETURNS integer
    LANGUAGE plpgsql
    AS $$

  declare xx integer;
  yy integer;
begin
  --declare xx2 integer;
  --declare yy2 integer;
   select sum(pcs) from m_detail as d,movement as m where d.move_id = m.move_id
      and d.m_repli_id = m.repli_id and m.stock_id1 = stockid
      and mat_id = matid and d1 between date1 and date2;
   select sum(pcs) from m_detail as d,movement as m where d.move_id = m.move_id
      and d.m_repli_id = m.repli_id and m.stock_id2 = stockid
      and mat_id = matid and d1 between date1 and date2;
  return(ifnull(yy,0,yy)-ifnull(xx,0,xx));
end;
$$;


ALTER FUNCTION public.onstockdate(matid integer, stockid character varying, date1 date, date2 date) OWNER TO dba;

--
-- Name: pricelocal(character varying, character varying, character varying); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.pricelocal(xdest character varying, xsource character varying, xfilter character varying) RETURNS void
    LANGUAGE plpgsql
    AS $$

  declare xxsqltxt varchar(255);
begin
  xxsqltxt := 'update ".plocal set ' || xdest || ' = ' || xsource || ' where plocal.kod like ''%'
     || xfilter || '%''';
  execute immediate xxsqltxt;
end;
$$;


ALTER FUNCTION public.pricelocal(xdest character varying, xsource character varying, xfilter character varying) OWNER TO dba;

--
-- Name: priceprod(character varying, character varying, character varying); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.priceprod(xdest character varying, xsource character varying, xfilter character varying) RETURNS void
    LANGUAGE plpgsql
    AS $$

  declare xxsqltxt varchar(255);
begin
  xxsqltxt := 'update ".product set ' || xdest || ' = ' || xsource || ' where product.kod like ''%'
     || xfilter || '%''';
  execute immediate xxsqltxt;
end;
$$;


ALTER FUNCTION public.priceprod(xdest character varying, xsource character varying, xfilter character varying) OWNER TO dba;

--
-- Name: pridajasuser(integer, integer, integer, numeric, character varying, numeric, date, date, date, integer, character varying, character varying, character varying, character varying, character varying, character varying, character varying, character varying); Type: PROCEDURE; Schema: public; Owner: dba
--

CREATE PROCEDURE public.pridajasuser(IN adresa integer, IN material integer, IN clipid integer, IN poc numeric, IN ddoctyp character varying, IN cena numeric, IN dd1 date, IN dd2 date, IN dd3 date, IN dm_type_id integer, IN dprice_type character varying, IN dstock_id1 character varying, IN dstock_id2 character varying, IN dtext1 character varying, IN dtext2 character varying, IN tempinfo character varying, IN thserial character varying, IN username character varying)
    LANGUAGE plpgsql
    AS $$

  declare zlava decimal(5,3);
  kkod varchar(4);
  xxean13 varchar(13);
  xxmaterial integer;
  xxxmaterial integer;
  xxtax decimal(11,6);
--   xspecprice varchar(3);
  xxsqltxt varchar;
begin
  --special price, not from left table:
--   select price into strict xspecprice from where user_name = username;
--   xxsqltxt := 'select ' || xspecprice || ' from product where mat_id='||material||' limit 1';
--   begin
--     execute xxsqltxt into strict cena;
--   exception
--     when undefined_column then -- nothing to do, implicit cena used
--   end;
  select mat_id into strict xxxmaterial from product where mat_id = material;
  select mat_id into xxmaterial from product where ean13 = xxean13;
  if found then
    material := xxmaterial;
  end if;
  case when(select 1 from tempmove where mat_id = material and clip_id = clipid and user_name = username and tempmove_info = tempinfo) is not null and thserial is null then
    update tempmove set pcs = pcs+poc where mat_id = material and clip_id = clipid and user_name = username and tempmove_info = tempinfo;
  else
    select tax into xxtax from product where mat_id = material;
        if adresa in (select adr_id from address where firma like '_ Export%') and xxtax = 2 then
                select perc into strict xxtax from vat where country=(select iso from address where adr_id=adresa) and category=2; 
        else
                if xxtax = 1 then
                   select tax1 into strict xxtax from office where user_name = username;
                elseif xxtax = 2 then
                   select tax2 into strict xxtax from office where user_name = username;
                elseif xxtax is null then
                   select tax2 into strict xxtax from office where user_name = username;        
                else
                  xxtax := 0;
                end if; 
        end if;

    if dm_type_id in( 20,87,88,97,147,149,86,89,110,5,14,16,56,67,70,106,30,31,99,106,403,404,423,424,444,504,505 ) then xxtax := 0;
	end if;
    if material in (241209) then xxtax := 0; -- zaokruhlenie
     	end if;
    insert into tempmove( adr_id, mat_id,pcs,price,tax,discount,all1,all2,all3,all4,all5,
      ean13,kod,model,pdescr,unit,currency,clip_id,d1,d2,d3,m_type_id,price_type,
      stock_id1,stock_id2,text1,text2,user_name,tempmove_info,fifo_move_id, thserial ) values( adresa,material,poc,cena,xxtax,0,0,0,0,0,0,
      (select ean13 from product where mat_id = material),
      (select kod from product where mat_id = material),
      (select model from product where mat_id = material),
      (select pdescr from product where mat_id = material),'',
      (select left(currency,3) from office where user_name = username),
      clipid,dd1,dd2,dd3,dm_type_id,dprice_type,trim(dstock_id1),trim(dstock_id2),dtext1,dtext2,
      username,tempinfo,(select coalesce(max(fifo_move_id)+1,1) from tempmove where clip_id = clipid and user_name = username ), thserial);
    if ddoctyp = 'E' then
      update tempmove set fifo_move_id = (select coalesce(max(fifo_move_id)+1,1) from tempmove where clip_id = clipid and user_name = username),tax = 0 where mat_id = material and clip_id = clipid and user_name = username and tempmove_info = tempinfo;
    end if;
  end case;
end
$$;


ALTER PROCEDURE public.pridajasuser(IN adresa integer, IN material integer, IN clipid integer, IN poc numeric, IN ddoctyp character varying, IN cena numeric, IN dd1 date, IN dd2 date, IN dd3 date, IN dm_type_id integer, IN dprice_type character varying, IN dstock_id1 character varying, IN dstock_id2 character varying, IN dtext1 character varying, IN dtext2 character varying, IN tempinfo character varying, IN thserial character varying, IN username character varying) OWNER TO dba;

--
-- Name: receive_on_the_way(integer, character varying); Type: PROCEDURE; Schema: public; Owner: dba
--

CREATE PROCEDURE public.receive_on_the_way(IN ccislo integer, IN xusername character varying)
    LANGUAGE plpgsql
    AS $$
  declare final_stock varchar(255);
  actual_stock varchar(255);
  from_stock varchar(255);
  matids  integer[];
  flag1 varchar(255);
  flag2 varchar(255);
  flag3 varchar(255);
  flag4 varchar(255);
  flag5 varchar(255);
  flag6 varchar(255);
  flag7 varchar(255);
  flag8 varchar(255);
  flag9 varchar(255);
  mystock varchar(255);
  already_received varchar(255);

begin 
   select stock_id2 into actual_stock from movement where move_id=ccislo;
   select stock_id1 into from_stock from movement where move_id=ccislo;
   --  searching final_stock in flags. Not strict, no err than
   select substring(flags, strpos(flags, ',') + 1) into final_stock  from mlog where move_id=ccislo and flags like 'OnTheWay,%';
   -- standard log from buttons in wstock
   if not found then
   	  select stock_id2 into final_stock from movement where move_id=ccislo;
   end if;
   -- just for sure...
   if length(final_stock) < 2 then
   	raise exception 'Do not know where to receive';
	return;
   end if;   
   -- shops only finalize their stocks
   select split_part(all_flags,'-',1) into flag1 from wuser where username=xusername;
   select split_part(all_flags,'-',2) into flag2 from wuser where username=xusername;
   select split_part(all_flags,'-',3) into flag3 from wuser where username=xusername;
   select split_part(all_flags,'-',4) into flag4 from wuser where username=xusername;
   select split_part(all_flags,'-',5) into flag5 from wuser where username=xusername;
   select split_part(all_flags,'-',6) into flag6 from wuser where username=xusername;
   select split_part(all_flags,'-',7) into flag7 from wuser where username=xusername;
   select split_part(all_flags,'-',8) into flag8 from wuser where username=xusername;
   select split_part(all_flags,'-',9) into flag9 from wuser where username=xusername;
   select stock_id into mystock from stock_detail where adr_id = convert_to_integer(flag2);
   if flag1 = 'shop' and mystock is not null and mystock <> final_stock then
   	raise exception ' You HAVE NO PERMISSION TO FINALIZE THIS STOCK!!!';
	return;
   end if;
   if flag1 = 'stock' and final_stock not in (flag2, flag3, flag4, flag5, flag6, flag7, flag8, flag9, 'SERH' ) then
   	raise exception ' You, although STOCK keeper, HAVE NO PERMISSION TO FINALIZE THIS STOCK!!!';
	return;
   end if;
   if flag1 <> 'stock' and flag1 <> 'shop' then
   	raise exception ' You have NO RIGHTS to STOCK KEEPING!!! Your group: %', flag1;
	return;
   end if;      
   -- now set regular received log msg
   select flags into already_received from mlog where move_id=ccislo and flags='Received';
   if already_received is null then
   	  -- set received only if has rights, vyssie som to uz osetril
      insert into  mlog (move_id,flags,note,user_name) values (ccislo,'Received','Receiving to stock confirmed',xusername);
   end if;
   
   -- now get affected matids and recount stocks after changing or deleting movement
   SELECT ARRAY(SELECT mat_id FROM m_detail where move_id=ccislo group by mat_id) into matids;
   if final_stock = from_stock then
       call delmove(ccislo);
	   insert into  mlog (move_id,flags,note,user_name) values (ccislo,'SysRontw','Receiving to original sender = movement deleted. Stock old value: '||actual_stock||', new value same as "From stock": '||final_stock,xusername);
	   call	dyninfo_recount_one_movement(matids);
	return;
   end if;
   if final_stock <> actual_stock then
	   update movement set stock_id2 = final_stock where move_id=ccislo;
	   insert into  mlog (move_id,flags,note,user_name) values (ccislo,'SysRontw','Receiving stock old value: '||actual_stock||', new value: '||final_stock,xusername);
	   call	dyninfo_recount_one_movement(matids);
	end if;

end
;
$$;


ALTER PROCEDURE public.receive_on_the_way(IN ccislo integer, IN xusername character varying) OWNER TO dba;

--
-- Name: setpricesp0ep0f(); Type: PROCEDURE; Schema: public; Owner: dba
--

CREATE PROCEDURE public.setpricesp0ep0f()
    LANGUAGE plpgsql
    AS $$
declare
        updated integer;
begin
 -- ZMENA LEN NA PAR TYZDNOV: PRE WLON IBA p1*0.9 INAC BOLO p1
  -- update product set p0e = floor(p1*0.9/100+.5)*100 where kod in ('WLON');
--  UPDATE product set p0e = p1 where kod not in( 'TISS','CERS' ) ;
  UPDATE product set p0e = p1 ;
  GET DIAGNOSTICS updated=ROW_COUNT;
  RAISE LOG 'setpricesp0ep0f updated % rows',updated;
--  RAISE LOG 'setpricesp0ep0f updated % not TISS CERS',updated;
  -- update product set p0e = floor(p1*.95+.5) where kod in( 'CERS' ) ;
  -- GET DIAGNOSTICS updated=ROW_COUNT;
  -- RAISE LOG 'setpricesp0ep0f updated % CERS',updated;
 --  update product
 --   set p0f = case when p0e*25 < 100000 then floor(25*p0e/10+.5)*10
 --   else floor(25*p0e/100+.5)*100
 --   end 
 --   where kod not ilike 'CERS' ;
--  GET DIAGNOSTICS updated=ROW_COUNT;
--  RAISE LOG 'setpricesp0ep0f updated % p0f not CERS',updated;
--  update product set p0f = p6 where kod ilike 'CERS';
--  GET DIAGNOSTICS updated=ROW_COUNT;
--  RAISE LOG 'setpricesp0ep0f updated % p0f CERS',updated;

end;
$$;


ALTER PROCEDURE public.setpricesp0ep0f() OWNER TO dba;

--
-- Name: slovo(integer); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.slovo(cc integer) RETURNS character varying
    LANGUAGE plpgsql
    AS $$
 begin
  if cc = 1 then
    return('');
  elseif cc = 2 then
    return('jeden');
  elseif cc = 3 then
    return('dva');
  elseif cc = 4 then
    return('tri');
  elseif cc = 5 then
    return('styri');
  elseif cc = 6 then
    return('päť');
  elseif cc = 7 then
    return('sesť');
  elseif cc = 8 then
    return('sedem');
  elseif cc = 9 then
    return('osem');
  elseif cc = 10 then
    return('deväť');
  end if;
  return(' ');
end;
$$;


ALTER FUNCTION public.slovo(cc integer) OWNER TO dba;

--
-- Name: stornopaper(integer, integer); Type: PROCEDURE; Schema: public; Owner: dba
--

CREATE PROCEDURE public.stornopaper(IN ccislo integer, IN typstorna integer)
    LANGUAGE plpgsql
    AS $$
  declare typ_pohybu integer;
  dcislo integer;
  cdokl integer;
  typ_dokladu varchar(30);
  mmoving varchar(1);
  ffrom varchar(30);
  tto varchar(30);
  fifogroup varchar(30);
  tenmoveid integer;
  textik varchar(255);
begin
  select m_type_id into typ_pohybu from movement where move_id = ccislo;
    if typstorna = 1 then
      select stock_id1 into ffrom from movement where move_id = ccislo;
      select stock_id2 into tto from movement where move_id = ccislo;
      select moving into mmoving from m_type where m_type_id = (select m_type_id from movement where move_id = ccislo);
    update movement set stock_id1 = null,stock_id2 = null
      where move_id = ccislo;
  end if;
  select doc_id into typ_dokladu from movement where move_id = ccislo;
  select number into dcislo from movement where move_id = ccislo;
  select number into cdokl from document where doc_id = typ_dokladu;
  if dcislo+1 = cdokl then
    update document set number = dcislo where doc_id = typ_dokladu;
    call delmove(ccislo);
  else
    call delmove(ccislo);
  end if;
end;
$$;


ALTER PROCEDURE public.stornopaper(IN ccislo integer, IN typstorna integer) OWNER TO dba;

--
-- Name: totalcount(integer); Type: PROCEDURE; Schema: public; Owner: dba
--

CREATE PROCEDURE public.totalcount(IN ccislo integer)
    LANGUAGE plpgsql
    AS $$
  declare xxtax1 decimal(11,2);
  xxtax2 decimal(11,2);
  xxround integer;
  xs0 decimal(11,2);
  xs1 decimal(11,2);
  xs2 decimal(11,2);
  dd1 decimal(11,2);
  dd2 decimal(11,2);
  xxza decimal(11,2);
  username varchar(255);
  adresa integer;
  xxcount integer;

begin
   select emp_id into strict username from movement where move_id=ccislo;
   select adr_id into strict adresa from movement where move_id=ccislo;



   select count(*) into strict xxcount from m_detail where move_id=ccislo group by tax;
   -- default tax podla toho co je v detaile pohybu
   select max(tax) into strict xxtax2 from m_detail where move_id=ccislo;
   -- ak tam nie je nic tak pre istotu z ciselnika office
   if xxtax2 = 0 then
	select tax2 into strict xxtax2 from office where user_name = username;
   end if;
   -- ak mame viac tax tak druha najvyssia je z ciselnika office
   if xxcount >=2 then
	select max(tax) into strict xxtax1 from m_detail where move_id=ccislo and tax <> xxtax2;
   else
	xxtax1 := 0;
   end if;
   if adresa in (select adr_id from address where firma like '_ Export%') then
		select perc into strict xxtax2 from vat where country=(select iso from address where adr_id=adresa) and category=2;
   end if;


   select decrounding into xxround from office where user_name = username;
   select sum(pcs*(price*(1-discount/100))) into strict xs0 from m_detail where move_id = ccislo and tax = 0;
   select sum(pcs*(price*(1-discount/100))) into strict xs1 from m_detail where move_id = ccislo and tax <> 0 and tax = xxtax1;
   select sum(pcs*(price*(1-discount/100))) into strict xs2 from m_detail where move_id = ccislo and tax <> 0 and tax = xxtax2;
  xs0 := coalesce(xs0,0);
  xs1 := coalesce(xs1,0);
  xs2 := coalesce(xs2,0);
  dd1 := coalesce(Round(xs1*(xxtax1/100),xxround),0);
  dd2 := coalesce(Round(xs2*(xxtax2/100),xxround),0);
  xxza := coalesce(Round(xs0+xs1+xs2,xxround)-xs0-xs1-xs2,0);
  update movement set total0 = xs0,total1 = xs1,total2 = xs2,
    tax1 = dd1,tax2 = dd2,rounding = xxza,total = xs0+xs1+xs2+dd1+dd2+xxza
    where move_id = ccislo;

end
;
$$;


ALTER PROCEDURE public.totalcount(IN ccislo integer) OWNER TO dba;

--
-- Name: totalcountasuser(integer, character varying); Type: PROCEDURE; Schema: public; Owner: dba
--

CREATE PROCEDURE public.totalcountasuser(IN ccislo integer, IN username character varying)
    LANGUAGE plpgsql
    AS $$

  declare xxtax1 decimal(8,6);
  xxtax2 decimal(8,6);
  xxround integer;
  xs0 decimal(11,2);
  xs1 decimal(11,2);
  xs2 decimal(11,2);
  dd1 decimal(11,2);
  dd2 decimal(11,2);
  xxza decimal(11,2);
  adresa integer;
  xxcount integer;
begin
   select tax1 into strict xxtax1 from office where user_name = username;
   select adr_id into strict adresa from movement where move_id = ccislo;


   select count(*) into strict xxcount from m_detail where move_id=ccislo group by tax;
   -- default tax podla toho co je v detaile pohybu
   select max(tax) into strict xxtax2 from m_detail where move_id=ccislo;
   -- ak tam nie je nic tak pre istotu z ciselnika office
   if xxtax2 = 0 then
	select tax2 into strict xxtax2 from office where user_name = username;
   end if;
   -- ak mame viac tax tak druha najvyssia je z ciselnika office
   if xxcount >=2 then
	select max(tax) into strict xxtax1 from m_detail where move_id=ccislo and tax <> xxtax2;
   else
	xxtax1 := 0;
   end if;
   if adresa in (select adr_id from address where firma like '_ Export%') then
		select perc into strict xxtax2 from vat where country=(select iso from address where adr_id=adresa) and category=2;
   end if;


   select decrounding into strict xxround from office where user_name = username;
   select sum(pcs*(price*(1-discount/100))) into strict xs0 from m_detail where move_id = ccislo and tax = 0;
   select sum(pcs*(price*(1-discount/100))) into strict xs1 from m_detail where move_id = ccislo and  tax <> 0 and tax = xxtax1;
   select sum(pcs*(price*(1-discount/100))) into strict xs2 from m_detail where move_id = ccislo and  tax <> 0 and tax = xxtax2;
  xs0 := coalesce(xs0,0,xs0);
  xs1 := coalesce(xs1,0,xs1);
  xs2 := coalesce(xs2,0,xs2);
  dd1 := coalesce(Round(xs1*(xxtax1/100),xxround),0,Round(xs1*(xxtax1/100),xxround));
  dd2 := coalesce(Round(xs2*(xxtax2/100),xxround),0,Round(xs2*(xxtax2/100),xxround));
  xxza := coalesce(Round(xs0+xs1+xs2,xxround)-xs0-xs1-xs2,0,Round(xs0+xs1+xs2,xxround)-xs0-xs1-xs2);
  update movement set total0 = xs0,total1 = xs1,total2 = xs2,
    tax1 = dd1,tax2 = dd2,rounding = xxza,total = xs0+xs1+xs2+dd1+dd2+xxza,emp_id = username
    where move_id = ccislo;
end;
$$;


ALTER PROCEDURE public.totalcountasuser(IN ccislo integer, IN username character varying) OWNER TO dba;

--
-- Name: triggerdetailchanged(); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.triggerdetailchanged() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
    BEGIN
		insert into  mlog (move_id,flags,note) values (OLD.move_id,'SysDc','(mat_id,pcs,price,tax) '||OLD.mat_id||','||OLD.pcs||','||OLD.price||','||OLD.tax||' to '||NEW.mat_id||','||NEW.pcs||','||NEW.price||','||NEW.tax);
    RETURN NEW;
    END;
$$;


ALTER FUNCTION public.triggerdetailchanged() OWNER TO dba;

--
-- Name: triggernewmovement(); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.triggernewmovement() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
    BEGIN
	    IF (TG_OP = 'DELETE') THEN
			insert into  mlog (move_id,flags,note,user_name) values (OLD.move_id,'SysNm','DELETED movement, total '||OLD.total,OLD.emp_id);
--         ELSIF (TG_OP = 'UPDATE') THEN
-- 			insert into  mlog (move_id,flags,note,user_name) values (NEW.move_id,'SysNm','Total '||NEW.total,NEW.emp_id);
        ELSIF (TG_OP = 'INSERT') THEN
			insert into  mlog (move_id,flags,note,user_name) values (NEW.move_id,'SysNm','Created',NEW.emp_id);
        END IF;
        RETURN NULL; -- result is ignored since this is an AFTER trigger
    END;
$$;


ALTER FUNCTION public.triggernewmovement() OWNER TO dba;

--
-- Name: triggerpdiscifnewproduct(); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.triggerpdiscifnewproduct() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
    BEGIN
	update product set p0a=(select perc from product_disc where ctype='B' and cgroup=(select cgroup from kod where kod=product.kod))
			,p0b=(select perc from product_disc where ctype='G' and cgroup=(select cgroup from kod where kod=product.kod))
			,p0c=(select perc from product_disc where ctype='P' and cgroup=(select cgroup from kod where kod=product.kod))
	where mat_id=NEW.mat_id;
    RETURN NEW;
    END;
$$;


ALTER FUNCTION public.triggerpdiscifnewproduct() OWNER TO dba;

--
-- Name: triggerpdiscinproduct(); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.triggerpdiscinproduct() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
    BEGIN
	update product set p0a=(select perc from product_disc where ctype='B' and cgroup=(select cgroup from kod where kod=product.kod))
			,p0b=(select perc from product_disc where ctype='G' and cgroup=(select cgroup from kod where kod=product.kod))
			,p0c=(select perc from product_disc where ctype='P' and cgroup=(select cgroup from kod where kod=product.kod))
	where kod=OLD.kod;
    RETURN NEW;
    END;
$$;


ALTER FUNCTION public.triggerpdiscinproduct() OWNER TO dba;

--
-- Name: triggertotalcount(); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.triggertotalcount() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
		DECLARE username varchar(255);
		declare oldtotal numeric(11,2);
		declare newtotal numeric(11,2);
    BEGIN
		select emp_id into username from movement where move_id=OLD.move_id;
		select total into oldtotal from movement where move_id=OLD.move_id;
		call totalcountasuser(OLD.move_id, username ); 
		select total into newtotal from movement where move_id=OLD.move_id;
		insert into  mlog (move_id,flags,note,user_name) values (OLD.move_id,'SysTtc','recounted total from '||oldtotal||' to '||newtotal, username);
    RETURN NEW;
    END;
$$;


ALTER FUNCTION public.triggertotalcount() OWNER TO dba;

--
-- Name: ucto_kartymodel(); Type: PROCEDURE; Schema: public; Owner: dba
--

CREATE PROCEDURE public.ucto_kartymodel()
    LANGUAGE plpgsql
    AS $$
  declare pom integer;
  declare ucto_rok integer;
  fsum integer;
  curMain scroll cursor for
    select md.pcs as xxtotalpcs,mo.number as xnumber,
      md.price*(1-md.discount/100) as xprice,mo.d1 as xd1,mo.m_type_id as xmtid,case when mo.m_type_id in( 23,24,55,87,102,110,112,114,115,116,147,149,167,169,25 ) then 'F' else 'R' end as plusminus,product.mat_id as xxkod,
      product.kod as kod, mo.move_id as xmoveid,md.mat_id as xmi,
	  case when mo.m_type_id = 116 then 'ANO' else 'NIE' end as dobropis
      from m_detail as md
        ,movement as mo
        ,product
      /* FILTER NA DATUM */
      where year(mo.d1) = ucto_rok
      and(md.move_id = mo.move_id)
      and(product.mat_id = md.mat_id)
/*	  and(product.kod <> 'SERV') */
      and ( m_type_id in( 69,55,23,24,86,87,102,97,109,110,114,115,116,167,169,168,170,25 ) or
        (m_type_id in (147,149,148) and emp_id='wowbb' ) )
      and not md.move_id = any(select move_id from movement where adr_id in( 5039,5040,4370,3882,3257,3405,3208,3430,3408,3418,3434,3432,3445,3444,3511,3512,3624,3731,3446 ) and m_type_id in( 87,97 ) )
	  
      order by plusminus desc,dobropis asc,xd1 asc,xmtid desc,xprice desc;
  cur2 scroll cursor (xxxkod integer, xd1 date) for
    select matid as umatid,remains as upcs,n_price as uprice
      from yuctomodel
      where xx = 'R' and remains > 0 and mat_id = xxxkod and 
      (d1 <= xd1 or (year(d1) = year(xd1) and month(d1) = month(xd1) ) )
      order by d1 asc,n_price desc;
  -- 117tky potrebuju rovnaku nakupnu
  -- 22.5.2018 modifikoval som order na d1 desc, nech necha potrebne veci pre ostatne predaje
  cur3 scroll cursor (xxxkod integer, xd1 date, xprice decimal(11,2) )  for
    select matid as umatid,remains as upcs,n_price as uprice
      from yuctomodel
      where xx = 'R' and remains > 0 and mat_id = xxxkod and 
      (d1 <= xd1 or (year(d1) = year(xd1) and month(d1) = month(xd1) ) )
      and n_price = xprice
      order by d1 desc,n_price desc;
      rec RECORD;
      rec2 RECORD;
      rec3 RECORD;
begin
  ucto_rok := 2024;
    drop table if exists yuctomodel;
  create table yuctomodel(
    matid serial,
    xx varchar(1) null,
    kod integer null,
    n_price decimal(11,2) null default 0,
    s_price decimal(11,2) null default 0,
    d1 date null,
    fnumber integer null,
    pcs integer null,
    remains integer null,
    beforeonstock integer null,
    move_id integer null,
    mat_id integer null,
    doc_nr integer null default 0,
    primary key(matid)
    );
  create index xxracioidx on yuctomodel(xx asc);
  create index remainsracioidx on yuctomodel(remains asc);
  create index kodracioidx on yuctomodel(kod asc);
  create index n_priceracioidx on yuctomodel(n_price asc);
  create index pcsracioidx on yuctomodel(pcs asc);
  create index move_idracioidx on yuctomodel(move_id asc);
    for rec in curMain LOOP
    fsum := rec.xxtotalpcs;
    if( (rec.xmtid in( 23,24,55,87,102,110,112,114,115,147,149,167,169,25 ) and fsum > 0 ) or (rec.xmtid in (116) and rec.kod in ('BTHC', 'ATHC', 'WTHS', 'WTHC')) ) then
      for rec2 in cur2 ( xxxkod := rec.xxkod, xd1 := rec.xd1 ) LOOP
        if fsum > 0 then
          select sum(remains) into strict pom from yuctomodel where n_price = rec2.uprice and xx = 'R' and remains > 0 and mat_id = rec.xxkod and 
          (d1 <= rec.xd1 or (year(d1) = year(rec.xd1) and month(d1) = month(rec.xd1) ) );
          if rec2.upcs >= fsum then
            update yuctomodel set remains = remains-fsum where matid = rec2.umatid;
            insert into yuctomodel( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) 
              values( 'F',rec.xxkod,rec2.uprice,rec.xprice,rec.xd1,-fsum,0,pom,rec.xnumber,rec.xmoveid,rec.xmi ) ;
            fsum := 0;
          else
            update yuctomodel set remains = 0 where matid = rec2.umatid;
            insert into yuctomodel( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) 
              values( 'F',rec.xxkod,rec2.uprice,rec.xprice,rec.xd1,-rec2.upcs,0,pom,rec.xnumber,rec.xmoveid,rec.xmi ) ;
            fsum := fsum - rec2.upcs;
          end if;
        end if;
      end loop;
      if fsum <> 0 then
        insert into yuctomodel( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) 
          values( '!',rec.xxkod,0,rec.xprice,rec.xd1,-fsum,0,0,rec.xnumber,rec.xmoveid,rec.xmi ); 
      end if;
    elseif (rec.xmtid in( 116 ) and fsum > 0 and rec.kod not in ('BTHC', 'ATHC', 'WTHS', 'WTHC')) then
      for rec3 in cur3  (xxxkod := rec.xxkod, xd1 := rec.xd1, xprice := rec.xprice ) LOOP  
        if fsum > 0 then
          select sum(remains) into strict pom from yuctomodel where n_price = rec3.uprice and xx = 'R' and remains > 0 and mat_id = rec.xxkod and 
          (d1 <= rec.xd1 or (year(d1) = year(rec.xd1) and month(d1) = month(rec.xd1) ) );
          if rec3.upcs >= fsum then
            update yuctomodel set remains = remains-fsum where matid = rec3.umatid;
            insert into yuctomodel( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) values 
              ( 'F',rec.xxkod,rec3.uprice,rec.xprice,rec.xd1,-fsum,0,pom,rec.xnumber,rec.xmoveid,rec.xmi ) ;
            fsum := 0;
          else
            update yuctomodel set remains = 0 where matid = rec3.umatid;
            insert into yuctomodel( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) values( 'F',rec.xxkod,rec3.uprice,rec.xprice,rec.xd1,-rec3.upcs,0,pom,rec.xnumber,rec.xmoveid,rec.xmi ) ;
            fsum := fsum - rec3.upcs;
          end if;
        end if;
      end loop;
      if fsum <> 0 then
        insert into yuctomodel( xx,kod,n_price,s_price,d1,pcs,remains,beforeonstock,fnumber,move_id,mat_id ) values( '!',rec.xxkod,0,rec.xprice,rec.xd1,-fsum,0,0,rec.xnumber,rec.xmoveid,rec.xmi ); 
      /* prijemky */
      end if;
    else 
	  if rec.xmtid in ( 23,24,55,87,102,110,112,114,115,116,147,149,167,169,25 ) then fsum := -fsum;
	  end if;
      insert into yuctomodel( xx,kod,n_price,d1,pcs,remains,fnumber,move_id,mat_id ) values( 'R',rec.xxkod,rec.xprice,rec.xd1,fsum,fsum,rec.xnumber,rec.xmoveid,rec.xmi ) ;
    end if;
  end loop;
end;
$$;


ALTER PROCEDURE public.ucto_kartymodel() OWNER TO dba;

--
-- Name: updatefifoprofit(integer); Type: PROCEDURE; Schema: public; Owner: dba
--

CREATE PROCEDURE public.updatefifoprofit(IN xmatid integer)
    LANGUAGE plpgsql
    AS $$
begin
delete from fifoprofit where mat_id=xmatid;
insert into fifoprofit (mat_id,move_id,d1,pcs,price_purchased,price_sold,profit)  (
                    select mat_id,s.move_id,s.d1,s.pcs,price_purchased,price_sold,
                        sum(price_sold - price_purchased) over(order by rn) as profit 
                    from
                        (
                            select 
                                row_number() over(order by d1, mat_id) as rn,
                                mat_id, move_id,d1, moving, pcs, xprice as price_purchased
                            from fifoview, generate_series(1, abs(pcs))
                            where moving = 'R' and mat_id = xmatid order by mat_id
                        ) p
                        full join
                        (
                            select
                                row_number() over(order by d1, mat_id) as rn,
                                mat_id, move_id, d1, moving, pcs, xprice as price_sold
                            from fifoview, generate_series(1, abs(pcs))
                            where moving = 'S' and mat_id = xmatid order by mat_id
                        ) s using (rn, mat_id)
);
end;
$$;


ALTER PROCEDURE public.updatefifoprofit(IN xmatid integer) OWNER TO dba;

--
-- Name: year(date); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.year(d1 date) RETURNS integer
    LANGUAGE plpgsql
    AS $$
begin
  return extract(year from d1);
end
$$;


ALTER FUNCTION public.year(d1 date) OWNER TO dba;

--
-- Name: year(timestamp with time zone); Type: FUNCTION; Schema: public; Owner: dba
--

CREATE FUNCTION public.year(d1 timestamp with time zone) RETURNS integer
    LANGUAGE plpgsql
    AS $$
begin
  return extract(year from d1);
end
$$;


ALTER FUNCTION public.year(d1 timestamp with time zone) OWNER TO dba;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: address; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.address (
    firma character varying(255),
    street character varying(33),
    city character varying(33),
    zip character varying(6),
    ico character varying(8),
    drc1 character varying(16),
    drc2 character varying(16),
    account character varying(25),
    bankcode character varying(4),
    memo character varying(40),
    adr_id integer NOT NULL,
    firma_id integer DEFAULT 0 NOT NULL,
    info character varying(255),
    credit_complet numeric(11,2),
    discount numeric(5,3) DEFAULT 0 NOT NULL,
    expire numeric(3,0) DEFAULT 0,
    akc_disc numeric(5,3) DEFAULT 0 NOT NULL,
    currency character varying(3) DEFAULT ' Sk'::character varying,
    owner_name character varying(26),
    area integer,
    iso character varying(2) DEFAULT 'sk'::character varying NOT NULL
);


ALTER TABLE public.address OWNER TO dba;

--
-- Name: address_adr_id_seq; Type: SEQUENCE; Schema: public; Owner: dba
--

CREATE SEQUENCE public.address_adr_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.address_adr_id_seq OWNER TO dba;

--
-- Name: address_adr_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dba
--

ALTER SEQUENCE public.address_adr_id_seq OWNED BY public.address.adr_id;


--
-- Name: adr_contact; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.adr_contact (
    adr_id integer NOT NULL,
    number character varying(20) NOT NULL,
    describe character varying(25),
    person character varying(20),
    repli_id integer DEFAULT 0 NOT NULL
);


ALTER TABLE public.adr_contact OWNER TO dba;

--
-- Name: authy; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.authy (
    id integer NOT NULL,
    userid integer NOT NULL,
    authyid integer NOT NULL,
    cellphone character varying(255) NOT NULL,
    countrycode integer NOT NULL
);


ALTER TABLE public.authy OWNER TO dba;

--
-- Name: authy_id_seq; Type: SEQUENCE; Schema: public; Owner: dba
--

CREATE SEQUENCE public.authy_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.authy_id_seq OWNER TO dba;

--
-- Name: authy_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dba
--

ALTER SEQUENCE public.authy_id_seq OWNED BY public.authy.id;


--
-- Name: authy_login; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.authy_login (
    id integer NOT NULL,
    authyid integer NOT NULL,
    ip character varying(255) NOT NULL,
    expire_at timestamp(0) without time zone NOT NULL,
    hostname character varying(255),
    device_type character varying(255),
    ip_org character varying(255),
    ip_country character varying(255),
    os character varying(255),
    browser character varying(255),
    brand character varying(255)
);


ALTER TABLE public.authy_login OWNER TO dba;

--
-- Name: authy_login_id_seq; Type: SEQUENCE; Schema: public; Owner: dba
--

CREATE SEQUENCE public.authy_login_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.authy_login_id_seq OWNER TO dba;

--
-- Name: authy_login_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dba
--

ALTER SEQUENCE public.authy_login_id_seq OWNED BY public.authy_login.id;


--
-- Name: countries; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.countries (
    id integer NOT NULL,
    name character varying(64) DEFAULT ''::character varying NOT NULL,
    iso_code_2 character varying(2) DEFAULT ''::character varying NOT NULL,
    iso_code_3 character varying(3) DEFAULT ''::character varying NOT NULL,
    address_format_id integer DEFAULT 0 NOT NULL
);


ALTER TABLE public.countries OWNER TO dba;

--
-- Name: country; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.country (
    iso character varying(2) NOT NULL,
    name character varying(80) NOT NULL,
    printable_name character varying(80) NOT NULL,
    iso3 character varying(3),
    numcode smallint
);


ALTER TABLE public.country OWNER TO dba;

--
-- Name: currencies; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.currencies (
    cur character varying(3) NOT NULL,
    name character varying(255),
    rate money
);


ALTER TABLE public.currencies OWNER TO dba;

--
-- Name: TABLE currencies; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON TABLE public.currencies IS 'currencies and rates';


--
-- Name: COLUMN currencies.cur; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON COLUMN public.currencies.cur IS 'abbreviation form';


--
-- Name: COLUMN currencies.name; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON COLUMN public.currencies.name IS 'full text of it';


--
-- Name: customers; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.customers (
    id integer NOT NULL,
    gender character varying(1) DEFAULT ''::character varying NOT NULL,
    firstname character varying(32) DEFAULT ''::character varying NOT NULL,
    lastname character varying(32) DEFAULT ''::character varying NOT NULL,
    dob character varying(8) DEFAULT ''::character varying NOT NULL,
    email_address character varying(96) DEFAULT ''::character varying NOT NULL,
    street_address character varying(64) DEFAULT ''::character varying NOT NULL,
    suburb character varying(32),
    postcode character varying(10) DEFAULT ''::character varying NOT NULL,
    city character varying(32) DEFAULT ''::character varying NOT NULL,
    state character varying(32),
    telephone character varying(32) DEFAULT ''::character varying NOT NULL,
    fax character varying(32),
    password character varying(40) DEFAULT ''::character varying NOT NULL,
    country_id integer DEFAULT 0 NOT NULL,
    zone_id integer DEFAULT 0 NOT NULL,
    nick character varying(20) DEFAULT ''::character varying NOT NULL,
    card_nr character varying(32) DEFAULT ''::character varying NOT NULL,
    issue_d date,
    expire_d date,
    card_type character varying(2) DEFAULT 'B'::character varying NOT NULL,
    card_discount numeric(2,0) DEFAULT '0'::numeric NOT NULL,
    cust_currency character varying(3) DEFAULT 'EUR'::character varying NOT NULL,
    card_memo character varying(255)
);


ALTER TABLE public.customers OWNER TO dba;

--
-- Name: customers_2022_11_08; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.customers_2022_11_08 (
    id integer,
    gender character varying(1),
    firstname character varying(32),
    lastname character varying(32),
    dob character varying(8),
    email_address character varying(96),
    street_address character varying(64),
    suburb character varying(32),
    postcode character varying(10),
    city character varying(32),
    state character varying(32),
    telephone character varying(32),
    fax character varying(32),
    password character varying(40),
    country_id integer,
    zone_id integer,
    nick character varying(20),
    card_nr character varying(32),
    issue_d date,
    expire_d date,
    card_type character varying(2),
    card_discount numeric(2,0),
    cust_currency character varying(3),
    card_memo character varying(255)
);


ALTER TABLE public.customers_2022_11_08 OWNER TO dba;

--
-- Name: customers_id_seq; Type: SEQUENCE; Schema: public; Owner: dba
--

CREATE SEQUENCE public.customers_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.customers_id_seq OWNER TO dba;

--
-- Name: customers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dba
--

ALTER SEQUENCE public.customers_id_seq OWNED BY public.customers.id;


--
-- Name: document; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.document (
    doc_id character varying(5) NOT NULL,
    name character varying(40),
    number integer
);


ALTER TABLE public.document OWNER TO dba;

--
-- Name: dyninfo; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.dyninfo (
    mat_id integer NOT NULL,
    kod character varying(255),
    model character varying(255)
);


ALTER TABLE public.dyninfo OWNER TO dba;

--
-- Name: dyninfox; Type: TABLE; Schema: public; Owner: dba
--

CREATE UNLOGGED TABLE public.dyninfox (
    mat_id integer NOT NULL,
    repli_id integer NOT NULL,
    kod character varying(4),
    model character varying(255),
    shop character varying(255) NOT NULL,
    remains integer
);


ALTER TABLE public.dyninfox OWNER TO dba;

--
-- Name: dyninfoy; Type: TABLE; Schema: public; Owner: dba
--

CREATE UNLOGGED TABLE public.dyninfoy (
    mat_id integer NOT NULL,
    repli_id integer NOT NULL,
    kod character varying(255),
    model character varying(255) NOT NULL,
    shop character varying(255) NOT NULL,
    remains integer NOT NULL
);


ALTER TABLE public.dyninfoy OWNER TO dba;

--
-- Name: eshop; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.eshop (
    id integer NOT NULL,
    status character varying(4) DEFAULT 'N'::character varying NOT NULL,
    orderid integer NOT NULL,
    "order" integer,
    created timestamp without time zone,
    email character varying(255),
    iname character varying(255),
    isurname character varying(255),
    istreet character varying(255),
    icity character varying(255),
    izip character varying(255),
    iphone character varying(255),
    icountry character varying(255),
    icompany character varying(255),
    iico character varying(255),
    idic character varying(255),
    dcompany character varying(255),
    dname character varying(255),
    dsurname character varying(255),
    dstreet character varying(255),
    dcity character varying(255),
    dzip character varying(255),
    dphone character varying(255),
    dcountry character varying(255),
    gcompany character varying(255),
    gname character varying(255),
    gsurname character varying(255),
    gstreet character varying(255),
    gcity character varying(255),
    gzip character varying(255),
    gphone character varying(255),
    gcountry character varying(255),
    gift character varying(1) DEFAULT 0 NOT NULL,
    note text,
    shipment character varying(255),
    branch character varying(255),
    voucher character varying(255),
    payment character varying(255),
    totalitems numeric(11,2),
    totalitemsnovat numeric(11,2),
    totalshipment numeric(11,2),
    totalpayment numeric(11,2),
    totalclub numeric(11,2),
    total numeric(11,2),
    currency character varying(11),
    products text,
    alljson text
);


ALTER TABLE public.eshop OWNER TO dba;

--
-- Name: eshop_id_seq; Type: SEQUENCE; Schema: public; Owner: dba
--

CREATE SEQUENCE public.eshop_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.eshop_id_seq OWNER TO dba;

--
-- Name: eshop_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dba
--

ALTER SEQUENCE public.eshop_id_seq OWNED BY public.eshop.id;


--
-- Name: fifoprofit; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.fifoprofit (
    mat_id integer,
    move_id integer,
    d1 date,
    pcs integer,
    price_purchased numeric,
    price_sold numeric,
    profit numeric
);


ALTER TABLE public.fifoprofit OWNER TO dba;

--
-- Name: m_detail; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.m_detail (
    move_id integer NOT NULL,
    mat_id integer NOT NULL,
    price numeric(11,2),
    pcs integer,
    discount numeric(17,8) DEFAULT 0,
    tax numeric(2,0) DEFAULT 0,
    m_repli_id integer DEFAULT 1 NOT NULL,
    currency character varying(3),
    p_repli_id integer DEFAULT 0 NOT NULL,
    fifo_currency character varying(3),
    fifo_price numeric(17,8) DEFAULT 0,
    fifo_move_id integer DEFAULT 0 NOT NULL,
    detail_info character varying(255) NOT NULL,
    fifo_repli_id smallint NOT NULL,
    all1 smallint
);


ALTER TABLE public.m_detail OWNER TO dba;

--
-- Name: m_type; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.m_type (
    m_type_id integer NOT NULL,
    name character varying(40),
    mdescr character varying(100),
    doc_id character varying(5),
    flag integer DEFAULT 1,
    usergroup character varying(40) DEFAULT 'managers'::character varying,
    moving character varying(1) DEFAULT 'N'::character varying NOT NULL,
    stock_id1 character varying(20) DEFAULT 's0'::character varying,
    stock_id2 character varying(20) DEFAULT 's1'::character varying,
    fifogroup character varying(2) DEFAULT 'NA'::character varying NOT NULL,
    mdescr2 character varying(20),
    changeprice character varying(1) DEFAULT 'Y'::character varying,
    taxable character varying(1) DEFAULT 'N'::character varying NOT NULL
);


ALTER TABLE public.m_type OWNER TO dba;

--
-- Name: TABLE m_type; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON TABLE public.m_type IS 'type of movement (invoice, moving, etc.)';


--
-- Name: COLUMN m_type.name; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON COLUMN public.m_type.name IS 'nickname of movetype';


--
-- Name: COLUMN m_type.mdescr; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON COLUMN public.m_type.mdescr IS 'more text if desired';


--
-- Name: COLUMN m_type.doc_id; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON COLUMN public.m_type.doc_id IS 'which number of document it changes';


--
-- Name: COLUMN m_type.flag; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON COLUMN public.m_type.flag IS 'for which programm code it is needed';


--
-- Name: COLUMN m_type.usergroup; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON COLUMN public.m_type.usergroup IS 'who can handle this type of movement';


--
-- Name: COLUMN m_type.moving; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON COLUMN public.m_type.moving IS '<S>ell <R>eceive <M>ove <N>othing <X>special /pr. pokl. doklad';


--
-- Name: COLUMN m_type.stock_id1; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON COLUMN public.m_type.stock_id1 IS 'From/ To which stock - default value';


--
-- Name: COLUMN m_type.stock_id2; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON COLUMN public.m_type.stock_id2 IS 'If moving, into which stock';


--
-- Name: COLUMN m_type.fifogroup; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON COLUMN public.m_type.fifogroup IS '<Y>es <N>o';


--
-- Name: movement; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.movement (
    move_id integer NOT NULL,
    adr_id integer,
    d1 date,
    d2 date,
    d3 date,
    doc_id character varying(5),
    number integer,
    emp_id character varying(20),
    total numeric(11,2),
    payment character varying(1) DEFAULT 'H'::character varying,
    "X" character varying(1) DEFAULT '0'::character varying,
    c_number integer,
    total0 numeric(11,2),
    total1 numeric(11,2),
    total2 numeric(11,2),
    rounding numeric(11,2),
    tax1 numeric(11,2),
    tax2 numeric(11,2),
    discount numeric(11,2),
    text1 character varying,
    text2 character varying,
    m_type_id integer DEFAULT 1,
    repli_id integer DEFAULT 1 NOT NULL,
    adr_repli_id integer DEFAULT 0 NOT NULL,
    stock_id1 character varying(6),
    stock_id2 character varying(6),
    acc_reg_id integer DEFAULT 1,
    acc_repli_id integer DEFAULT 1,
    parent_move_id integer
);


ALTER TABLE public.movement OWNER TO dba;

--
-- Name: COLUMN movement."X"; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON COLUMN public.movement."X" IS 'if N than type 87 is intended WITHOUT VAT';


--
-- Name: COLUMN movement.total0; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON COLUMN public.movement.total0 IS 'total with tax 0%';


--
-- Name: COLUMN movement.tax1; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON COLUMN public.movement.tax1 IS 'tax amount nr. 1';


--
-- Name: COLUMN movement.tax2; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON COLUMN public.movement.tax2 IS 'tax amount nr. 2';


--
-- Name: COLUMN movement.discount; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON COLUMN public.movement.discount IS 'sum of pcs*price minus (total0+total1+total2)';


--
-- Name: COLUMN movement.text1; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON COLUMN public.movement.text1 IS 'add info for paper if needed (nr of order, etc.)';


--
-- Name: COLUMN movement.text2; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON COLUMN public.movement.text2 IS 'text in paper (short sentences)';


--
-- Name: COLUMN movement.m_type_id; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON COLUMN public.movement.m_type_id IS 'exact type of movement';


--
-- Name: COLUMN movement.acc_reg_id; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON COLUMN public.movement.acc_reg_id IS 'contact to acc_register';


--
-- Name: nbs_rates; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.nbs_rates (
    nbs_date date NOT NULL,
    nbs_cur character varying(3) NOT NULL,
    nbs_rate numeric(11,3) NOT NULL,
    nbs_multi numeric(11,0) NOT NULL
);


ALTER TABLE public.nbs_rates OWNER TO dba;

--
-- Name: fifoview; Type: VIEW; Schema: public; Owner: dba
--

CREATE VIEW public.fifoview AS
 SELECT row_number() OVER () AS transactionid,
    mat_id,
    move_id,
    d1,
    moving,
    pcs,
    xprice
   FROM ( SELECT d.mat_id,
            d.move_id,
            t.moving,
            m.d1,
            d.pcs,
                CASE
                    WHEN (((d.currency)::text <> 'EUR'::text) AND (d.currency IS NOT NULL)) THEN (d.price * COALESCE(( SELECT nbs_rates.nbs_rate
                       FROM public.nbs_rates
                      WHERE (((nbs_rates.nbs_cur)::text = (d.currency)::text) AND (nbs_rates.nbs_date <= m.d1))
                      ORDER BY m.d1 DESC
                     LIMIT 1), (1)::numeric))
                    ELSE d.price
                END AS xprice
           FROM ((public.movement m
             JOIN public.m_detail d ON ((m.move_id = d.move_id)))
             JOIN public.m_type t ON ((m.m_type_id = t.m_type_id)))
          WHERE ((t.moving)::text = ANY (ARRAY[('R'::character varying)::text, ('S'::character varying)::text]))
          ORDER BY d.mat_id, d.detail_info, m.d1, m.move_id) alldata;


ALTER VIEW public.fifoview OWNER TO dba;

--
-- Name: garmin; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.garmin (
    idx integer,
    idy integer,
    id bigint,
    model text,
    ean13 text,
    xx text
);


ALTER TABLE public.garmin OWNER TO dba;

--
-- Name: garminstock; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.garminstock (
    code character varying(255) NOT NULL,
    ean character varying(255),
    free integer
);


ALTER TABLE public.garminstock OWNER TO dba;

--
-- Name: heureka; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.heureka (
    id integer NOT NULL,
    ctime timestamp without time zone,
    mat_id integer,
    hmodel character varying(255),
    hurl character varying(255),
    estock numeric(11,2),
    wdl numeric(11,2),
    minprice numeric(11,2),
    s0 numeric(11,2),
    s1 numeric(11,2),
    s2 numeric(11,2),
    s3 numeric(11,2),
    s4 numeric(11,2),
    s5 numeric(11,2),
    s6 numeric(11,2),
    s7 numeric(11,2),
    s8 numeric(11,2),
    s9 numeric(11,2),
    n0 character varying(255),
    n1 character varying(255),
    n2 character varying(255),
    n3 character varying(255),
    n4 character varying(255),
    n5 character varying(255),
    n6 character varying(255),
    n7 character varying(255),
    n8 character varying(255),
    n9 character varying(255)
);


ALTER TABLE public.heureka OWNER TO dba;

--
-- Name: heureka_id_seq; Type: SEQUENCE; Schema: public; Owner: dba
--

CREATE SEQUENCE public.heureka_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.heureka_id_seq OWNER TO dba;

--
-- Name: heureka_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dba
--

ALTER SEQUENCE public.heureka_id_seq OWNED BY public.heureka.id;


--
-- Name: kod; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.kod (
    kod character varying(4) NOT NULL,
    name0 character varying(60),
    name1 character varying(60),
    name2 character varying(60),
    name3 character varying(60),
    label1 character varying(60),
    name4 character varying(60),
    cur character varying(3) DEFAULT 'USD'::character varying,
    cgroup integer DEFAULT 1 NOT NULL
);


ALTER TABLE public.kod OWNER TO dba;

--
-- Name: labels; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.labels (
    line1 character varying(255) NOT NULL,
    line2 character varying(255) NOT NULL,
    line3 character varying(255) NOT NULL,
    line4 character varying(255),
    line5 character varying(255),
    line6 character varying(255)
);


ALTER TABLE public.labels OWNER TO dba;

--
-- Name: m1; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.m1 (
    move_id integer,
    adr_id integer,
    d1 date,
    d2 date,
    d3 date,
    doc_id character varying(5),
    number integer,
    emp_id character varying(10),
    total numeric(11,2),
    payment character varying(1),
    "X" character varying(1),
    c_number integer,
    total0 numeric(11,2),
    total1 numeric(11,2),
    total2 numeric(11,2),
    rounding numeric(11,2),
    tax1 numeric(11,2),
    tax2 numeric(11,2),
    discount numeric(11,2),
    text1 character varying,
    text2 character varying,
    m_type_id integer,
    repli_id integer,
    adr_repli_id integer,
    stock_id1 character varying(6),
    stock_id2 character varying(6),
    acc_reg_id integer,
    acc_repli_id integer
);


ALTER TABLE public.m1 OWNER TO dba;

--
-- Name: product; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.product (
    mat_id integer DEFAULT 999999 NOT NULL,
    ean13 character varying(13) NOT NULL,
    kod character varying(4) NOT NULL,
    model character varying(255) NOT NULL,
    price numeric(11,2) DEFAULT 0,
    p0 numeric(11,2) DEFAULT 0,
    p1 numeric(11,2) DEFAULT 0,
    p2 numeric(11,2) DEFAULT 0.0,
    p3 numeric(11,2) DEFAULT 0,
    p4 numeric(11,2) DEFAULT 0,
    p5 numeric(11,2) DEFAULT 0,
    p6 numeric(11,2) DEFAULT 0,
    p8 numeric(11,2) DEFAULT 0,
    p9 numeric(11,2) DEFAULT 0,
    pdescr character varying(120) DEFAULT ''::character varying NOT NULL,
    tax numeric(2,0) DEFAULT 2,
    unit character varying(3) DEFAULT ' '::character varying NOT NULL,
    min numeric(9,3) DEFAULT 0,
    max numeric(9,3) DEFAULT 999999,
    p7 numeric(11,2) DEFAULT 0,
    p0a numeric(11,2),
    p0b numeric(11,2),
    p0c numeric(11,2),
    p0d numeric(11,2),
    p0e numeric(11,2),
    p0f numeric(11,2),
    p0g numeric(11,2),
    p0h numeric(11,2),
    p0i numeric(11,2),
    p0j numeric(11,2),
    p1b numeric(11,2),
    p1c numeric(11,2),
    p1d numeric(11,2),
    p1e numeric(11,2),
    p1f numeric(11,2),
    p1g numeric(11,2),
    p1h numeric(11,2),
    p1i numeric(11,2),
    p1j numeric(11,2),
    p2b numeric(11,2),
    p2c numeric(11,2),
    p2d numeric(11,2),
    p2e numeric(11,2),
    p2f numeric(11,2),
    p2g numeric(11,2),
    p2h numeric(11,2),
    p2i numeric(11,2),
    p2j numeric(11,2),
    p1a numeric(11,2),
    p2a numeric(11,2)
);


ALTER TABLE public.product OWNER TO dba;

--
-- Name: TABLE product; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON TABLE public.product IS 'Main products table - must be the same in all officies';


--
-- Name: COLUMN product.min; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON COLUMN public.product.min IS 'minimum on the stock...';


--
-- Name: COLUMN product.max; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON COLUMN public.product.max IS 'maximum on the stock...';


--
-- Name: COLUMN product.p0a; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON COLUMN public.product.p0a IS 'discount group A';


--
-- Name: m_detail_prod; Type: VIEW; Schema: public; Owner: dba
--

CREATE VIEW public.m_detail_prod AS
 SELECT d.move_id,
    d.m_repli_id AS repli_id,
    d.mat_id,
    p.kod,
    p.model,
    d.price,
    d.pcs,
    d.discount,
    d.tax,
    d.currency,
    d.detail_info
   FROM (public.m_detail d
     JOIN public.product p ON ((d.mat_id = p.mat_id)));


ALTER VIEW public.m_detail_prod OWNER TO dba;

--
-- Name: m_type_m_type_id_seq; Type: SEQUENCE; Schema: public; Owner: dba
--

CREATE SEQUENCE public.m_type_m_type_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.m_type_m_type_id_seq OWNER TO dba;

--
-- Name: m_type_m_type_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dba
--

ALTER SEQUENCE public.m_type_m_type_id_seq OWNED BY public.m_type.m_type_id;


--
-- Name: m_user_config; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.m_user_config (
    id integer NOT NULL,
    userid integer NOT NULL,
    db character varying(255) NOT NULL,
    clip_id integer NOT NULL,
    pictures character varying(4) DEFAULT 'NO'::character varying NOT NULL,
    price character varying(5) DEFAULT 'p0'::character varying NOT NULL,
    adr_id integer NOT NULL,
    stock_id1 character varying(4),
    stock_id2 character varying(4),
    m_type_id integer NOT NULL,
    text1 character varying,
    text2 character varying,
    d1 date,
    d2 date,
    d3 date,
    active integer DEFAULT 0
);


ALTER TABLE public.m_user_config OWNER TO dba;

--
-- Name: m_user_config_id_seq; Type: SEQUENCE; Schema: public; Owner: dba
--

CREATE SEQUENCE public.m_user_config_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.m_user_config_id_seq OWNER TO dba;

--
-- Name: m_user_config_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dba
--

ALTER SEQUENCE public.m_user_config_id_seq OWNED BY public.m_user_config.id;


--
-- Name: m_user_id_seq; Type: SEQUENCE; Schema: public; Owner: dba
--

CREATE SEQUENCE public.m_user_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.m_user_id_seq OWNER TO dba;

--
-- Name: migration; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.migration (
    version character varying(180) NOT NULL,
    apply_time integer
);


ALTER TABLE public.migration OWNER TO dba;

--
-- Name: minfo; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.minfo (
    id integer NOT NULL,
    move_id integer NOT NULL,
    status character varying(4) DEFAULT 'N'::character varying NOT NULL,
    orderid integer,
    email character varying(255),
    iname character varying(255),
    isurname character varying(255),
    istreet character varying(255),
    icity character varying(255),
    izip character varying(255),
    iphone character varying(255),
    icountry character varying(255),
    icompany character varying(255),
    iico character varying(255),
    idic character varying(255),
    iicdph character varying(255),
    dcompany character varying(255),
    dname character varying(255),
    dsurname character varying(255),
    dstreet character varying(255),
    dcity character varying(255),
    dzip character varying(255),
    dphone character varying(255),
    dcountry character varying(255),
    mnote text,
    shipment character varying(255),
    branch character varying(255),
    voucher character varying(255),
    payment character varying(255),
    incoterms character varying(255)
);


ALTER TABLE public.minfo OWNER TO dba;

--
-- Name: minfo_id_seq; Type: SEQUENCE; Schema: public; Owner: dba
--

CREATE SEQUENCE public.minfo_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.minfo_id_seq OWNER TO dba;

--
-- Name: minfo_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dba
--

ALTER SEQUENCE public.minfo_id_seq OWNED BY public.minfo.id;


--
-- Name: mlog; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.mlog (
    id integer NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    move_id integer NOT NULL,
    flags character varying(255),
    note character varying(255),
    user_name character varying(255)
);


ALTER TABLE public.mlog OWNER TO dba;

--
-- Name: mlog_id_seq; Type: SEQUENCE; Schema: public; Owner: dba
--

CREATE SEQUENCE public.mlog_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.mlog_id_seq OWNER TO dba;

--
-- Name: mlog_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dba
--

ALTER SEQUENCE public.mlog_id_seq OWNED BY public.mlog.id;


--
-- Name: movement_move_id_seq; Type: SEQUENCE; Schema: public; Owner: dba
--

CREATE SEQUENCE public.movement_move_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.movement_move_id_seq OWNER TO dba;

--
-- Name: movement_move_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dba
--

ALTER SEQUENCE public.movement_move_id_seq OWNED BY public.movement.move_id;


--
-- Name: office_old; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.office_old (
    office_nr integer NOT NULL,
    descr character varying(20) NOT NULL,
    active integer DEFAULT 0,
    tax1 numeric(2,0),
    tax2 numeric(2,0),
    decrounding integer,
    currency character varying(3),
    price character varying(255)
);


ALTER TABLE public.office_old OWNER TO dba;

--
-- Name: wuser; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.wuser (
    id integer NOT NULL,
    username character varying(255) NOT NULL,
    auth_key character varying(32) NOT NULL,
    password_hash character varying(255) NOT NULL,
    password_reset_token character varying(255) NOT NULL,
    email character varying(100) DEFAULT ''::character varying NOT NULL,
    status smallint NOT NULL,
    role integer NOT NULL,
    profile character varying(255),
    repli integer DEFAULT 0 NOT NULL,
    cstring character varying(255),
    all_flags character varying(255),
    created_at date,
    updated_at date,
    password character varying(255) DEFAULT ''::character varying NOT NULL,
    banned_at date
);


ALTER TABLE public.wuser OWNER TO dba;

--
-- Name: office; Type: VIEW; Schema: public; Owner: dba
--

CREATE VIEW public.office AS
 SELECT office_old.office_nr,
    office_old.descr,
    office_old.active,
    office_old.tax1,
    office_old.tax2,
    office_old.decrounding,
    office_old.currency,
    office_old.price,
    wuser.username AS user_name
   FROM (public.office_old
     JOIN public.wuser ON ((wuser.repli = office_old.office_nr)));


ALTER VIEW public.office OWNER TO dba;

--
-- Name: p1; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.p1 (
    mat_id integer,
    ean13 character varying(13),
    kod character varying(4),
    model character varying(255),
    price numeric(11,2),
    p0 numeric(11,2),
    p1 numeric(11,2),
    p2 numeric(11,2),
    p3 numeric(11,2),
    p4 numeric(11,2),
    p5 numeric(11,2),
    p6 numeric(11,2),
    p8 numeric(11,2),
    p9 numeric(11,2),
    pdescr character varying(120),
    tax numeric(2,0),
    unit character varying(3),
    min numeric(9,3),
    max numeric(9,3),
    p7 numeric(11,2),
    p0a numeric(11,2),
    p0b numeric(11,2),
    p0c numeric(11,2),
    p0d numeric(11,2),
    p0e numeric(11,2),
    p0f numeric(11,2),
    p0g numeric(11,2),
    p0h numeric(11,2),
    p0i numeric(11,2),
    p0j numeric(11,2),
    p1b numeric(11,2),
    p1c numeric(11,2),
    p1d numeric(11,2),
    p1e numeric(11,2),
    p1f numeric(11,2),
    p1g numeric(11,2),
    p1h numeric(11,2),
    p1i numeric(11,2),
    p1j numeric(11,2),
    p2b numeric(11,2),
    p2c numeric(11,2),
    p2d numeric(11,2),
    p2e numeric(11,2),
    p2f numeric(11,2),
    p2g numeric(11,2),
    p2h numeric(11,2),
    p2i numeric(11,2),
    p2j numeric(11,2),
    p1a numeric(11,2),
    p2a numeric(11,2)
);


ALTER TABLE public.p1 OWNER TO dba;

--
-- Name: p2; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.p2 (
    mat_id integer NOT NULL
);


ALTER TABLE public.p2 OWNER TO dba;

--
-- Name: price_detail; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.price_detail (
    price_id character varying(20) NOT NULL,
    "desc" character varying(255) NOT NULL
);


ALTER TABLE public.price_detail OWNER TO dba;

--
-- Name: prints; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.prints (
    move_id integer NOT NULL,
    repli_id integer NOT NULL,
    nr integer NOT NULL,
    info character varying(150)
);


ALTER TABLE public.prints OWNER TO dba;

--
-- Name: product_disc; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.product_disc (
    perc integer DEFAULT 0 NOT NULL,
    cgroup integer NOT NULL,
    ctype character(1) NOT NULL
);


ALTER TABLE public.product_disc OWNER TO dba;

--
-- Name: profile; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.profile (
    id integer NOT NULL,
    user_id integer NOT NULL,
    created_at timestamp(0) without time zone,
    updated_at timestamp(0) without time zone,
    full_name character varying(255),
    timezone character varying(255)
);


ALTER TABLE public.profile OWNER TO dba;

--
-- Name: profile_id_seq; Type: SEQUENCE; Schema: public; Owner: dba
--

CREATE SEQUENCE public.profile_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.profile_id_seq OWNER TO dba;

--
-- Name: profile_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dba
--

ALTER SEQUENCE public.profile_id_seq OWNED BY public.profile.id;


--
-- Name: rem; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.rem (
    office_nr integer
);


ALTER TABLE public.rem OWNER TO dba;

--
-- Name: role; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.role (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    created_at timestamp(0) without time zone,
    updated_at timestamp(0) without time zone,
    can_admin smallint DEFAULT 0 NOT NULL
);


ALTER TABLE public.role OWNER TO dba;

--
-- Name: role_id_seq; Type: SEQUENCE; Schema: public; Owner: dba
--

CREATE SEQUENCE public.role_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.role_id_seq OWNER TO dba;

--
-- Name: role_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dba
--

ALTER SEQUENCE public.role_id_seq OWNED BY public.role.id;


--
-- Name: shopremains2; Type: VIEW; Schema: public; Owner: dba
--

CREATE VIEW public.shopremains2 AS
 SELECT d.mat_id,
    m.adr_id,
    m.adr_repli_id AS repli_id,
    (
        CASE
            WHEN ((m.stock_id2)::text = 'sj'::text) THEN COALESCE(sum(d.pcs), (0)::bigint)
            ELSE (0)::bigint
        END -
        CASE
            WHEN ((m.stock_id1)::text = 'sj'::text) THEN COALESCE(sum(d.pcs), (0)::bigint)
            ELSE (0)::bigint
        END) AS remains
   FROM ((public.product p
     JOIN public.m_detail d ON ((p.mat_id = d.mat_id)))
     JOIN public.movement m ON ((d.move_id = m.move_id)))
  WHERE (((m.stock_id1)::text = 'sj'::text) OR ((m.stock_id2)::text = 'sj'::text))
  GROUP BY m.adr_id, m.adr_repli_id, m.stock_id1, m.stock_id2, m.repli_id, d.mat_id;


ALTER VIEW public.shopremains2 OWNER TO dba;

--
-- Name: sql_commands; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.sql_commands (
    sql_id integer NOT NULL,
    sql_command text NOT NULL,
    sql_descr character varying(255) DEFAULT 'N/A'::character varying NOT NULL,
    user_name character varying(255) DEFAULT 'dba'::character varying NOT NULL,
    user_group character varying(255)
);


ALTER TABLE public.sql_commands OWNER TO dba;

--
-- Name: sql_commands_sql_id_seq; Type: SEQUENCE; Schema: public; Owner: dba
--

CREATE SEQUENCE public.sql_commands_sql_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.sql_commands_sql_id_seq OWNER TO dba;

--
-- Name: sql_commands_sql_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dba
--

ALTER SEQUENCE public.sql_commands_sql_id_seq OWNED BY public.sql_commands.sql_id;


--
-- Name: stock_detail; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.stock_detail (
    stock_id character varying(6) NOT NULL,
    sdescr character varying(255) NOT NULL,
    adr_id integer
);


ALTER TABLE public.stock_detail OWNER TO dba;

--
-- Name: TABLE stock_detail; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON TABLE public.stock_detail IS 'info about stocks used in system';


--
-- Name: stockremains2; Type: VIEW; Schema: public; Owner: dba
--

CREATE VIEW public.stockremains2 AS
 SELECT d.mat_id,
    m.stock_id1,
    m.stock_id2,
    sum(d.pcs) AS aa
   FROM public.m_detail d,
    public.movement m
  WHERE ((d.move_id = m.move_id) AND (d.m_repli_id = m.repli_id))
  GROUP BY m.stock_id1, m.stock_id2, d.mat_id;


ALTER VIEW public.stockremains2 OWNER TO dba;

--
-- Name: stockremains3; Type: VIEW; Schema: public; Owner: dba
--

CREATE VIEW public.stockremains3 AS
 SELECT d.mat_id,
    m.repli_id,
    sd.stock_id AS firma,
    sd.stock_id AS city,
    (
        CASE
            WHEN ((m.stock_id2)::text = (sd.stock_id)::text) THEN COALESCE(sum(d.pcs), (0)::bigint)
            ELSE (0)::bigint
        END -
        CASE
            WHEN ((m.stock_id1)::text = (sd.stock_id)::text) THEN COALESCE(sum(d.pcs), (0)::bigint)
            ELSE (0)::bigint
        END) AS remains
   FROM ((public.m_detail d
     JOIN public.movement m ON ((m.move_id = d.move_id)))
     JOIN public.stock_detail sd ON (((((sd.stock_id)::text = (m.stock_id1)::text) OR ((sd.stock_id)::text = (m.stock_id2)::text)) AND (((m.stock_id1)::text = (sd.stock_id)::text) OR ((m.stock_id2)::text = (sd.stock_id)::text)))))
  GROUP BY m.stock_id1, m.stock_id2, m.repli_id, d.mat_id, sd.stock_id;


ALTER VIEW public.stockremains3 OWNER TO dba;

--
-- Name: tempmove; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.tempmove (
    mat_id integer NOT NULL,
    price numeric(11,2) NOT NULL,
    pcs numeric(11,0) NOT NULL,
    discount numeric(17,8) NOT NULL,
    tax numeric(2,0) NOT NULL,
    act_price numeric(11,2),
    currency character varying(3),
    all1 numeric(11,0) DEFAULT 0 NOT NULL,
    all2 numeric(11,0) DEFAULT 0 NOT NULL,
    all3 numeric(11,0) DEFAULT 0 NOT NULL,
    all4 numeric(11,0) DEFAULT 0 NOT NULL,
    all5 numeric(11,0) DEFAULT 0 NOT NULL,
    ean13 character varying(13),
    kod character varying(4),
    model character varying(255),
    pdescr character varying(120),
    unit character varying(3),
    user_name character varying(20) NOT NULL,
    clip_id integer NOT NULL,
    d1 date,
    d2 date,
    d3 date,
    text1 character varying,
    text2 character varying,
    m_type_id integer DEFAULT 1,
    stock_id1 character varying(6),
    stock_id2 character varying(6),
    adr_id integer,
    price_type character varying(20),
    tempmove_info character varying(255) DEFAULT ''::character varying NOT NULL,
    fifo_currency character varying(3),
    fifo_price numeric(17,8) DEFAULT 0 NOT NULL,
    fifo_move_id integer DEFAULT 0,
    nr_counter integer NOT NULL,
    thserial character varying(255)
);


ALTER TABLE public.tempmove OWNER TO dba;

--
-- Name: tempmove_nr_counter_seq; Type: SEQUENCE; Schema: public; Owner: dba
--

CREATE SEQUENCE public.tempmove_nr_counter_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.tempmove_nr_counter_seq OWNER TO dba;

--
-- Name: tempmove_nr_counter_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dba
--

ALTER SEQUENCE public.tempmove_nr_counter_seq OWNED BY public.tempmove.nr_counter;


--
-- Name: thserials; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.thserials (
    id integer NOT NULL,
    mat_id integer DEFAULT 0 NOT NULL,
    move_id integer NOT NULL,
    thserial character varying(255) NOT NULL
);


ALTER TABLE public.thserials OWNER TO dba;

--
-- Name: thserials_id_seq; Type: SEQUENCE; Schema: public; Owner: dba
--

CREATE SEQUENCE public.thserials_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.thserials_id_seq OWNER TO dba;

--
-- Name: thserials_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dba
--

ALTER SEQUENCE public.thserials_id_seq OWNED BY public.thserials.id;


--
-- Name: user; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public."user" (
    id integer NOT NULL,
    role_id integer NOT NULL,
    status smallint NOT NULL,
    email character varying(255),
    username character varying(255),
    password character varying(255),
    auth_key character varying(255),
    access_token character varying(255),
    logged_in_ip character varying(255),
    logged_in_at timestamp(0) without time zone,
    created_ip character varying(255),
    created_at timestamp(0) without time zone,
    updated_at timestamp(0) without time zone,
    banned_at timestamp(0) without time zone,
    banned_reason character varying(255)
);


ALTER TABLE public."user" OWNER TO dba;

--
-- Name: user_auth; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.user_auth (
    id integer NOT NULL,
    user_id integer NOT NULL,
    provider character varying(255) NOT NULL,
    provider_id character varying(255) NOT NULL,
    provider_attributes text NOT NULL,
    created_at timestamp(0) without time zone,
    updated_at timestamp(0) without time zone
);


ALTER TABLE public.user_auth OWNER TO dba;

--
-- Name: user_auth_id_seq; Type: SEQUENCE; Schema: public; Owner: dba
--

CREATE SEQUENCE public.user_auth_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.user_auth_id_seq OWNER TO dba;

--
-- Name: user_auth_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dba
--

ALTER SEQUENCE public.user_auth_id_seq OWNED BY public.user_auth.id;


--
-- Name: user_id_seq; Type: SEQUENCE; Schema: public; Owner: dba
--

CREATE SEQUENCE public.user_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.user_id_seq OWNER TO dba;

--
-- Name: user_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dba
--

ALTER SEQUENCE public.user_id_seq OWNED BY public.wuser.id;


--
-- Name: user_id_seq1; Type: SEQUENCE; Schema: public; Owner: dba
--

CREATE SEQUENCE public.user_id_seq1
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.user_id_seq1 OWNER TO dba;

--
-- Name: user_id_seq1; Type: SEQUENCE OWNED BY; Schema: public; Owner: dba
--

ALTER SEQUENCE public.user_id_seq1 OWNED BY public."user".id;


--
-- Name: user_rights; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.user_rights (
    user_name character varying(20) NOT NULL,
    repli_id smallint NOT NULL,
    all_flags character varying(255)
);


ALTER TABLE public.user_rights OWNER TO dba;

--
-- Name: user_token; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.user_token (
    id integer NOT NULL,
    user_id integer,
    type smallint NOT NULL,
    token character varying(255) NOT NULL,
    data character varying(255),
    created_at timestamp(0) without time zone,
    expired_at timestamp(0) without time zone
);


ALTER TABLE public.user_token OWNER TO dba;

--
-- Name: user_token_id_seq; Type: SEQUENCE; Schema: public; Owner: dba
--

CREATE SEQUENCE public.user_token_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.user_token_id_seq OWNER TO dba;

--
-- Name: user_token_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dba
--

ALTER SEQUENCE public.user_token_id_seq OWNED BY public.user_token.id;


--
-- Name: vat; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.vat (
    id integer NOT NULL,
    country character varying(255) NOT NULL,
    countryname character varying(255),
    perc smallint DEFAULT 20 NOT NULL,
    category smallint DEFAULT 2 NOT NULL,
    vatname character varying(255)
);


ALTER TABLE public.vat OWNER TO dba;

--
-- Name: TABLE vat; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON TABLE public.vat IS 'actual EU countries VAT rates';


--
-- Name: COLUMN vat.country; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON COLUMN public.vat.country IS 'iso code';


--
-- Name: COLUMN vat.category; Type: COMMENT; Schema: public; Owner: dba
--

COMMENT ON COLUMN public.vat.category IS '1 reduced 2 default 3 other vat rate';


--
-- Name: vat_id_seq; Type: SEQUENCE; Schema: public; Owner: dba
--

ALTER TABLE public.vat ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.vat_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: wow_cards; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.wow_cards (
    card_nr character varying(32) DEFAULT ''::character varying NOT NULL,
    parent_nr character varying(32) DEFAULT ''::character varying NOT NULL,
    issue_d date,
    expire_d date,
    cust_id integer DEFAULT 0 NOT NULL,
    card_type character varying(2) DEFAULT 'B'::character varying NOT NULL,
    card_discount numeric(1,0) DEFAULT '0'::numeric NOT NULL,
    cust_currency character varying(3) DEFAULT 'USD'::character varying NOT NULL,
    card_memo character varying(255)
);


ALTER TABLE public.wow_cards OWNER TO dba;

--
-- Name: wow_sales; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.wow_sales (
    orders_id integer DEFAULT 0 NOT NULL,
    mat_id integer DEFAULT 0 NOT NULL,
    pcs integer DEFAULT 1 NOT NULL,
    sale_price numeric(11,2) DEFAULT 0.00 NOT NULL,
    card_nr character varying(32) DEFAULT ''::character varying NOT NULL,
    sale_date date,
    sale_spare numeric(11,2) DEFAULT 0.00 NOT NULL,
    sale_type character varying(2) DEFAULT 'BD'::character varying NOT NULL,
    sale_info character varying(255),
    cust_id integer DEFAULT 0 NOT NULL
);


ALTER TABLE public.wow_sales OWNER TO dba;

--
-- Name: wow_sales_2022_11_08; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.wow_sales_2022_11_08 (
    orders_id integer,
    mat_id integer,
    pcs integer,
    sale_price numeric(11,2),
    card_nr character varying(32),
    sale_date date,
    sale_spare numeric(11,2),
    sale_type character varying(2),
    sale_info character varying(255),
    cust_id integer
);


ALTER TABLE public.wow_sales_2022_11_08 OWNER TO dba;

--
-- Name: xx; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.xx (
    sum bigint
);


ALTER TABLE public.xx OWNER TO dba;

--
-- Name: xxtax; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.xxtax (
    perc smallint
);


ALTER TABLE public.xxtax OWNER TO dba;

--
-- Name: yuctogw3731model; Type: TABLE; Schema: public; Owner: dba
--

CREATE UNLOGGED TABLE public.yuctogw3731model (
    matid integer NOT NULL,
    xx character varying(1),
    kod integer,
    n_price numeric(11,2) DEFAULT 0,
    s_price numeric(11,2) DEFAULT 0,
    d1 date,
    fnumber integer,
    pcs integer,
    remains integer,
    beforeonstock integer,
    move_id integer,
    mat_id integer,
    doc_nr integer DEFAULT 0
);


ALTER TABLE public.yuctogw3731model OWNER TO dba;

--
-- Name: yuctogw3731model_matid_seq; Type: SEQUENCE; Schema: public; Owner: dba
--

CREATE SEQUENCE public.yuctogw3731model_matid_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.yuctogw3731model_matid_seq OWNER TO dba;

--
-- Name: yuctogw3731model_matid_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dba
--

ALTER SEQUENCE public.yuctogw3731model_matid_seq OWNED BY public.yuctogw3731model.matid;


--
-- Name: yuctogw5039model; Type: TABLE; Schema: public; Owner: dba
--

CREATE UNLOGGED TABLE public.yuctogw5039model (
    matid integer NOT NULL,
    xx character varying(1),
    kod integer,
    n_price numeric(11,2) DEFAULT 0,
    s_price numeric(11,2) DEFAULT 0,
    d1 date,
    fnumber integer,
    pcs integer,
    remains integer,
    beforeonstock integer,
    move_id integer,
    mat_id integer,
    doc_nr integer DEFAULT 0
);


ALTER TABLE public.yuctogw5039model OWNER TO dba;

--
-- Name: yuctogw5039model_matid_seq; Type: SEQUENCE; Schema: public; Owner: dba
--

CREATE SEQUENCE public.yuctogw5039model_matid_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.yuctogw5039model_matid_seq OWNER TO dba;

--
-- Name: yuctogw5039model_matid_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dba
--

ALTER SEQUENCE public.yuctogw5039model_matid_seq OWNED BY public.yuctogw5039model.matid;


--
-- Name: yuctogw5040model; Type: TABLE; Schema: public; Owner: dba
--

CREATE UNLOGGED TABLE public.yuctogw5040model (
    matid integer NOT NULL,
    xx character varying(1),
    kod integer,
    n_price numeric(11,2) DEFAULT 0,
    s_price numeric(11,2) DEFAULT 0,
    d1 date,
    fnumber integer,
    pcs integer,
    remains integer,
    beforeonstock integer,
    move_id integer,
    mat_id integer,
    doc_nr integer DEFAULT 0
);


ALTER TABLE public.yuctogw5040model OWNER TO dba;

--
-- Name: yuctogw5040model_matid_seq; Type: SEQUENCE; Schema: public; Owner: dba
--

CREATE SEQUENCE public.yuctogw5040model_matid_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.yuctogw5040model_matid_seq OWNER TO dba;

--
-- Name: yuctogw5040model_matid_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dba
--

ALTER SEQUENCE public.yuctogw5040model_matid_seq OWNED BY public.yuctogw5040model.matid;


--
-- Name: yuctogw5117model; Type: TABLE; Schema: public; Owner: dba
--

CREATE TABLE public.yuctogw5117model (
    matid integer NOT NULL,
    xx character varying(1),
    kod integer,
    n_price numeric(11,2) DEFAULT 0,
    s_price numeric(11,2) DEFAULT 0,
    d1 date,
    fnumber integer,
    pcs integer,
    remains integer,
    beforeonstock integer,
    move_id integer,
    mat_id integer,
    doc_nr integer DEFAULT 0
);


ALTER TABLE public.yuctogw5117model OWNER TO dba;

--
-- Name: yuctogw5117model_matid_seq; Type: SEQUENCE; Schema: public; Owner: dba
--

CREATE SEQUENCE public.yuctogw5117model_matid_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.yuctogw5117model_matid_seq OWNER TO dba;

--
-- Name: yuctogw5117model_matid_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dba
--

ALTER SEQUENCE public.yuctogw5117model_matid_seq OWNED BY public.yuctogw5117model.matid;


--
-- Name: yuctogwmodel; Type: TABLE; Schema: public; Owner: dba
--

CREATE UNLOGGED TABLE public.yuctogwmodel (
    matid integer NOT NULL,
    xx character varying(1),
    kod integer,
    n_price numeric(11,2) DEFAULT 0,
    s_price numeric(11,2) DEFAULT 0,
    d1 date,
    fnumber integer,
    pcs integer,
    remains integer,
    beforeonstock integer,
    move_id integer,
    mat_id integer,
    doc_nr integer DEFAULT 0
);


ALTER TABLE public.yuctogwmodel OWNER TO dba;

--
-- Name: yuctogwmodel_matid_seq; Type: SEQUENCE; Schema: public; Owner: dba
--

CREATE SEQUENCE public.yuctogwmodel_matid_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.yuctogwmodel_matid_seq OWNER TO dba;

--
-- Name: yuctogwmodel_matid_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dba
--

ALTER SEQUENCE public.yuctogwmodel_matid_seq OWNED BY public.yuctogwmodel.matid;


--
-- Name: yuctomodel; Type: TABLE; Schema: public; Owner: dba
--

CREATE UNLOGGED TABLE public.yuctomodel (
    matid integer NOT NULL,
    xx character varying(1),
    kod integer,
    n_price numeric(11,2) DEFAULT 0,
    s_price numeric(11,2) DEFAULT 0,
    d1 date,
    fnumber integer,
    pcs integer,
    remains integer,
    beforeonstock integer,
    move_id integer,
    mat_id integer,
    doc_nr integer DEFAULT 0
);


ALTER TABLE public.yuctomodel OWNER TO dba;

--
-- Name: yuctomodel_matid_seq; Type: SEQUENCE; Schema: public; Owner: dba
--

CREATE SEQUENCE public.yuctomodel_matid_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.yuctomodel_matid_seq OWNER TO dba;

--
-- Name: yuctomodel_matid_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: dba
--

ALTER SEQUENCE public.yuctomodel_matid_seq OWNED BY public.yuctomodel.matid;


--
-- Name: address adr_id; Type: DEFAULT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.address ALTER COLUMN adr_id SET DEFAULT nextval('public.address_adr_id_seq'::regclass);


--
-- Name: authy id; Type: DEFAULT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.authy ALTER COLUMN id SET DEFAULT nextval('public.authy_id_seq'::regclass);


--
-- Name: authy_login id; Type: DEFAULT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.authy_login ALTER COLUMN id SET DEFAULT nextval('public.authy_login_id_seq'::regclass);


--
-- Name: customers id; Type: DEFAULT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.customers ALTER COLUMN id SET DEFAULT nextval('public.customers_id_seq'::regclass);


--
-- Name: eshop id; Type: DEFAULT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.eshop ALTER COLUMN id SET DEFAULT nextval('public.eshop_id_seq'::regclass);


--
-- Name: heureka id; Type: DEFAULT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.heureka ALTER COLUMN id SET DEFAULT nextval('public.heureka_id_seq'::regclass);


--
-- Name: m_type m_type_id; Type: DEFAULT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.m_type ALTER COLUMN m_type_id SET DEFAULT nextval('public.m_type_m_type_id_seq'::regclass);


--
-- Name: m_user_config id; Type: DEFAULT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.m_user_config ALTER COLUMN id SET DEFAULT nextval('public.m_user_config_id_seq'::regclass);


--
-- Name: minfo id; Type: DEFAULT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.minfo ALTER COLUMN id SET DEFAULT nextval('public.minfo_id_seq'::regclass);


--
-- Name: mlog id; Type: DEFAULT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.mlog ALTER COLUMN id SET DEFAULT nextval('public.mlog_id_seq'::regclass);


--
-- Name: movement move_id; Type: DEFAULT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.movement ALTER COLUMN move_id SET DEFAULT nextval('public.movement_move_id_seq'::regclass);


--
-- Name: profile id; Type: DEFAULT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.profile ALTER COLUMN id SET DEFAULT nextval('public.profile_id_seq'::regclass);


--
-- Name: role id; Type: DEFAULT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.role ALTER COLUMN id SET DEFAULT nextval('public.role_id_seq'::regclass);


--
-- Name: sql_commands sql_id; Type: DEFAULT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.sql_commands ALTER COLUMN sql_id SET DEFAULT nextval('public.sql_commands_sql_id_seq'::regclass);


--
-- Name: tempmove nr_counter; Type: DEFAULT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.tempmove ALTER COLUMN nr_counter SET DEFAULT nextval('public.tempmove_nr_counter_seq'::regclass);


--
-- Name: thserials id; Type: DEFAULT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.thserials ALTER COLUMN id SET DEFAULT nextval('public.thserials_id_seq'::regclass);


--
-- Name: user id; Type: DEFAULT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public."user" ALTER COLUMN id SET DEFAULT nextval('public.user_id_seq1'::regclass);


--
-- Name: user_auth id; Type: DEFAULT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.user_auth ALTER COLUMN id SET DEFAULT nextval('public.user_auth_id_seq'::regclass);


--
-- Name: user_token id; Type: DEFAULT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.user_token ALTER COLUMN id SET DEFAULT nextval('public.user_token_id_seq'::regclass);


--
-- Name: wuser id; Type: DEFAULT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.wuser ALTER COLUMN id SET DEFAULT nextval('public.user_id_seq'::regclass);


--
-- Name: yuctogw3731model matid; Type: DEFAULT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.yuctogw3731model ALTER COLUMN matid SET DEFAULT nextval('public.yuctogw3731model_matid_seq'::regclass);


--
-- Name: yuctogw5039model matid; Type: DEFAULT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.yuctogw5039model ALTER COLUMN matid SET DEFAULT nextval('public.yuctogw5039model_matid_seq'::regclass);


--
-- Name: yuctogw5040model matid; Type: DEFAULT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.yuctogw5040model ALTER COLUMN matid SET DEFAULT nextval('public.yuctogw5040model_matid_seq'::regclass);


--
-- Name: yuctogw5117model matid; Type: DEFAULT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.yuctogw5117model ALTER COLUMN matid SET DEFAULT nextval('public.yuctogw5117model_matid_seq'::regclass);


--
-- Name: yuctogwmodel matid; Type: DEFAULT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.yuctogwmodel ALTER COLUMN matid SET DEFAULT nextval('public.yuctogwmodel_matid_seq'::regclass);


--
-- Name: yuctomodel matid; Type: DEFAULT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.yuctomodel ALTER COLUMN matid SET DEFAULT nextval('public.yuctomodel_matid_seq'::regclass);


--
-- Name: address address_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.address
    ADD CONSTRAINT address_pkey PRIMARY KEY (adr_id);


--
-- Name: adr_contact adr_contact_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.adr_contact
    ADD CONSTRAINT adr_contact_pkey PRIMARY KEY (adr_id, number, repli_id);


--
-- Name: authy_login authy_login_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.authy_login
    ADD CONSTRAINT authy_login_pkey PRIMARY KEY (id);


--
-- Name: authy authy_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.authy
    ADD CONSTRAINT authy_pkey PRIMARY KEY (id);


--
-- Name: countries countries_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.countries
    ADD CONSTRAINT countries_pkey PRIMARY KEY (id);


--
-- Name: country country_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.country
    ADD CONSTRAINT country_pkey PRIMARY KEY (iso);


--
-- Name: currencies currencies_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.currencies
    ADD CONSTRAINT currencies_pkey PRIMARY KEY (cur);


--
-- Name: customers customers_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.customers
    ADD CONSTRAINT customers_pkey PRIMARY KEY (id);


--
-- Name: document document_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.document
    ADD CONSTRAINT document_pkey PRIMARY KEY (doc_id);


--
-- Name: dyninfo dyninfo_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.dyninfo
    ADD CONSTRAINT dyninfo_pkey PRIMARY KEY (mat_id);


--
-- Name: dyninfox dyninfox_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.dyninfox
    ADD CONSTRAINT dyninfox_pkey PRIMARY KEY (mat_id, repli_id, shop);


--
-- Name: dyninfoy dyninfoy_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.dyninfoy
    ADD CONSTRAINT dyninfoy_pkey PRIMARY KEY (mat_id, repli_id, shop);


--
-- Name: eshop eshop_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.eshop
    ADD CONSTRAINT eshop_pkey PRIMARY KEY (id);


--
-- Name: garminstock garminstock_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.garminstock
    ADD CONSTRAINT garminstock_pkey PRIMARY KEY (code);


--
-- Name: heureka heureka_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.heureka
    ADD CONSTRAINT heureka_pkey PRIMARY KEY (id);


--
-- Name: kod kod_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.kod
    ADD CONSTRAINT kod_pkey PRIMARY KEY (kod);


--
-- Name: m_detail m_detail_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.m_detail
    ADD CONSTRAINT m_detail_pkey PRIMARY KEY (move_id, mat_id, m_repli_id, fifo_move_id, detail_info, fifo_repli_id);


--
-- Name: m_type m_type_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.m_type
    ADD CONSTRAINT m_type_pkey PRIMARY KEY (m_type_id);


--
-- Name: m_user_config m_user_config_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.m_user_config
    ADD CONSTRAINT m_user_config_pkey PRIMARY KEY (id);


--
-- Name: migration migration_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.migration
    ADD CONSTRAINT migration_pkey PRIMARY KEY (version);


--
-- Name: minfo minfo_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.minfo
    ADD CONSTRAINT minfo_pkey PRIMARY KEY (id);


--
-- Name: mlog mlog_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.mlog
    ADD CONSTRAINT mlog_pkey PRIMARY KEY (id);


--
-- Name: movement movement_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.movement
    ADD CONSTRAINT movement_pkey PRIMARY KEY (move_id);


--
-- Name: nbs_rates nbs_rates_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.nbs_rates
    ADD CONSTRAINT nbs_rates_pkey PRIMARY KEY (nbs_date, nbs_cur);


--
-- Name: office_old office_old_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.office_old
    ADD CONSTRAINT office_old_pkey PRIMARY KEY (office_nr);


--
-- Name: p2 p2_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.p2
    ADD CONSTRAINT p2_pkey PRIMARY KEY (mat_id);


--
-- Name: price_detail price_detail_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.price_detail
    ADD CONSTRAINT price_detail_pkey PRIMARY KEY (price_id);


--
-- Name: prints prints_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.prints
    ADD CONSTRAINT prints_pkey PRIMARY KEY (move_id, repli_id);


--
-- Name: product_disc product_disc_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.product_disc
    ADD CONSTRAINT product_disc_pkey PRIMARY KEY (cgroup, ctype);


--
-- Name: product product_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.product
    ADD CONSTRAINT product_pkey PRIMARY KEY (mat_id);


--
-- Name: profile profile_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.profile
    ADD CONSTRAINT profile_pkey PRIMARY KEY (id);


--
-- Name: role role_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.role
    ADD CONSTRAINT role_pkey PRIMARY KEY (id);


--
-- Name: sql_commands sql_commands_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.sql_commands
    ADD CONSTRAINT sql_commands_pkey PRIMARY KEY (sql_id);


--
-- Name: stock_detail stock_detail_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.stock_detail
    ADD CONSTRAINT stock_detail_pkey PRIMARY KEY (stock_id);


--
-- Name: tempmove tempmove_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.tempmove
    ADD CONSTRAINT tempmove_pkey PRIMARY KEY (mat_id, user_name, clip_id, tempmove_info);


--
-- Name: thserials thserials_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.thserials
    ADD CONSTRAINT thserials_pkey PRIMARY KEY (id);


--
-- Name: user_auth user_auth_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.user_auth
    ADD CONSTRAINT user_auth_pkey PRIMARY KEY (id);


--
-- Name: wuser user_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.wuser
    ADD CONSTRAINT user_pkey PRIMARY KEY (id);


--
-- Name: user user_pkey1; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public."user"
    ADD CONSTRAINT user_pkey1 PRIMARY KEY (id);


--
-- Name: user_rights user_rights_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.user_rights
    ADD CONSTRAINT user_rights_pkey PRIMARY KEY (user_name);


--
-- Name: user_token user_token_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.user_token
    ADD CONSTRAINT user_token_pkey PRIMARY KEY (id);


--
-- Name: vat vat_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.vat
    ADD CONSTRAINT vat_pkey PRIMARY KEY (id);


--
-- Name: wow_cards wow_cards_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.wow_cards
    ADD CONSTRAINT wow_cards_pkey PRIMARY KEY (card_nr);


--
-- Name: wow_sales wow_sales_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.wow_sales
    ADD CONSTRAINT wow_sales_pkey PRIMARY KEY (orders_id, card_nr, mat_id);


--
-- Name: yuctogw3731model yuctogw3731model_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.yuctogw3731model
    ADD CONSTRAINT yuctogw3731model_pkey PRIMARY KEY (matid);


--
-- Name: yuctogw5039model yuctogw5039model_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.yuctogw5039model
    ADD CONSTRAINT yuctogw5039model_pkey PRIMARY KEY (matid);


--
-- Name: yuctogw5040model yuctogw5040model_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.yuctogw5040model
    ADD CONSTRAINT yuctogw5040model_pkey PRIMARY KEY (matid);


--
-- Name: yuctogw5117model yuctogw5117model_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.yuctogw5117model
    ADD CONSTRAINT yuctogw5117model_pkey PRIMARY KEY (matid);


--
-- Name: yuctogwmodel yuctogwmodel_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.yuctogwmodel
    ADD CONSTRAINT yuctogwmodel_pkey PRIMARY KEY (matid);


--
-- Name: yuctomodel yuctomodel_pkey; Type: CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.yuctomodel
    ADD CONSTRAINT yuctomodel_pkey PRIMARY KEY (matid);


--
-- Name: adrid; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX adrid ON public.adr_contact USING btree (adr_id);


--
-- Name: ctime; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX ctime ON public.heureka USING btree (ctime);


--
-- Name: cust_nick; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX cust_nick ON public.customers USING btree (nick);


--
-- Name: customers_card_nr; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX customers_card_nr ON public.customers USING btree (card_nr);


--
-- Name: customers_firstname; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX customers_firstname ON public.customers USING btree (firstname);


--
-- Name: customers_lastname; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX customers_lastname ON public.customers USING btree (lastname);


--
-- Name: customers_nick; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX customers_nick ON public.customers USING btree (nick);


--
-- Name: d1; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX d1 ON public.movement USING btree (d1);


--
-- Name: detailinfo_mdetail; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX detailinfo_mdetail ON public.m_detail USING btree (detail_info);


--
-- Name: dyninfox_model; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX dyninfox_model ON public.dyninfox USING btree (model);


--
-- Name: dyninfox_remains; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX dyninfox_remains ON public.dyninfox USING btree (remains);


--
-- Name: dyninfox_repli_id; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX dyninfox_repli_id ON public.dyninfox USING btree (repli_id);


--
-- Name: dyninfox_shop; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX dyninfox_shop ON public.dyninfox USING btree (shop);


--
-- Name: ean13; Type: INDEX; Schema: public; Owner: dba
--

CREATE UNIQUE INDEX ean13 ON public.product USING btree (ean13);


--
-- Name: fifoprofit_mat_id; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX fifoprofit_mat_id ON public.fifoprofit USING btree (mat_id);


--
-- Name: fki_fkmovementadr_id; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX fki_fkmovementadr_id ON public.movement USING btree (adr_id);


--
-- Name: hmodel; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX hmodel ON public.heureka USING btree (hmodel);


--
-- Name: idx-authy_login-authyid; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX "idx-authy_login-authyid" ON public.authy_login USING btree (authyid);


--
-- Name: kod3731idx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX kod3731idx ON public.yuctogw3731model USING btree (kod);


--
-- Name: kod5039idx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX kod5039idx ON public.yuctogw5039model USING btree (kod);


--
-- Name: kod5040idx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX kod5040idx ON public.yuctogw5040model USING btree (kod);


--
-- Name: kodgwkartyidx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX kodgwkartyidx ON public.yuctogwmodel USING btree (kod);


--
-- Name: kodidx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX kodidx ON public.yuctogw5117model USING btree (kod);


--
-- Name: kodracioidx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX kodracioidx ON public.yuctomodel USING btree (kod);


--
-- Name: m_x_m_type_id; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX m_x_m_type_id ON public.movement USING btree (m_type_id, "X");


--
-- Name: mat_id; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX mat_id ON public.thserials USING btree (mat_id);


--
-- Name: mdmatid; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX mdmatid ON public.m_detail USING btree (mat_id);


--
-- Name: mlog_movement_fkey; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX mlog_movement_fkey ON public.mlog USING btree (move_id);


--
-- Name: mov_m_type_id_d1desc; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX mov_m_type_id_d1desc ON public.movement USING btree (d1 DESC, m_type_id);


--
-- Name: move_id; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX move_id ON public.thserials USING btree (move_id);


--
-- Name: move_id3731idx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX move_id3731idx ON public.yuctogw3731model USING btree (move_id);


--
-- Name: move_id5039idx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX move_id5039idx ON public.yuctogw5039model USING btree (move_id);


--
-- Name: move_id5040idx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX move_id5040idx ON public.yuctogw5040model USING btree (move_id);


--
-- Name: move_idgwkartyidx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX move_idgwkartyidx ON public.yuctogwmodel USING btree (move_id);


--
-- Name: move_ididx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX move_ididx ON public.yuctogw5117model USING btree (move_id);


--
-- Name: move_idracioidx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX move_idracioidx ON public.yuctomodel USING btree (move_id);


--
-- Name: movement_cnumber; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX movement_cnumber ON public.movement USING btree (c_number);


--
-- Name: movement_document; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX movement_document ON public.movement USING btree (number);


--
-- Name: movement_emp_id; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX movement_emp_id ON public.movement USING btree (emp_id);


--
-- Name: n_price3731idx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX n_price3731idx ON public.yuctogw3731model USING btree (n_price);


--
-- Name: n_price5039idx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX n_price5039idx ON public.yuctogw5039model USING btree (n_price);


--
-- Name: n_price5040idx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX n_price5040idx ON public.yuctogw5040model USING btree (n_price);


--
-- Name: n_pricegwkartyidx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX n_pricegwkartyidx ON public.yuctogwmodel USING btree (n_price);


--
-- Name: n_priceidx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX n_priceidx ON public.yuctogw5117model USING btree (n_price);


--
-- Name: n_priceracioidx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX n_priceracioidx ON public.yuctomodel USING btree (n_price);


--
-- Name: nr_counter; Type: INDEX; Schema: public; Owner: dba
--

CREATE UNIQUE INDEX nr_counter ON public.tempmove USING btree (nr_counter);


--
-- Name: orderid; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX orderid ON public.eshop USING btree (orderid);


--
-- Name: parent_nr; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX parent_nr ON public.wow_cards USING btree (parent_nr);


--
-- Name: pcs3731idx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX pcs3731idx ON public.yuctogw3731model USING btree (pcs);


--
-- Name: pcs5039idx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX pcs5039idx ON public.yuctogw5039model USING btree (pcs);


--
-- Name: pcs5040idx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX pcs5040idx ON public.yuctogw5040model USING btree (pcs);


--
-- Name: pcsgwkartyidx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX pcsgwkartyidx ON public.yuctogwmodel USING btree (pcs);


--
-- Name: pcsidx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX pcsidx ON public.yuctogw5117model USING btree (pcs);


--
-- Name: pcsracioidx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX pcsracioidx ON public.yuctomodel USING btree (pcs);


--
-- Name: pdescr_model; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX pdescr_model ON public.product USING btree (pdescr);


--
-- Name: pmat_id; Type: INDEX; Schema: public; Owner: dba
--

CREATE UNIQUE INDEX pmat_id ON public.product USING btree (mat_id);


--
-- Name: prod_model; Type: INDEX; Schema: public; Owner: dba
--

CREATE UNIQUE INDEX prod_model ON public.product USING btree (model);


--
-- Name: remains3731idx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX remains3731idx ON public.yuctogw3731model USING btree (remains);


--
-- Name: remains5039idx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX remains5039idx ON public.yuctogw5039model USING btree (remains);


--
-- Name: remains5040idx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX remains5040idx ON public.yuctogw5040model USING btree (remains);


--
-- Name: remainsgwkartyidx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX remainsgwkartyidx ON public.yuctogwmodel USING btree (remains);


--
-- Name: remainsidx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX remainsidx ON public.yuctogw5117model USING btree (remains);


--
-- Name: remainsracioidx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX remainsracioidx ON public.yuctomodel USING btree (remains);


--
-- Name: status; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX status ON public.eshop USING btree (status);


--
-- Name: stock_id1; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX stock_id1 ON public.movement USING btree (stock_id1);


--
-- Name: stock_id2; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX stock_id2 ON public.movement USING btree (stock_id2);


--
-- Name: tempmove_user_name_clip_id; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX tempmove_user_name_clip_id ON public.tempmove USING btree (user_name, clip_id);


--
-- Name: thserial; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX thserial ON public.thserials USING btree (thserial);


--
-- Name: uniqueserialmovematid; Type: INDEX; Schema: public; Owner: dba
--

CREATE UNIQUE INDEX uniqueserialmovematid ON public.thserials USING btree (move_id, mat_id, thserial);


--
-- Name: user_auth_provider_id; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX user_auth_provider_id ON public.user_auth USING btree (provider_id);


--
-- Name: user_email; Type: INDEX; Schema: public; Owner: dba
--

CREATE UNIQUE INDEX user_email ON public."user" USING btree (email);


--
-- Name: user_group; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX user_group ON public.sql_commands USING btree (user_group);


--
-- Name: user_name; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX user_name ON public.sql_commands USING btree (user_name);


--
-- Name: user_token_token; Type: INDEX; Schema: public; Owner: dba
--

CREATE UNIQUE INDEX user_token_token ON public.user_token USING btree (token);


--
-- Name: user_username; Type: INDEX; Schema: public; Owner: dba
--

CREATE UNIQUE INDEX user_username ON public."user" USING btree (username);


--
-- Name: ws_cust_id; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX ws_cust_id ON public.wow_sales USING btree (cust_id);


--
-- Name: xx3731idx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX xx3731idx ON public.yuctogw3731model USING btree (xx);


--
-- Name: xx5039idx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX xx5039idx ON public.yuctogw5039model USING btree (xx);


--
-- Name: xx5040idx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX xx5040idx ON public.yuctogw5040model USING btree (xx);


--
-- Name: xxgwkartyidx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX xxgwkartyidx ON public.yuctogwmodel USING btree (xx);


--
-- Name: xxidx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX xxidx ON public.yuctogw5117model USING btree (xx);


--
-- Name: xxracioidx; Type: INDEX; Schema: public; Owner: dba
--

CREATE INDEX xxracioidx ON public.yuctomodel USING btree (xx);


--
-- Name: thserials check_serial_pcs; Type: TRIGGER; Schema: public; Owner: dba
--

CREATE TRIGGER check_serial_pcs AFTER INSERT OR UPDATE ON public.thserials FOR EACH ROW EXECUTE FUNCTION public.check_serial_pcs();


--
-- Name: tempmove check_serial_string; Type: TRIGGER; Schema: public; Owner: dba
--

CREATE TRIGGER check_serial_string BEFORE INSERT OR UPDATE ON public.tempmove FOR EACH ROW EXECUTE FUNCTION public.check_serial_string_in_clipboard();


--
-- Name: thserials check_serial_string; Type: TRIGGER; Schema: public; Owner: dba
--

CREATE TRIGGER check_serial_string BEFORE INSERT OR UPDATE ON public.thserials FOR EACH ROW EXECUTE FUNCTION public.check_serial_string();


--
-- Name: m_detail m_det_edit; Type: TRIGGER; Schema: public; Owner: dba
--

CREATE TRIGGER m_det_edit AFTER UPDATE ON public.m_detail FOR EACH ROW EXECUTE FUNCTION public.triggertotalcount();


--
-- Name: m_detail m_det_row_edit; Type: TRIGGER; Schema: public; Owner: dba
--

CREATE TRIGGER m_det_row_edit AFTER UPDATE ON public.m_detail FOR EACH ROW EXECUTE FUNCTION public.triggerdetailchanged();


--
-- Name: movement new_movement; Type: TRIGGER; Schema: public; Owner: dba
--

CREATE TRIGGER new_movement AFTER INSERT OR DELETE OR UPDATE ON public.movement FOR EACH ROW EXECUTE FUNCTION public.triggernewmovement();


--
-- Name: product pdisc_after_new_product; Type: TRIGGER; Schema: public; Owner: dba
--

CREATE TRIGGER pdisc_after_new_product AFTER INSERT ON public.product FOR EACH ROW EXECUTE FUNCTION public.triggerpdiscifnewproduct();


--
-- Name: kod product_pdisc_from_kod; Type: TRIGGER; Schema: public; Owner: dba
--

CREATE TRIGGER product_pdisc_from_kod AFTER INSERT OR UPDATE ON public.kod FOR EACH ROW EXECUTE FUNCTION public.triggerpdiscinproduct();


--
-- Name: wow_sales cust_id; Type: FK CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.wow_sales
    ADD CONSTRAINT cust_id FOREIGN KEY (cust_id) REFERENCES public.customers(id);


--
-- Name: authy_login fk-authy_login-authyid; Type: FK CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.authy_login
    ADD CONSTRAINT "fk-authy_login-authyid" FOREIGN KEY (authyid) REFERENCES public.authy(id) ON DELETE CASCADE;


--
-- Name: adr_contact fkadrcontact_adrid; Type: FK CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.adr_contact
    ADD CONSTRAINT fkadrcontact_adrid FOREIGN KEY (adr_id) REFERENCES public.address(adr_id) NOT VALID;


--
-- Name: thserials fkmat_id; Type: FK CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.thserials
    ADD CONSTRAINT fkmat_id FOREIGN KEY (mat_id) REFERENCES public.product(mat_id);


--
-- Name: m_detail fkmdetmat_id; Type: FK CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.m_detail
    ADD CONSTRAINT fkmdetmat_id FOREIGN KEY (mat_id) REFERENCES public.product(mat_id);


--
-- Name: m_detail fkmdetmove_id; Type: FK CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.m_detail
    ADD CONSTRAINT fkmdetmove_id FOREIGN KEY (move_id) REFERENCES public.movement(move_id) ON DELETE CASCADE;


--
-- Name: movement fkmmovmtype_id; Type: FK CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.movement
    ADD CONSTRAINT fkmmovmtype_id FOREIGN KEY (m_type_id) REFERENCES public.m_type(m_type_id) ON DELETE CASCADE;


--
-- Name: movement fkmovementadr_id; Type: FK CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.movement
    ADD CONSTRAINT fkmovementadr_id FOREIGN KEY (adr_id) REFERENCES public.address(adr_id) NOT VALID;


--
-- Name: product fkmprodkod; Type: FK CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.product
    ADD CONSTRAINT fkmprodkod FOREIGN KEY (kod) REFERENCES public.kod(kod);


--
-- Name: m_type fkmtypedoc_id; Type: FK CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.m_type
    ADD CONSTRAINT fkmtypedoc_id FOREIGN KEY (doc_id) REFERENCES public.document(doc_id);


--
-- Name: movement fkstock_id1; Type: FK CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.movement
    ADD CONSTRAINT fkstock_id1 FOREIGN KEY (stock_id1) REFERENCES public.stock_detail(stock_id);


--
-- Name: movement fkstock_id2; Type: FK CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.movement
    ADD CONSTRAINT fkstock_id2 FOREIGN KEY (stock_id2) REFERENCES public.stock_detail(stock_id);


--
-- Name: thserials fkthmove_id; Type: FK CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.thserials
    ADD CONSTRAINT fkthmove_id FOREIGN KEY (move_id) REFERENCES public.movement(move_id);


--
-- Name: minfo minfo_movement_fkey; Type: FK CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.minfo
    ADD CONSTRAINT minfo_movement_fkey FOREIGN KEY (move_id) REFERENCES public.movement(move_id) ON UPDATE CASCADE ON DELETE CASCADE NOT VALID;


--
-- Name: profile profile_user_id; Type: FK CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.profile
    ADD CONSTRAINT profile_user_id FOREIGN KEY (user_id) REFERENCES public."user"(id);


--
-- Name: user_auth user_auth_user_id; Type: FK CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.user_auth
    ADD CONSTRAINT user_auth_user_id FOREIGN KEY (user_id) REFERENCES public."user"(id);


--
-- Name: user user_role_id; Type: FK CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public."user"
    ADD CONSTRAINT user_role_id FOREIGN KEY (role_id) REFERENCES public.role(id);


--
-- Name: user_token user_token_user_id; Type: FK CONSTRAINT; Schema: public; Owner: dba
--

ALTER TABLE ONLY public.user_token
    ADD CONSTRAINT user_token_user_id FOREIGN KEY (user_id) REFERENCES public."user"(id);


--
-- PostgreSQL database dump complete
--

