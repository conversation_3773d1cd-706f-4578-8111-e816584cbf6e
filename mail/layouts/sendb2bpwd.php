<?php
use yii\helpers\Html;

/* @var $this \yii\web\View view component instance */
/* @var $message \yii\mail\MessageInterface the message being composed */
/* @var $content string main view render result */
?>
<?php $this->beginPage() ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=<?= Yii::$app->charset ?>" />
    <title><?= Html::encode($this->title) ?></title>
    <?php $this->head() ?>
</head>
<body>
    <?php $this->beginBody() ?>
    <H4>Zdravíme vás!</H4>
    <p>Práve sme vám vytvorili prihlasovacie údaje do B2B nášho nového IT systému. Pomocou nich môžete tvoriť a sledovať objednávky.</p>

    <?= $content ?>

    <p>Starostlivo si prihlasovacie údaje uschovajte. Môžete si ich uložiť do svojho prehliadača po prihlásení, ak pracujete na počítači, ktorý máte pre seba dostatočne chránený. Kedykoľvek v prípade akéhokoľvek náznaku o možnom odcudzení prosím kontaktujte svojho obchodníka, alebo administrá<NAME_EMAIL> - heslo Vám zmeníme na počkanie. 
    </p>

    <p>S pozdravom</p>
    <p>Support team</p>
    <p><?= $_SERVER["HTTP_HOST"] ?></p>


    <H4>Welcome from B2B.RACIO.COM!</H4>
    <p>Your login was created/ updated. You can use it for creating and checking orders.</p>

    <?= $content ?>
    <p>Feel free to contact your dealer, or <NAME_EMAIL> if you need new password or help how to protect your login data. Thank you.
    </p>
    <p>Best regards</p>
    <p>Support team</p>
    <p><?= $_SERVER["HTTP_HOST"] ?></p>
    <?php $this->endBody() ?>
</body>
</html>
<?php $this->endPage() ?>
